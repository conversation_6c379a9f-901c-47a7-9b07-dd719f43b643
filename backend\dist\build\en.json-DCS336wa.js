var e={"settings.page.title":"Review Workflows","settings.page.subtitle":"{count, plural, one {# stage} other {# stages}}","settings.page.isLoading":"Workflow is loading","settings.page.delete.confirm.body":"All entries assigned to deleted stages will be moved to the previous stage. Are you sure you want to save?","settings.stage.name.label":"Stage name","settings.not-available":"Review Workflows is only available as part of the Enterprise Edition. Upgrade to create and manage workflows.","settings.review-workflows.workflow.stageRequiredToPublish.label":"Required stage for publishing","settings.review-workflows.workflow.stageRequiredToPublish.any":"Any stage","settings.review-workflows.workflow.stageRequiredToPublish.hint":"Prevents entries from being published if they are not at the required stage.","settings.page.purchase.description":"Manage your content review process","settings.page.purchase.perks1":"Customizable review stages","settings.page.purchase.perks2":"Manage user permissions","settings.page.purchase.perks3":"Support for webhooks","widget.assigned.title":"Assigned to me","widget.assigned.no-data":"No entries"};export{e as default};
