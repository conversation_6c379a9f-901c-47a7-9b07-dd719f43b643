import{bQ as I,b9 as v,w as z,a as O,v as R,ao as q,bR as w,r as _,bS as G,j as d,P as b,bT as j,aq as Q}from"./strapi-z7ApxZZq.js";import{C as U,T as $}from"./Form-D2oLFdBV.js";import{u as k}from"./hooks-DCxDPOZs.js";import"./sortable.esm-4pfM8QNQ.js";import"./FieldTypeIcon-B64159oL.js";const B=I.injectEndpoints({endpoints:e=>({getComponentConfiguration:e.query({query:t=>`/content-manager/components/${t}/configuration`,transformResponse:t=>t.data,providesTags:(t,o,s)=>[{type:"ComponentConfiguration",id:s}]}),updateComponentConfiguration:e.mutation({query:({uid:t,...o})=>({url:`/content-manager/components/${t}/configuration`,method:"PUT",data:o}),transformResponse:t=>t.data,invalidatesTags:(t,o,{uid:s})=>[{type:"ComponentConfiguration",id:s},{type:"ContentTypeSettings",id:"LIST"}]})})}),{useGetComponentConfigurationQuery:D,useUpdateComponentConfigurationMutation:H}=B,K=()=>{const{slug:e}=v(),{toggleNotification:t}=z(),{formatMessage:o}=O(),{_unstableFormatAPIError:s}=R(),{components:f,fieldSizes:E,schema:r,error:i,isLoading:c,isFetching:F}=q(void 0,{selectFromResult:a=>{const y=a.data?.components.find(n=>n.uid===e),C=a.data?.components.reduce((n,u)=>(n[u.uid]=u,n),{}),p=w(y?.attributes,C),m=Object.entries(a.data?.fieldSizes??{}).reduce((n,[u,{default:T}])=>(n[u]=T,n),{});return{isFetching:a.isFetching,isLoading:a.isLoading,error:a.error,components:p,schema:y,fieldSizes:m}}});_.useEffect(()=>{i&&t({type:"danger",message:s(i)})},[i,s,t]);const{data:g,isLoading:x,isFetching:M,error:l}=D(e??"");_.useEffect(()=>{l&&t({type:"danger",message:s(l)})},[l,s,t]);const h=x||c||M||F,L=_.useMemo(()=>g&&!h?V(g,{schema:r,components:f}):{layout:[],components:{},metadatas:{},options:{},settings:G},[g,h,r,f]),[P]=H(),A=async a=>{try{const y=Object.entries(g?.component.metadatas??{}).reduce((p,[m,{edit:n,list:u}])=>{const{__temp_key__:T,size:J,name:W,...N}=a.layout.flatMap(S=>S.children).find(S=>S.name===m)??{};return p[m]={edit:{...n,...N},list:u},p},{}),C=await P({layouts:{edit:a.layout.map(p=>p.children.reduce((m,{name:n,size:u})=>n!==$?[...m,{name:n,size:u}]:m,[])),list:g?.component.layouts.list},settings:Q(a.settings,"displayName",void 0),metadatas:y,uid:e});"data"in C?t({type:"success",message:o({id:"notification.success.saved",defaultMessage:"Saved"})}):t({type:"danger",message:s(C.error)})}catch{t({type:"danger",message:o({id:"notification.error",defaultMessage:"An error occurred"})})}};return h?d.jsx(b.Loading,{}):l||i||!r?d.jsx(b.Error,{}):d.jsxs(d.Fragment,{children:[d.jsx(b.Title,{children:`Configure ${L.settings.displayName} Edit View`}),d.jsx(U,{onSubmit:A,attributes:r.attributes,fieldSizes:E,layout:L})]})},V=(e,{schema:t,components:o})=>{const s=j(e.component.layouts.edit,t?.attributes,e.component.metadatas,{configurations:e.components,schemas:o}),f=Object.entries(e.components).reduce((r,[i,c])=>(r[i]={layout:j(c.layouts.edit,o[i].attributes,c.metadatas),settings:{...c.settings,icon:o[i].info.icon,displayName:o[i].info.displayName}},r),{}),E=Object.entries(e.component.metadatas).reduce((r,[i,c])=>({...r,[i]:c.edit}),{});return{layout:[s],components:f,metadatas:E,options:{...t?.options,...t?.pluginOptions},settings:{...e.component.settings,displayName:t?.info.displayName}}},nt=()=>{const e=k(t=>t.admin_app.permissions.contentManager?.componentsConfigurations);return d.jsx(b.Protect,{permissions:e,children:d.jsx(K,{})})};export{K as ComponentConfigurationPage,nt as ProtectedComponentConfigurationPage};
