import {
  selectAdminPermissions
} from "./chunk-FMATG6ZT.js";
import {
  useAdminRoles
} from "./chunk-Y76D23YT.js";
import {
  useFetchClient
} from "./chunk-3OKC5SXR.js";
import {
  SearchInput
} from "./chunk-ITIYAEI7.js";
import {
  ConfirmDialog
} from "./chunk-K4LYEYIN.js";
import "./chunk-DZIOEGNZ.js";
import "./chunk-L3WQFZOD.js";
import "./chunk-YXDCVYVT.js";
import {
  useRBAC
} from "./chunk-WIFIVZU3.js";
import "./chunk-T3UNFN7Y.js";
import {
  Layouts
} from "./chunk-DETWX3NC.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-FTSHQ5RF.js";
import {
  useQueryParams
} from "./chunk-RZWN45NY.js";
import {
  useTypedSelector
} from "./chunk-FOUXGIF2.js";
import {
  isFetchError
} from "./chunk-TXOJWASW.js";
import "./chunk-BUMITFEB.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import {
  fn
} from "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-PW6GS6S3.js";
import {
  Box,
  Button,
  Dialog,
  Flex,
  IconButton,
  TFooter,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Typography,
  VisuallyHidden,
  useIntl
} from "./chunk-Y6YT4U2T.js";
import {
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h,
  ForwardRef$1v,
  ForwardRef$3R,
  ForwardRef$j
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/ListPage.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/components/RoleRow.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var RoleRow = ({ id, name, description, usersCount, icons, rowIndex, canUpdate, cursor }) => {
  const { formatMessage } = useIntl();
  const [, editObject] = icons;
  const usersCountText = formatMessage({
    id: `Roles.RoleRow.user-count`,
    defaultMessage: "{number, plural, =0 {#  user} one {#  user} other {# users}}"
  }, {
    number: usersCount
  });
  return (0, import_jsx_runtime.jsxs)(Tr, {
    cursor,
    "aria-rowindex": rowIndex,
    // @ts-expect-error – the prop uses `HTMLButtonElement` but we just specify `HTMLElement`
    onClick: canUpdate ? editObject.onClick : void 0,
    children: [
      (0, import_jsx_runtime.jsx)(Td, {
        maxWidth: `13rem`,
        children: (0, import_jsx_runtime.jsx)(Typography, {
          ellipsis: true,
          textColor: "neutral800",
          children: name
        })
      }),
      (0, import_jsx_runtime.jsx)(Td, {
        maxWidth: `25rem`,
        children: (0, import_jsx_runtime.jsx)(Typography, {
          ellipsis: true,
          textColor: "neutral800",
          children: description
        })
      }),
      (0, import_jsx_runtime.jsx)(Td, {
        children: (0, import_jsx_runtime.jsx)(Typography, {
          textColor: "neutral800",
          children: usersCountText
        })
      }),
      (0, import_jsx_runtime.jsx)(Td, {
        children: (0, import_jsx_runtime.jsx)(Flex, {
          justifyContent: "flex-end",
          onClick: (e) => e.stopPropagation(),
          children: icons.map((icon, i) => {
            if (icon) {
              return (0, import_jsx_runtime.jsx)(Box, {
                paddingLeft: i === 0 ? 0 : 1,
                children: (0, import_jsx_runtime.jsx)(IconButton, {
                  ...icon,
                  variant: "ghost"
                })
              }, icon.label);
            }
            return null;
          })
        })
      })
    ]
  }, id);
};

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Roles/ListPage.mjs
var ListPage = () => {
  var _a;
  const { formatMessage } = useIntl();
  const permissions = useTypedSelector(selectAdminPermissions);
  const { formatAPIError } = useAPIErrorHandler();
  const { toggleNotification } = useNotification();
  const [isWarningDeleteAllOpened, setIsWarningDeleteAllOpenend] = React.useState(false);
  const [{ query }] = useQueryParams();
  const { isLoading: isLoadingForPermissions, allowedActions: { canCreate, canDelete, canRead, canUpdate } } = useRBAC((_a = permissions.settings) == null ? void 0 : _a.roles);
  const { roles, refetch: refetchRoles } = useAdminRoles({
    filters: (query == null ? void 0 : query._q) ? {
      name: {
        $containsi: query._q
      }
    } : void 0
  }, {
    refetchOnMountOrArgChange: true,
    skip: isLoadingForPermissions || !canRead
  });
  const navigate = useNavigate();
  const [{ roleToDelete }, dispatch] = React.useReducer(reducer, initialState);
  const { post } = useFetchClient();
  const handleDeleteData = async () => {
    try {
      dispatch({
        type: "ON_REMOVE_ROLES"
      });
      await post("/admin/roles/batch-delete", {
        ids: [
          roleToDelete
        ]
      });
      await refetchRoles();
      dispatch({
        type: "RESET_DATA_TO_DELETE"
      });
    } catch (error) {
      if (isFetchError(error)) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(error)
        });
      }
    }
  };
  const handleNewRoleClick = () => navigate("new");
  const handleToggleModal = () => setIsWarningDeleteAllOpenend((prev) => !prev);
  const handleClickDelete = (role) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (role.usersCount) {
      toggleNotification({
        type: "info",
        message: formatMessage({
          id: "Roles.ListPage.notification.delete-not-allowed"
        })
      });
    } else {
      dispatch({
        type: "SET_ROLE_TO_DELETE",
        id: role.id
      });
      handleToggleModal();
    }
  };
  const handleClickDuplicate = (role) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    navigate(`duplicate/${role.id}`);
  };
  const rowCount = roles.length + 1;
  const colCount = 6;
  if (isLoadingForPermissions) {
    return (0, import_jsx_runtime2.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime2.jsxs)(Page.Main, {
    children: [
      (0, import_jsx_runtime2.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "Roles"
        })
      }),
      (0, import_jsx_runtime2.jsx)(Layouts.Header, {
        primaryAction: canCreate ? (0, import_jsx_runtime2.jsx)(Button, {
          onClick: handleNewRoleClick,
          startIcon: (0, import_jsx_runtime2.jsx)(ForwardRef$1h, {}),
          size: "S",
          children: formatMessage({
            id: "Settings.roles.list.button.add",
            defaultMessage: "Add new role"
          })
        }) : null,
        title: formatMessage({
          id: "global.roles",
          defaultMessage: "roles"
        }),
        subtitle: formatMessage({
          id: "Settings.roles.list.description",
          defaultMessage: "List of roles"
        })
      }),
      canRead && (0, import_jsx_runtime2.jsx)(Layouts.Action, {
        startActions: (0, import_jsx_runtime2.jsx)(SearchInput, {
          label: formatMessage({
            id: "app.component.search.label",
            defaultMessage: "Search for {target}"
          }, {
            target: formatMessage({
              id: "global.roles",
              defaultMessage: "roles"
            })
          })
        })
      }),
      canRead && (0, import_jsx_runtime2.jsx)(Layouts.Content, {
        children: (0, import_jsx_runtime2.jsxs)(Table, {
          colCount,
          rowCount,
          footer: canCreate ? (0, import_jsx_runtime2.jsx)(TFooter, {
            cursor: "pointer",
            onClick: handleNewRoleClick,
            icon: (0, import_jsx_runtime2.jsx)(ForwardRef$1h, {}),
            children: formatMessage({
              id: "Settings.roles.list.button.add",
              defaultMessage: "Add new role"
            })
          }) : null,
          children: [
            (0, import_jsx_runtime2.jsx)(Thead, {
              children: (0, import_jsx_runtime2.jsxs)(Tr, {
                "aria-rowindex": 1,
                children: [
                  (0, import_jsx_runtime2.jsx)(Th, {
                    children: (0, import_jsx_runtime2.jsx)(Typography, {
                      variant: "sigma",
                      textColor: "neutral600",
                      children: formatMessage({
                        id: "global.name",
                        defaultMessage: "Name"
                      })
                    })
                  }),
                  (0, import_jsx_runtime2.jsx)(Th, {
                    children: (0, import_jsx_runtime2.jsx)(Typography, {
                      variant: "sigma",
                      textColor: "neutral600",
                      children: formatMessage({
                        id: "global.description",
                        defaultMessage: "Description"
                      })
                    })
                  }),
                  (0, import_jsx_runtime2.jsx)(Th, {
                    children: (0, import_jsx_runtime2.jsx)(Typography, {
                      variant: "sigma",
                      textColor: "neutral600",
                      children: formatMessage({
                        id: "global.users",
                        defaultMessage: "Users"
                      })
                    })
                  }),
                  (0, import_jsx_runtime2.jsx)(Th, {
                    children: (0, import_jsx_runtime2.jsx)(VisuallyHidden, {
                      children: formatMessage({
                        id: "global.actions",
                        defaultMessage: "Actions"
                      })
                    })
                  })
                ]
              })
            }),
            (0, import_jsx_runtime2.jsx)(Tbody, {
              children: roles == null ? void 0 : roles.map((role, index) => (0, import_jsx_runtime2.jsx)(RoleRow, {
                cursor: "pointer",
                id: role.id,
                name: role.name,
                description: role.description,
                usersCount: role.usersCount,
                icons: [
                  canCreate && {
                    onClick: handleClickDuplicate(role),
                    label: formatMessage({
                      id: "app.utils.duplicate",
                      defaultMessage: "Duplicate"
                    }),
                    children: (0, import_jsx_runtime2.jsx)(ForwardRef$3R, {})
                  },
                  canUpdate && {
                    onClick: () => navigate(role.id.toString()),
                    label: formatMessage({
                      id: "app.utils.edit",
                      defaultMessage: "Edit"
                    }),
                    children: (0, import_jsx_runtime2.jsx)(ForwardRef$1v, {})
                  },
                  canDelete && {
                    onClick: handleClickDelete(role),
                    label: formatMessage({
                      id: "global.delete",
                      defaultMessage: "Delete"
                    }),
                    children: (0, import_jsx_runtime2.jsx)(ForwardRef$j, {})
                  }
                ].filter(Boolean),
                rowIndex: index + 2,
                canUpdate
              }, role.id))
            })
          ]
        })
      }),
      (0, import_jsx_runtime2.jsx)(Dialog.Root, {
        open: isWarningDeleteAllOpened,
        onOpenChange: handleToggleModal,
        children: (0, import_jsx_runtime2.jsx)(ConfirmDialog, {
          onConfirm: handleDeleteData
        })
      })
    ]
  });
};
var initialState = {
  roleToDelete: null,
  showModalConfirmButtonLoading: false,
  shouldRefetchData: false
};
var reducer = (state, action) => fn(state, (draftState) => {
  switch (action.type) {
    case "ON_REMOVE_ROLES": {
      draftState.showModalConfirmButtonLoading = true;
      break;
    }
    case "ON_REMOVE_ROLES_SUCCEEDED": {
      draftState.shouldRefetchData = true;
      draftState.roleToDelete = null;
      break;
    }
    case "RESET_DATA_TO_DELETE": {
      draftState.shouldRefetchData = false;
      draftState.roleToDelete = null;
      draftState.showModalConfirmButtonLoading = false;
      break;
    }
    case "SET_ROLE_TO_DELETE": {
      draftState.roleToDelete = action.id;
      break;
    }
    default:
      return draftState;
  }
});
var ProtectedListPage = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a.roles.read;
  });
  return (0, import_jsx_runtime2.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime2.jsx)(ListPage, {})
  });
};
export {
  ListPage,
  ProtectedListPage
};
//# sourceMappingURL=ListPage-CRSFW5HS.js.map
