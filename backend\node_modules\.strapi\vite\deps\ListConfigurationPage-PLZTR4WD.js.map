{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/pages/ListConfiguration/components/Header.tsx", "../../../@strapi/content-manager/admin/src/pages/ListConfiguration/components/Settings.tsx", "../../../@strapi/content-manager/admin/src/pages/ListConfiguration/components/EditFieldForm.tsx", "../../../@strapi/content-manager/admin/src/pages/ListConfiguration/components/DraggableCard.tsx", "../../../@strapi/content-manager/admin/src/pages/ListConfiguration/components/SortDisplayedFields.tsx", "../../../@strapi/content-manager/admin/src/pages/ListConfiguration/ListConfigurationPage.tsx"], "sourcesContent": ["import { useForm, BackButton, Layouts } from '@strapi/admin/strapi-admin';\nimport { Button } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { useParams } from 'react-router-dom';\n\nimport { COLLECTION_TYPES } from '../../../constants/collections';\nimport { capitalise } from '../../../utils/strings';\nimport { getTranslation } from '../../../utils/translations';\n\ninterface HeaderProps {\n  collectionType: string;\n  name: string;\n  model: string;\n}\n\nconst Header = ({ name }: HeaderProps) => {\n  const { formatMessage } = useIntl();\n  const params = useParams<{ slug: string }>();\n\n  const modified = useForm('Header', (state) => state.modified);\n  const isSubmitting = useForm('Header', (state) => state.isSubmitting);\n\n  return (\n    <Layouts.Header\n      navigationAction={<BackButton fallback={`../${COLLECTION_TYPES}/${params.slug}`} />}\n      primaryAction={\n        <Button size=\"S\" disabled={!modified} type=\"submit\" loading={isSubmitting}>\n          {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n        </Button>\n      }\n      subtitle={formatMessage({\n        id: getTranslation('components.SettingsViewWrapper.pluginHeader.description.list-settings'),\n        defaultMessage: 'Define the settings of the list view.',\n      })}\n      title={formatMessage(\n        {\n          id: getTranslation('components.SettingsViewWrapper.pluginHeader.title'),\n          defaultMessage: 'Configure the view - {name}',\n        },\n        { name: capitalise(name) }\n      )}\n    />\n  );\n};\n\nexport { Header };\nexport type { HeaderProps };\n", "import * as React from 'react';\n\nimport { useForm, InputRenderer, type InputProps } from '@strapi/admin/strapi-admin';\nimport { Flex, Grid, Typography, useCollator } from '@strapi/design-system';\nimport { type MessageDescriptor, useIntl } from 'react-intl';\n\nimport { useDoc } from '../../../hooks/useDocument';\nimport { type EditFieldLayout } from '../../../hooks/useDocumentLayout';\nimport { getTranslation } from '../../../utils/translations';\nimport { type FormData } from '../ListConfigurationPage';\n\nimport type { DistributiveOmit } from 'react-redux';\n\nexport type InputPropsWithMessageDescriptors = DistributiveOmit<\n  InputProps,\n  'hint' | 'label' | 'placeholder'\n> & {\n  hint?: MessageDescriptor;\n  label: MessageDescriptor;\n  placeholder?: MessageDescriptor;\n};\n\n/**\n * @internal\n * @description Form inputs are always displayed in a grid, so we need\n * to use the size property to determine how many columns the input should\n * take up.\n */\nexport type FormLayoutInputProps = InputPropsWithMessageDescriptors & { size: number };\n\nconst EXCLUDED_SORT_ATTRIBUTE_TYPES = [\n  'media',\n  'richtext',\n  'dynamiczone',\n  'relation',\n  'component',\n  'json',\n  'blocks',\n];\n\ninterface SortOption {\n  value: string;\n  label: string;\n}\n\nconst Settings = () => {\n  const { formatMessage, locale } = useIntl();\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n  const { schema } = useDoc();\n\n  const layout = useForm<FormData['layout']>('Settings', (state) => state.values.layout ?? []);\n  const currentSortBy = useForm<FormData['settings']['defaultSortBy']>(\n    'Settings',\n    (state) => state.values.settings.defaultSortBy\n  );\n  const onChange = useForm('Settings', (state) => state.onChange);\n\n  const sortOptions = React.useMemo(\n    () =>\n      Object.values(layout).reduce<SortOption[]>((acc, field) => {\n        if (schema && !EXCLUDED_SORT_ATTRIBUTE_TYPES.includes(schema.attributes[field.name].type)) {\n          acc.push({\n            value: field.name,\n            label: typeof field.label !== 'string' ? formatMessage(field.label) : field.label,\n          });\n        }\n\n        return acc;\n      }, []),\n    [formatMessage, layout, schema]\n  );\n\n  const sortOptionsSorted = sortOptions.sort((a, b) => formatter.compare(a.label, b.label));\n\n  React.useEffect(() => {\n    if (sortOptionsSorted.findIndex((opt) => opt.value === currentSortBy) === -1) {\n      onChange('settings.defaultSortBy', sortOptionsSorted[0]?.value);\n    }\n  }, [currentSortBy, onChange, sortOptionsSorted]);\n\n  const formLayout = React.useMemo(\n    () =>\n      SETTINGS_FORM_LAYOUT.map((row) =>\n        row.map((field) => {\n          if (field.type === 'enumeration') {\n            return {\n              ...field,\n              hint: field.hint ? formatMessage(field.hint) : undefined,\n              label: formatMessage(field.label),\n              options: field.name === 'settings.defaultSortBy' ? sortOptionsSorted : field.options,\n            };\n          } else {\n            return {\n              ...field,\n              hint: field.hint ? formatMessage(field.hint) : undefined,\n              label: formatMessage(field.label),\n            };\n          }\n        })\n      ) as [top: EditFieldLayout[], bottom: EditFieldLayout[]],\n    [formatMessage, sortOptionsSorted]\n  );\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n      <Typography variant=\"delta\" tag=\"h2\">\n        {formatMessage({\n          id: getTranslation('containers.SettingPage.settings'),\n          defaultMessage: 'Settings',\n        })}\n      </Typography>\n      <Grid.Root key=\"bottom\" gap={4}>\n        {formLayout.map((row) =>\n          row.map(({ size, ...field }) => (\n            <Grid.Item key={field.name} s={12} col={size} direction=\"column\" alignItems=\"stretch\">\n              {/* @ts-expect-error – issue with EnumerationProps conflicting with InputProps */}\n              <InputRenderer {...field} />\n            </Grid.Item>\n          ))\n        )}\n      </Grid.Root>\n    </Flex>\n  );\n};\n\nconst SETTINGS_FORM_LAYOUT: FormLayoutInputProps[][] = [\n  [\n    {\n      label: {\n        id: getTranslation('form.Input.search'),\n        defaultMessage: 'Enable search',\n      },\n      name: 'settings.searchable',\n      size: 4,\n      type: 'boolean' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.filters'),\n        defaultMessage: 'Enable filters',\n      },\n      name: 'settings.filterable',\n      size: 4,\n      type: 'boolean' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.bulkActions'),\n        defaultMessage: 'Enable bulk actions',\n      },\n      name: 'settings.bulkable',\n      size: 4,\n      type: 'boolean' as const,\n    },\n  ],\n  [\n    {\n      hint: {\n        id: getTranslation('form.Input.pageEntries.inputDescription'),\n        defaultMessage: 'Note: You can override this value in the Collection Type settings page.',\n      },\n      label: {\n        id: getTranslation('form.Input.pageEntries'),\n        defaultMessage: 'Entries per page',\n      },\n      name: 'settings.pageSize',\n      options: ['10', '20', '50', '100'].map((value) => ({ value, label: value })),\n      size: 6,\n      type: 'enumeration' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.defaultSort'),\n        defaultMessage: 'Default sort attribute',\n      },\n      name: 'settings.defaultSortBy',\n      options: [],\n      size: 3,\n      type: 'enumeration' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.sort.order'),\n        defaultMessage: 'Default sort order',\n      },\n      name: 'settings.defaultSortOrder',\n      options: ['ASC', 'DESC'].map((value) => ({ value, label: value })),\n      size: 3,\n      type: 'enumeration' as const,\n    },\n  ],\n];\n\nexport { Settings };\n", "import { Form, useField, InputRenderer, useNotification } from '@strapi/admin/strapi-admin';\nimport { Button, Flex, FlexComponent, Grid, Modal } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\nimport * as yup from 'yup';\n\nimport { FieldTypeIcon } from '../../../components/FieldTypeIcon';\nimport { capitalise } from '../../../utils/strings';\nimport { getTranslation } from '../../../utils/translations';\n\nimport type { ListFieldLayout } from '../../../hooks/useDocumentLayout';\nimport type { FormData } from '../ListConfigurationPage';\n\ninterface EditFieldFormProps extends Pick<ListFieldLayout, 'attribute'> {\n  name: string;\n  onClose: () => void;\n}\n\nconst FIELD_SCHEMA = yup.object().shape({\n  label: yup.string().required(),\n  sortable: yup.boolean(),\n});\n\nconst EditFieldForm = ({ attribute, name, onClose }: EditFieldFormProps) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n\n  const { value, onChange } = useField<FormData['layout'][number]>(name);\n\n  if (!value) {\n    // This is very unlikely to happen, but it ensures the form is not opened without a value.\n    console.error(\n      \"You've opened a field to edit without it being part of the form, this is likely a bug with Strapi. Please open an issue.\"\n    );\n\n    toggleNotification({\n      message: formatMessage({\n        id: 'content-manager.containers.list-settings.modal-form.error',\n        defaultMessage: 'An error occurred while trying to open the form.',\n      }),\n      type: 'danger',\n    });\n\n    return null;\n  }\n\n  let shouldDisplaySortToggle = !['media', 'relation'].includes(attribute.type);\n\n  if ('relation' in attribute && ['oneWay', 'oneToOne', 'manyToOne'].includes(attribute.relation)) {\n    shouldDisplaySortToggle = true;\n  }\n\n  return (\n    <Modal.Content>\n      <Form\n        method=\"PUT\"\n        initialValues={value}\n        validationSchema={FIELD_SCHEMA}\n        onSubmit={(data) => {\n          onChange(name, data);\n          onClose();\n        }}\n      >\n        <Modal.Header>\n          <HeaderContainer>\n            {/* @ts-expect-error attribute.type === custom does not work here */}\n            <FieldTypeIcon type={attribute.type} />\n            <Modal.Title>\n              {formatMessage(\n                {\n                  id: getTranslation('containers.list-settings.modal-form.label'),\n                  defaultMessage: 'Edit {fieldName}',\n                },\n                { fieldName: capitalise(value.label) }\n              )}\n            </Modal.Title>\n          </HeaderContainer>\n        </Modal.Header>\n        <Modal.Body>\n          <Grid.Root gap={4}>\n            {[\n              {\n                name: 'label',\n                label: formatMessage({\n                  id: getTranslation('form.Input.label'),\n                  defaultMessage: 'Label',\n                }),\n                hint: formatMessage({\n                  id: getTranslation('form.Input.label.inputDescription'),\n                  defaultMessage: \"This value overrides the label displayed in the table's head\",\n                }),\n                size: 6,\n                type: 'string' as const,\n              },\n              {\n                label: formatMessage({\n                  id: getTranslation('form.Input.sort.field'),\n                  defaultMessage: 'Enable sort on this field',\n                }),\n                name: 'sortable',\n                size: 6,\n                type: 'boolean' as const,\n              },\n            ]\n              .filter(\n                (field) =>\n                  field.name !== 'sortable' ||\n                  (field.name === 'sortable' && shouldDisplaySortToggle)\n              )\n              .map(({ size, ...field }) => (\n                <Grid.Item\n                  key={field.name}\n                  s={12}\n                  col={size}\n                  direction=\"column\"\n                  alignItems=\"stretch\"\n                >\n                  <InputRenderer {...field} />\n                </Grid.Item>\n              ))}\n          </Grid.Root>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button onClick={onClose} variant=\"tertiary\">\n            {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n          </Button>\n          <Button type=\"submit\">\n            {formatMessage({ id: 'global.finish', defaultMessage: 'Finish' })}\n          </Button>\n        </Modal.Footer>\n      </Form>\n    </Modal.Content>\n  );\n};\n\nconst HeaderContainer = styled<FlexComponent>(Flex)`\n  svg {\n    width: 3.2rem;\n    margin-right: ${({ theme }) => theme.spaces[3]};\n  }\n`;\n\nexport { EditFieldForm };\nexport type { EditFieldFormProps };\n", "import * as React from 'react';\n\nimport {\n  Box,\n  BoxComponent,\n  Flex,\n  FlexComponent,\n  Modal,\n  Typography,\n  useComposedRefs,\n} from '@strapi/design-system';\nimport { Cross, Drag, Pencil } from '@strapi/icons';\nimport { getEmptyImage } from 'react-dnd-html5-backend';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { CardDragPreview } from '../../../components/DragPreviews/CardDragPreview';\nimport { ItemTypes } from '../../../constants/dragAndDrop';\nimport { useDragAndDrop } from '../../../hooks/useDragAndDrop';\nimport { getTranslation } from '../../../utils/translations';\n\nimport { EditFieldForm } from './EditFieldForm';\n\nimport type { ListFieldLayout } from '../../../hooks/useDocumentLayout';\n\ntype DraggableCardProps = Omit<ListFieldLayout, 'label'> & {\n  label: string;\n  index: number;\n  isDraggingSibling: boolean;\n  onMoveField: (dragIndex: number, hoverIndex: number) => void;\n  onRemoveField: () => void;\n  setIsDraggingSibling: (isDragging: boolean) => void;\n};\n\nconst DraggableCard = ({\n  attribute,\n  index,\n  isDraggingSibling,\n  label,\n  name,\n  onMoveField,\n  onRemoveField,\n  setIsDraggingSibling,\n}: DraggableCardProps) => {\n  const [isModalOpen, setIsModalOpen] = React.useState(false);\n  const { formatMessage } = useIntl();\n  const [, forceRerenderAfterDnd] = React.useState(false);\n\n  const [{ isDragging }, objectRef, dropRef, dragRef, dragPreviewRef] = useDragAndDrop(true, {\n    type: ItemTypes.FIELD,\n    item: { index, label, name },\n    index,\n    onMoveItem: onMoveField,\n    onEnd: () => setIsDraggingSibling(false),\n  });\n\n  React.useEffect(() => {\n    dragPreviewRef(getEmptyImage(), { captureDraggingState: false });\n  }, [dragPreviewRef]);\n\n  React.useEffect(() => {\n    if (isDragging) {\n      setIsDraggingSibling(true);\n    }\n  }, [isDragging, setIsDraggingSibling]);\n\n  // Effect in order to force a rerender after reordering the components\n  // Since we are removing the Accordion when doing the DnD  we are losing the dragRef, therefore the replaced element cannot be dragged\n  // anymore, this hack forces a rerender in order to apply the dragRef\n  React.useEffect(() => {\n    if (!isDraggingSibling) {\n      forceRerenderAfterDnd((prev) => !prev);\n    }\n  }, [isDraggingSibling]);\n\n  const composedRefs = useComposedRefs<HTMLButtonElement>(\n    dropRef,\n    objectRef as React.RefObject<HTMLButtonElement>\n  );\n\n  return (\n    <FieldWrapper ref={composedRefs}>\n      {isDragging && <CardDragPreview label={label} />}\n      {!isDragging && isDraggingSibling && <CardDragPreview isSibling label={label} />}\n\n      {!isDragging && !isDraggingSibling && (\n        <FieldContainer\n          borderColor=\"neutral150\"\n          background=\"neutral100\"\n          hasRadius\n          justifyContent=\"space-between\"\n          onClick={() => setIsModalOpen(true)}\n        >\n          <Flex gap={3}>\n            <DragButton\n              ref={dragRef}\n              aria-label={formatMessage(\n                {\n                  id: getTranslation('components.DraggableCard.move.field'),\n                  defaultMessage: 'Move {item}',\n                },\n                { item: label }\n              )}\n              onClick={(e) => e.stopPropagation()}\n            >\n              <Drag />\n            </DragButton>\n            <Typography fontWeight=\"bold\">{label}</Typography>\n          </Flex>\n          <Flex paddingLeft={3} onClick={(e) => e.stopPropagation()}>\n            <Modal.Root open={isModalOpen} onOpenChange={setIsModalOpen}>\n              <Modal.Trigger>\n                <ActionButton\n                  onClick={(e) => {\n                    e.stopPropagation();\n                  }}\n                  aria-label={formatMessage(\n                    {\n                      id: getTranslation('components.DraggableCard.edit.field'),\n                      defaultMessage: 'Edit {item}',\n                    },\n                    { item: label }\n                  )}\n                  type=\"button\"\n                >\n                  <Pencil width=\"1.2rem\" height=\"1.2rem\" />\n                </ActionButton>\n              </Modal.Trigger>\n              <EditFieldForm\n                attribute={attribute}\n                name={`layout.${index}`}\n                onClose={() => {\n                  setIsModalOpen(false);\n                }}\n              />\n            </Modal.Root>\n            <ActionButton\n              onClick={onRemoveField}\n              data-testid={`delete-${name}`}\n              aria-label={formatMessage(\n                {\n                  id: getTranslation('components.DraggableCard.delete.field'),\n                  defaultMessage: 'Delete {item}',\n                },\n                { item: label }\n              )}\n              type=\"button\"\n            >\n              <Cross width=\"1.2rem\" height=\"1.2rem\" />\n            </ActionButton>\n          </Flex>\n        </FieldContainer>\n      )}\n    </FieldWrapper>\n  );\n};\n\nconst ActionButton = styled.button`\n  display: flex;\n  align-items: center;\n  height: ${({ theme }) => theme.spaces[7]};\n  color: ${({ theme }) => theme.colors.neutral600};\n\n  &:hover {\n    color: ${({ theme }) => theme.colors.neutral700};\n  }\n\n  &:last-child {\n    padding: 0 ${({ theme }) => theme.spaces[3]};\n  }\n`;\n\nconst DragButton = styled(ActionButton)`\n  padding: 0 ${({ theme }) => theme.spaces[3]};\n  border-right: 1px solid ${({ theme }) => theme.colors.neutral150};\n  cursor: all-scroll;\n`;\n\nconst FieldContainer = styled<FlexComponent>(Flex)`\n  max-height: 3.2rem;\n  cursor: pointer;\n`;\n\nconst FieldWrapper = styled<BoxComponent>(Box)`\n  &:last-child {\n    padding-right: ${({ theme }) => theme.spaces[3]};\n  }\n`;\n\nexport { DraggableCard };\nexport type { DraggableCardProps };\n", "import * as React from 'react';\n\nimport { useForm } from '@strapi/admin/strapi-admin';\nimport { Box, Flex, VisuallyHidden, Typography, Menu } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { useDoc } from '../../../hooks/useDocument';\nimport { useGetContentTypeConfigurationQuery } from '../../../services/contentTypes';\nimport { checkIfAttributeIsDisplayable } from '../../../utils/attributes';\nimport { getTranslation } from '../../../utils/translations';\n\nimport { DraggableCard, DraggableCardProps } from './DraggableCard';\n\nimport type { ListLayout } from '../../../hooks/useDocumentLayout';\nimport type { FormData } from '../ListConfigurationPage';\n\ninterface SortDisplayedFieldsProps extends Pick<ListLayout, 'layout'> {}\n\nconst SortDisplayedFields = () => {\n  const { formatMessage } = useIntl();\n  const { model, schema } = useDoc();\n  const [isDraggingSibling, setIsDraggingSibling] = React.useState(false);\n  const [lastAction, setLastAction] = React.useState<string | null>(null);\n  const scrollableContainerRef = React.useRef<HTMLDivElement>(null);\n\n  const values = useForm<FormData['layout']>(\n    'SortDisplayedFields',\n    (state) => state.values.layout ?? []\n  );\n  const addFieldRow = useForm('SortDisplayedFields', (state) => state.addFieldRow);\n  const removeFieldRow = useForm('SortDisplayedFields', (state) => state.removeFieldRow);\n  const moveFieldRow = useForm('SortDisplayedFields', (state) => state.moveFieldRow);\n\n  const { metadata: allMetadata } = useGetContentTypeConfigurationQuery(model, {\n    selectFromResult: ({ data }) => ({ metadata: data?.contentType.metadatas ?? {} }),\n  });\n\n  /**\n   * This is our list of fields that are not displayed in the current layout\n   * so we create their default state to be added to the layout.\n   */\n  const nonDisplayedFields = React.useMemo(() => {\n    if (!schema) {\n      return [];\n    }\n\n    const displayedFieldNames = values.map((field) => field.name);\n\n    return Object.entries(schema.attributes).reduce<Array<FormData['layout'][number]>>(\n      (acc, [name, attribute]) => {\n        if (!displayedFieldNames.includes(name) && checkIfAttributeIsDisplayable(attribute)) {\n          const { list: metadata } = allMetadata[name];\n\n          acc.push({\n            name,\n            label: metadata.label || name,\n            sortable: metadata.sortable,\n          });\n        }\n\n        return acc;\n      },\n      []\n    );\n  }, [allMetadata, values, schema]);\n\n  const handleAddField = (field: FormData['layout'][number]) => {\n    setLastAction('add');\n    addFieldRow('layout', field);\n  };\n\n  const handleRemoveField = (index: number) => {\n    setLastAction('remove');\n    removeFieldRow('layout', index);\n  };\n\n  const handleMoveField: DraggableCardProps['onMoveField'] = (dragIndex, hoverIndex) => {\n    moveFieldRow('layout', dragIndex, hoverIndex);\n  };\n\n  React.useEffect(() => {\n    if (lastAction === 'add' && scrollableContainerRef?.current) {\n      scrollableContainerRef.current.scrollLeft = scrollableContainerRef.current.scrollWidth;\n    }\n  }, [lastAction]);\n\n  return (\n    <Flex alignItems=\"stretch\" direction=\"column\" gap={4}>\n      <Typography variant=\"delta\" tag=\"h2\">\n        {formatMessage({\n          id: getTranslation('containers.SettingPage.view'),\n          defaultMessage: 'View',\n        })}\n      </Typography>\n\n      <Flex padding={4} borderColor=\"neutral300\" borderStyle=\"dashed\" borderWidth=\"1px\" hasRadius>\n        <Box flex=\"1\" overflow=\"auto hidden\" ref={scrollableContainerRef}>\n          <Flex gap={3}>\n            {values.map((field, index) => (\n              <DraggableCard\n                key={field.name}\n                index={index}\n                isDraggingSibling={isDraggingSibling}\n                onMoveField={handleMoveField}\n                onRemoveField={() => handleRemoveField(index)}\n                setIsDraggingSibling={setIsDraggingSibling}\n                {...field}\n                attribute={schema!.attributes[field.name]}\n                label={typeof field.label === 'object' ? formatMessage(field.label) : field.label}\n              />\n            ))}\n          </Flex>\n        </Box>\n\n        <Menu.Root>\n          <Menu.Trigger\n            paddingLeft={2}\n            paddingRight={2}\n            justifyContent=\"center\"\n            endIcon={null}\n            disabled={nonDisplayedFields.length === 0}\n            variant=\"tertiary\"\n          >\n            <VisuallyHidden tag=\"span\">\n              {formatMessage({\n                id: getTranslation('components.FieldSelect.label'),\n                defaultMessage: 'Add a field',\n              })}\n            </VisuallyHidden>\n            <Plus aria-hidden focusable={false} style={{ position: 'relative', top: 2 }} />\n          </Menu.Trigger>\n          <Menu.Content>\n            {nonDisplayedFields.map((field) => (\n              <Menu.Item key={field.name} onSelect={() => handleAddField(field)}>\n                {typeof field.label === 'object' ? formatMessage(field.label) : field.label}\n              </Menu.Item>\n            ))}\n          </Menu.Content>\n        </Menu.Root>\n      </Flex>\n    </Flex>\n  );\n};\n\nexport { SortDisplayedFields };\nexport type { SortDisplayedFieldsProps };\n", "import * as React from 'react';\n\nimport {\n  Form,\n  type FormProps,\n  useNotification,\n  useTracking,\n  useAPIError<PERSON>andler,\n  Page,\n  Layouts,\n} from '@strapi/admin/strapi-admin';\nimport { Divider, Flex, Main } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { Navigate } from 'react-router-dom';\n\nimport { SINGLE_TYPES } from '../../constants/collections';\nimport { useDoc } from '../../hooks/useDocument';\nimport { ListFieldLayout, ListLayout, useDocLayout } from '../../hooks/useDocumentLayout';\nimport { useTypedSelector } from '../../modules/hooks';\nimport { useUpdateContentTypeConfigurationMutation } from '../../services/contentTypes';\nimport { setIn } from '../../utils/objects';\n\nimport { Header } from './components/Header';\nimport { Settings } from './components/Settings';\nimport { SortDisplayedFields } from './components/SortDisplayedFields';\n\nimport type { Metadatas } from '../../../../shared/contracts/content-types';\n\ninterface FormData extends Pick<ListLayout, 'settings'> {\n  layout: Array<Pick<ListFieldLayout, 'sortable' | 'name'> & { label: string }>;\n}\n\nconst ListConfiguration = () => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { model, collectionType } = useDoc();\n\n  const { isLoading: isLoadingLayout, list, edit } = useDocLayout();\n\n  const [updateContentTypeConfiguration] = useUpdateContentTypeConfigurationMutation();\n  const handleSubmit: FormProps<FormData>['onSubmit'] = async (data) => {\n    try {\n      trackUsage('willSaveContentTypeLayout');\n      const layoutData = data.layout ?? [];\n      /**\n       * We reconstruct the metadatas object by taking the existing edit metadatas\n       * and re-merging that by attribute name with the current list metadatas, whilst overwriting\n       * the data from the form we've built.\n       */\n      const meta = Object.entries(edit.metadatas).reduce<Metadatas>((acc, [name, editMeta]) => {\n        const { mainField: _mainField, ...listMeta } = list.metadatas[name];\n\n        const { label, sortable } = layoutData.find((field) => field.name === name) ?? {};\n\n        acc[name] = {\n          edit: editMeta,\n          list: {\n            ...listMeta,\n            label: label || listMeta.label,\n            sortable: sortable || listMeta.sortable,\n          },\n        };\n\n        return acc;\n      }, {});\n\n      const res = await updateContentTypeConfiguration({\n        layouts: {\n          edit: edit.layout.flatMap((panel) =>\n            panel.map((row) => row.map(({ name, size }) => ({ name, size })))\n          ),\n          list: layoutData.map((field) => field.name),\n        },\n        settings: setIn(data.settings, 'displayName', undefined),\n        metadatas: meta,\n        uid: model,\n      });\n\n      if ('data' in res) {\n        trackUsage('didEditListSettings');\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'notification.success.saved', defaultMessage: 'Saved' }),\n        });\n      } else {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n      }\n    } catch (err) {\n      console.error(err);\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n    }\n  };\n\n  const initialValues = React.useMemo(() => {\n    return {\n      layout: list.layout.map(({ label, sortable, name }) => ({\n        label: typeof label === 'string' ? label : formatMessage(label),\n        sortable,\n        name,\n      })),\n      settings: list.settings,\n    } satisfies FormData;\n  }, [formatMessage, list.layout, list.settings]);\n\n  if (collectionType === SINGLE_TYPES) {\n    return <Navigate to={`/single-types/${model}`} />;\n  }\n\n  if (isLoadingLayout) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Title>{`Configure ${list.settings.displayName} List View`}</Page.Title>\n      <Main>\n        <Form initialValues={initialValues} onSubmit={handleSubmit} method=\"PUT\">\n          <Header\n            collectionType={collectionType}\n            model={model}\n            name={list.settings.displayName ?? ''}\n          />\n          <Layouts.Content>\n            <Flex\n              alignItems=\"stretch\"\n              background=\"neutral0\"\n              direction=\"column\"\n              gap={6}\n              hasRadius\n              shadow=\"tableShadow\"\n              paddingTop={6}\n              paddingBottom={6}\n              paddingLeft={7}\n              paddingRight={7}\n            >\n              <Settings />\n              <Divider />\n              <SortDisplayedFields />\n            </Flex>\n          </Layouts.Content>\n        </Form>\n      </Main>\n    </Layouts.Root>\n  );\n};\n\nconst ProtectedListConfiguration = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.contentManager?.collectionTypesConfigurations\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListConfiguration />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedListConfiguration, ListConfiguration };\nexport type { FormData };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAMA,SAAS,CAAC,EAAEC,KAAI,MAAe;AACnC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,SAASC,UAAAA;AAEf,QAAMC,WAAWC,QAAQ,UAAU,CAACC,UAAUA,MAAMF,QAAQ;AAC5D,QAAMG,eAAeF,QAAQ,UAAU,CAACC,UAAUA,MAAMC,YAAY;AAEpE,aACEC,wBAACC,QAAQX,QAAM;IACbY,sBAAkBF,wBAACG,YAAAA;MAAWC,UAAU,MAAMC,gBAAAA,IAAoBX,OAAOY,IAAI;;IAC7EC,mBACEP,wBAACQ,QAAAA;MAAOC,MAAK;MAAIC,UAAU,CAACd;MAAUe,MAAK;MAASC,SAASb;gBAC1DP,cAAc;QAAEqB,IAAI;QAAeC,gBAAgB;MAAO,CAAA;;IAG/DC,UAAUvB,cAAc;MACtBqB,IAAIG,eAAe,uEAAA;MACnBF,gBAAgB;IAClB,CAAA;IACAG,OAAOzB,cACL;MACEqB,IAAIG,eAAe,mDAAA;MACnBF,gBAAgB;OAElB;MAAEvB,MAAM2B,WAAW3B,IAAAA;IAAM,CAAA;;AAIjC;;;;;ACbA,IAAM4B,gCAAgC;EACpC;EACA;EACA;EACA;EACA;EACA;EACA;AACD;AAOD,IAAMC,WAAW,MAAA;AACf,QAAM,EAAEC,eAAeC,OAAM,IAAKC,QAAAA;AAClC,QAAMC,YAAYC,YAAYH,QAAQ;IACpCI,aAAa;EACf,CAAA;AACA,QAAM,EAAEC,OAAM,IAAKC,OAAAA;AAEnB,QAAMC,SAASC,QAA4B,YAAY,CAACC,UAAUA,MAAMC,OAAOH,UAAU,CAAA,CAAE;AAC3F,QAAMI,gBAAgBH,QACpB,YACA,CAACC,UAAUA,MAAMC,OAAOE,SAASC,aAAa;AAEhD,QAAMC,WAAWN,QAAQ,YAAY,CAACC,UAAUA,MAAMK,QAAQ;AAE9D,QAAMC,cAAoBC,cACxB,MACEC,OAAOP,OAAOH,MAAQW,EAAAA,OAAqB,CAACC,KAAKC,UAAAA;AAC/C,QAAIf,UAAU,CAACR,8BAA8BwB,SAAShB,OAAOiB,WAAWF,MAAMG,IAAI,EAAEC,IAAI,GAAG;AACzFL,UAAIM,KAAK;QACPC,OAAON,MAAMG;QACbI,OAAO,OAAOP,MAAMO,UAAU,WAAW5B,cAAcqB,MAAMO,KAAK,IAAIP,MAAMO;MAC9E,CAAA;IACF;AAEA,WAAOR;EACT,GAAG,CAAA,CAAE,GACP;IAACpB;IAAeQ;IAAQF;EAAO,CAAA;AAGjC,QAAMuB,oBAAoBb,YAAYc,KAAK,CAACC,GAAGC,MAAM7B,UAAU8B,QAAQF,EAAEH,OAAOI,EAAEJ,KAAK,CAAA;AAEvFM,EAAMC,gBAAU,MAAA;;AACd,QAAIN,kBAAkBO,UAAU,CAACC,QAAQA,IAAIV,UAAUf,aAAmB,MAAA,IAAI;AAC5EG,eAAS,2BAA0Bc,uBAAkB,CAAA,MAAlBA,mBAAsBF,KAAAA;IAC3D;KACC;IAACf;IAAeG;IAAUc;EAAkB,CAAA;AAE/C,QAAMS,aAAmBrB,cACvB,MACEsB,qBAAqBC,IAAI,CAACC,QACxBA,IAAID,IAAI,CAACnB,UAAAA;AACP,QAAIA,MAAMI,SAAS,eAAe;AAChC,aAAO;QACL,GAAGJ;QACHqB,MAAMrB,MAAMqB,OAAO1C,cAAcqB,MAAMqB,IAAI,IAAIC;QAC/Cf,OAAO5B,cAAcqB,MAAMO,KAAK;QAChCgB,SAASvB,MAAMG,SAAS,2BAA2BK,oBAAoBR,MAAMuB;MAC/E;WACK;AACL,aAAO;QACL,GAAGvB;QACHqB,MAAMrB,MAAMqB,OAAO1C,cAAcqB,MAAMqB,IAAI,IAAIC;QAC/Cf,OAAO5B,cAAcqB,MAAMO,KAAK;MAClC;IACF;GAGN,CAAA,GAAA;IAAC5B;IAAe6B;EAAkB,CAAA;AAGpC,aACEgB,0BAACC,MAAAA;IAAKC,WAAU;IAASC,YAAW;IAAUC,KAAK;;UACjDC,yBAACC,YAAAA;QAAWC,SAAQ;QAAQC,KAAI;kBAC7BrD,cAAc;UACbsD,IAAIC,eAAe,iCAAA;UACnBC,gBAAgB;QAClB,CAAA;;UAEFN,yBAACO,KAAKC,MAAI;QAAcT,KAAK;QAC1BX,UAAAA,WAAWE,IAAI,CAACC,QACfA,IAAID,IAAI,CAAC,EAAEmB,MAAM,GAAGtC,MAAAA,UAClB6B,yBAACO,KAAKG,MAAI;UAAkBC,GAAG;UAAIC,KAAKH;UAAMZ,WAAU;UAASC,YAAW;UAE1E,cAAAE,yBAACa,uBAAAA;YAAe,GAAG1C;;QAFLA,GAAAA,MAAMG,IAAI,CAAA,CAAA;MAHjB,GAAA,QAAA;;;AAYrB;AAEA,IAAMe,uBAAiD;EACrD;IACE;MACEX,OAAO;QACL0B,IAAIC,eAAe,mBAAA;QACnBC,gBAAgB;MAClB;MACAhC,MAAM;MACNmC,MAAM;MACNlC,MAAM;IACR;IACA;MACEG,OAAO;QACL0B,IAAIC,eAAe,oBAAA;QACnBC,gBAAgB;MAClB;MACAhC,MAAM;MACNmC,MAAM;MACNlC,MAAM;IACR;IACA;MACEG,OAAO;QACL0B,IAAIC,eAAe,wBAAA;QACnBC,gBAAgB;MAClB;MACAhC,MAAM;MACNmC,MAAM;MACNlC,MAAM;IACR;EACD;EACD;IACE;MACEiB,MAAM;QACJY,IAAIC,eAAe,yCAAA;QACnBC,gBAAgB;MAClB;MACA5B,OAAO;QACL0B,IAAIC,eAAe,wBAAA;QACnBC,gBAAgB;MAClB;MACAhC,MAAM;MACNoB,SAAS;QAAC;QAAM;QAAM;QAAM;MAAM,EAACJ,IAAI,CAACb,WAAW;QAAEA;QAAOC,OAAOD;QAAM;MACzEgC,MAAM;MACNlC,MAAM;IACR;IACA;MACEG,OAAO;QACL0B,IAAIC,eAAe,wBAAA;QACnBC,gBAAgB;MAClB;MACAhC,MAAM;MACNoB,SAAS,CAAA;MACTe,MAAM;MACNlC,MAAM;IACR;IACA;MACEG,OAAO;QACL0B,IAAIC,eAAe,uBAAA;QACnBC,gBAAgB;MAClB;MACAhC,MAAM;MACNoB,SAAS;QAAC;QAAO;MAAO,EAACJ,IAAI,CAACb,WAAW;QAAEA;QAAOC,OAAOD;QAAM;MAC/DgC,MAAM;MACNlC,MAAM;IACR;EACD;AACF;;;;;;;;;;;;AC/KD,IAAMuC,eAAmBC,QAAM,EAAGC,MAAM;EACtCC,OAAWC,QAAM,EAAGC,SAAQ;EAC5BC,UAAcC,OAAO;AACvB,CAAA;AAEMC,IAAAA,gBAAgB,CAAC,EAAEC,WAAWC,MAAMC,QAAO,MAAsB;AACrE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAE/B,QAAM,EAAEC,OAAOC,SAAQ,IAAKC,SAAqCR,IAAAA;AAEjE,MAAI,CAACM,OAAO;AAEVG,YAAQC,MACN,0HAAA;AAGFN,uBAAmB;MACjBO,SAAST,cAAc;QACrBU,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAC,MAAM;IACR,CAAA;AAEA,WAAO;EACT;AAEA,MAAIC,0BAA0B,CAAC;IAAC;IAAS;IAAYC,SAASjB,UAAUe,IAAI;AAE5E,MAAI,cAAcf,aAAa;IAAC;IAAU;IAAY;EAAY,EAACiB,SAASjB,UAAUkB,QAAQ,GAAG;AAC/FF,8BAA0B;EAC5B;AAEA,aACEG,yBAACC,MAAMC,SAAO;IACZ,cAAAC,0BAACC,MAAAA;MACCC,QAAO;MACPC,eAAelB;MACfmB,kBAAkBnC;MAClBoC,UAAU,CAACC,SAAAA;AACTpB,iBAASP,MAAM2B,IAAAA;AACf1B,gBAAAA;MACF;;YAEAiB,yBAACC,MAAMS,QAAM;UACX,cAAAP,0BAACQ,iBAAAA;;kBAECX,yBAACY,eAAAA;gBAAchB,MAAMf,UAAUe;;kBAC/BI,yBAACC,MAAMY,OAAK;0BACT7B,cACC;kBACEU,IAAIoB,eAAe,2CAAA;kBACnBnB,gBAAgB;mBAElB;kBAAEoB,WAAWC,WAAW5B,MAAMb,KAAK;gBAAE,CAAA;;;;;YAK7CyB,yBAACC,MAAMgB,MAAI;wBACTjB,yBAACkB,KAAKC,MAAI;YAACC,KAAK;YACb,UAAA;cACC;gBACEtC,MAAM;gBACNP,OAAOS,cAAc;kBACnBU,IAAIoB,eAAe,kBAAA;kBACnBnB,gBAAgB;gBAClB,CAAA;gBACA0B,MAAMrC,cAAc;kBAClBU,IAAIoB,eAAe,mCAAA;kBACnBnB,gBAAgB;gBAClB,CAAA;gBACA2B,MAAM;gBACN1B,MAAM;cACR;cACA;gBACErB,OAAOS,cAAc;kBACnBU,IAAIoB,eAAe,uBAAA;kBACnBnB,gBAAgB;gBAClB,CAAA;gBACAb,MAAM;gBACNwC,MAAM;gBACN1B,MAAM;cACR;cAEC2B,OACC,CAACC,UACCA,MAAM1C,SAAS,cACd0C,MAAM1C,SAAS,cAAce,uBAAAA,EAEjC4B,IAAI,CAAC,EAAEH,MAAM,GAAGE,MAAO,UACtBxB,yBAACkB,KAAKQ,MAAI;cAERC,GAAG;cACHC,KAAKN;cACLO,WAAU;cACVC,YAAW;cAEX,cAAA9B,yBAAC+B,uBAAAA;gBAAe,GAAGP;;YANdA,GAAAA,MAAM1C,IAAI,CAAA;;;YAWzBqB,0BAACF,MAAM+B,QAAM;;gBACXhC,yBAACiC,QAAAA;cAAOC,SAASnD;cAASoD,SAAQ;wBAC/BnD,cAAc;gBAAEU,IAAI;gBAAgCC,gBAAgB;cAAS,CAAA;;gBAEhFK,yBAACiC,QAAAA;cAAOrC,MAAK;wBACVZ,cAAc;gBAAEU,IAAI;gBAAiBC,gBAAgB;cAAS,CAAA;;;;;;;AAM3E;AAEA,IAAMgB,kBAAkByB,GAAsBC,IAAAA;;;oBAG1B,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;;;ACxGlD,IAAMC,gBAAgB,CAAC,EACrBC,WACAC,OACAC,mBACAC,OACAC,MACAC,aACAC,eACAC,qBAAoB,MACD;AACnB,QAAM,CAACC,aAAaC,cAAAA,IAAwBC,gBAAS,KAAA;AACrD,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAAA,EAAGC,qBAAAA,IAA+BH,gBAAS,KAAA;AAEjD,QAAM,CAAC,EAAEI,WAAU,GAAIC,WAAWC,SAASC,SAASC,cAAAA,IAAkBC,eAAe,MAAM;IACzFC,MAAMC,UAAUC;IAChBC,MAAM;MAAEtB;MAAOE;MAAOC;IAAK;IAC3BH;IACAuB,YAAYnB;IACZoB,OAAO,MAAMlB,qBAAqB,KAAA;EACpC,CAAA;AAEAmB,EAAMC,iBAAU,MAAA;AACdT,mBAAeU,cAAiB,GAAA;MAAEC,sBAAsB;IAAM,CAAA;KAC7D;IAACX;EAAe,CAAA;AAEnBQ,EAAMC,iBAAU,MAAA;AACd,QAAIb,YAAY;AACdP,2BAAqB,IAAA;IACvB;KACC;IAACO;IAAYP;EAAqB,CAAA;AAKrCmB,EAAMC,iBAAU,MAAA;AACd,QAAI,CAACzB,mBAAmB;AACtBW,4BAAsB,CAACiB,SAAS,CAACA,IAAAA;IACnC;KACC;IAAC5B;EAAkB,CAAA;AAEtB,QAAM6B,eAAeC,gBACnBhB,SACAD,SAAAA;AAGF,aACEkB,0BAACC,cAAAA;IAAaC,KAAKJ;;MAChBjB,kBAAcsB,yBAACC,iBAAAA;QAAgBlC;;MAC/B,CAACW,cAAcZ,yBAAqBkC,yBAACC,iBAAAA;QAAgBC,WAAS;QAACnC;;MAE/D,CAACW,cAAc,CAACZ,yBACf+B,0BAACM,gBAAAA;QACCC,aAAY;QACZC,YAAW;QACXC,WAAS;QACTC,gBAAe;QACfC,SAAS,MAAMnC,eAAe,IAAA;;cAE9BwB,0BAACY,MAAAA;YAAKC,KAAK;;kBACTV,yBAACW,YAAAA;gBACCZ,KAAKlB;gBACL+B,cAAYrC,cACV;kBACEsC,IAAIC,eAAe,qCAAA;kBACnBC,gBAAgB;mBAElB;kBAAE5B,MAAMpB;gBAAM,CAAA;gBAEhByC,SAAS,CAACQ,MAAMA,EAAEC,gBAAe;gBAEjC,cAAAjB,yBAACkB,eAAAA,CAAAA,CAAAA;;kBAEHlB,yBAACmB,YAAAA;gBAAWC,YAAW;gBAAQrD,UAAAA;;;;cAEjC8B,0BAACY,MAAAA;YAAKY,aAAa;YAAGb,SAAS,CAACQ,MAAMA,EAAEC,gBAAe;;kBACrDpB,0BAACyB,MAAMC,MAAI;gBAACC,MAAMpD;gBAAaqD,cAAcpD;;sBAC3C2B,yBAACsB,MAAMI,SAAO;oBACZ,cAAA1B,yBAAC2B,cAAAA;sBACCnB,SAAS,CAACQ,MAAAA;AACRA,0BAAEC,gBAAe;sBACnB;sBACAL,cAAYrC,cACV;wBACEsC,IAAIC,eAAe,qCAAA;wBACnBC,gBAAgB;yBAElB;wBAAE5B,MAAMpB;sBAAM,CAAA;sBAEhBiB,MAAK;sBAEL,cAAAgB,yBAAC4B,eAAAA;wBAAOC,OAAM;wBAASC,QAAO;;;;sBAGlC9B,yBAAC+B,eAAAA;oBACCnE;oBACAI,MAAM,UAAUH,KAAAA;oBAChBmE,SAAS,MAAA;AACP3D,qCAAe,KAAA;oBACjB;;;;kBAGJ2B,yBAAC2B,cAAAA;gBACCnB,SAAStC;gBACT+D,eAAa,UAAUjE,IAAAA;gBACvB4C,cAAYrC,cACV;kBACEsC,IAAIC,eAAe,uCAAA;kBACnBC,gBAAgB;mBAElB;kBAAE5B,MAAMpB;gBAAM,CAAA;gBAEhBiB,MAAK;gBAEL,cAAAgB,yBAACkC,eAAAA;kBAAML,OAAM;kBAASC,QAAO;;;;;;;;;AAO3C;AAEA,IAAMH,eAAeQ,GAAOC;;;YAGhB,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;WAC/B,CAAC,EAAED,MAAK,MAAOA,MAAME,OAAOC,UAAU;;;aAGpC,CAAC,EAAEH,MAAK,MAAOA,MAAME,OAAOE,UAAU;;;;iBAIlC,CAAC,EAAEJ,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;AAI/C,IAAM3B,aAAawB,GAAOR,YAAAA;eACX,CAAC,EAAEU,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;4BACjB,CAAC,EAAED,MAAK,MAAOA,MAAME,OAAOG,UAAU;;;AAIlE,IAAMvC,iBAAiBgC,GAAsB1B,IAAAA;;;;AAK7C,IAAMX,eAAeqC,GAAqBQ,GAAAA;;qBAErB,CAAC,EAAEN,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;;;ACtKnD,IAAMM,sBAAsB,MAAA;AAC1B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,OAAOC,OAAM,IAAKC,OAAAA;AAC1B,QAAM,CAACC,mBAAmBC,oBAAAA,IAA8BC,gBAAS,KAAA;AACjE,QAAM,CAACC,YAAYC,aAAAA,IAAuBF,gBAAwB,IAAA;AAClE,QAAMG,yBAA+BC,cAAuB,IAAA;AAE5D,QAAMC,SAASC,QACb,uBACA,CAACC,UAAUA,MAAMF,OAAOG,UAAU,CAAA,CAAE;AAEtC,QAAMC,cAAcH,QAAQ,uBAAuB,CAACC,UAAUA,MAAME,WAAW;AAC/E,QAAMC,iBAAiBJ,QAAQ,uBAAuB,CAACC,UAAUA,MAAMG,cAAc;AACrF,QAAMC,eAAeL,QAAQ,uBAAuB,CAACC,UAAUA,MAAMI,YAAY;AAEjF,QAAM,EAAEC,UAAUC,YAAW,IAAKC,oCAAoCnB,OAAO;IAC3EoB,kBAAkB,CAAC,EAAEC,KAAI,OAAQ;MAAEJ,WAAUI,6BAAMC,YAAYC,cAAa,CAAA;;EAC9E,CAAA;AAMA,QAAMC,qBAA2BC,eAAQ,MAAA;AACvC,QAAI,CAACxB,QAAQ;AACX,aAAO,CAAA;IACT;AAEA,UAAMyB,sBAAsBhB,OAAOiB,IAAI,CAACC,UAAUA,MAAMC,IAAI;AAE5D,WAAOC,OAAOC,QAAQ9B,OAAO+B,UAAU,EAAEC,OACvC,CAACC,KAAK,CAACL,MAAMM,SAAU,MAAA;AACrB,UAAI,CAACT,oBAAoBU,SAASP,IAAAA,KAASQ,8BAA8BF,SAAY,GAAA;AACnF,cAAM,EAAEG,MAAMrB,SAAQ,IAAKC,YAAYW,IAAK;AAE5CK,YAAIK,KAAK;UACPV;UACAW,OAAOvB,SAASuB,SAASX;UACzBY,UAAUxB,SAASwB;QACrB,CAAA;MACF;AAEA,aAAOP;IACT,GACA,CAAA,CAAE;KAEH;IAAChB;IAAaR;IAAQT;EAAO,CAAA;AAEhC,QAAMyC,iBAAiB,CAACd,UAAAA;AACtBrB,kBAAc,KAAA;AACdO,gBAAY,UAAUc,KAAAA;EACxB;AAEA,QAAMe,oBAAoB,CAACC,UAAAA;AACzBrC,kBAAc,QAAA;AACdQ,mBAAe,UAAU6B,KAAAA;EAC3B;AAEA,QAAMC,kBAAqD,CAACC,WAAWC,eAAAA;AACrE/B,iBAAa,UAAU8B,WAAWC,UAAAA;EACpC;AAEAC,EAAMC,iBAAU,MAAA;AACd,QAAI3C,eAAe,UAASE,iEAAwB0C,UAAS;AAC3D1C,6BAAuB0C,QAAQC,aAAa3C,uBAAuB0C,QAAQE;IAC7E;KACC;IAAC9C;EAAW,CAAA;AAEf,aACE+C,0BAACC,MAAAA;IAAKC,YAAW;IAAUC,WAAU;IAASC,KAAK;;UACjDC,yBAACC,YAAAA;QAAWC,SAAQ;QAAQC,KAAI;kBAC7B/D,cAAc;UACbgE,IAAIC,eAAe,6BAAA;UACnBC,gBAAgB;QAClB,CAAA;;UAGFX,0BAACC,MAAAA;QAAKW,SAAS;QAAGC,aAAY;QAAaC,aAAY;QAASC,aAAY;QAAMC,WAAS;;cACzFX,yBAACY,KAAAA;YAAIC,MAAK;YAAIC,UAAS;YAAcC,KAAKjE;YACxC,cAAAkD,yBAACJ,MAAAA;cAAKG,KAAK;cACR/C,UAAAA,OAAOiB,IAAI,CAACC,OAAOgB,cAClBc,yBAACgB,eAAAA;gBAEC9B;gBACAzC;gBACAwE,aAAa9B;gBACb+B,eAAe,MAAMjC,kBAAkBC,KAAAA;gBACvCxC;gBACC,GAAGwB;gBACJO,WAAWlC,OAAQ+B,WAAWJ,MAAMC,IAAI;gBACxCW,OAAO,OAAOZ,MAAMY,UAAU,WAAW1C,cAAc8B,MAAMY,KAAK,IAAIZ,MAAMY;cARvEZ,GAAAA,MAAMC,IAAI,CAAA;;;cAcvBwB,0BAACwB,KAAKC,MAAI;;kBACRzB,0BAACwB,KAAKE,SAAO;gBACXC,aAAa;gBACbC,cAAc;gBACdC,gBAAe;gBACfC,SAAS;gBACTC,UAAU5D,mBAAmB6D,WAAW;gBACxCzB,SAAQ;;sBAERF,yBAAC4B,gBAAAA;oBAAezB,KAAI;8BACjB/D,cAAc;sBACbgE,IAAIC,eAAe,8BAAA;sBACnBC,gBAAgB;oBAClB,CAAA;;sBAEFN,yBAAC6B,eAAAA;oBAAKC,eAAW;oBAACC,WAAW;oBAAOC,OAAO;sBAAEC,UAAU;sBAAYC,KAAK;oBAAE;;;;kBAE5ElC,yBAACmB,KAAKgB,SAAO;gBACVrE,UAAAA,mBAAmBG,IAAI,CAACC,cACvB8B,yBAACmB,KAAKiB,MAAI;kBAAkBC,UAAU,MAAMrD,eAAed,KAAAA;4BACxD,OAAOA,MAAMY,UAAU,WAAW1C,cAAc8B,MAAMY,KAAK,IAAIZ,MAAMY;gBADxDZ,GAAAA,MAAMC,IAAI,CAAA;;;;;;;;AASxC;;;AC/GA,IAAMmE,oBAAoB,MAAA;AACxB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpD,QAAM,EAAEC,OAAOC,eAAc,IAAKC,OAAAA;AAElC,QAAM,EAAEC,WAAWC,iBAAiBC,MAAMC,KAAI,IAAKC,aAAAA;AAEnD,QAAM,CAACC,8BAAAA,IAAkCC,0CAAAA;AACzC,QAAMC,eAAgD,OAAOC,SAAAA;AAC3D,QAAI;AACFlB,iBAAW,2BAAA;AACX,YAAMmB,aAAaD,KAAKE,UAAU,CAAA;AAMlC,YAAMC,OAAOC,OAAOC,QAAQV,KAAKW,SAAS,EAAEC,OAAkB,CAACC,KAAK,CAACC,MAAMC,QAAS,MAAA;AAClF,cAAM,EAAEC,WAAWC,YAAY,GAAGC,SAAAA,IAAanB,KAAKY,UAAUG,IAAK;AAEnE,cAAM,EAAEK,OAAOC,SAAQ,IAAKd,WAAWe,KAAK,CAACC,UAAUA,MAAMR,SAASA,IAAAA,KAAS,CAAA;AAE/ED,YAAIC,IAAAA,IAAQ;UACVd,MAAMe;UACNhB,MAAM;YACJ,GAAGmB;YACHC,OAAOA,SAASD,SAASC;YACzBC,UAAUA,YAAYF,SAASE;UACjC;QACF;AAEA,eAAOP;MACT,GAAG,CAAA,CAAC;AAEJ,YAAMU,MAAM,MAAMrB,+BAA+B;QAC/CsB,SAAS;UACPxB,MAAMA,KAAKO,OAAOkB,QAAQ,CAACC,UACzBA,MAAMC,IAAI,CAACC,QAAQA,IAAID,IAAI,CAAC,EAAEb,MAAMe,KAAI,OAAQ;YAAEf;YAAMe;YAAK,CAAA,CAAA;UAE/D9B,MAAMO,WAAWqB,IAAI,CAACL,UAAUA,MAAMR,IAAI;QAC5C;QACAgB,UAAUC,MAAM1B,KAAKyB,UAAU,eAAeE,MAAAA;QAC9CrB,WAAWH;QACXyB,KAAKvC;MACP,CAAA;AAEA,UAAI,UAAU6B,KAAK;AACjBpC,mBAAW,qBAAA;AACXE,2BAAmB;UACjB6C,MAAM;UACNC,SAASlD,cAAc;YAAEmD,IAAI;YAA8BC,gBAAgB;UAAQ,CAAA;QACrF,CAAA;aACK;AACLhD,2BAAmB;UACjB6C,MAAM;UACNC,SAAS3C,eAAe+B,IAAIe,KAAK;QACnC,CAAA;MACF;IACF,SAASC,KAAK;AACZC,cAAQF,MAAMC,GAAAA;AACdlD,yBAAmB;QACjB6C,MAAM;QACNC,SAASlD,cAAc;UAAEmD,IAAI;UAAsBC,gBAAgB;QAAoB,CAAA;MACzF,CAAA;IACF;EACF;AAEA,QAAMI,gBAAsBC,eAAQ,MAAA;AAClC,WAAO;MACLnC,QAAQR,KAAKQ,OAAOoB,IAAI,CAAC,EAAER,OAAOC,UAAUN,KAAI,OAAQ;QACtDK,OAAO,OAAOA,UAAU,WAAWA,QAAQlC,cAAckC,KAAAA;QACzDC;QACAN;QACF;MACAgB,UAAU/B,KAAK+B;IACjB;KACC;IAAC7C;IAAec,KAAKQ;IAAQR,KAAK+B;EAAS,CAAA;AAE9C,MAAInC,mBAAmBgD,cAAc;AACnC,eAAOC,yBAACC,UAAAA;MAASC,IAAI,iBAAiBpD,KAAAA;;EACxC;AAEA,MAAII,iBAAiB;AACnB,eAAO8C,yBAACG,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,0BAACC,QAAQC,MAAI;;UACXP,yBAACG,KAAKK,OAAK;kBAAE,aAAarD,KAAK+B,SAASuB,WAAW;;UACnDT,yBAACU,MAAAA;QACC,cAAAL,0BAACM,MAAAA;UAAKd;UAA8Be,UAAUpD;UAAcqD,QAAO;;gBACjEb,yBAACc,QAAAA;cACC/D;cACAD;cACAoB,MAAMf,KAAK+B,SAASuB,eAAe;;gBAErCT,yBAACM,QAAQS,SAAO;cACd,cAAAV,0BAACW,MAAAA;gBACCC,YAAW;gBACXC,YAAW;gBACXC,WAAU;gBACVC,KAAK;gBACLC,WAAS;gBACTC,QAAO;gBACPC,YAAY;gBACZC,eAAe;gBACfC,aAAa;gBACbC,cAAc;;sBAEd1B,yBAAC2B,UAAAA,CAAAA,CAAAA;sBACD3B,yBAAC4B,SAAAA,CAAAA,CAAAA;sBACD5B,yBAAC6B,qBAAAA,CAAAA,CAAAA;;;;;;;;;AAOf;AAEA,IAAMC,6BAA6B,MAAA;AACjC,QAAMC,cAAcC,iBAClB,CAACC,UAAUA;;AAAAA,uBAAMC,UAAUH,YAAYI,mBAA5BF,mBAA4CG;GAAAA;AAGzD,aACEpC,yBAACG,KAAKkC,SAAO;IAACN;IACZ,cAAA/B,yBAAC5D,mBAAAA,CAAAA,CAAAA;;AAGP;", "names": ["Header", "name", "formatMessage", "useIntl", "params", "useParams", "modified", "useForm", "state", "isSubmitting", "_jsx", "Layouts", "navigationAction", "BackButton", "fallback", "COLLECTION_TYPES", "slug", "primaryAction", "<PERSON><PERSON>", "size", "disabled", "type", "loading", "id", "defaultMessage", "subtitle", "getTranslation", "title", "capitalise", "EXCLUDED_SORT_ATTRIBUTE_TYPES", "Settings", "formatMessage", "locale", "useIntl", "formatter", "useCollator", "sensitivity", "schema", "useDoc", "layout", "useForm", "state", "values", "currentSortBy", "settings", "defaultSortBy", "onChange", "sortOptions", "useMemo", "Object", "reduce", "acc", "field", "includes", "attributes", "name", "type", "push", "value", "label", "sortOptionsSorted", "sort", "a", "b", "compare", "React", "useEffect", "findIndex", "opt", "formLayout", "SETTINGS_FORM_LAYOUT", "map", "row", "hint", "undefined", "options", "_jsxs", "Flex", "direction", "alignItems", "gap", "_jsx", "Typography", "variant", "tag", "id", "getTranslation", "defaultMessage", "Grid", "Root", "size", "<PERSON><PERSON>", "s", "col", "InputR<PERSON><PERSON>", "FIELD_SCHEMA", "object", "shape", "label", "string", "required", "sortable", "boolean", "EditFieldForm", "attribute", "name", "onClose", "formatMessage", "useIntl", "toggleNotification", "useNotification", "value", "onChange", "useField", "console", "error", "message", "id", "defaultMessage", "type", "shouldDisplaySortToggle", "includes", "relation", "_jsx", "Modal", "Content", "_jsxs", "Form", "method", "initialValues", "validationSchema", "onSubmit", "data", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FieldTypeIcon", "Title", "getTranslation", "fieldName", "capitalise", "Body", "Grid", "Root", "gap", "hint", "size", "filter", "field", "map", "<PERSON><PERSON>", "s", "col", "direction", "alignItems", "InputR<PERSON><PERSON>", "Footer", "<PERSON><PERSON>", "onClick", "variant", "styled", "Flex", "theme", "spaces", "DraggableCard", "attribute", "index", "isDraggingSibling", "label", "name", "onMoveField", "onRemoveField", "setIsDraggingSibling", "isModalOpen", "setIsModalOpen", "useState", "formatMessage", "useIntl", "forceRerenderAfterDnd", "isDragging", "objectRef", "dropRef", "dragRef", "dragPreviewRef", "useDragAndDrop", "type", "ItemTypes", "FIELD", "item", "onMoveItem", "onEnd", "React", "useEffect", "getEmptyImage", "captureDraggingState", "prev", "composedRefs", "useComposedRefs", "_jsxs", "FieldWrapper", "ref", "_jsx", "CardDragPreview", "is<PERSON><PERSON>ling", "FieldC<PERSON>r", "borderColor", "background", "hasRadius", "justifyContent", "onClick", "Flex", "gap", "Drag<PERSON><PERSON><PERSON>", "aria-label", "id", "getTranslation", "defaultMessage", "e", "stopPropagation", "Drag", "Typography", "fontWeight", "paddingLeft", "Modal", "Root", "open", "onOpenChange", "<PERSON><PERSON>", "ActionButton", "Pencil", "width", "height", "EditFieldForm", "onClose", "data-testid", "Cross", "styled", "button", "theme", "spaces", "colors", "neutral600", "neutral700", "neutral150", "Box", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "formatMessage", "useIntl", "model", "schema", "useDoc", "isDraggingSibling", "setIsDraggingSibling", "useState", "lastAction", "setLastAction", "scrollableContainerRef", "useRef", "values", "useForm", "state", "layout", "addFieldRow", "removeFieldRow", "moveFieldRow", "metadata", "allMetadata", "useGetContentTypeConfigurationQuery", "selectFromResult", "data", "contentType", "metadatas", "nonDisplayedFields", "useMemo", "displayedFieldNames", "map", "field", "name", "Object", "entries", "attributes", "reduce", "acc", "attribute", "includes", "checkIfAttributeIsDisplayable", "list", "push", "label", "sortable", "handleAddField", "handleRemoveField", "index", "handleMoveField", "dragIndex", "hoverIndex", "React", "useEffect", "current", "scrollLeft", "scrollWidth", "_jsxs", "Flex", "alignItems", "direction", "gap", "_jsx", "Typography", "variant", "tag", "id", "getTranslation", "defaultMessage", "padding", "borderColor", "borderStyle", "borderWidth", "hasRadius", "Box", "flex", "overflow", "ref", "DraggableCard", "onMoveField", "onRemoveField", "<PERSON><PERSON>", "Root", "<PERSON><PERSON>", "paddingLeft", "paddingRight", "justifyContent", "endIcon", "disabled", "length", "VisuallyHidden", "Plus", "aria-hidden", "focusable", "style", "position", "top", "Content", "<PERSON><PERSON>", "onSelect", "ListConfiguration", "formatMessage", "useIntl", "trackUsage", "useTracking", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "model", "collectionType", "useDoc", "isLoading", "isLoadingLayout", "list", "edit", "useDocLayout", "updateContentTypeConfiguration", "useUpdateContentTypeConfigurationMutation", "handleSubmit", "data", "layoutData", "layout", "meta", "Object", "entries", "metadatas", "reduce", "acc", "name", "editMeta", "mainField", "_mainField", "listMeta", "label", "sortable", "find", "field", "res", "layouts", "flatMap", "panel", "map", "row", "size", "settings", "setIn", "undefined", "uid", "type", "message", "id", "defaultMessage", "error", "err", "console", "initialValues", "useMemo", "SINGLE_TYPES", "_jsx", "Navigate", "to", "Page", "Loading", "_jsxs", "Layouts", "Root", "Title", "displayName", "Main", "Form", "onSubmit", "method", "Header", "Content", "Flex", "alignItems", "background", "direction", "gap", "hasRadius", "shadow", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "Settings", "Divider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s", "ProtectedListConfiguration", "permissions", "useTypedSelector", "state", "admin_app", "contentManager", "collectionTypesConfigurations", "Protect"]}