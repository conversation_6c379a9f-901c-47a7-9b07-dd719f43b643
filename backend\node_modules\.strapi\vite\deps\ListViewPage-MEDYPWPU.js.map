{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/pages/ListView/components/Filters.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableCells/CellValue.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableCells/Components.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableCells/Media.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableCells/Relations.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableCells/CellContent.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/ViewSettingsMenu.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/ListViewPage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  Filters,\n  useField,\n  useAuth,\n  useTracking,\n  useQueryParams,\n  useAdminUsers,\n} from '@strapi/admin/strapi-admin';\nimport { Combobox, ComboboxOption, useCollator } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { CREATOR_FIELDS } from '../../../constants/attributes';\nimport { useContentTypeSchema } from '../../../hooks/useContentTypeSchema';\nimport { useDebounce } from '../../../hooks/useDebounce';\nimport { Schema } from '../../../hooks/useDocument';\nimport { useGetContentTypeConfigurationQuery } from '../../../services/contentTypes';\nimport { getMainField } from '../../../utils/attributes';\nimport { getDisplayName } from '../../../utils/users';\n\n/**\n * If new attributes are added, this list needs to be updated.\n */\nconst NOT_ALLOWED_FILTERS = [\n  'json',\n  'component',\n  'media',\n  'richtext',\n  'dynamiczone',\n  'password',\n  'blocks',\n];\nconst DEFAULT_ALLOWED_FILTERS = ['createdAt', 'updatedAt'];\nconst USER_FILTER_ATTRIBUTES = [...CREATOR_FIELDS, 'strapi_assignee'];\n\n/* -------------------------------------------------------------------------------------------------\n * Filters\n * -----------------------------------------------------------------------------------------------*/\ninterface FiltersProps {\n  disabled?: boolean;\n  schema: Schema;\n}\n\nconst FiltersImpl = ({ disabled, schema }: FiltersProps) => {\n  const { attributes, uid: model, options } = schema;\n  const { formatMessage, locale } = useIntl();\n  const { trackUsage } = useTracking();\n  const allPermissions = useAuth('FiltersImpl', (state) => state.permissions);\n  const [{ query }] = useQueryParams<Filters.Query>();\n  const { schemas } = useContentTypeSchema();\n\n  const canReadAdminUsers = React.useMemo(\n    () =>\n      allPermissions.filter(\n        (permission) => permission.action === 'admin::users.read' && permission.subject === null\n      ).length > 0,\n    [allPermissions]\n  );\n\n  const selectedUserIds = (query?.filters?.$and ?? []).reduce<string[]>((acc, filter) => {\n    const [key, value] = Object.entries(filter)[0];\n    if (typeof value.id !== 'object') {\n      return acc;\n    }\n\n    const id = value.id.$eq || value.id.$ne;\n\n    if (id && USER_FILTER_ATTRIBUTES.includes(key) && !acc.includes(id)) {\n      acc.push(id);\n    }\n\n    return acc;\n  }, []);\n\n  const { data: userData, isLoading: isLoadingAdminUsers } = useAdminUsers(\n    { filters: { id: { $in: selectedUserIds } } },\n    {\n      // fetch the list of admin users only if the filter contains users and the\n      // current user has permissions to display users\n      skip: selectedUserIds.length === 0 || !canReadAdminUsers,\n    }\n  );\n\n  const { users = [] } = userData ?? {};\n\n  const { metadata } = useGetContentTypeConfigurationQuery(model, {\n    selectFromResult: ({ data }) => ({ metadata: data?.contentType.metadatas ?? {} }),\n  });\n\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const displayedFilters = React.useMemo(() => {\n    const [{ properties: { fields = [] } = { fields: [] } }] = allPermissions.filter(\n      (permission) =>\n        permission.action === 'plugin::content-manager.explorer.read' &&\n        permission.subject === model\n    );\n\n    const allowedFields = fields.filter((field) => {\n      const attribute = attributes[field] ?? {};\n\n      return attribute.type && !NOT_ALLOWED_FILTERS.includes(attribute.type);\n    });\n\n    return (\n      [\n        'id',\n        'documentId',\n        ...allowedFields,\n        ...DEFAULT_ALLOWED_FILTERS,\n        ...(canReadAdminUsers ? CREATOR_FIELDS : []),\n      ]\n        .map((name) => {\n          const attribute = attributes[name];\n\n          if (NOT_ALLOWED_FILTERS.includes(attribute.type)) {\n            return null;\n          }\n\n          const { mainField: mainFieldName = '', label } = metadata[name].list;\n\n          let filter: Filters.Filter = {\n            name,\n            label: label ?? '',\n            mainField: getMainField(attribute, mainFieldName, { schemas, components: {} }),\n            // @ts-expect-error – TODO: this is filtered out above in the `allowedFields` call but TS complains, is there a better way to solve this?\n            type: attribute.type,\n          };\n\n          if (\n            attribute.type === 'relation' &&\n            'target' in attribute &&\n            attribute.target === 'admin::user'\n          ) {\n            filter = {\n              ...filter,\n              input: AdminUsersFilter,\n              options: users.map((user) => ({\n                label: getDisplayName(user),\n                value: user.id.toString(),\n              })),\n              operators: [\n                {\n                  label: formatMessage({\n                    id: 'components.FilterOptions.FILTER_TYPES.$eq',\n                    defaultMessage: 'is',\n                  }),\n                  value: '$eq',\n                },\n                {\n                  label: formatMessage({\n                    id: 'components.FilterOptions.FILTER_TYPES.$ne',\n                    defaultMessage: 'is not',\n                  }),\n                  value: '$ne',\n                },\n              ],\n              mainField: {\n                name: 'id',\n                type: 'integer',\n              },\n            };\n          }\n\n          if (attribute.type === 'enumeration') {\n            filter = {\n              ...filter,\n              options: attribute.enum.map((value) => ({\n                label: value,\n                value,\n              })),\n            };\n          }\n\n          return filter;\n        })\n        .filter(Boolean) as Filters.Filter[]\n    ).toSorted((a, b) => formatter.compare(a.label, b.label));\n  }, [\n    allPermissions,\n    canReadAdminUsers,\n    model,\n    attributes,\n    metadata,\n    schemas,\n    users,\n    formatMessage,\n    formatter,\n  ]);\n\n  const onOpenChange = (isOpen: boolean) => {\n    if (isOpen) {\n      trackUsage('willFilterEntries');\n    }\n  };\n\n  const handleFilterChange: Filters.Props['onChange'] = (data) => {\n    const attribute = attributes[data.name];\n\n    if (attribute) {\n      trackUsage('didFilterEntries', {\n        useRelation: attribute.type === 'relation',\n      });\n    }\n  };\n\n  return (\n    <Filters.Root\n      disabled={disabled}\n      options={displayedFilters}\n      onOpenChange={onOpenChange}\n      onChange={handleFilterChange}\n    >\n      <Filters.Trigger />\n      <Filters.Popover />\n      <Filters.List />\n    </Filters.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * AdminUsersFilter\n * -----------------------------------------------------------------------------------------------*/\n\nconst AdminUsersFilter = ({ name }: Filters.ValueInputProps) => {\n  const [pageSize, setPageSize] = React.useState(10);\n  const [search, setSearch] = React.useState('');\n  const { formatMessage } = useIntl();\n\n  const debouncedSearch = useDebounce(search, 300);\n\n  const { data, isLoading } = useAdminUsers({\n    pageSize,\n    _q: debouncedSearch,\n  });\n  const field = useField(name);\n\n  const handleOpenChange = (isOpen?: boolean) => {\n    if (!isOpen) {\n      setPageSize(10);\n    }\n  };\n\n  const { users = [], pagination } = data ?? {};\n  const { pageCount = 1, page = 1 } = pagination ?? {};\n\n  return (\n    <Combobox\n      value={field.value}\n      aria-label={formatMessage({\n        id: 'content-manager.components.Filters.usersSelect.label',\n        defaultMessage: 'Search and select a user to filter',\n      })}\n      onOpenChange={handleOpenChange}\n      onChange={(value) => field.onChange(name, value)}\n      loading={isLoading}\n      onLoadMore={() => setPageSize(pageSize + 10)}\n      hasMoreItems={page < pageCount}\n      onInputChange={(e: React.ChangeEvent<HTMLInputElement>) => {\n        setSearch(e.currentTarget.value);\n      }}\n    >\n      {users.map((user) => {\n        return (\n          <ComboboxOption key={user.id} value={user.id.toString()}>\n            {getDisplayName(user)}\n          </ComboboxOption>\n        );\n      })}\n    </Combobox>\n  );\n};\n\nexport { FiltersImpl as Filters };\nexport type { FiltersProps };\n", "import parseISO from 'date-fns/parseISO';\nimport toString from 'lodash/toString';\nimport { useIntl } from 'react-intl';\n\nimport type { Schema } from '@strapi/types';\n\ninterface CellValueProps {\n  type: Schema.Attribute.Kind | 'custom';\n  value: any;\n}\n\nconst CellValue = ({ type, value }: CellValueProps) => {\n  const { formatDate, formatTime, formatNumber } = useIntl();\n  let formattedValue = value;\n\n  if (type === 'date') {\n    formattedValue = formatDate(parseISO(value), { dateStyle: 'full' });\n  }\n\n  if (type === 'datetime') {\n    formattedValue = formatDate(value, { dateStyle: 'full', timeStyle: 'short' });\n  }\n\n  if (type === 'time') {\n    const [hour, minute, second] = value.split(':');\n    const date = new Date();\n    date.setHours(hour);\n    date.setMinutes(minute);\n    date.setSeconds(second);\n\n    formattedValue = formatTime(date, {\n      timeStyle: 'short',\n    });\n  }\n\n  if (['float', 'decimal'].includes(type)) {\n    formattedValue = formatNumber(value, {\n      // Should be kept in sync with the corresponding value\n      // in the design-system/NumberInput: https://github.com/strapi/design-system/blob/main/packages/strapi-design-system/src/NumberInput/NumberInput.js#L53\n      maximumFractionDigits: 20,\n    });\n  }\n\n  if (['integer', 'biginteger'].includes(type)) {\n    formattedValue = formatNumber(value, { maximumFractionDigits: 0 });\n  }\n\n  return toString(formattedValue);\n};\n\nexport { CellValue };\nexport type { CellValueProps };\n", "import { Bad<PERSON>, Tooltip, Typography, Menu } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { CellContentProps } from './CellContent';\nimport { CellValue } from './CellValue';\n\nimport type { Schema } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * SingleComponent\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SingleComponentProps extends Pick<CellContentProps, 'mainField'> {\n  content: Schema.Attribute.Value<Schema.Attribute.Component<`${string}.${string}`, false>>;\n}\n\nconst SingleComponent = ({ content, mainField }: SingleComponentProps) => {\n  if (!mainField) {\n    return null;\n  }\n\n  return (\n    <Tooltip label={content[mainField.name]}>\n      <Typography maxWidth=\"25rem\" textColor=\"neutral800\" ellipsis>\n        <CellValue type={mainField.type} value={content[mainField.name]} />\n      </Typography>\n    </Tooltip>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * RepeatableComponent\n * -----------------------------------------------------------------------------------------------*/\n\ninterface RepeatableComponentProps extends Pick<CellContentProps, 'mainField'> {\n  content: Schema.Attribute.Value<Schema.Attribute.Component<`${string}.${string}`, true>>;\n}\n\nconst RepeatableComponent = ({ content, mainField }: RepeatableComponentProps) => {\n  const { formatMessage } = useIntl();\n\n  if (!mainField) {\n    return null;\n  }\n\n  return (\n    <Menu.Root>\n      <Menu.Trigger onClick={(e) => e.stopPropagation()}>\n        <Badge>{content.length}</Badge>\n        {formatMessage(\n          {\n            id: 'content-manager.containers.list.items',\n            defaultMessage: '{number, plural, =0 {items} one {item} other {items}}',\n          },\n          { number: content.length }\n        )}\n      </Menu.Trigger>\n      <Menu.Content>\n        {content.map((item) => (\n          <Menu.Item key={item.id} disabled>\n            <Typography maxWidth=\"50rem\" ellipsis>\n              <CellValue type={mainField.type} value={item[mainField.name]} />\n            </Typography>\n          </Menu.Item>\n        ))}\n      </Menu.Content>\n    </Menu.Root>\n  );\n};\n\nexport { SingleComponent, RepeatableComponent };\nexport type { SingleComponentProps, RepeatableComponentProps };\n", "import * as React from 'react';\n\nimport { <PERSON><PERSON>, Flex, Tooltip, Typography, TypographyComponent } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nimport { prefixFileUrlWithBackendUrl } from '../../../../utils/urls';\n\nimport type { Data } from '@strapi/types';\n\ninterface MediaFile {\n  id?: Data.ID;\n  alternativeText?: string;\n  ext: string;\n  formats: {\n    thumbnail?: {\n      url?: string;\n    };\n  };\n  mime: string;\n  name: string;\n  url: string;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Media\n * -----------------------------------------------------------------------------------------------*/\n\ninterface MediaSingleProps extends MediaFile {}\n\nconst getFileExtension = (ext: string) => (ext && ext[0] === '.' ? ext.substring(1) : ext);\n\nconst MediaSingle = ({ url, mime, alternativeText, name, ext, formats }: MediaSingleProps) => {\n  const fileURL = prefixFileUrlWithBackendUrl(url)!;\n\n  if (mime.includes('image')) {\n    const thumbnail = formats?.thumbnail?.url;\n    const mediaURL = prefixFileUrlWithBackendUrl(thumbnail) || fileURL;\n\n    return (\n      <Avatar.Item\n        src={mediaURL}\n        alt={alternativeText || name}\n        fallback={alternativeText || name}\n        preview\n      />\n    );\n  }\n\n  const fileExtension = getFileExtension(ext);\n  const fileName = name.length > 100 ? `${name.substring(0, 100)}...` : name;\n\n  return (\n    <Tooltip description={fileName}>\n      <FileWrapper>{fileExtension}</FileWrapper>\n    </Tooltip>\n  );\n};\n\nconst FileWrapper = ({ children }: { children: React.ReactNode }) => {\n  return (\n    <Flex\n      tag=\"span\"\n      position=\"relative\"\n      borderRadius=\"50%\"\n      width=\"26px\"\n      height=\"26px\"\n      borderColor=\"neutral200\"\n      background=\"neutral150\"\n      paddingLeft=\"1px\"\n      justifyContent=\"center\"\n      alignItems=\"center\"\n    >\n      <FileTypography variant=\"sigma\" textColor=\"neutral600\">\n        {children}\n      </FileTypography>\n    </Flex>\n  );\n};\n\nconst FileTypography = styled<TypographyComponent>(Typography)`\n  font-size: 0.9rem;\n  line-height: 0.9rem;\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * MediaMultiple\n * -----------------------------------------------------------------------------------------------*/\n\ninterface MediaMultipleProps {\n  content: MediaFile[];\n}\n\nconst MediaMultiple = ({ content }: MediaMultipleProps) => {\n  return (\n    <Avatar.Group>\n      {content.map((file, index) => {\n        const key = `${file.id}${index}`;\n\n        if (index === 3) {\n          const remainingFiles = `+${content.length - 3}`;\n\n          return <FileWrapper key={key}>{remainingFiles}</FileWrapper>;\n        }\n\n        if (index > 3) {\n          return null;\n        }\n\n        return <MediaSingle key={key} {...file} />;\n      })}\n    </Avatar.Group>\n  );\n};\n\nexport { MediaMultiple, MediaSingle };\nexport type { MediaMultipleProps, MediaSingleProps };\n", "import * as React from 'react';\n\nimport { Typography, Loader, useNotifyAT, Menu } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { useDoc } from '../../../../hooks/useDocument';\nimport { useGetRelationsQuery } from '../../../../services/relations';\nimport { getRelationLabel } from '../../../../utils/relations';\nimport { getTranslation } from '../../../../utils/translations';\n\nimport type { CellContentProps } from './CellContent';\n\n/* -------------------------------------------------------------------------------------------------\n * RelationSingle\n * -----------------------------------------------------------------------------------------------*/\n\ninterface RelationSingleProps extends Pick<CellContentProps, 'mainField' | 'content'> {}\n\nconst RelationSingle = ({ mainField, content }: RelationSingleProps) => {\n  return (\n    <Typography maxWidth=\"50rem\" textColor=\"neutral800\" ellipsis>\n      {getRelationLabel(content, mainField)}\n    </Typography>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * RelationMultiple\n * -----------------------------------------------------------------------------------------------*/\n\ninterface RelationMultipleProps\n  extends Pick<CellContentProps, 'mainField' | 'content' | 'name' | 'rowId'> {}\n\n/**\n * TODO: fix this component – tracking issue https://strapi-inc.atlassian.net/browse/CONTENT-2184\n */\nconst RelationMultiple = ({ mainField, content, rowId, name }: RelationMultipleProps) => {\n  const { model } = useDoc();\n  const { formatMessage } = useIntl();\n  const { notifyStatus } = useNotifyAT();\n  const [isOpen, setIsOpen] = React.useState(false);\n\n  const [targetField] = name.split('.');\n\n  const { data, isLoading } = useGetRelationsQuery(\n    {\n      model,\n      id: rowId,\n      targetField,\n    },\n    {\n      skip: !isOpen,\n      refetchOnMountOrArgChange: true,\n    }\n  );\n\n  const contentCount = Array.isArray(content) ? content.length : content.count;\n\n  React.useEffect(() => {\n    if (data) {\n      notifyStatus(\n        formatMessage({\n          id: getTranslation('DynamicTable.relation-loaded'),\n          defaultMessage: 'Relations have been loaded',\n        })\n      );\n    }\n  }, [data, formatMessage, notifyStatus]);\n\n  return (\n    <Menu.Root onOpenChange={(isOpen) => setIsOpen(isOpen)}>\n      <Menu.Trigger onClick={(e) => e.stopPropagation()}>\n        <Typography style={{ cursor: 'pointer' }} textColor=\"neutral800\" fontWeight=\"regular\">\n          {contentCount > 0\n            ? formatMessage(\n                {\n                  id: 'content-manager.containers.list.items',\n                  defaultMessage: '{number} {number, plural, =0 {items} one {item} other {items}}',\n                },\n                { number: contentCount }\n              )\n            : '-'}\n        </Typography>\n      </Menu.Trigger>\n      <Menu.Content>\n        {isLoading && (\n          <Menu.Item disabled>\n            <Loader small>\n              {formatMessage({\n                id: getTranslation('ListViewTable.relation-loading'),\n                defaultMessage: 'Relations are loading',\n              })}\n            </Loader>\n          </Menu.Item>\n        )}\n        {data?.results && (\n          <>\n            {data.results.map((entry) => (\n              <Menu.Item key={entry.documentId}>\n                <Typography maxWidth=\"50rem\" ellipsis>\n                  {getRelationLabel(entry, mainField)}\n                </Typography>\n              </Menu.Item>\n            ))}\n\n            {data?.pagination && data?.pagination.total > 10 && (\n              <Menu.Item\n                aria-disabled\n                aria-label={formatMessage({\n                  id: getTranslation('ListViewTable.relation-more'),\n                  defaultMessage: 'This relation contains more entities than displayed',\n                })}\n              >\n                <Typography>…</Typography>\n              </Menu.Item>\n            )}\n          </>\n        )}\n      </Menu.Content>\n    </Menu.Root>\n  );\n};\n\nexport { RelationSingle, RelationMultiple };\nexport type { RelationSingleProps, RelationMultipleProps };\n", "import { Tooltip, Typography } from '@strapi/design-system';\nimport isEmpty from 'lodash/isEmpty';\n\nimport { CellValue } from './CellValue';\nimport { SingleComponent, RepeatableComponent } from './Components';\nimport { MediaSingle, MediaMultiple } from './Media';\nimport { RelationMultiple, RelationSingle } from './Relations';\n\nimport type { ListFieldLayout } from '../../../../hooks/useDocumentLayout';\nimport type { Schema, Data } from '@strapi/types';\n\ninterface CellContentProps extends Omit<ListFieldLayout, 'cellFormatter'> {\n  content: Schema.Attribute.Value<Schema.Attribute.AnyAttribute>;\n  rowId: Data.ID;\n}\n\nconst CellContent = ({ content, mainField, attribute, rowId, name }: CellContentProps) => {\n  if (!hasContent(content, mainField, attribute)) {\n    return (\n      <Typography\n        textColor=\"neutral800\"\n        paddingLeft={attribute.type === ('relation' || 'component') ? '1.6rem' : 0}\n        paddingRight={attribute.type === ('relation' || 'component') ? '1.6rem' : 0}\n      >\n        -\n      </Typography>\n    );\n  }\n\n  switch (attribute.type) {\n    case 'media':\n      if (!attribute.multiple) {\n        return <MediaSingle {...content} />;\n      }\n\n      return <MediaMultiple content={content} />;\n\n    case 'relation': {\n      if (isSingleRelation(attribute.relation)) {\n        return <RelationSingle mainField={mainField} content={content} />;\n      }\n\n      return <RelationMultiple rowId={rowId} mainField={mainField} content={content} name={name} />;\n    }\n\n    case 'component':\n      if (attribute.repeatable) {\n        return <RepeatableComponent mainField={mainField} content={content} />;\n      }\n\n      return <SingleComponent mainField={mainField} content={content} />;\n\n    case 'string':\n      return (\n        <Tooltip description={content}>\n          <Typography maxWidth=\"30rem\" ellipsis textColor=\"neutral800\">\n            <CellValue type={attribute.type} value={content} />\n          </Typography>\n        </Tooltip>\n      );\n\n    default:\n      return (\n        <Typography maxWidth=\"30rem\" ellipsis textColor=\"neutral800\">\n          <CellValue type={attribute.type} value={content} />\n        </Typography>\n      );\n  }\n};\n\nconst hasContent = (\n  content: CellContentProps['content'],\n  mainField: CellContentProps['mainField'],\n  attribute: CellContentProps['attribute']\n) => {\n  if (attribute.type === 'component') {\n    // Repeatable fields show the ID as fallback, in case the mainField\n    // doesn't have any content\n    if (attribute.repeatable || !mainField) {\n      return content?.length > 0;\n    }\n\n    const value = content?.[mainField.name];\n\n    // relations, media ... show the id as fallback\n    if (mainField.name === 'id' && ![undefined, null].includes(value)) {\n      return true;\n    }\n\n    return !isEmpty(value);\n  }\n\n  if (attribute.type === 'relation') {\n    if (isSingleRelation(attribute.relation)) {\n      return !isEmpty(content);\n    }\n\n    if (Array.isArray(content)) {\n      return content.length > 0;\n    }\n\n    return content?.count > 0;\n  }\n\n  /*\n      Biginteger fields need to be treated as strings, as `isNumber`\n      doesn't deal with them.\n  */\n  if (['integer', 'decimal', 'float', 'number'].includes(attribute.type)) {\n    return typeof content === 'number';\n  }\n\n  if (attribute.type === 'boolean') {\n    return content !== null;\n  }\n\n  return !isEmpty(content);\n};\n\nconst isSingleRelation = (\n  type: Extract<CellContentProps['attribute'], { type: 'relation' }>['relation']\n) => ['oneToOne', 'manyToOne', 'oneToOneMorph'].includes(type);\n\nexport { CellContent };\nexport type { CellContentProps };\n", "import * as React from 'react';\n\nimport { useTracking, useRBAC, useQueryParams } from '@strapi/admin/strapi-admin';\nimport {\n  Flex,\n  IconButton,\n  Popover,\n  Checkbox,\n  TextButton,\n  Typography,\n  useCollator,\n  LinkButton,\n} from '@strapi/design-system';\nimport { Cog, ListPlus } from '@strapi/icons';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\n\nimport { useDoc } from '../../../hooks/useDocument';\nimport { useDocumentLayout } from '../../../hooks/useDocumentLayout';\nimport { useTypedSelector } from '../../../modules/hooks';\nimport { checkIfAttributeIsDisplayable } from '../../../utils/attributes';\n\ninterface ViewSettingsMenuProps extends FieldPickerProps {}\n\nconst ViewSettingsMenu = (props: ViewSettingsMenuProps) => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.contentManager?.collectionTypesConfigurations ?? []\n  );\n  const [{ query }] = useQueryParams<{ plugins?: Record<string, unknown> }>();\n  const { formatMessage } = useIntl();\n  const {\n    allowedActions: { canConfigureView },\n  } = useRBAC(permissions);\n\n  return (\n    <Popover.Root>\n      <Popover.Trigger>\n        <IconButton\n          label={formatMessage({\n            id: 'components.ViewSettings.tooltip',\n            defaultMessage: 'View Settings',\n          })}\n        >\n          <Cog />\n        </IconButton>\n      </Popover.Trigger>\n      <Popover.Content side=\"bottom\" align=\"end\" sideOffset={4}>\n        <Flex alignItems=\"stretch\" direction=\"column\" padding={3} gap={3}>\n          {canConfigureView ? (\n            <LinkButton\n              size=\"S\"\n              startIcon={<ListPlus />}\n              variant=\"secondary\"\n              tag={NavLink}\n              to={{\n                pathname: 'configurations/list',\n                search: query.plugins\n                  ? stringify({ plugins: query.plugins }, { encode: false })\n                  : '',\n              }}\n            >\n              {formatMessage({\n                id: 'app.links.configure-view',\n                defaultMessage: 'Configure the view',\n              })}\n            </LinkButton>\n          ) : null}\n          <FieldPicker {...props} />\n        </Flex>\n      </Popover.Content>\n    </Popover.Root>\n  );\n};\n\ninterface FieldPickerProps {\n  headers?: string[];\n  setHeaders: (headers: string[]) => void;\n  resetHeaders: () => void;\n}\n\nconst FieldPicker = ({ headers = [], resetHeaders, setHeaders }: FieldPickerProps) => {\n  const { trackUsage } = useTracking();\n  const { formatMessage, locale } = useIntl();\n\n  const { schema, model } = useDoc();\n  const { list } = useDocumentLayout(model);\n\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const attributes = schema?.attributes ?? {};\n\n  const columns = Object.keys(attributes)\n    .filter((name) => checkIfAttributeIsDisplayable(attributes[name]))\n    .map((name) => ({\n      name,\n      label: list.metadatas[name]?.label ?? '',\n    }))\n    .sort((a, b) => formatter.compare(a.label, b.label));\n\n  const handleChange = (name: string) => {\n    trackUsage('didChangeDisplayedFields');\n\n    /**\n     * create an array of the new headers, if the new name exists it should be removed,\n     * otherwise it should be added\n     */\n    const newHeaders = headers.includes(name)\n      ? headers.filter((header) => header !== name)\n      : [...headers, name];\n\n    setHeaders(newHeaders);\n  };\n\n  const handleReset = () => {\n    resetHeaders();\n  };\n\n  return (\n    <Flex\n      tag=\"fieldset\"\n      direction=\"column\"\n      alignItems=\"stretch\"\n      gap={3}\n      borderWidth={0}\n      maxHeight={'240px'}\n      overflow={'scroll'}\n    >\n      <Flex justifyContent=\"space-between\">\n        <Typography tag=\"legend\" variant=\"pi\" fontWeight=\"bold\">\n          {formatMessage({\n            id: 'containers.list.displayedFields',\n            defaultMessage: 'Displayed fields',\n          })}\n        </Typography>\n\n        <TextButton onClick={handleReset}>\n          {formatMessage({\n            id: 'app.components.Button.reset',\n            defaultMessage: 'Reset',\n          })}\n        </TextButton>\n      </Flex>\n\n      <Flex direction=\"column\" alignItems=\"stretch\">\n        {columns.map((header) => {\n          const isActive = headers.includes(header.name);\n\n          return (\n            <Flex\n              wrap=\"wrap\"\n              gap={2}\n              background={isActive ? 'primary100' : 'transparent'}\n              hasRadius\n              padding={2}\n              key={header.name}\n            >\n              <Checkbox\n                onCheckedChange={() => handleChange(header.name)}\n                checked={isActive}\n                name={header.name}\n              >\n                <Typography fontSize={1}>{header.label}</Typography>\n              </Checkbox>\n            </Flex>\n          );\n        })}\n      </Flex>\n    </Flex>\n  );\n};\n\nexport { ViewSettingsMenu };\nexport type { ViewSettingsMenuProps, FieldPickerProps };\n", "import * as React from 'react';\n\nimport {\n  Page,\n  Pagination,\n  SearchInput,\n  Table,\n  BackButton,\n  useNotification,\n  useStrapiApp,\n  useTracking,\n  useAPIError<PERSON>and<PERSON>,\n  useQueryParams,\n  useRBAC,\n  Layouts,\n  useTable,\n  unstable_tours,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Button,\n  Flex,\n  Typography,\n  ButtonProps,\n  Box,\n  EmptyStateLayout,\n} from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport isEqual from 'lodash/isEqual';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { useNavigate, Link as ReactRouterLink, useParams } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { InjectionZone } from '../../components/InjectionZone';\nimport { HOOKS } from '../../constants/hooks';\nimport { PERMISSIONS } from '../../constants/plugin';\nimport { DocumentRBAC, useDocumentRBAC } from '../../features/DocumentRBAC';\nimport { useDoc } from '../../hooks/useDocument';\nimport {\n  ListFieldLayout,\n  convertListLayoutToFieldLayouts,\n  useDocumentLayout,\n} from '../../hooks/useDocumentLayout';\nimport { usePrev } from '../../hooks/usePrev';\nimport { useGetAllDocumentsQuery } from '../../services/documents';\nimport { buildValidParams } from '../../utils/api';\nimport { getTranslation } from '../../utils/translations';\nimport { getDisplayName } from '../../utils/users';\nimport { DocumentStatus } from '../EditView/components/DocumentStatus';\n\nimport { BulkActionsRenderer } from './components/BulkActions/Actions';\nimport { Filters } from './components/Filters';\nimport { TableActions } from './components/TableActions';\nimport { CellContent } from './components/TableCells/CellContent';\nimport { ViewSettingsMenu } from './components/ViewSettingsMenu';\n\nimport type { Modules } from '@strapi/types';\n\nconst { INJECT_COLUMN_IN_TABLE } = HOOKS;\n\n/* -------------------------------------------------------------------------------------------------\n * ListViewPage\n * -----------------------------------------------------------------------------------------------*/\nconst LayoutsHeaderCustom = styled(Layouts.Header)`\n  overflow-wrap: anywhere;\n`;\n\nconst ListViewPage = () => {\n  const { trackUsage } = useTracking();\n  const navigate = useNavigate();\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler(getTranslation);\n\n  const { collectionType, model, schema } = useDoc();\n  const { list } = useDocumentLayout(model);\n\n  const [displayedHeaders, setDisplayedHeaders] = React.useState<ListFieldLayout[]>([]);\n\n  const listLayout = usePrev(list.layout);\n  React.useEffect(() => {\n    /**\n     * ONLY update the displayedHeaders if the document\n     * layout has actually changed in value.\n     */\n    if (!isEqual(listLayout, list.layout)) {\n      setDisplayedHeaders(list.layout);\n    }\n  }, [list.layout, listLayout]);\n\n  const handleSetHeaders = (headers: string[]) => {\n    setDisplayedHeaders(\n      convertListLayoutToFieldLayouts(headers, schema!.attributes, list.metadatas)\n    );\n  };\n\n  const [{ query }] = useQueryParams<{\n    plugins?: Record<string, unknown>;\n    page?: string;\n    pageSize?: string;\n    sort?: string;\n  }>({\n    page: '1',\n    pageSize: list.settings.pageSize.toString(),\n    sort: list.settings.defaultSortBy\n      ? `${list.settings.defaultSortBy}:${list.settings.defaultSortOrder}`\n      : '',\n  });\n\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n\n  const { data, error, isFetching } = useGetAllDocumentsQuery({\n    model,\n    params,\n  });\n\n  /**\n   * If the API returns an error, display a notification\n   */\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  const { results = [], pagination } = data ?? {};\n\n  React.useEffect(() => {\n    if (pagination && pagination.pageCount > 0 && pagination.page > pagination.pageCount) {\n      navigate(\n        {\n          search: stringify({\n            ...query,\n            page: pagination.pageCount,\n          }),\n        },\n        { replace: true }\n      );\n    }\n  }, [pagination, formatMessage, query, navigate]);\n\n  const { canCreate } = useDocumentRBAC('ListViewPage', ({ canCreate }) => ({\n    canCreate,\n  }));\n\n  const runHookWaterfall = useStrapiApp('ListViewPage', ({ runHookWaterfall }) => runHookWaterfall);\n  /**\n   * Run the waterfall and then inject our additional table headers.\n   */\n  const tableHeaders = React.useMemo(() => {\n    const headers = runHookWaterfall(INJECT_COLUMN_IN_TABLE, {\n      displayedHeaders,\n      layout: list,\n    });\n\n    const formattedHeaders = headers.displayedHeaders.map<ListFieldLayout>((header) => {\n      /**\n       * When the header label is a string, it is an attribute on the current content-type:\n       * Use the attribute name value to compute the translation.\n       * Otherwise, it should be a  translation object coming from a plugin that injects into the table (ie i18n, content-releases, review-workflows):\n       * Use the translation object as is.\n       */\n      const translation =\n        typeof header.label === 'string'\n          ? {\n              id: `content-manager.content-types.${model}.${header.name}`,\n              defaultMessage: header.label,\n            }\n          : header.label;\n\n      return {\n        ...header,\n        label: formatMessage(translation),\n        name: `${header.name}${header.mainField?.name ? `.${header.mainField.name}` : ''}`,\n      };\n    });\n\n    if (schema?.options?.draftAndPublish) {\n      formattedHeaders.push({\n        attribute: {\n          type: 'custom',\n        },\n        name: 'status',\n        label: formatMessage({\n          id: getTranslation(`containers.list.table-headers.status`),\n          defaultMessage: 'status',\n        }),\n        searchable: false,\n        sortable: false,\n      } satisfies ListFieldLayout);\n    }\n\n    return formattedHeaders;\n  }, [\n    displayedHeaders,\n    formatMessage,\n    list,\n    runHookWaterfall,\n    schema?.options?.draftAndPublish,\n    model,\n  ]);\n\n  if (isFetching) {\n    return <Page.Loading />;\n  }\n\n  if (error) {\n    return <Page.Error />;\n  }\n\n  const contentTypeTitle = schema?.info.displayName\n    ? formatMessage({ id: schema.info.displayName, defaultMessage: schema.info.displayName })\n    : formatMessage({\n        id: 'content-manager.containers.untitled',\n        defaultMessage: 'Untitled',\n      });\n\n  const handleRowClick = (id: Modules.Documents.ID) => () => {\n    trackUsage('willEditEntryFromList');\n    navigate({\n      pathname: id.toString(),\n      search: stringify({ plugins: query.plugins }),\n    });\n  };\n\n  if (!isFetching && results.length === 0) {\n    return (\n      <>\n        <unstable_tours.contentManager.Introduction>\n          {/* Invisible Anchor */}\n          <Box paddingTop={5} />\n        </unstable_tours.contentManager.Introduction>\n        <Page.Main>\n          <Page.Title>{`${contentTypeTitle}`}</Page.Title>\n          <LayoutsHeaderCustom\n            primaryAction={canCreate ? <CreateButton /> : null}\n            subtitle={formatMessage(\n              {\n                id: getTranslation('pages.ListView.header-subtitle'),\n                defaultMessage:\n                  '{number, plural, =0 {# entries} one {# entry} other {# entries}} found',\n              },\n              { number: pagination?.total }\n            )}\n            title={contentTypeTitle}\n            navigationAction={<BackButton />}\n          />\n          <Layouts.Action\n            endActions={\n              <>\n                <InjectionZone area=\"listView.actions\" />\n                <ViewSettingsMenu\n                  setHeaders={handleSetHeaders}\n                  resetHeaders={() => setDisplayedHeaders(list.layout)}\n                  headers={displayedHeaders.map((header) => header.name)}\n                />\n              </>\n            }\n            startActions={\n              <>\n                {list.settings.searchable && (\n                  <SearchInput\n                    disabled={results.length === 0}\n                    label={formatMessage(\n                      { id: 'app.component.search.label', defaultMessage: 'Search for {target}' },\n                      { target: contentTypeTitle }\n                    )}\n                    placeholder={formatMessage({\n                      id: 'global.search',\n                      defaultMessage: 'Search',\n                    })}\n                    trackedEvent=\"didSearch\"\n                  />\n                )}\n                {list.settings.filterable && schema ? (\n                  <Filters disabled={results.length === 0} schema={schema} />\n                ) : null}\n              </>\n            }\n          />\n          <Layouts.Content>\n            <Box background=\"neutral0\" shadow=\"filterShadow\" hasRadius>\n              <EmptyStateLayout\n                action={canCreate ? <CreateButton variant=\"secondary\" /> : null}\n                content={formatMessage({\n                  id: 'app.components.EmptyStateLayout.content-document',\n                  defaultMessage: 'No content found',\n                })}\n                hasRadius\n                icon={<EmptyDocuments width=\"16rem\" />}\n              />\n            </Box>\n          </Layouts.Content>\n        </Page.Main>\n      </>\n    );\n  }\n\n  return (\n    <Page.Main>\n      <Page.Title>{`${contentTypeTitle}`}</Page.Title>\n      <LayoutsHeaderCustom\n        primaryAction={canCreate ? <CreateButton /> : null}\n        subtitle={formatMessage(\n          {\n            id: getTranslation('pages.ListView.header-subtitle'),\n            defaultMessage:\n              '{number, plural, =0 {# entries} one {# entry} other {# entries}} found',\n          },\n          { number: pagination?.total }\n        )}\n        title={contentTypeTitle}\n        navigationAction={<BackButton />}\n      />\n      <Layouts.Action\n        endActions={\n          <>\n            <InjectionZone area=\"listView.actions\" />\n            <ViewSettingsMenu\n              setHeaders={handleSetHeaders}\n              resetHeaders={() => setDisplayedHeaders(list.layout)}\n              headers={displayedHeaders.map((header) => header.name)}\n            />\n          </>\n        }\n        startActions={\n          <>\n            {list.settings.searchable && (\n              <SearchInput\n                disabled={results.length === 0}\n                label={formatMessage(\n                  { id: 'app.component.search.label', defaultMessage: 'Search for {target}' },\n                  { target: contentTypeTitle }\n                )}\n                placeholder={formatMessage({\n                  id: 'global.search',\n                  defaultMessage: 'Search',\n                })}\n                trackedEvent=\"didSearch\"\n              />\n            )}\n            {list.settings.filterable && schema ? (\n              <Filters disabled={results.length === 0} schema={schema} />\n            ) : null}\n          </>\n        }\n      />\n      <Layouts.Content>\n        <Flex gap={4} direction=\"column\" alignItems=\"stretch\">\n          <Table.Root rows={results} headers={tableHeaders} isLoading={isFetching}>\n            <TableActionsBar />\n            <Table.Content>\n              <Table.Head>\n                <Table.HeaderCheckboxCell />\n                {tableHeaders.map((header: ListFieldLayout) => (\n                  <Table.HeaderCell key={header.name} {...header} />\n                ))}\n              </Table.Head>\n              <Table.Loading />\n              <Table.Empty action={canCreate ? <CreateButton variant=\"secondary\" /> : null} />\n              <Table.Body>\n                {results.map((row) => {\n                  return (\n                    <Table.Row\n                      cursor=\"pointer\"\n                      key={row.id}\n                      onClick={handleRowClick(row.documentId)}\n                    >\n                      <Table.CheckboxCell id={row.id} />\n                      {tableHeaders.map(({ cellFormatter, ...header }) => {\n                        if (header.name === 'status') {\n                          const { status } = row;\n\n                          return (\n                            <Table.Cell key={header.name}>\n                              <DocumentStatus status={status} maxWidth={'min-content'} />\n                            </Table.Cell>\n                          );\n                        }\n                        if (['createdBy', 'updatedBy'].includes(header.name.split('.')[0])) {\n                          // Display the users full name\n                          // Some entries doesn't have a user assigned as creator/updater (ex: entries created through content API)\n                          // In this case, we display a dash\n                          return (\n                            <Table.Cell key={header.name}>\n                              <Typography textColor=\"neutral800\">\n                                {row[header.name.split('.')[0]]\n                                  ? getDisplayName(row[header.name.split('.')[0]])\n                                  : '-'}\n                              </Typography>\n                            </Table.Cell>\n                          );\n                        }\n                        if (typeof cellFormatter === 'function') {\n                          return (\n                            <Table.Cell key={header.name}>\n                              {/* @ts-expect-error – TODO: fix this TS error */}\n                              {cellFormatter(row, header, { collectionType, model })}\n                            </Table.Cell>\n                          );\n                        }\n                        return (\n                          <Table.Cell key={header.name}>\n                            <CellContent\n                              content={row[header.name.split('.')[0]]}\n                              rowId={row.documentId}\n                              {...header}\n                            />\n                          </Table.Cell>\n                        );\n                      })}\n                      {/* we stop propagation here to allow the menu to trigger it's events without triggering the row redirect */}\n                      <ActionsCell onClick={(e) => e.stopPropagation()}>\n                        <TableActions document={row} />\n                      </ActionsCell>\n                    </Table.Row>\n                  );\n                })}\n              </Table.Body>\n            </Table.Content>\n          </Table.Root>\n          <Pagination.Root\n            {...pagination}\n            onPageSizeChange={() => trackUsage('willChangeNumberOfEntriesPerPage')}\n          >\n            <Pagination.PageSize />\n            <Pagination.Links />\n          </Pagination.Root>\n        </Flex>\n      </Layouts.Content>\n    </Page.Main>\n  );\n};\n\nconst ActionsCell = styled(Table.Cell)`\n  display: flex;\n  justify-content: flex-end;\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * TableActionsBar\n * -----------------------------------------------------------------------------------------------*/\n\nconst TableActionsBar = () => {\n  const selectRow = useTable('TableActionsBar', (state) => state.selectRow);\n  const [{ query }] = useQueryParams<{ plugins: { i18n: { locale: string } } }>();\n  const locale = query?.plugins?.i18n?.locale;\n  const prevLocale = usePrev(locale);\n\n  // TODO: find a better way to reset the selected rows when the locale changes across all the app\n  React.useEffect(() => {\n    if (prevLocale !== locale) {\n      selectRow([]);\n    }\n  }, [selectRow, prevLocale, locale]);\n\n  return (\n    <Table.ActionBar>\n      <BulkActionsRenderer />\n    </Table.ActionBar>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * CreateButton\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CreateButtonProps extends Pick<ButtonProps, 'variant'> {}\n\nconst CreateButton = ({ variant }: CreateButtonProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const [{ query }] = useQueryParams<{ plugins: object }>();\n\n  return (\n    <Button\n      variant={variant}\n      tag={ReactRouterLink}\n      onClick={() => {\n        trackUsage('willCreateEntry', { status: 'draft' });\n      }}\n      startIcon={<Plus />}\n      style={{ textDecoration: 'none' }}\n      to={{\n        pathname: 'create',\n        search: stringify({ plugins: query.plugins }),\n      }}\n      minWidth=\"max-content\"\n      marginLeft={2}\n    >\n      {formatMessage({\n        id: getTranslation('HeaderLayout.button.label-add-entry'),\n        defaultMessage: 'Create new entry',\n      })}\n    </Button>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedListViewPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedListViewPage = () => {\n  const { slug = '' } = useParams<{\n    slug: string;\n  }>();\n  const {\n    permissions = [],\n    isLoading,\n    error,\n  } = useRBAC(\n    PERMISSIONS.map((action) => ({\n      action,\n      subject: slug,\n    }))\n  );\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (error || !slug) {\n    return <Page.Error />;\n  }\n\n  return (\n    <Page.Protect permissions={permissions}>\n      {({ permissions }) => (\n        <DocumentRBAC permissions={permissions}>\n          <ListViewPage />\n        </DocumentRBAC>\n      )}\n    </Page.Protect>\n  );\n};\n\nexport { ListViewPage, ProtectedListViewPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAMA,sBAAsB;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;AACD;AACD,IAAMC,0BAA0B;EAAC;EAAa;AAAY;AAC1D,IAAMC,yBAAyB;EAAIC,GAAAA;EAAgB;AAAkB;AAUrE,IAAMC,cAAc,CAAC,EAAEC,UAAUC,OAAM,MAAgB;;AACrD,QAAM,EAAEC,YAAYC,KAAKC,OAAOC,QAAO,IAAKJ;AAC5C,QAAM,EAAEK,eAAeC,OAAM,IAAKC,QAAAA;AAClC,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAMC,iBAAiBC,QAAQ,eAAe,CAACC,UAAUA,MAAMC,WAAW;AAC1E,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAM,EAAEC,QAAO,IAAKC,qBAAAA;AAEpB,QAAMC,oBAA0BC,cAC9B,MACET,eAAeU,OACb,CAACC,eAAeA,WAAWC,WAAW,uBAAuBD,WAAWE,YAAY,IACpFC,EAAAA,SAAS,GACb;IAACd;EAAe,CAAA;AAGlB,QAAMe,qBAAmBX,oCAAOY,YAAPZ,mBAAgBa,SAAQ,CAAA,GAAIC,OAAiB,CAACC,KAAKT,WAAAA;AAC1E,UAAM,CAACU,KAAKC,KAAM,IAAGC,OAAOC,QAAQb,MAAO,EAAC,CAAE;AAC9C,QAAI,OAAOW,MAAMG,OAAO,UAAU;AAChC,aAAOL;IACT;AAEA,UAAMK,KAAKH,MAAMG,GAAGC,OAAOJ,MAAMG,GAAGE;AAEpC,QAAIF,MAAMtC,uBAAuByC,SAASP,GAAAA,KAAQ,CAACD,IAAIQ,SAASH,EAAK,GAAA;AACnEL,UAAIS,KAAKJ,EAAAA;IACX;AAEA,WAAOL;EACT,GAAG,CAAA,CAAE;AAEL,QAAM,EAAEU,MAAMC,UAAUC,WAAWC,oBAAmB,IAAKC,cACzD;IAAEjB,SAAS;MAAEQ,IAAI;QAAEU,KAAKnB;MAAgB;IAAE;KAC1C;;;IAGEoB,MAAMpB,gBAAgBD,WAAW,KAAK,CAACN;EACzC,CAAA;AAGF,QAAM,EAAE4B,QAAQ,CAAA,EAAE,IAAKN,YAAY,CAAA;AAEnC,QAAM,EAAEO,SAAQ,IAAKC,oCAAoC7C,OAAO;IAC9D8C,kBAAkB,CAAC,EAAEV,KAAI,OAAQ;MAAEQ,WAAUR,6BAAMW,YAAYC,cAAa,CAAA;;EAC9E,CAAA;AAEA,QAAMC,YAAYC,YAAY/C,QAAQ;IACpCgD,aAAa;EACf,CAAA;AAEA,QAAMC,mBAAyBpC,cAAQ,MAAA;AACrC,UAAM,CAAC,EAAEqC,YAAY,EAAEC,SAAS,CAAA,EAAE,IAAK;MAAEA,QAAQ,CAAA;IAAG,EAAC,CAAE,IAAI/C,eAAeU,OACxE,CAACC,eACCA,WAAWC,WAAW,2CACtBD,WAAWE,YAAYpB,KAAAA;AAG3B,UAAMuD,gBAAgBD,OAAOrC,OAAO,CAACuC,UAAAA;AACnC,YAAMC,YAAY3D,WAAW0D,KAAAA,KAAU,CAAA;AAEvC,aAAOC,UAAUC,QAAQ,CAACnE,oBAAoB2C,SAASuB,UAAUC,IAAI;IACvE,CAAA;AAEA,WAAO;MAEH;MACA;MACGH,GAAAA;MACA/D,GAAAA;MACCuB,GAAAA,oBAAoBrB,iBAAiB,CAAA;MAExCiE,IAAI,CAACC,SAAAA;AACJ,YAAMH,YAAY3D,WAAW8D,IAAK;AAElC,UAAIrE,oBAAoB2C,SAASuB,UAAUC,IAAI,GAAG;AAChD,eAAO;MACT;AAEA,YAAM,EAAEG,WAAWC,gBAAgB,IAAIC,MAAK,IAAKnB,SAASgB,IAAK,EAACI;AAEhE,UAAI/C,SAAyB;QAC3B2C;QACAG,OAAOA,SAAS;QAChBF,WAAWI,aAAaR,WAAWK,eAAe;UAAEjD;UAASqD,YAAY,CAAA;QAAG,CAAA;;QAE5ER,MAAMD,UAAUC;MAClB;AAEA,UACED,UAAUC,SAAS,cACnB,YAAYD,aACZA,UAAUU,WAAW,eACrB;AACAlD,iBAAS;UACP,GAAGA;UACHmD,OAAOC;UACPpE,SAAS0C,MAAMgB,IAAI,CAACW,UAAU;YAC5BP,OAAOQ,eAAeD,IAAAA;YACtB1C,OAAO0C,KAAKvC,GAAGyC,SAAQ;YACzB;UACAC,WAAW;YACT;cACEV,OAAO7D,cAAc;gBACnB6B,IAAI;gBACJ2C,gBAAgB;cAClB,CAAA;cACA9C,OAAO;YACT;YACA;cACEmC,OAAO7D,cAAc;gBACnB6B,IAAI;gBACJ2C,gBAAgB;cAClB,CAAA;cACA9C,OAAO;YACT;UACD;UACDiC,WAAW;YACTD,MAAM;YACNF,MAAM;UACR;QACF;MACF;AAEA,UAAID,UAAUC,SAAS,eAAe;AACpCzC,iBAAS;UACP,GAAGA;UACHhB,SAASwD,UAAUkB,KAAKhB,IAAI,CAAC/B,WAAW;YACtCmC,OAAOnC;YACPA;YACF;QACF;MACF;AAEA,aAAOX;IACT,CAAA,EACCA,OAAO2D,OACVC,EAAAA,SAAS,CAACC,GAAGC,MAAM9B,UAAU+B,QAAQF,EAAEf,OAAOgB,EAAEhB,KAAK,CAAA;KACtD;IACDxD;IACAQ;IACAf;IACAF;IACA8C;IACA/B;IACA8B;IACAzC;IACA+C;EACD,CAAA;AAED,QAAMgC,eAAe,CAACC,WAAAA;AACpB,QAAIA,QAAQ;AACV7E,iBAAW,mBAAA;IACb;EACF;AAEA,QAAM8E,qBAAgD,CAAC/C,SAAAA;AACrD,UAAMqB,YAAY3D,WAAWsC,KAAKwB,IAAI;AAEtC,QAAIH,WAAW;AACbpD,iBAAW,oBAAoB;QAC7B+E,aAAa3B,UAAUC,SAAS;MAClC,CAAA;IACF;EACF;AAEA,aACE2B,yBAACC,QAAQC,MAAI;IACX3F;IACAK,SAASmD;IACT6B;IACAO,UAAUL;;UAEVM,wBAACH,QAAQI,SAAO,CAAA,CAAA;UAChBD,wBAACH,QAAQK,SAAO,CAAA,CAAA;UAChBF,wBAACH,QAAQM,MAAI,CAAA,CAAA;;;AAGnB;AAMA,IAAMvB,mBAAmB,CAAC,EAAET,KAAI,MAA2B;AACzD,QAAM,CAACiC,UAAUC,WAAAA,IAAqBC,eAAS,EAAA;AAC/C,QAAM,CAACC,QAAQC,SAAAA,IAAmBF,eAAS,EAAA;AAC3C,QAAM,EAAE7F,cAAa,IAAKE,QAAAA;AAE1B,QAAM8F,kBAAkBC,YAAYH,QAAQ,GAAA;AAE5C,QAAM,EAAE5D,MAAME,UAAS,IAAKE,cAAc;IACxCqD;IACAO,IAAIF;EACN,CAAA;AACA,QAAM1C,QAAQ6C,SAASzC,IAAAA;AAEvB,QAAM0C,mBAAmB,CAACpB,WAAAA;AACxB,QAAI,CAACA,QAAQ;AACXY,kBAAY,EAAA;IACd;EACF;AAEA,QAAM,EAAEnD,QAAQ,CAAA,GAAI4D,WAAU,IAAKnE,QAAQ,CAAA;AAC3C,QAAM,EAAEoE,YAAY,GAAGC,OAAO,EAAC,IAAKF,cAAc,CAAA;AAElD,aACEd,wBAACiB,UAAAA;IACC9E,OAAO4B,MAAM5B;IACb+E,cAAYzG,cAAc;MACxB6B,IAAI;MACJ2C,gBAAgB;IAClB,CAAA;IACAO,cAAcqB;IACdd,UAAU,CAAC5D,UAAU4B,MAAMgC,SAAS5B,MAAMhC,KAAAA;IAC1CgF,SAAStE;IACTuE,YAAY,MAAMf,YAAYD,WAAW,EAAA;IACzCiB,cAAcL,OAAOD;IACrBO,eAAe,CAACC,MAAAA;AACdf,gBAAUe,EAAEC,cAAcrF,KAAK;IACjC;cAECe,MAAMgB,IAAI,CAACW,SAAAA;AACV,iBACEmB,wBAACyB,QAAAA;QAA6BtF,OAAO0C,KAAKvC,GAAGyC,SAAQ;kBAClDD,eAAeD,IAAAA;MADGA,GAAAA,KAAKvC,EAAE;IAIhC,CAAA;;AAGN;;;;;;;;ACvQA,IAAMoF,YAAY,CAAC,EAAEC,MAAMC,MAAK,MAAkB;AAChD,QAAM,EAAEC,YAAYC,YAAYC,aAAY,IAAKC,QAAAA;AACjD,MAAIC,iBAAiBL;AAErB,MAAID,SAAS,QAAQ;AACnBM,qBAAiBJ,WAAWK,SAASN,KAAQ,GAAA;MAAEO,WAAW;IAAO,CAAA;EACnE;AAEA,MAAIR,SAAS,YAAY;AACvBM,qBAAiBJ,WAAWD,OAAO;MAAEO,WAAW;MAAQC,WAAW;IAAQ,CAAA;EAC7E;AAEA,MAAIT,SAAS,QAAQ;AACnB,UAAM,CAACU,MAAMC,QAAQC,MAAAA,IAAUX,MAAMY,MAAM,GAAA;AAC3C,UAAMC,OAAO,oBAAIC,KAAAA;AACjBD,SAAKE,SAASN,IAAAA;AACdI,SAAKG,WAAWN,MAAAA;AAChBG,SAAKI,WAAWN,MAAAA;AAEhBN,qBAAiBH,WAAWW,MAAM;MAChCL,WAAW;IACb,CAAA;EACF;AAEA,MAAI;IAAC;IAAS;IAAWU,SAASnB,IAAO,GAAA;AACvCM,qBAAiBF,aAAaH,OAAO;;;MAGnCmB,uBAAuB;IACzB,CAAA;EACF;AAEA,MAAI;IAAC;IAAW;IAAcD,SAASnB,IAAO,GAAA;AAC5CM,qBAAiBF,aAAaH,OAAO;MAAEmB,uBAAuB;IAAE,CAAA;EAClE;AAEA,aAAOC,gBAAAA,SAASf,cAAAA;AAClB;;;;AC/BA,IAAMgB,kBAAkB,CAAC,EAAEC,SAASC,UAAS,MAAwB;AACnE,MAAI,CAACA,WAAW;AACd,WAAO;EACT;AAEA,aACEC,yBAACC,aAAAA;IAAQC,OAAOJ,QAAQC,UAAUI,IAAI;IACpC,cAAAH,yBAACI,YAAAA;MAAWC,UAAS;MAAQC,WAAU;MAAaC,UAAQ;MAC1D,cAAAP,yBAACQ,WAAAA;QAAUC,MAAMV,UAAUU;QAAMC,OAAOZ,QAAQC,UAAUI,IAAI;;;;AAItE;AAUA,IAAMQ,sBAAsB,CAAC,EAAEb,SAASC,UAAS,MAA4B;AAC3E,QAAM,EAAEa,cAAa,IAAKC,QAAAA;AAE1B,MAAI,CAACd,WAAW;AACd,WAAO;EACT;AAEA,aACEe,0BAACC,KAAKC,MAAI;;UACRF,0BAACC,KAAKE,SAAO;QAACC,SAAS,CAACC,MAAMA,EAAEC,gBAAe;;cAC7CpB,yBAACqB,OAAAA;YAAOvB,UAAAA,QAAQwB;;UACfV,cACC;YACEW,IAAI;YACJC,gBAAgB;aAElB;YAAEC,QAAQ3B,QAAQwB;UAAO,CAAA;;;UAG7BtB,yBAACe,KAAKW,SAAO;QACV5B,UAAAA,QAAQ6B,IAAI,CAACC,aACZ5B,yBAACe,KAAKc,MAAI;UAAeC,UAAQ;UAC/B,cAAA9B,yBAACI,YAAAA;YAAWC,UAAS;YAAQE,UAAQ;YACnC,cAAAP,yBAACQ,WAAAA;cAAUC,MAAMV,UAAUU;cAAMC,OAAOkB,KAAK7B,UAAUI,IAAI;;;QAF/CyB,GAAAA,KAAKL,EAAE,CAAA;;;;AASjC;;;;;ACxCA,IAAMQ,mBAAmB,CAACC,QAAiBA,OAAOA,IAAI,CAAE,MAAK,MAAMA,IAAIC,UAAU,CAAKD,IAAAA;AAEtF,IAAME,cAAc,CAAC,EAAEC,KAAKC,MAAMC,iBAAiBC,MAAMN,KAAKO,QAAO,MAAoB;;AACvF,QAAMC,UAAUC,4BAA4BN,GAAAA;AAE5C,MAAIC,KAAKM,SAAS,OAAU,GAAA;AAC1B,UAAMC,aAAYJ,wCAASI,cAATJ,mBAAoBJ;AACtC,UAAMS,WAAWH,4BAA4BE,SAAcH,KAAAA;AAE3D,eACEK,yBAACC,OAAOC,MAAI;MACVC,KAAKJ;MACLK,KAAKZ,mBAAmBC;MACxBY,UAAUb,mBAAmBC;MAC7Ba,SAAO;;EAGb;AAEA,QAAMC,gBAAgBrB,iBAAiBC,GAAAA;AACvC,QAAMqB,WAAWf,KAAKgB,SAAS,MAAM,GAAGhB,KAAKL,UAAU,GAAG,GAAK,CAAA,QAAOK;AAEtE,aACEO,yBAACU,aAAAA;IAAQC,aAAaH;IACpB,cAAAR,yBAACY,aAAAA;MAAaL,UAAAA;;;AAGpB;AAEA,IAAMK,cAAc,CAAC,EAAEC,SAAQ,MAAiC;AAC9D,aACEb,yBAACc,MAAAA;IACCC,KAAI;IACJC,UAAS;IACTC,cAAa;IACbC,OAAM;IACNC,QAAO;IACPC,aAAY;IACZC,YAAW;IACXC,aAAY;IACZC,gBAAe;IACfC,YAAW;IAEX,cAAAxB,yBAACyB,gBAAAA;MAAeC,SAAQ;MAAQC,WAAU;MACvCd;;;AAIT;AAEA,IAAMY,iBAAiBG,GAA4BC,UAAAA;;;;AAanD,IAAMC,gBAAgB,CAAC,EAAEC,QAAO,MAAsB;AACpD,aACE/B,yBAACC,OAAO+B,OAAK;cACVD,QAAQE,IAAI,CAACC,MAAMC,UAAAA;AAClB,YAAMC,MAAM,GAAGF,KAAKG,EAAE,GAAGF,KAAAA;AAEzB,UAAIA,UAAU,GAAG;AACf,cAAMG,iBAAiB,IAAIP,QAAQtB,SAAS,CAAA;AAE5C,mBAAOT,yBAACY,aAAAA;UAAuB0B,UAAAA;QAANF,GAAAA,GAAAA;MAC3B;AAEA,UAAID,QAAQ,GAAG;AACb,eAAO;MACT;AAEA,iBAAOnC,yBAACX,aAAAA;QAAuB,GAAG6C;MAATE,GAAAA,GAAAA;IAC3B,CAAA;;AAGN;;;;;AC9FA,IAAMG,iBAAiB,CAAC,EAAEC,WAAWC,QAAO,MAAuB;AACjE,aACEC,yBAACC,YAAAA;IAAWC,UAAS;IAAQC,WAAU;IAAaC,UAAQ;IACzDC,UAAAA,iBAAiBN,SAASD,SAAAA;;AAGjC;AAYA,IAAMQ,mBAAmB,CAAC,EAAER,WAAWC,SAASQ,OAAOC,KAAI,MAAyB;AAClF,QAAM,EAAEC,MAAK,IAAKC,OAAAA;AAClB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,aAAY,IAAKC,YAAAA;AACzB,QAAM,CAACC,QAAQC,SAAAA,IAAmBC,gBAAS,KAAA;AAE3C,QAAM,CAACC,WAAAA,IAAeV,KAAKW,MAAM,GAAA;AAEjC,QAAM,EAAEC,MAAMC,UAAS,IAAKC,qBAC1B;IACEb;IACAc,IAAIhB;IACJW;KAEF;IACEM,MAAM,CAACT;IACPU,2BAA2B;EAC7B,CAAA;AAGF,QAAMC,eAAeC,MAAMC,QAAQ7B,OAAAA,IAAWA,QAAQ8B,SAAS9B,QAAQ+B;AAEvEC,EAAMC,iBAAU,MAAA;AACd,QAAIZ,MAAM;AACRP,mBACEF,cAAc;QACZY,IAAIU,eAAe,8BAAA;QACnBC,gBAAgB;MAClB,CAAA,CAAA;IAEJ;KACC;IAACd;IAAMT;IAAeE;EAAa,CAAA;AAEtC,aACEsB,0BAACC,KAAKC,MAAI;IAACC,cAAc,CAACvB,YAAWC,UAAUD,OAAAA;;UAC7Cf,yBAACoC,KAAKG,SAAO;QAACC,SAAS,CAACC,MAAMA,EAAEC,gBAAe;QAC7C,cAAA1C,yBAACC,YAAAA;UAAW0C,OAAO;YAAEC,QAAQ;UAAU;UAAGzC,WAAU;UAAa0C,YAAW;UACzEnB,UAAAA,eAAe,IACZf,cACE;YACEY,IAAI;YACJW,gBAAgB;aAElB;YAAEY,QAAQpB;WAEZ,IAAA;;;UAGRS,0BAACC,KAAKW,SAAO;;UACV1B,iBACCrB,yBAACoC,KAAKY,MAAI;YAACC,UAAQ;YACjB,cAAAjD,yBAACkD,QAAAA;cAAOC,OAAK;wBACVxC,cAAc;gBACbY,IAAIU,eAAe,gCAAA;gBACnBC,gBAAgB;cAClB,CAAA;;;WAILd,6BAAMgC,gBACLjB,0BAAAkB,8BAAA;;cACGjC,KAAKgC,QAAQE,IAAI,CAACC,cACjBvD,yBAACoC,KAAKY,MAAI;gBACR,cAAAhD,yBAACC,YAAAA;kBAAWC,UAAS;kBAAQE,UAAQ;kBAClCC,UAAAA,iBAAiBkD,OAAOzD,SAAAA;;cAFbyD,GAAAA,MAAMC,UAAU,CAAA;eAOjCpC,6BAAMqC,gBAAcrC,6BAAMqC,WAAWC,SAAQ,UAC5C1D,yBAACoC,KAAKY,MAAI;gBACRW,iBAAa;gBACbC,cAAYjD,cAAc;kBACxBY,IAAIU,eAAe,6BAAA;kBACnBC,gBAAgB;gBAClB,CAAA;gBAEA,cAAAlC,yBAACC,YAAAA;kBAAW,UAAA;;;;;;;;;AAQ5B;;;ACzGA,IAAM4D,cAAc,CAAC,EAAEC,SAASC,WAAWC,WAAWC,OAAOC,KAAI,MAAoB;AACnF,MAAI,CAACC,WAAWL,SAASC,WAAWC,SAAY,GAAA;AAC9C,eACEI,yBAACC,YAAAA;MACCC,WAAU;MACVC,aAAaP,UAAUQ,SAAU,aAA6B,WAAW;MACzEC,cAAcT,UAAUQ,SAAU,aAA6B,WAAW;MAC3E,UAAA;;EAIL;AAEA,UAAQR,UAAUQ,MAAI;IACpB,KAAK;AACH,UAAI,CAACR,UAAUU,UAAU;AACvB,mBAAON,yBAACO,aAAAA;UAAa,GAAGb;;MAC1B;AAEA,iBAAOM,yBAACQ,eAAAA;QAAcd;;IAExB,KAAK,YAAY;AACf,UAAIe,iBAAiBb,UAAUc,QAAQ,GAAG;AACxC,mBAAOV,yBAACW,gBAAAA;UAAehB;UAAsBD;;MAC/C;AAEA,iBAAOM,yBAACY,kBAAAA;QAAiBf;QAAcF;QAAsBD;QAAkBI;;IACjF;IAEA,KAAK;AACH,UAAIF,UAAUiB,YAAY;AACxB,mBAAOb,yBAACc,qBAAAA;UAAoBnB;UAAsBD;;MACpD;AAEA,iBAAOM,yBAACe,iBAAAA;QAAgBpB;QAAsBD;;IAEhD,KAAK;AACH,iBACEM,yBAACgB,aAAAA;QAAQC,aAAavB;QACpB,cAAAM,yBAACC,YAAAA;UAAWiB,UAAS;UAAQC,UAAQ;UAACjB,WAAU;UAC9C,cAAAF,yBAACoB,WAAAA;YAAUhB,MAAMR,UAAUQ;YAAMiB,OAAO3B;;;;IAKhD;AACE,iBACEM,yBAACC,YAAAA;QAAWiB,UAAS;QAAQC,UAAQ;QAACjB,WAAU;QAC9C,cAAAF,yBAACoB,WAAAA;UAAUhB,MAAMR,UAAUQ;UAAMiB,OAAO3B;;;EAGhD;AACF;AAEA,IAAMK,aAAa,CACjBL,SACAC,WACAC,cAAAA;AAEA,MAAIA,UAAUQ,SAAS,aAAa;AAGlC,QAAIR,UAAUiB,cAAc,CAAClB,WAAW;AACtC,cAAOD,mCAAS4B,UAAS;IAC3B;AAEA,UAAMD,QAAQ3B,mCAAUC,UAAUG;AAGlC,QAAIH,UAAUG,SAAS,QAAQ,CAAC;MAACyB;MAAW;MAAMC,SAASH,KAAQ,GAAA;AACjE,aAAO;IACT;AAEA,WAAO,KAACI,eAAAA,SAAQJ,KAAAA;EAClB;AAEA,MAAIzB,UAAUQ,SAAS,YAAY;AACjC,QAAIK,iBAAiBb,UAAUc,QAAQ,GAAG;AACxC,aAAO,KAACe,eAAAA,SAAQ/B,OAAAA;IAClB;AAEA,QAAIgC,MAAMC,QAAQjC,OAAU,GAAA;AAC1B,aAAOA,QAAQ4B,SAAS;IAC1B;AAEA,YAAO5B,mCAASkC,SAAQ;EAC1B;AAMA,MAAI;IAAC;IAAW;IAAW;IAAS;EAAS,EAACJ,SAAS5B,UAAUQ,IAAI,GAAG;AACtE,WAAO,OAAOV,YAAY;EAC5B;AAEA,MAAIE,UAAUQ,SAAS,WAAW;AAChC,WAAOV,YAAY;EACrB;AAEA,SAAO,KAAC+B,eAAAA,SAAQ/B,OAAAA;AAClB;AAEA,IAAMe,mBAAmB,CACvBL,SACG;EAAC;EAAY;EAAa;AAAgB,EAACoB,SAASpB,IAAAA;;;;;;AChGzD,IAAMyB,mBAAmB,CAACC,UAAAA;AACxB,QAAMC,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,wBAAMC,UAAUH,YAAYI,mBAA5BF,mBAA4CG,kCAAiC,CAAA;GAAE;AAE5F,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EACJC,gBAAgB,EAAEC,iBAAgB,EAAE,IAClCC,QAAQZ,WAAAA;AAEZ,aACEa,0BAACC,QAAQC,MAAI;;UACXC,yBAACF,QAAQG,SAAO;QACd,cAAAD,yBAACE,YAAAA;UACCC,OAAOX,cAAc;YACnBY,IAAI;YACJC,gBAAgB;UAClB,CAAA;UAEA,cAAAL,yBAACM,eAAAA,CAAAA,CAAAA;;;UAGLN,yBAACF,QAAQS,SAAO;QAACC,MAAK;QAASC,OAAM;QAAMC,YAAY;QACrD,cAAAb,0BAACc,MAAAA;UAAKC,YAAW;UAAUC,WAAU;UAASC,SAAS;UAAGC,KAAK;;YAC5DpB,uBACCK,yBAACgB,YAAAA;cACCC,MAAK;cACLC,eAAWlB,yBAACmB,eAAAA,CAAAA,CAAAA;cACZC,SAAQ;cACRC,KAAKC;cACLC,IAAI;gBACFC,UAAU;gBACVC,QAAQnC,MAAMoC,cACVC,qBAAU;kBAAED,SAASpC,MAAMoC;mBAAW;kBAAEE,QAAQ;iBAChD,IAAA;cACN;wBAECpC,cAAc;gBACbY,IAAI;gBACJC,gBAAgB;cAClB,CAAA;YAEA,CAAA,IAAA;gBACJL,yBAAC6B,aAAAA;cAAa,GAAG9C;;;;;;;AAK3B;AAQA,IAAM8C,cAAc,CAAC,EAAEC,UAAU,CAAA,GAAIC,cAAcC,WAAU,MAAoB;AAC/E,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAE1C,eAAe2C,OAAM,IAAK1C,QAAAA;AAElC,QAAM,EAAE2C,QAAQC,MAAK,IAAKC,OAAAA;AAC1B,QAAM,EAAEC,KAAI,IAAKC,kBAAkBH,KAAAA;AAEnC,QAAMI,YAAYC,YAAYP,QAAQ;IACpCQ,aAAa;EACf,CAAA;AAEA,QAAMC,cAAaR,iCAAQQ,eAAc,CAAA;AAEzC,QAAMC,UAAUC,OAAOC,KAAKH,UACzBI,EAAAA,OAAO,CAACC,SAASC,8BAA8BN,WAAWK,IAAK,CAAA,CAAA,EAC/DE,IAAI,CAACF,SAAAA;;AAAU;MACdA;MACA9C,SAAOoC,UAAKa,UAAUH,IAAAA,MAAfV,mBAAsBpC,UAAS;IACxC;GACCkD,EAAAA,KAAK,CAACC,GAAGC,MAAMd,UAAUe,QAAQF,EAAEnD,OAAOoD,EAAEpD,KAAK,CAAA;AAEpD,QAAMsD,eAAe,CAACR,SAAAA;AACpBhB,eAAW,0BAAA;AAMX,UAAMyB,aAAa5B,QAAQ6B,SAASV,IAAAA,IAChCnB,QAAQkB,OAAO,CAACY,WAAWA,WAAWX,IACtC,IAAA;MAAInB,GAAAA;MAASmB;IAAK;AAEtBjB,eAAW0B,UAAAA;EACb;AAEA,QAAMG,cAAc,MAAA;AAClB9B,iBAAAA;EACF;AAEA,aACElC,0BAACc,MAAAA;IACCU,KAAI;IACJR,WAAU;IACVD,YAAW;IACXG,KAAK;IACL+C,aAAa;IACbC,WAAW;IACXC,UAAU;;UAEVnE,0BAACc,MAAAA;QAAKsD,gBAAe;;cACnBjE,yBAACkE,YAAAA;YAAW7C,KAAI;YAASD,SAAQ;YAAK+C,YAAW;sBAC9C3E,cAAc;cACbY,IAAI;cACJC,gBAAgB;YAClB,CAAA;;cAGFL,yBAACoE,YAAAA;YAAWC,SAASR;sBAClBrE,cAAc;cACbY,IAAI;cACJC,gBAAgB;YAClB,CAAA;;;;UAIJL,yBAACW,MAAAA;QAAKE,WAAU;QAASD,YAAW;kBACjCiC,QAAQM,IAAI,CAACS,WAAAA;AACZ,gBAAMU,WAAWxC,QAAQ6B,SAASC,OAAOX,IAAI;AAE7C,qBACEjD,yBAACW,MAAAA;YACC4D,MAAK;YACLxD,KAAK;YACLyD,YAAYF,WAAW,eAAe;YACtCG,WAAS;YACT3D,SAAS;YAGT,cAAAd,yBAAC0E,cAAAA;cACCC,iBAAiB,MAAMlB,aAAaG,OAAOX,IAAI;cAC/C2B,SAASN;cACTrB,MAAMW,OAAOX;cAEb,cAAAjD,yBAACkE,YAAAA;gBAAWW,UAAU;gBAAIjB,UAAAA,OAAOzD;;;UAP9ByD,GAAAA,OAAOX,IAAI;QAWtB,CAAA;;;;AAIR;;;ACjHA,IAAM,EAAE6B,uBAAsB,IAAKC;AAKnC,IAAMC,sBAAsBC,GAAOC,QAAQC,MAAM;;;AAIjD,IAAMC,eAAe,MAAA;;AACnB,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAmBC,cAAAA;AAEvE,QAAM,EAAEC,gBAAgBC,OAAOC,OAAM,IAAKC,OAAAA;AAC1C,QAAM,EAAEC,KAAI,IAAKC,kBAAkBJ,KAAAA;AAEnC,QAAM,CAACK,kBAAkBC,mBAAAA,IAA6BC,gBAA4B,CAAA,CAAE;AAEpF,QAAMC,aAAaC,QAAQN,KAAKO,MAAM;AACtCC,EAAMC,iBAAU,MAAA;AAKd,QAAI,KAACC,eAAAA,SAAQL,YAAYL,KAAKO,MAAM,GAAG;AACrCJ,0BAAoBH,KAAKO,MAAM;IACjC;KACC;IAACP,KAAKO;IAAQF;EAAW,CAAA;AAE5B,QAAMM,mBAAmB,CAACC,YAAAA;AACxBT,wBACEU,gCAAgCD,SAASd,OAAQgB,YAAYd,KAAKe,SAAS,CAAA;EAE/E;AAEA,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAKjB;IACDC,MAAM;IACNC,UAAUnB,KAAKoB,SAASD,SAASE,SAAQ;IACzCC,MAAMtB,KAAKoB,SAASG,gBAChB,GAAGvB,KAAKoB,SAASG,aAAa,IAAIvB,KAAKoB,SAASI,gBAAgB,KAChE;EACN,CAAA;AAEA,QAAMC,SAAeC,eAAQ,MAAMC,iBAAiBX,KAAQ,GAAA;IAACA;EAAM,CAAA;AAEnE,QAAM,EAAEY,MAAMC,OAAOC,WAAU,IAAKC,wBAAwB;IAC1DlC;IACA4B;EACF,CAAA;AAKAjB,EAAMC,iBAAU,MAAA;AACd,QAAIoB,OAAO;AACTvC,yBAAmB;QACjB0C,MAAM;QACNC,SAASxC,eAAeoC,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAOpC;IAAgBH;EAAmB,CAAA;AAE9C,QAAM,EAAE4C,UAAU,CAAA,GAAIC,WAAU,IAAKP,QAAQ,CAAA;AAE7CpB,EAAMC,iBAAU,MAAA;AACd,QAAI0B,cAAcA,WAAWC,YAAY,KAAKD,WAAWjB,OAAOiB,WAAWC,WAAW;AACpFlD,eACE;QACEmD,YAAQC,sBAAU;UAChB,GAAGtB;UACHE,MAAMiB,WAAWC;QACnB,CAAA;SAEF;QAAEG,SAAS;MAAK,CAAA;IAEpB;KACC;IAACJ;IAAY/C;IAAe4B;IAAO9B;EAAS,CAAA;AAE/C,QAAM,EAAEsD,UAAS,IAAKC,gBAAgB,gBAAgB,CAAC,EAAED,WAAAA,WAAS,OAAQ;IACxEA,WAAAA;IACF;AAEA,QAAME,mBAAmBC,aAAa,gBAAgB,CAAC,EAAED,kBAAAA,kBAAgB,MAAOA,iBAAAA;AAIhF,QAAME,eAAqBlB,eAAQ,MAAA;;AACjC,UAAMd,UAAU8B,iBAAiBjE,wBAAwB;MACvDyB;MACAK,QAAQP;IACV,CAAA;AAEA,UAAM6C,mBAAmBjC,QAAQV,iBAAiB4C,IAAqB,CAACC,WAAAA;;AAOtE,YAAMC,cACJ,OAAOD,OAAOE,UAAU,WACpB;QACEC,IAAI,iCAAiCrD,KAAAA,IAASkD,OAAOI,IAAI;QACzDC,gBAAgBL,OAAOE;MACzB,IACAF,OAAOE;AAEb,aAAO;QACL,GAAGF;QACHE,OAAO7D,cAAc4D,WAAAA;QACrBG,MAAM,GAAGJ,OAAOI,IAAI,KAAGJ,MAAAA,OAAOM,cAAPN,gBAAAA,IAAkBI,QAAO,IAAIJ,OAAOM,UAAUF,IAAI,KAAK,EAAA;MAChF;IACF,CAAA;AAEA,SAAIrD,MAAAA,iCAAQwD,YAARxD,gBAAAA,IAAiByD,iBAAiB;AACpCV,uBAAiBW,KAAK;QACpBC,WAAW;UACTzB,MAAM;QACR;QACAmB,MAAM;QACNF,OAAO7D,cAAc;UACnB8D,IAAIvD,eAAe,sCAAsC;UACzDyD,gBAAgB;QAClB,CAAA;QACAM,YAAY;QACZC,UAAU;MACZ,CAAA;IACF;AAEA,WAAOd;KACN;IACD3C;IACAd;IACAY;IACA0C;KACA5C,sCAAQwD,YAARxD,mBAAiByD;IACjB1D;EACD,CAAA;AAED,MAAIiC,YAAY;AACd,eAAO8B,yBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,MAAIjC,OAAO;AACT,eAAO+B,yBAACC,KAAKE,OAAK,CAAA,CAAA;EACpB;AAEA,QAAMC,oBAAmBlE,iCAAQmE,KAAKC,eAClC9E,cAAc;IAAE8D,IAAIpD,OAAOmE,KAAKC;IAAad,gBAAgBtD,OAAOmE,KAAKC;EAAY,CAAA,IACrF9E,cAAc;IACZ8D,IAAI;IACJE,gBAAgB;EAClB,CAAA;AAEJ,QAAMe,iBAAiB,CAACjB,OAA6B,MAAA;AACnDlE,eAAW,uBAAA;AACXE,aAAS;MACPkF,UAAUlB,GAAG7B,SAAQ;MACrBgB,YAAQC,sBAAU;QAAE+B,SAASrD,MAAMqD;MAAQ,CAAA;IAC7C,CAAA;EACF;AAEA,MAAI,CAACvC,cAAcI,QAAQoC,WAAW,GAAG;AACvC,eACEC,0BAAAC,8BAAA;;YACEZ,yBAACa,MAAeC,eAAeC,cAAY;UAEzC,cAAAf,yBAACgB,KAAAA;YAAIC,YAAY;;;YAEnBN,0BAACV,KAAKiB,MAAI;;gBACRlB,yBAACC,KAAKkB,OAAK;wBAAE,GAAGf,gBAAAA;;gBAChBJ,yBAACjF,qBAAAA;cACCqG,eAAexC,gBAAYoB,yBAACqB,cAAkB,CAAA,CAAA,IAAA;cAC9CC,UAAU9F,cACR;gBACE8D,IAAIvD,eAAe,gCAAA;gBACnByD,gBACE;iBAEJ;gBAAE+B,QAAQhD,yCAAYiD;cAAM,CAAA;cAE9BC,OAAOrB;cACPsB,sBAAkB1B,yBAAC2B,YAAAA,CAAAA,CAAAA;;gBAErB3B,yBAAC/E,QAAQ2G,QAAM;cACbC,gBACElB,0BAAAC,8BAAA;;sBACEZ,yBAAC8B,eAAAA;oBAAcC,MAAK;;sBACpB/B,yBAACgC,kBAAAA;oBACCC,YAAYlF;oBACZmF,cAAc,MAAM3F,oBAAoBH,KAAKO,MAAM;oBACnDK,SAASV,iBAAiB4C,IAAI,CAACC,WAAWA,OAAOI,IAAI;;;;cAI3D4C,kBACExB,0BAAAC,8BAAA;;kBACGxE,KAAKoB,SAASsC,kBACbE,yBAACoC,aAAAA;oBACCC,UAAU/D,QAAQoC,WAAW;oBAC7BrB,OAAO7D,cACL;sBAAE8D,IAAI;sBAA8BE,gBAAgB;uBACpD;sBAAE8C,QAAQlC;oBAAiB,CAAA;oBAE7BmC,aAAa/G,cAAc;sBACzB8D,IAAI;sBACJE,gBAAgB;oBAClB,CAAA;oBACAgD,cAAa;;kBAGhBpG,KAAKoB,SAASiF,cAAcvG,aAC3B8D,yBAAC0C,aAAAA;oBAAQL,UAAU/D,QAAQoC,WAAW;oBAAGxE;kBACvC,CAAA,IAAA;;;;gBAIV8D,yBAAC/E,QAAQ0H,SAAO;cACd,cAAA3C,yBAACgB,KAAAA;gBAAI4B,YAAW;gBAAWC,QAAO;gBAAeC,WAAS;gBACxD,cAAA9C,yBAAC+C,kBAAAA;kBACCC,QAAQpE,gBAAYoB,yBAACqB,cAAAA;oBAAa4B,SAAQ;kBAAiB,CAAA,IAAA;kBAC3DC,SAAS1H,cAAc;oBACrB8D,IAAI;oBACJE,gBAAgB;kBAClB,CAAA;kBACAsD,WAAS;kBACTK,UAAMnD,yBAACoD,cAAAA;oBAAeC,OAAM;;;;;;;;;EAO1C;AAEA,aACE1C,0BAACV,KAAKiB,MAAI;;UACRlB,yBAACC,KAAKkB,OAAK;kBAAE,GAAGf,gBAAAA;;UAChBJ,yBAACjF,qBAAAA;QACCqG,eAAexC,gBAAYoB,yBAACqB,cAAkB,CAAA,CAAA,IAAA;QAC9CC,UAAU9F,cACR;UACE8D,IAAIvD,eAAe,gCAAA;UACnByD,gBACE;WAEJ;UAAE+B,QAAQhD,yCAAYiD;QAAM,CAAA;QAE9BC,OAAOrB;QACPsB,sBAAkB1B,yBAAC2B,YAAAA,CAAAA,CAAAA;;UAErB3B,yBAAC/E,QAAQ2G,QAAM;QACbC,gBACElB,0BAAAC,8BAAA;;gBACEZ,yBAAC8B,eAAAA;cAAcC,MAAK;;gBACpB/B,yBAACgC,kBAAAA;cACCC,YAAYlF;cACZmF,cAAc,MAAM3F,oBAAoBH,KAAKO,MAAM;cACnDK,SAASV,iBAAiB4C,IAAI,CAACC,WAAWA,OAAOI,IAAI;;;;QAI3D4C,kBACExB,0BAAAC,8BAAA;;YACGxE,KAAKoB,SAASsC,kBACbE,yBAACoC,aAAAA;cACCC,UAAU/D,QAAQoC,WAAW;cAC7BrB,OAAO7D,cACL;gBAAE8D,IAAI;gBAA8BE,gBAAgB;iBACpD;gBAAE8C,QAAQlC;cAAiB,CAAA;cAE7BmC,aAAa/G,cAAc;gBACzB8D,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACAgD,cAAa;;YAGhBpG,KAAKoB,SAASiF,cAAcvG,aAC3B8D,yBAAC0C,aAAAA;cAAQL,UAAU/D,QAAQoC,WAAW;cAAGxE;YACvC,CAAA,IAAA;;;;UAIV8D,yBAAC/E,QAAQ0H,SAAO;QACd,cAAAhC,0BAAC2C,MAAAA;UAAKC,KAAK;UAAGC,WAAU;UAASC,YAAW;;gBAC1C9C,0BAAC+C,MAAMC,MAAI;cAACC,MAAMtF;cAAStB,SAASgC;cAAc6E,WAAW3F;;oBAC3D8B,yBAAC8D,iBAAAA,CAAAA,CAAAA;oBACDnD,0BAAC+C,MAAMf,SAAO;;wBACZhC,0BAAC+C,MAAMK,MAAI;;4BACT/D,yBAAC0D,MAAMM,oBAAkB,CAAA,CAAA;wBACxBhF,aAAaE,IAAI,CAACC,eACjBa,yBAAC0D,MAAMO,YAAU;0BAAoB,GAAG9E;wBAAjBA,GAAAA,OAAOI,IAAI,CAAA;;;wBAGtCS,yBAAC0D,MAAMxD,SAAO,CAAA,CAAA;wBACdF,yBAAC0D,MAAMQ,OAAK;sBAAClB,QAAQpE,gBAAYoB,yBAACqB,cAAAA;wBAAa4B,SAAQ;sBAAiB,CAAA,IAAA;;wBACxEjD,yBAAC0D,MAAMS,MAAI;gCACR7F,QAAQY,IAAI,CAACkF,QAAAA;AACZ,mCACEzD,0BAAC+C,MAAMW,KAAG;0BACRC,QAAO;0BAEPC,SAAShE,eAAe6D,IAAII,UAAU;;gCAEtCxE,yBAAC0D,MAAMe,cAAY;8BAACnF,IAAI8E,IAAI9E;;4BAC3BN,aAAaE,IAAI,CAAC,EAAEwF,eAAe,GAAGvF,OAAQ,MAAA;AAC7C,kCAAIA,OAAOI,SAAS,UAAU;AAC5B,sCAAM,EAAEoF,OAAM,IAAKP;AAEnB,2CACEpE,yBAAC0D,MAAMkB,MAAI;kCACT,cAAA5E,yBAAC6E,gBAAAA;oCAAeF;oCAAgBG,UAAU;;gCAD3B3F,GAAAA,OAAOI,IAAI;8BAIhC;AACA,kCAAI;gCAAC;gCAAa;gCAAawF,SAAS5F,OAAOI,KAAKyF,MAAM,GAAA,EAAK,CAAA,CAAE,GAAG;AAIlE,2CACEhF,yBAAC0D,MAAMkB,MAAI;kCACT,cAAA5E,yBAACiF,YAAAA;oCAAWC,WAAU;8CACnBd,IAAIjF,OAAOI,KAAKyF,MAAM,GAAA,EAAK,CAAA,CAAE,IAC1BG,eAAef,IAAIjF,OAAOI,KAAKyF,MAAM,GAAI,EAAC,CAAE,CAAA,CAAC,IAC7C;;gCAJS7F,GAAAA,OAAOI,IAAI;8BAQhC;AACA,kCAAI,OAAOmF,kBAAkB,YAAY;AACvC,2CACE1E,yBAAC0D,MAAMkB,MAAI;kCAERF,UAAAA,cAAcN,KAAKjF,QAAQ;oCAAEnD;oCAAgBC;kCAAM,CAAA;gCAFrCkD,GAAAA,OAAOI,IAAI;8BAKhC;AACA,yCACES,yBAAC0D,MAAMkB,MAAI;gCACT,cAAA5E,yBAACoF,aAAAA;kCACClC,SAASkB,IAAIjF,OAAOI,KAAKyF,MAAM,GAAA,EAAK,CAAA,CAAE;kCACtCK,OAAOjB,IAAII;kCACV,GAAGrF;;8BAJSA,GAAAA,OAAOI,IAAI;4BAQhC,CAAA;gCAEAS,yBAACsF,aAAAA;8BAAYf,SAAS,CAACgB,MAAMA,EAAEC,gBAAe;8BAC5C,cAAAxF,yBAACyF,cAAAA;gCAAaC,UAAUtB;;;;wBAhDrBA,GAAAA,IAAI9E,EAAE;sBAoDjB,CAAA;;;;;;gBAINqB,0BAACgF,WAAWhC,MAAI;cACb,GAAGpF;cACJqH,kBAAkB,MAAMxK,WAAW,kCAAA;;oBAEnC4E,yBAAC2F,WAAWE,UAAQ,CAAA,CAAA;oBACpB7F,yBAAC2F,WAAWG,OAAK,CAAA,CAAA;;;;;;;;AAM7B;AAEA,IAAMR,cAActK,GAAO0I,MAAMkB,IAAI;;;;AASrC,IAAMd,kBAAkB,MAAA;;AACtB,QAAMiC,YAAYC,SAAS,mBAAmB,CAACC,UAAUA,MAAMF,SAAS;AACxE,QAAM,CAAC,EAAE3I,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAM6I,UAAS9I,0CAAOqD,YAAPrD,mBAAgB+I,SAAhB/I,mBAAsB8I;AACrC,QAAME,aAAa1J,QAAQwJ,MAAAA;AAG3BtJ,EAAMC,iBAAU,MAAA;AACd,QAAIuJ,eAAeF,QAAQ;AACzBH,gBAAU,CAAA,CAAE;IACd;KACC;IAACA;IAAWK;IAAYF;EAAO,CAAA;AAElC,aACElG,yBAAC0D,MAAM2C,WAAS;IACd,cAAArG,yBAACsG,qBAAAA,CAAAA,CAAAA;;AAGP;AAQA,IAAMjF,eAAe,CAAC,EAAE4B,QAAO,MAAqB;AAClD,QAAM,EAAEzH,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEL,WAAU,IAAKC,YAAAA;AACvB,QAAM,CAAC,EAAE+B,MAAK,CAAE,IAAIC,eAAAA;AAEpB,aACE2C,yBAACuG,QAAAA;IACCtD;IACAuD,KAAKC;IACLlC,SAAS,MAAA;AACPnJ,iBAAW,mBAAmB;QAAEuJ,QAAQ;MAAQ,CAAA;IAClD;IACA+B,eAAW1G,yBAAC2G,eAAAA,CAAAA,CAAAA;IACZC,OAAO;MAAEC,gBAAgB;IAAO;IAChCC,IAAI;MACFtG,UAAU;MACV/B,YAAQC,sBAAU;QAAE+B,SAASrD,MAAMqD;MAAQ,CAAA;IAC7C;IACAsG,UAAS;IACTC,YAAY;cAEXxL,cAAc;MACb8D,IAAIvD,eAAe,qCAAA;MACnByD,gBAAgB;IAClB,CAAA;;AAGN;AAIkG,IAE5FyH,wBAAwB,MAAA;AAC5B,QAAM,EAAEC,OAAO,GAAE,IAAKC,UAAAA;AAGtB,QAAM,EACJC,cAAc,CAAA,GACdvD,WACA5F,MAAK,IACHoJ,QACFC,YAAYpI,IAAI,CAAC8D,YAAY;IAC3BA;IACAuE,SAASL;IACX,CAAA;AAGF,MAAIrD,WAAW;AACb,eAAO7D,yBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,MAAIjC,SAAS,CAACiJ,MAAM;AAClB,eAAOlH,yBAACC,KAAKE,OAAK,CAAA,CAAA;EACpB;AAEA,aACEH,yBAACC,KAAKuH,SAAO;IAACJ;IACX,UAAA,CAAC,EAAEA,aAAAA,aAAW,UACbpH,yBAACyH,cAAAA;MAAaL,aAAaA;MACzB,cAAApH,yBAAC7E,cAAAA,CAAAA,CAAAA;;;AAKX;", "names": ["NOT_ALLOWED_FILTERS", "DEFAULT_ALLOWED_FILTERS", "USER_FILTER_ATTRIBUTES", "CREATOR_FIELDS", "FiltersImpl", "disabled", "schema", "attributes", "uid", "model", "options", "formatMessage", "locale", "useIntl", "trackUsage", "useTracking", "allPermissions", "useAuth", "state", "permissions", "query", "useQueryParams", "schemas", "useContentTypeSchema", "canReadAdminUsers", "useMemo", "filter", "permission", "action", "subject", "length", "selectedUserIds", "filters", "$and", "reduce", "acc", "key", "value", "Object", "entries", "id", "$eq", "$ne", "includes", "push", "data", "userData", "isLoading", "isLoadingAdminUsers", "useAdminUsers", "$in", "skip", "users", "metadata", "useGetContentTypeConfigurationQuery", "selectFromResult", "contentType", "metadatas", "formatter", "useCollator", "sensitivity", "displayedFilters", "properties", "fields", "allowedFields", "field", "attribute", "type", "map", "name", "mainField", "mainFieldName", "label", "list", "getMainField", "components", "target", "input", "AdminUsersFilter", "user", "getDisplayName", "toString", "operators", "defaultMessage", "enum", "Boolean", "toSorted", "a", "b", "compare", "onOpenChange", "isOpen", "handleFilterChange", "useRelation", "_jsxs", "Filters", "Root", "onChange", "_jsx", "<PERSON><PERSON>", "Popover", "List", "pageSize", "setPageSize", "useState", "search", "setSearch", "debouncedSearch", "useDebounce", "_q", "useField", "handleOpenChange", "pagination", "pageCount", "page", "Combobox", "aria-label", "loading", "onLoadMore", "hasMoreItems", "onInputChange", "e", "currentTarget", "ComboboxOption", "CellValue", "type", "value", "formatDate", "formatTime", "formatNumber", "useIntl", "formattedValue", "parseISO", "dateStyle", "timeStyle", "hour", "minute", "second", "split", "date", "Date", "setHours", "setMinutes", "setSeconds", "includes", "maximumFractionDigits", "toString", "SingleComponent", "content", "mainField", "_jsx", "<PERSON><PERSON><PERSON>", "label", "name", "Typography", "max<PERSON><PERSON><PERSON>", "textColor", "ellipsis", "CellValue", "type", "value", "RepeatableComponent", "formatMessage", "useIntl", "_jsxs", "<PERSON><PERSON>", "Root", "<PERSON><PERSON>", "onClick", "e", "stopPropagation", "Badge", "length", "id", "defaultMessage", "number", "Content", "map", "item", "<PERSON><PERSON>", "disabled", "getFileExtension", "ext", "substring", "MediaSingle", "url", "mime", "alternativeText", "name", "formats", "fileURL", "prefixFileUrlWithBackendUrl", "includes", "thumbnail", "mediaURL", "_jsx", "Avatar", "<PERSON><PERSON>", "src", "alt", "fallback", "preview", "fileExtension", "fileName", "length", "<PERSON><PERSON><PERSON>", "description", "FileWrapper", "children", "Flex", "tag", "position", "borderRadius", "width", "height", "borderColor", "background", "paddingLeft", "justifyContent", "alignItems", "FileTypography", "variant", "textColor", "styled", "Typography", "MediaMultiple", "content", "Group", "map", "file", "index", "key", "id", "remainingFiles", "RelationSingle", "mainField", "content", "_jsx", "Typography", "max<PERSON><PERSON><PERSON>", "textColor", "ellipsis", "getRelationLabel", "RelationMultiple", "rowId", "name", "model", "useDoc", "formatMessage", "useIntl", "notify<PERSON><PERSON><PERSON>", "useNotifyAT", "isOpen", "setIsOpen", "useState", "targetField", "split", "data", "isLoading", "useGetRelationsQuery", "id", "skip", "refetchOnMountOrArgChange", "contentCount", "Array", "isArray", "length", "count", "React", "useEffect", "getTranslation", "defaultMessage", "_jsxs", "<PERSON><PERSON>", "Root", "onOpenChange", "<PERSON><PERSON>", "onClick", "e", "stopPropagation", "style", "cursor", "fontWeight", "number", "Content", "<PERSON><PERSON>", "disabled", "Loader", "small", "results", "_Fragment", "map", "entry", "documentId", "pagination", "total", "aria-disabled", "aria-label", "CellContent", "content", "mainField", "attribute", "rowId", "name", "<PERSON><PERSON><PERSON><PERSON>", "_jsx", "Typography", "textColor", "paddingLeft", "type", "paddingRight", "multiple", "MediaSingle", "MediaMultiple", "isSingleRelation", "relation", "RelationSingle", "RelationMultiple", "repeatable", "RepeatableComponent", "SingleComponent", "<PERSON><PERSON><PERSON>", "description", "max<PERSON><PERSON><PERSON>", "ellipsis", "CellValue", "value", "length", "undefined", "includes", "isEmpty", "Array", "isArray", "count", "ViewSettingsMenu", "props", "permissions", "useTypedSelector", "state", "admin_app", "contentManager", "collectionTypesConfigurations", "query", "useQueryParams", "formatMessage", "useIntl", "allowedActions", "canConfigureView", "useRBAC", "_jsxs", "Popover", "Root", "_jsx", "<PERSON><PERSON>", "IconButton", "label", "id", "defaultMessage", "Cog", "Content", "side", "align", "sideOffset", "Flex", "alignItems", "direction", "padding", "gap", "LinkButton", "size", "startIcon", "ListPlus", "variant", "tag", "NavLink", "to", "pathname", "search", "plugins", "stringify", "encode", "FieldPicker", "headers", "resetHeaders", "setHeaders", "trackUsage", "useTracking", "locale", "schema", "model", "useDoc", "list", "useDocumentLayout", "formatter", "useCollator", "sensitivity", "attributes", "columns", "Object", "keys", "filter", "name", "checkIfAttributeIsDisplayable", "map", "metadatas", "sort", "a", "b", "compare", "handleChange", "newHeaders", "includes", "header", "handleReset", "borderWidth", "maxHeight", "overflow", "justifyContent", "Typography", "fontWeight", "TextButton", "onClick", "isActive", "wrap", "background", "hasRadius", "Checkbox", "onCheckedChange", "checked", "fontSize", "INJECT_COLUMN_IN_TABLE", "HOOKS", "LayoutsHeaderCustom", "styled", "Layouts", "Header", "ListViewPage", "trackUsage", "useTracking", "navigate", "useNavigate", "formatMessage", "useIntl", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "getTranslation", "collectionType", "model", "schema", "useDoc", "list", "useDocumentLayout", "displayedHeaders", "setDisplayedHeaders", "useState", "listLayout", "usePrev", "layout", "React", "useEffect", "isEqual", "handleSetHeaders", "headers", "convertListLayoutToFieldLayouts", "attributes", "metadatas", "query", "useQueryParams", "page", "pageSize", "settings", "toString", "sort", "defaultSortBy", "defaultSortOrder", "params", "useMemo", "buildValidParams", "data", "error", "isFetching", "useGetAllDocumentsQuery", "type", "message", "results", "pagination", "pageCount", "search", "stringify", "replace", "canCreate", "useDocumentRBAC", "runHookWaterfall", "useStrapiApp", "tableHeaders", "formattedHeaders", "map", "header", "translation", "label", "id", "name", "defaultMessage", "mainField", "options", "draftAndPublish", "push", "attribute", "searchable", "sortable", "_jsx", "Page", "Loading", "Error", "contentTypeTitle", "info", "displayName", "handleRowClick", "pathname", "plugins", "length", "_jsxs", "_Fragment", "unstable_tours", "contentManager", "Introduction", "Box", "paddingTop", "Main", "Title", "primaryAction", "CreateButton", "subtitle", "number", "total", "title", "navigationAction", "BackButton", "Action", "endActions", "InjectionZone", "area", "ViewSettingsMenu", "setHeaders", "resetHeaders", "startActions", "SearchInput", "disabled", "target", "placeholder", "trackedEvent", "filterable", "Filters", "Content", "background", "shadow", "hasRadius", "EmptyStateLayout", "action", "variant", "content", "icon", "EmptyDocuments", "width", "Flex", "gap", "direction", "alignItems", "Table", "Root", "rows", "isLoading", "TableActionsBar", "Head", "HeaderCheckboxCell", "<PERSON><PERSON><PERSON><PERSON>", "Empty", "Body", "row", "Row", "cursor", "onClick", "documentId", "CheckboxCell", "cell<PERSON>ormatt<PERSON>", "status", "Cell", "DocumentStatus", "max<PERSON><PERSON><PERSON>", "includes", "split", "Typography", "textColor", "getDisplayName", "CellContent", "rowId", "ActionsCell", "e", "stopPropagation", "TableActions", "document", "Pagination", "onPageSizeChange", "PageSize", "Links", "selectRow", "useTable", "state", "locale", "i18n", "prevLocale", "ActionBar", "BulkActions<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "tag", "ReactRouterLink", "startIcon", "Plus", "style", "textDecoration", "to", "min<PERSON><PERSON><PERSON>", "marginLeft", "ProtectedListViewPage", "slug", "useParams", "permissions", "useRBAC", "PERMISSIONS", "subject", "Protect", "DocumentRBAC"]}