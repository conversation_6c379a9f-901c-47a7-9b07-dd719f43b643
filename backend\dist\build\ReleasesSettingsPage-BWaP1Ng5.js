import{j as e,P as d,a as j,v as C,w,x as A,y as I,z as S,A as y,B as m,C as F,G as E,D as L,E as D,e as B,T as G,H as T,J as H,K as N,M as l,N as U,O as _}from"./strapi-z7ApxZZq.js";import{u as p}from"./hooks-YytzvSTS.js";import{S as V}from"./schemas-CbOl1I6u.js";const O=()=>{const{formatMessage:s}=j(),{formatAPIError:o}=C(),{toggleNotification:i}=w(),{data:c,isLoading:a}=A(),[t,{isLoading:z}]=I(),v=p(r=>r.admin_app.permissions.settings?.releases),{allowedActions:{canUpdate:b}}=S(v),{timezoneList:M}=y(new Date),R=async(r,{setErrors:u})=>{const{defaultTimezone:g}=r,h=g,x=M.some(n=>n.value===h);if(!x&&g){const n=s({id:"components.Input.error.validation.combobox.invalid",defaultMessage:"The value provided is not valid"});u({defaultTimezone:n}),i({type:"danger",message:n});return}const P=!g||!x?{defaultTimezone:null}:{defaultTimezone:h};try{const n=await t(P);"data"in n?i({type:"success",message:s({id:"content-releases.pages.Settings.releases.setting.default-timezone-notification-success",defaultMessage:"Default timezone updated."})}):H(n.error)?i({type:"danger",message:o(n.error)}):i({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occurred"})})}catch{i({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occurred"})})}};if(a)return e.jsx(d.Loading,{});const f=s({id:"content-releases.pages.Releases.title",defaultMessage:"Releases"});return e.jsxs(m.Root,{children:[e.jsx(d.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:f})}),e.jsx(d.Main,{"aria-busy":a,tabIndex:-1,children:e.jsx(F,{method:"PUT",initialValues:{defaultTimezone:c?.data.defaultTimezone},onSubmit:R,validationSchema:V,children:({modified:r,isSubmitting:u})=>e.jsxs(e.Fragment,{children:[e.jsx(m.Header,{primaryAction:b?e.jsx(L,{disabled:!r||z,loading:u,startIcon:e.jsx(D,{}),type:"submit",children:s({id:"global.save",defaultMessage:"Save"})}):null,secondaryAction:e.jsx(E,{label:s({id:"components.premiumFeature.title",defaultMessage:"Premium feature"})}),title:f,subtitle:s({id:"content-releases.pages.Settings.releases.description",defaultMessage:"Create and manage content updates"})}),e.jsx(m.Content,{children:e.jsxs(B,{direction:"column",background:"neutral0",alignItems:"stretch",padding:6,gap:6,shadow:"filterShadow",hasRadius:!0,children:[e.jsx(G,{variant:"delta",tag:"h2",children:s({id:"content-releases.pages.Settings.releases.preferences.title",defaultMessage:"Preferences"})}),e.jsx(T.Root,{children:e.jsx(T.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsx(k,{})})})]})})]})})})]})},k=()=>{const s=p(t=>t.admin_app.permissions.settings?.releases),{allowedActions:{canUpdate:o}}=S(s),{formatMessage:i}=j(),{timezoneList:c}=y(new Date),a=N("defaultTimezone");return e.jsxs(l.Root,{name:"defaultTimezone",hint:i({id:"content-releases.pages.Settings.releases.timezone.hint",defaultMessage:"The timezone of every release can still be changed individually."}),error:a.error,children:[e.jsx(l.Label,{children:i({id:"content-releases.pages.Settings.releases.timezone.label",defaultMessage:"Default timezone"})}),e.jsx(U,{autocomplete:{type:"list",filter:"contains"},onTextValueChange:t=>a.onChange("defaultTimezone",t),onChange:t=>{(a.value&&t||!a.value)&&a.onChange("defaultTimezone",t)},onClear:()=>a.onChange("defaultTimezone",""),value:a.value,disabled:!o,children:c.map(t=>e.jsx(_,{value:t.value,children:t.value.replace(/&/," ")},t.value))}),e.jsx(l.Hint,{}),e.jsx(l.Error,{})]})},W=()=>{const s=p(o=>o.admin_app.permissions.settings?.releases?.read);return e.jsx(d.Protect,{permissions:s,children:e.jsx(O,{})})};export{W as ProtectedReleasesSettingsPage};
