import{a as x,U as u,j as e,b as k,V as T,W as m,X as w,Y as f,Z as i,$ as g,a0 as C,T as d,a1 as v,a2 as b,I as E,a3 as D,g as $,k as M}from"./strapi-z7ApxZZq.js";import{W as o}from"./WidgetHelpers-CmZQzLJr.js";const p=M(d).attrs({maxWidth:"14.4rem",display:"block"})`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`,R=({documents:n})=>{const{formatMessage:s}=x(),{trackUsage:l}=k(),r=T(),c=t=>{const a=t.kind==="singleType",j=a?"single-types":"collection-types",y=t.locale?`?plugins[i18n][locale]=${t.locale}`:"";return`/content-manager/${j}/${t.contentTypeUid}${a?"":"/"+t.documentId}${y}`},h=t=>()=>{l("willEditEntryFromHome");const a=c(t);r(a)};return e.jsx(m,{colCount:6,rowCount:n?.length??0,children:e.jsx(w,{children:n?.map(t=>e.jsxs(f,{onClick:h(t),cursor:"pointer",children:[e.jsx(i,{children:e.jsx(p,{title:t.title,variant:"omega",textColor:"neutral800",children:t.title})}),e.jsx(i,{children:e.jsx(p,{variant:"omega",textColor:"neutral600",children:t.kind==="singleType"?s({id:"content-manager.widget.last-edited.single-type",defaultMessage:"Single-Type"}):s({id:t.contentTypeDisplayName,defaultMessage:t.contentTypeDisplayName})})}),e.jsx(i,{children:e.jsx(g,{display:"inline-block",children:t.status?e.jsx(C,{status:t.status}):e.jsx(d,{textColor:"neutral600","aria-hidden":!0,children:"-"})})}),e.jsx(i,{children:e.jsx(d,{textColor:"neutral600",children:e.jsx(v,{timestamp:new Date(t.updatedAt)})})}),e.jsx(i,{children:e.jsx(b,{strapi_stage:t.strapi_stage})}),e.jsx(i,{onClick:a=>a.stopPropagation(),children:e.jsx(g,{display:"inline-block",children:e.jsx(E,{tag:D,to:c(t),onClick:()=>l("willEditEntryFromHome"),label:s({id:"content-manager.actions.edit.label",defaultMessage:"Edit"}),variant:"ghost",children:e.jsx($,{})})})})]},t.documentId))})})},W=()=>{const{formatMessage:n}=x(),{data:s,isLoading:l,error:r}=u();return l?e.jsx(o.Loading,{}):r||!s?e.jsx(o.Error,{}):s.length===0?e.jsx(o.NoData,{children:n({id:"review-workflows.widget.assigned.no-data",defaultMessage:"No entries"})}):e.jsx(R,{documents:s})};export{W as AssignedWidget};
