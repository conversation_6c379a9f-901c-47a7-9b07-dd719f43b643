import{a as $,j as e,M as P,c8 as le,aJ as ue,cc as M,cq as ce,cr as pe,be,bf as he,e as me,H as X,D as Z,aP as S,bZ as n,aQ as i,b0 as U,aR as d,b_ as ge,b as fe,r as w,w as ve,bW as xe,v as je,b3 as Pe,z as ye,b$ as ae,c0 as qe,c1 as Me,P as C,B as F,W as Le,bn as Re,Y as G,bo as z,T as L,bq as Te,X as we,Z as I,I as Ce,g as ke,aV as Ee}from"./strapi-z7ApxZZq.js";import{P as s}from"./index-BpJMkQJ_.js";const B=({description:r,disabled:v,intlLabel:h,error:u,name:c,onChange:x,placeholder:t,providerToEditName:m,type:b,value:l})=>{const{formatMessage:o}=$(),g=c==="noName"?`${window.strapi.backendURL}/api/connect/${m}/callback`:l,j=o({id:h.id,defaultMessage:h.defaultMessage},{provider:m,...h.values}),p=r?o({id:r.id,defaultMessage:r.defaultMessage},{provider:m,...r.values}):"";if(b==="bool")return e.jsxs(P.Root,{hint:p,name:c,children:[e.jsx(P.Label,{children:j}),e.jsx(le,{"aria-label":c,checked:l,disabled:v,offLabel:o({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"Off"}),onLabel:o({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"On"}),onChange:q=>{x({target:{name:c,value:q.target.checked}})}}),e.jsx(P.Hint,{})]});const y=t?o({id:t.id,defaultMessage:t.defaultMessage},{...t.values}):"",k=u?o({id:u,defaultMessage:u}):"";return e.jsxs(P.Root,{error:k,name:c,children:[e.jsx(P.Label,{children:j}),e.jsx(ue,{disabled:v,onChange:x,placeholder:y,type:b,value:g}),e.jsx(P.Error,{})]})};B.defaultProps={description:null,disabled:!1,error:"",placeholder:null,value:""};B.propTypes={description:s.shape({id:s.string.isRequired,defaultMessage:s.string.isRequired,values:s.object}),disabled:s.bool,error:s.string,intlLabel:s.shape({id:s.string.isRequired,defaultMessage:s.string.isRequired,values:s.object}).isRequired,name:s.string.isRequired,onChange:s.func.isRequired,placeholder:s.shape({id:s.string.isRequired,defaultMessage:s.string.isRequired,values:s.object}),providerToEditName:s.string.isRequired,type:s.string.isRequired,value:s.oneOfType([s.bool,s.string])};const D=({headerBreadcrumbs:r,initialData:v,isSubmiting:h,layout:u,isOpen:c,onSubmit:x,onToggle:t,providerToEditName:m})=>{const{formatMessage:b}=$();return e.jsx(M.Root,{open:c,onOpenChange:t,children:e.jsxs(M.Content,{children:[e.jsx(M.Header,{children:e.jsx(ce,{label:r.join(", "),children:r.map((l,o,g)=>e.jsx(pe,{isCurrent:o===g.length-1,children:l},l))})}),e.jsx(be,{onSubmit:l=>x(l),initialValues:v,validationSchema:u.schema,validateOnChange:!1,children:({errors:l,handleChange:o,values:g})=>e.jsxs(he,{children:[e.jsx(M.Body,{children:e.jsx(me,{direction:"column",alignItems:"stretch",gap:1,children:e.jsx(X.Root,{gap:5,children:u.form.map(j=>j.map(p=>e.jsx(X.Item,{col:p.size,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(B,{...p,error:l[p.name],onChange:o,value:g[p.name],providerToEditName:m})},p.name)))})})}),e.jsxs(M.Footer,{children:[e.jsx(Z,{variant:"tertiary",onClick:t,type:"button",children:b({id:"app.components.Button.cancel",defaultMessage:"Cancel"})}),e.jsx(Z,{type:"submit",loading:h,children:b({id:"global.save",defaultMessage:"Save"})})]})]})})]})})};D.defaultProps={initialData:null,providerToEditName:null};D.propTypes={headerBreadcrumbs:s.arrayOf(s.string).isRequired,initialData:s.object,layout:s.shape({form:s.arrayOf(s.array),schema:s.object}).isRequired,isOpen:s.bool.isRequired,isSubmiting:s.bool.isRequired,onSubmit:s.func.isRequired,onToggle:s.func.isRequired,providerToEditName:s.string};const _={id:n("PopUpForm.Providers.redirectURL.front-end.label"),defaultMessage:"The redirect URL to your front-end app"},J={id:"http://www.client-app.com",defaultMessage:"http://www.client-app.com"},N={id:n("PopUpForm.Providers.enabled.description"),defaultMessage:"If disabled, users won't be able to use this provider."},O={id:n("PopUpForm.Providers.enabled.label"),defaultMessage:"Enable"},K={id:n("PopUpForm.Providers.key.label"),defaultMessage:"Client ID"},Y={id:n("PopUpForm.Providers.redirectURL.label"),defaultMessage:"The redirect URL to add in your {provider} application configurations"},R={id:n("PopUpForm.Providers.key.placeholder"),defaultMessage:"TEXT"},ee={id:n("PopUpForm.Providers.secret.label"),defaultMessage:"Client Secret"},se=/^$|^[a-z][a-z0-9+.-]*:\/\/[^\s/$.?#](?:[^\s]*[^\s/$.?#])?$/i,Se=/^(([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+)(:\d+)?(\/\S*)?$/i,A={email:{form:[[{intlLabel:O,name:"enabled",type:"bool",description:N,size:6}]],schema:S().shape({enabled:U().required(d.required.id)})},providers:{form:[[{intlLabel:O,name:"enabled",type:"bool",description:N,size:6,validations:{required:!0}}],[{intlLabel:K,name:"key",type:"text",placeholder:R,size:12,validations:{required:!0}}],[{intlLabel:ee,name:"secret",type:"text",placeholder:R,size:12,validations:{required:!0}}],[{intlLabel:_,placeholder:J,name:"callback",type:"text",size:12,validations:{required:!0}}],[{intlLabel:Y,name:"noName",type:"text",validations:{},size:12,disabled:!0}]],schema:S().shape({enabled:U().required(d.required.id),key:i().when("enabled",{is:!0,then:i().required(d.required.id),otherwise:i()}),secret:i().when("enabled",{is:!0,then:i().required(d.required.id),otherwise:i()}),callback:i().when("enabled",{is:!0,then:i().matches(se,d.regex.id).required(d.required.id),otherwise:i()})})},providersWithSubdomain:{form:[[{intlLabel:O,name:"enabled",type:"bool",description:N,size:6,validations:{required:!0}}],[{intlLabel:K,name:"key",type:"text",placeholder:R,size:12,validations:{required:!0}}],[{intlLabel:ee,name:"secret",type:"text",placeholder:R,size:12,validations:{required:!0}}],[{intlLabel:{id:n({id:"PopUpForm.Providers.jwksurl.label"}),defaultMessage:"JWKS URL"},name:"jwksurl",type:"text",placeholder:R,size:12,validations:{required:!1}}],[{intlLabel:{id:n("PopUpForm.Providers.subdomain.label"),defaultMessage:"Host URI (Subdomain)"},name:"subdomain",type:"text",placeholder:{id:n("PopUpForm.Providers.subdomain.placeholder"),defaultMessage:"my.subdomain.com"},size:12,validations:{required:!0}}],[{intlLabel:_,placeholder:J,name:"callback",type:"text",size:12,validations:{required:!0}}],[{intlLabel:Y,name:"noName",type:"text",validations:{},size:12,disabled:!0}]],schema:S().shape({enabled:U().required(d.required.id),key:i().when("enabled",{is:!0,then:i().required(d.required.id),otherwise:i()}),secret:i().when("enabled",{is:!0,then:i().required(d.required.id),otherwise:i()}),subdomain:i().when("enabled",{is:!0,then:i().matches(Se,d.regex.id).required(d.required.id),otherwise:i()}),callback:i().when("enabled",{is:!0,then:i().matches(se,d.regex.id).required(d.required.id),otherwise:i()})})}},Ue=()=>{const{formatMessage:r,locale:v}=$(),h=ge(),{trackUsage:u}=fe(),[c,x]=w.useState(!1),[t,m]=w.useState(null),{toggleNotification:b}=ve(),{get:l,put:o}=xe(),{formatAPIError:g}=je(),j=Pe(v,{sensitivity:"base"}),{isLoading:p,allowedActions:{canUpdate:y}}=ye({update:ae.updateProviders}),{isLoading:k,data:q}=qe(["users-permissions","get-providers"],async()=>{const{data:a}=await l("/users-permissions/providers");return a},{initialData:{}}),H=Me(a=>o("/users-permissions/providers",a),{async onSuccess(){await h.invalidateQueries(["users-permissions","get-providers"]),b({type:"success",message:r({id:n("notification.success.submit")})}),u("didEditAuthenticationProvider"),E()},onError(a){b({type:"danger",message:g(a)})},refetchActive:!1}),T=Object.entries(q).reduce((a,[f,ne])=>{const{icon:V,enabled:de,subdomain:oe}=ne;return a.push({name:f,icon:V==="envelope"?["fas","envelope"]:["fab",V],enabled:de,subdomain:oe}),a},[]).sort((a,f)=>j.compare(a.name,f.name)),ie=k||p,W=w.useMemo(()=>t?!!T.find(f=>f.name===t)?.subdomain:!1,[T,t]),re=w.useMemo(()=>t==="email"?A.email:W?A.providersWithSubdomain:A.providers,[t,W]),E=()=>{x(a=>!a)},Q=a=>{y&&(m(a.name),E())},te=async a=>{u("willEditAuthenticationProvider"),H.mutate({providers:{...q,[t]:a}})};return ie?e.jsx(C.Loading,{}):e.jsxs(F.Root,{children:[e.jsx(C.Title,{children:r({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:r({id:n("HeaderNav.link.providers"),defaultMessage:"Providers"})})}),e.jsxs(C.Main,{children:[e.jsx(F.Header,{title:r({id:n("HeaderNav.link.providers"),defaultMessage:"Providers"})}),e.jsx(F.Content,{children:e.jsxs(Le,{colCount:3,rowCount:T.length+1,children:[e.jsx(Re,{children:e.jsxs(G,{children:[e.jsx(z,{children:e.jsx(L,{variant:"sigma",textColor:"neutral600",children:r({id:"global.name",defaultMessage:"Name"})})}),e.jsx(z,{children:e.jsx(L,{variant:"sigma",textColor:"neutral600",children:r({id:n("Providers.status"),defaultMessage:"Status"})})}),e.jsx(z,{children:e.jsx(L,{variant:"sigma",children:e.jsx(Te,{children:r({id:"global.settings",defaultMessage:"Settings"})})})})]})}),e.jsx(we,{children:T.map(a=>e.jsxs(G,{onClick:()=>y?Q(a):void 0,children:[e.jsx(I,{width:"45%",children:e.jsx(L,{fontWeight:"semiBold",textColor:"neutral800",children:a.name})}),e.jsx(I,{width:"65%",children:e.jsx(L,{textColor:a.enabled?"success600":"danger600","data-testid":`enable-${a.name}`,children:a.enabled?r({id:"global.enabled",defaultMessage:"Enabled"}):r({id:"global.disabled",defaultMessage:"Disabled"})})}),e.jsx(I,{onClick:f=>f.stopPropagation(),children:y&&e.jsx(Ce,{onClick:()=>Q(a),variant:"ghost",label:"Edit",children:e.jsx(ke,{})})})]},a.name))})]})})]}),e.jsx(D,{initialData:q[t],isOpen:c,isSubmiting:H.isLoading,layout:re,headerBreadcrumbs:[r({id:n("PopUpForm.header.edit.providers"),defaultMessage:"Edit Provider"}),Ee(t)],onToggle:E,onSubmit:te,providerToEditName:t})]})},Ie=()=>e.jsx(C.Protect,{permissions:ae.readProviders,children:e.jsx(Ue,{})});export{Ue as ProvidersPage,Ie as default};
