import {
  useLicenseLimitNotification
} from "./chunk-LBIJENJ4.js";
import "./chunk-AOLNGJZC.js";
import {
  ListPageCE
} from "./chunk-QLJRPE4P.js";
import "./chunk-64ROWGZK.js";
import "./chunk-Y76D23YT.js";
import "./chunk-AEVEKNWA.js";
import "./chunk-AGIQEOXA.js";
import "./chunk-BEZCWAXF.js";
import "./chunk-ITIYAEI7.js";
import "./chunk-K4LYEYIN.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-7US6K6XC.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-DZIOEGNZ.js";
import "./chunk-L3WQFZOD.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-67LA7IEH.js";
import "./chunk-OGSEDJD2.js";
import "./chunk-GM54BMM2.js";
import "./chunk-2DNMQP4H.js";
import "./chunk-L32VSWBJ.js";
import "./chunk-XVW7MCOW.js";
import "./chunk-2U7FOQNK.js";
import "./chunk-WIFIVZU3.js";
import "./chunk-T3UNFN7Y.js";
import "./chunk-DETWX3NC.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import "./chunk-FTSHQ5RF.js";
import "./chunk-RZWN45NY.js";
import "./chunk-FOUXGIF2.js";
import "./chunk-TXOJWASW.js";
import "./chunk-BUMITFEB.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-PW6GS6S3.js";
import "./chunk-Y6YT4U2T.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-3CQBCJ3G.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/SettingsPage/pages/Users/<USER>
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var UserListPageEE = () => {
  useLicenseLimitNotification();
  return (0, import_jsx_runtime.jsx)(ListPageCE, {});
};
export {
  UserListPageEE
};
//# sourceMappingURL=ListPage-PPXL6WPS.js.map
