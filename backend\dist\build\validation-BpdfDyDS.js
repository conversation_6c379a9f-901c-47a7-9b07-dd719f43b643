import{aQ as s,aR as r,aW as n}from"./strapi-z7ApxZZq.js";const i={firstname:s().trim().required({id:r.required.id,defaultMessage:"This field is required"}),lastname:s().nullable(),email:s().email(r.email).lowercase().required({id:r.required.id,defaultMessage:"This field is required"}),username:s().transform(e=>e===""?void 0:e).nullable(),password:s().transform(e=>e===""||e===null?void 0:e).nullable().min(8,{...r.minLength,values:{min:8}}).test("max-bytes",{id:"components.Input.error.contain.maxBytes",defaultMessage:"Password must be less than 73 bytes"},function(e){return e?new TextEncoder().encode(e).length<=72:!0}).matches(/[a-z]/,{id:"components.Input.error.contain.lowercase",defaultMessage:"Password must contain at least one lowercase character"}).matches(/[A-Z]/,{id:"components.Input.error.contain.uppercase",defaultMessage:"Password must contain at least one uppercase character"}).matches(/\d/,{id:"components.Input.error.contain.number",defaultMessage:"Password must contain at least one number"}),confirmPassword:s().transform(e=>e===""?null:e).nullable().min(8,{...r.minLength,values:{min:8}}).oneOf([n("password"),null],{id:"components.Input.error.password.noMatch",defaultMessage:"Passwords must match"}).when("password",(e,a)=>e?a.required({id:r.required.id,defaultMessage:"This field is required"}).nullable():a)};export{i as C};
