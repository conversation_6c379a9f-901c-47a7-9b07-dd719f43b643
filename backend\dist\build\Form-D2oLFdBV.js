import{a as B,w as ue,K as te,ao as ge,cb as ne,j as e,cc as $,C as ae,aP as he,e as j,cd as ie,H as C,ar as u,aU as se,D as G,by as fe,b0 as xe,aQ as X,ce as z,r as L,T as R,$ as ye,cf as H,av as be,bV as U,f as je,I as q,g as Ie,h as ve,bH as Ce,au as oe,cg as Fe,ch as Me,ci as Q,k as re,cj as Se,B as Y,aE as _e,b7 as Te,bg as ke}from"./strapi-z7ApxZZq.js";import{D as Re,S as De,a as $e,b as we,u as ze,c as de,C as Ee}from"./sortable.esm-4pfM8QNQ.js";import{F as Ne}from"./FieldTypeIcon-B64159oL.js";const Pe=he().shape({label:X().required().nullable(),description:X(),editable:xe(),size:fe().required()}),Le=({attribute:t,name:a,onClose:s})=>{const{formatMessage:o}=B(),{toggleNotification:l}=ue(),{value:c,onChange:g}=te(a),{data:I}=ge(void 0,{selectFromResult:p=>{if(t?.type!=="relation"||!p.data)return{data:[]};if("targetModel"in t&&typeof t.targetModel=="string"){const v=p.data.contentTypes.find(x=>x.uid===t.targetModel);if(v)return{data:Object.entries(v.attributes).reduce((x,[M,_])=>(ne.includes(_.type)||x.push({label:M,value:M}),x),[])}}return{data:[]}},skip:t?.type!=="relation"});return!c||c.name===k||!t?(console.error("You've opened a field to edit without it being part of the form, this is likely a bug with Strapi. Please open an issue."),l({message:o({id:"content-manager.containers.edit-settings.modal-form.error",defaultMessage:"An error occurred while trying to open the form."}),type:"danger"}),null):e.jsx($.Content,{children:e.jsxs(ae,{method:"PUT",initialValues:c,validationSchema:Pe,onSubmit:p=>{g(a,p),s()},children:[e.jsx($.Header,{children:e.jsxs(j,{gap:3,children:[e.jsx(Ne,{type:t.type}),e.jsx($.Title,{children:o({id:"content-manager.containers.edit-settings.modal-form.label",defaultMessage:"Edit {fieldName}"},{fieldName:ie(c.name)})})]})}),e.jsx($.Body,{children:e.jsx(C.Root,{gap:4,children:[{name:"label",label:o({id:u("containers.edit-settings.modal-form.label"),defaultMessage:"Label"}),size:6,type:"string"},{name:"description",label:o({id:u("containers.edit-settings.modal-form.description"),defaultMessage:"Description"}),size:6,type:"string"},{name:"placeholder",label:o({id:u("containers.edit-settings.modal-form.placeholder"),defaultMessage:"Placeholder"}),size:6,type:"string"},{name:"editable",label:o({id:u("containers.edit-settings.modal-form.editable"),defaultMessage:"Editable"}),size:6,type:"boolean"},{name:"mainField",label:o({id:u("containers.edit-settings.modal-form.mainField"),defaultMessage:"Entry title"}),hint:o({id:u("containers.SettingPage.edit-settings.modal-form.mainField.hint"),defaultMessage:"Set the displayed field"}),size:6,options:I,type:"enumeration"},{name:"size",label:o({id:u("containers.ListSettingsView.modal-form.size"),defaultMessage:"Size"}),size:6,options:[{value:"4",label:"33%"},{value:"6",label:"50%"},{value:"8",label:"66%"},{value:"12",label:"100%"}],type:"enumeration"}].filter(Be(t.type)).map(({size:p,...v})=>e.jsx(C.Item,{col:p,direction:"column",alignItems:"stretch",children:e.jsx(se,{...v})},v.name))})}),e.jsxs($.Footer,{children:[e.jsx($.Close,{children:e.jsx(G,{variant:"tertiary",children:o({id:"app.components.Button.cancel",defaultMessage:"Cancel"})})}),e.jsx(G,{type:"submit",children:o({id:"global.finish",defaultMessage:"Finish"})})]})]})})},Be=t=>a=>{switch(t){case"blocks":case"richtext":return a.name!=="size"&&a.name!=="mainField";case"boolean":case"media":return a.name!=="placeholder"&&a.name!=="mainField";case"component":case"dynamiczone":return a.name==="label"||a.name==="editable";case"json":return a.name!=="placeholder"&&a.name!=="mainField"&&a.name!=="size";case"relation":return!0;default:return a.name!=="mainField"}},P=12,Ae=({id:t,children:a})=>{const s=ze({id:t});return a(s)},Oe=({id:t,children:a})=>{const{attributes:s,setNodeRef:o,transform:l,transition:c}=de({id:t}),g={transform:Ee.Transform.toString(l),transition:c,height:"100%"};return e.jsx("div",{ref:o,style:g,...s,children:a})},Z=t=>t.map((a,s)=>({...a,dndId:`container-${s}`,children:a.children.map((o,l)=>({...o,dndId:`container-${s}-child-${l}`,formName:`layout.${s}.children.${l}`}))})),He=({attributes:t,fieldSizes:a,components:s,metadatas:o={}})=>{const{formatMessage:l}=B(),c=z("Fields",n=>n.values.layout??[]),g=z("Fields",n=>n.onChange),I=z("Fields",n=>n.addFieldRow),p=z("Fields",n=>n.removeFieldRow),v=c.map(n=>n.children.map(i=>i.name)).flat(),x=Object.entries(o).reduce((n,i)=>{const[r,{visible:m,...y}]=i;if(!v.includes(r)&&m===!0){const f=t[r]?.type,b=f?a[f]:P;n.push({...y,label:y.label??r,name:r,size:b})}return n},[]),M=(n,i)=>()=>{c[n].children.length===1?p("layout",n):g(`layout.${n}.children`,[...c[n].children.slice(0,i),...c[n].children.slice(i+1)])},_=n=>()=>{I("layout",{children:[n]})},[d,T]=L.useState(()=>Z(c)),[D,J]=L.useState(null);function E(n,i){return n in i?n:Object.keys(i).find(r=>i[r].children.find(m=>m.dndId===n))}const W=(n,i)=>i.children.find(r=>n===r.dndId),V=()=>Object.fromEntries(d.map(n=>[n.dndId,n])),le=n=>n.map(i=>({...i,children:i.children.filter(r=>r.name!==k)})).filter(i=>i.children.length>0).map(i=>{const r=i.children.reduce((m,y)=>m+y.size,0);if(r<P){const[m]=Q(i.children.at(-1)?.__temp_key__,void 0,1);return{...i,children:[...i.children,{name:k,size:P-r,__temp_key__:m}]}}return i});return L.useEffect(()=>{const n=Z(c);T(n)},[c,T]),e.jsx(Re,{onDragStart:n=>{const i=V(),r=E(n.active.id,i);if(!r)return;const m=W(n.active.id,i[r]);m&&J(m)},onDragOver:({active:n,over:i})=>{const r=V(),m=E(n.id,r),y=E(i?.id??"",r),f=d.findIndex(h=>h.dndId===m),b=d.findIndex(h=>h.dndId===y);if(!m||!y)return;const S=W(n.id,r[m]),A=W(i?.id??"",r[y]),N=r[y].children.findIndex(h=>h.dndId===i?.id);if(!S)return;if(S?.size===P){const h=U(d,O=>{O[f].children=d[b].children,O[b].children=d[f].children});T(h);return}const K=U(d,h=>{if(h[f].children=h[f].children.filter(w=>w.dndId!==n.id),h[b].children.reduce((w,F)=>F.name===k?w:w+F.size,0)+S.size>P){h[f].children=d[f].children;return}if(A?.name===k){h[b].children.splice(N,1,S);return}h[b].children.splice(N,0,S)});T(K)},onDragEnd:n=>{const{active:i,over:r}=n,{id:m}=i,y=r?.id,f=V(),b=E(m,f),S=E(y,f);if(!b||!S)return;const A=f[b].children.findIndex(F=>F.dndId===m),N=f[S].children.findIndex(F=>F.dndId===y),K=U(f,F=>{A!==N&&b===S&&(F[b].children=we(F[b].children,A,N))}),h=Object.values(K),w=le(h).map(({dndId:F,children:ce,...me})=>({...me,children:ce.map(({dndId:Je,formName:Xe,...pe})=>pe)}));g("layout",w),J(null)},children:e.jsxs(j,{paddingTop:6,direction:"column",alignItems:"stretch",gap:4,children:[e.jsxs(j,{alignItems:"flex-start",direction:"column",justifyContent:"space-between",children:[e.jsx(R,{fontWeight:"bold",children:l({id:u("containers.list.displayedFields"),defaultMessage:"Displayed fields"})}),e.jsx(R,{variant:"pi",textColor:"neutral600",children:l({id:"containers.SettingPage.editSettings.description",defaultMessage:"Drag & drop the fields to build the layout"})})]}),e.jsx(ye,{padding:4,hasRadius:!0,borderStyle:"dashed",borderWidth:"1px",borderColor:"neutral300",children:e.jsxs(j,{direction:"column",alignItems:"stretch",gap:2,children:[d.map((n,i)=>e.jsx(De,{id:n.dndId,items:n.children.map(r=>({id:r.dndId})),children:e.jsx(Ae,{id:n.dndId,children:({setNodeRef:r})=>e.jsx(C.Root,{ref:r,gap:2,children:n.children.map((m,y)=>e.jsx(C.Item,{col:m.size,direction:"column",alignItems:"stretch",children:e.jsx(Oe,{id:m.dndId,children:e.jsx(ee,{attribute:t[m.name],components:s,name:m.formName,onRemoveField:M(i,y),dndId:m.dndId})})},m.dndId))},n.dndId)})},n.dndId)),e.jsx($e,{children:D?e.jsx(ee,{attribute:t[D.name],components:s,name:D.formName,dndId:D.dndId}):null}),e.jsxs(H.Root,{children:[e.jsx(H.Trigger,{startIcon:e.jsx(be,{}),endIcon:null,disabled:x.length===0,fullWidth:!0,variant:"secondary",children:l({id:u("containers.SettingPage.add.field"),defaultMessage:"Insert another field"})}),e.jsx(H.Content,{children:x.map(n=>e.jsx(H.Item,{onSelect:_(n),children:n.label},n.name))})]})]})})]})})},k="_TEMP_",ee=({attribute:t,components:a,name:s,onRemoveField:o,dndId:l})=>{const[c,g]=L.useState(!1),{formatMessage:I}=B(),{value:p}=te(s),{listeners:v,setActivatorNodeRef:x}=de({id:l}),M=d=>{d.preventDefault(),d.stopPropagation(),o&&o?.(d)},_=d=>{d.preventDefault(),d.stopPropagation(),g(!0)};return p?p.name===k?e.jsx(j,{tag:"span",height:"100%",style:{opacity:0}}):t?e.jsxs($.Root,{open:c,onOpenChange:g,children:[e.jsxs(j,{borderColor:"neutral150",background:"neutral100",hasRadius:!0,gap:3,cursor:"pointer",onClick:()=>{g(!0)},children:[e.jsx(We,{ref:x,tag:"span",withTooltip:!1,label:I({id:u("components.DraggableCard.move.field"),defaultMessage:"Move {item}"},{item:p.label}),...v,children:e.jsx(je,{})}),e.jsxs(j,{direction:"column",alignItems:"flex-start",grow:1,overflow:"hidden",children:[e.jsxs(j,{gap:3,justifyContent:"space-between",width:"100%",children:[e.jsx(R,{ellipsis:!0,fontWeight:"bold",children:p.label}),e.jsxs(j,{children:[e.jsx(q,{type:"button",variant:"ghost",background:"transparent",onClick:_,withTooltip:!1,label:I({id:u("components.DraggableCard.edit.field"),defaultMessage:"Edit {item}"},{item:p.label}),children:e.jsx(Ie,{})}),e.jsx(q,{type:"button",variant:"ghost",onClick:M,background:"transparent",withTooltip:!1,label:I({id:u("components.DraggableCard.delete.field"),defaultMessage:"Delete {item}"},{item:p.label}),children:e.jsx(ve,{})})]})]}),t?.type==="component"?e.jsxs(j,{paddingTop:3,paddingRight:3,paddingBottom:3,paddingLeft:0,alignItems:"flex-start",direction:"column",gap:2,width:"100%",children:[e.jsx(C.Root,{gap:4,width:"100%",children:a[t.component].layout.map(d=>d.map(({size:T,...D})=>e.jsx(C.Item,{col:T,direction:"column",alignItems:"stretch",children:e.jsx(j,{alignItems:"center",background:"neutral0",paddingTop:2,paddingBottom:2,paddingLeft:3,paddingRight:3,hasRadius:!0,borderColor:"neutral200",children:e.jsx(R,{textColor:"neutral800",children:D.name})})},D.name)))}),e.jsx(Ce,{onClick:d=>d.stopPropagation(),startIcon:e.jsx(Fe,{}),tag:oe,to:`../components/${t.component}/configurations/edit`,children:I({id:u("components.FieldItem.linkToComponentLayout"),defaultMessage:"Set the component's layout"})})]}):null,t?.type==="dynamiczone"?e.jsx(j,{paddingTop:3,paddingRight:3,paddingBottom:3,paddingLeft:0,alignItems:"flex-start",gap:2,width:"100%",wrap:"wrap",children:t?.components.map(d=>e.jsxs(Ve,{onClick:T=>T.stopPropagation(),to:`../components/${d}/configurations/edit`,children:[e.jsx(Me,{icon:a[d].settings.icon}),e.jsx(R,{fontSize:1,textColor:"neutral600",fontWeight:"bold",children:a[d].settings.displayName})]},d))}):null]})]}),p.name!==k&&e.jsx(Le,{attribute:t,name:s,onClose:()=>g(!1)})]}):null:null},We=re(q)`
  height: unset;
  align-self: stretch;
  display: flex;
  align-items: center;
  padding: 0;
  border: none;
  background-color: transparent;
  border-radius: 0px;
  border-right: 1px solid ${({theme:t})=>t.colors.neutral150};
  cursor: all-scroll;

  svg {
    width: 1.2rem;
    height: 1.2rem;
  }
`,Ve=re(oe)`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${({theme:t})=>t.spaces[1]};
  padding: ${t=>t.theme.spaces[2]};
  border: 1px solid ${({theme:t})=>t.colors.neutral200};
  background: ${({theme:t})=>t.colors.neutral0};
  width: 14rem;
  border-radius: ${({theme:t})=>t.borderRadius};
  text-decoration: none;

  &:focus,
  &:hover {
    ${({theme:t})=>`
      background-color: ${t.colors.primary100};
      border-color: ${t.colors.primary200};

      ${R} {
          color: ${t.colors.primary600};
      }
    `}

    /* > ComponentIcon */
    > div:first-child {
      background: ${({theme:t})=>t.colors.primary200};
      color: ${({theme:t})=>t.colors.primary600};

      svg {
        path {
          fill: ${({theme:t})=>t.colors.primary600};
        }
      }
    }
  }
`,nt=({attributes:t,fieldSizes:a,layout:s,onSubmit:o})=>{const{components:l,settings:c,layout:g,metadatas:I}=s,{formatMessage:p}=B(),v=L.useMemo(()=>({layout:Se(Ke,Ue,Ge,qe,Qe)(g),settings:c}),[g,c]);return e.jsx(Y.Root,{children:e.jsx(_e,{children:e.jsxs(ae,{initialValues:v,onSubmit:o,method:"PUT",children:[e.jsx(Ye,{name:c.displayName??""}),e.jsx(Y.Content,{children:e.jsxs(j,{alignItems:"stretch",background:"neutral0",direction:"column",gap:6,hasRadius:!0,shadow:"tableShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:[e.jsx(R,{variant:"delta",tag:"h2",children:p({id:u("containers.SettingPage.settings"),defaultMessage:"Settings"})}),e.jsxs(C.Root,{children:[e.jsx(C.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsx(se,{type:"enumeration",label:p({id:u("containers.SettingPage.editSettings.entry.title"),defaultMessage:"Entry title"}),hint:p({id:u("containers.SettingPage.editSettings.entry.title.description"),defaultMessage:"Set the display field of your entry"}),name:"settings.mainField",options:Object.entries(t).reduce((x,[M,_])=>(_&&(ne.includes(_.type)||x.push({label:M,value:M})),x),[])})}),e.jsx(C.Item,{paddingTop:6,paddingBottom:6,col:12,s:12,direction:"column",alignItems:"stretch",children:e.jsx(Te,{})}),e.jsx(C.Item,{col:12,s:12,direction:"column",alignItems:"stretch",children:e.jsx(R,{variant:"delta",tag:"h3",children:p({id:u("containers.SettingPage.view"),defaultMessage:"View"})})}),e.jsx(C.Item,{col:12,s:12,direction:"column",alignItems:"stretch",children:e.jsx(He,{attributes:t,components:l,fieldSizes:a,metadatas:I})})]})]})})]})})})},Ke=t=>t.flat(1),Ue=t=>t.map(a=>a.map(s=>({...s,mainField:s.mainField?.name}))),Ge=t=>t.map(a=>a.map(({label:s,disabled:o,hint:l,placeholder:c,size:g,name:I,mainField:p})=>({label:s,editable:!o,description:l,mainField:p,placeholder:c,size:g,name:I,__temp_key__:""}))),qe=t=>[...t.map(a=>{const s=a.reduce((o,l)=>o+l.size,0);return s<12?[...a,{name:k,size:12-s,__temp_key__:""}]:a})],Qe=t=>{const a=Q(void 0,void 0,t.length);return t.map((s,o)=>{const l=Q(void 0,void 0,s.length);return{__temp_key__:a[o],children:s.map((c,g)=>({...c,__temp_key__:l[g]}))}})},Ye=({name:t})=>{const{formatMessage:a}=B(),s=z("Header",l=>l.modified),o=z("Header",l=>l.isSubmitting);return e.jsx(Y.Header,{title:a({id:u("components.SettingsViewWrapper.pluginHeader.title"),defaultMessage:"Configure the view - {name}"},{name:ie(t)}),subtitle:a({id:u("components.SettingsViewWrapper.pluginHeader.description.edit-settings"),defaultMessage:"Customize how the edit view will look like."}),navigationAction:e.jsx(ke,{}),primaryAction:e.jsx(G,{disabled:!s,loading:o,type:"submit",children:a({id:"global.save",defaultMessage:"Save"})})})};export{nt as C,k as T};
