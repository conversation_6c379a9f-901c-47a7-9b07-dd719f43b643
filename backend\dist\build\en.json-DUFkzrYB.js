var e="Link",i={link:e,"Settings.email.plugin.button.test-email":"Send test email","Settings.email.plugin.label.defaultFrom":"Default sender email","Settings.email.plugin.label.defaultReplyTo":"Default response email","Settings.email.plugin.label.provider":"Email provider","Settings.email.plugin.label.testAddress":"Recipient email","Settings.email.plugin.notification.config.error":"Failed to retrieve the email config","Settings.email.plugin.notification.data.loaded":"Email settings data has been loaded","Settings.email.plugin.notification.test.error":"Failed to send a test mail to {to}","Settings.email.plugin.notification.test.success":"Email test succeeded, check the {to} mailbox","Settings.email.plugin.placeholder.defaultFrom":"ex: Strapi No-Reply <<EMAIL>>","Settings.email.plugin.placeholder.defaultReplyTo":"ex: Strapi <<EMAIL>>","Settings.email.plugin.placeholder.testAddress":"ex: <EMAIL>","Settings.email.plugin.subTitle":"Test the settings for the Email plugin","Settings.email.plugin.text.configuration":"The plugin is configured through the {file} file, checkout this {link} for the documentation.","Settings.email.plugin.title":"Configuration","Settings.email.plugin.title.config":"Configuration","Settings.email.plugin.title.test":"Test email delivery","SettingsNav.link.settings":"Settings","SettingsNav.section-label":"Email plugin","components.Input.error.validation.email":"This is not a valid email"};export{i as default,e as link};
