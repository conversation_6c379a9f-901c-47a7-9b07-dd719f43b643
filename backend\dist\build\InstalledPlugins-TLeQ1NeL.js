import{a as x,aL as m,j as s,P as n,aM as b,w as P,v as M,bz as T,r as y,B as d,W as C,bn as E,Y as u,bo as c,T as l,X as I,Z as p}from"./strapi-z7ApxZZq.js";const L=()=>{const{formatMessage:e}=x(),{notifyStatus:a}=b(),{toggleNotification:t}=P(),{_unstableFormatAPIError:g}=M(),{isLoading:h,data:i,error:r}=T();return y.useEffect(()=>{i&&a(e({id:"app.utils.notify.data-loaded",defaultMessage:"The {target} has loaded"},{target:e({id:"global.plugins",defaultMessage:"Plugins"})})),r&&t({type:"danger",message:g(r)})},[i,r,g,e,a,t]),h?s.jsx(n.Loading,{}):s.jsx(d.<PERSON>,{children:s.jsxs(n.Main,{children:[s.jsx(d.<PERSON>er,{title:e({id:"global.plugins",defaultMessage:"Plugins"}),subtitle:e({id:"app.components.ListPluginsPage.description",defaultMessage:"List of the installed plugins in the project."})}),s.jsx(d.Content,{children:s.jsxs(C,{colCount:2,rowCount:i?.plugins?.length??1,children:[s.jsx(E,{children:s.jsxs(u,{children:[s.jsx(c,{children:s.jsx(l,{variant:"sigma",textColor:"neutral600",children:e({id:"global.name",defaultMessage:"Name"})})}),s.jsx(c,{children:s.jsx(l,{variant:"sigma",textColor:"neutral600",children:e({id:"global.description",defaultMessage:"description"})})})]})}),s.jsx(I,{children:i?.plugins.map(({name:o,displayName:f,description:j})=>s.jsxs(u,{children:[s.jsx(p,{children:s.jsx(l,{textColor:"neutral800",variant:"omega",fontWeight:"bold",children:e({id:`global.plugins.${o}`,defaultMessage:f})})}),s.jsx(p,{children:s.jsx(l,{textColor:"neutral800",children:e({id:`global.plugins.${o}.description`,defaultMessage:j})})})]},o))})]})})]})})},A=()=>{const{formatMessage:e}=x(),a=m(t=>t.admin_app.permissions);return s.jsxs(n.Protect,{permissions:a.marketplace?.main,children:[s.jsx(n.Title,{children:e({id:"global.plugins",defaultMessage:"Plugins"})}),s.jsx(L,{})]})};export{L as InstalledPlugins,A as ProtectedInstalledPlugins};
