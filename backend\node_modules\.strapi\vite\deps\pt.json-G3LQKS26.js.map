{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/pt.json.mjs"], "sourcesContent": ["var pt = {\n    \"BoundRoute.title\": \"Ligar rota a\",\n    \"EditForm.inputSelect.description.role\": \"Vai atribuir o grupo selecionado ao novo utilizador autenticado.\",\n    \"EditForm.inputSelect.label.role\": \"Grupo por defeito para utilizadores autenticados\",\n    \"EditForm.inputToggle.description.email\": \"Proibir a criação de múltiplas contas com o mesmo email por serviços de autenticação diferentes.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"Quando ativado (ON), os novos utilizadores recebem um email de confirmação.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Após confirmar o seu email, escolha para onde vai ser redirecionado.\",\n    \"EditForm.inputToggle.description.sign-up\": \"Quando desativado (OFF), o processo de registo está proibido. Ninguém se consegue registar mais, independentemente do serviço de authenticação.\",\n    \"EditForm.inputToggle.label.email\": \"Uma conta por endereço de email\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Ativar email de confirmação\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"Endereço de redirecionamento (URL)\",\n    \"EditForm.inputToggle.label.sign-up\": \"Ativar registos\",\n    \"HeaderNav.link.advancedSettings\": \"Configurações avançadas\",\n    \"HeaderNav.link.emailTemplates\": \"Modelos de email\",\n    \"HeaderNav.link.providers\": \"Serviços de autenticação\",\n    \"Plugin.permissions.plugins.description\": \"Defina todas as ações permitidas para o plugin {name}.\",\n    \"Plugins.header.description\": \"Todas as ações associadas a uma rota estão listadas abaixo.\",\n    \"Plugins.header.title\": \"Permissões\",\n    \"Policies.header.hint\": \"Selecione as ações da aplicação ou dos plugins e clique no ícone para mostrar as rotas associadas\",\n    \"Policies.header.title\": \"Configurações avançadas\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"Se não tem a certeza de como usar as variáveis, {link}\",\n    \"PopUpForm.Email.options.from.email.label\": \"Shipper email\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Shipper name\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n    \"PopUpForm.Email.options.message.label\": \"Mensagem\",\n    \"PopUpForm.Email.options.object.label\": \"Assunto\",\n    \"PopUpForm.Email.options.response_email.label\": \"Email de resposta\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"Se desativado, os utilizadores não conseguirão utilizar este serviço de autenticação.\",\n    \"PopUpForm.Providers.enabled.label\": \"Ativar\",\n    \"PopUpForm.Providers.key.label\": \"ID de Client\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXTO\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"Endereço de redirecionamento para a sua aplicação de front-end\",\n    \"PopUpForm.Providers.secret.label\": \"Segredo de cliente\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXTO\",\n    \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Editar Modelos de Email\",\n    \"notification.success.submit\": \"As configurações foram atualizadas\",\n    \"plugin.description.long\": \"Proteja a sua API com um processo completo de autenticação baseado em JWT. Este plugin também vem com estratégia de ACL que permite gerir permissões entre grupos de utilizadores.\",\n    \"plugin.description.short\": \"Proteja a sua API com um processo completo de autenticação baseado em JWT\",\n    \"plugin.name\": \"Grupos & Permissões\"\n};\n\nexport { pt as default };\n//# sourceMappingURL=pt.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACnB;", "names": []}