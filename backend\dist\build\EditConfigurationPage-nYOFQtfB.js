import{b as T,a as I,w as A,v as N,am as v,an as w,ao as D,r as k,ap as O,j as s,P as u,aq as R}from"./strapi-z7ApxZZq.js";import{C as U,T as q}from"./Form-D2oLFdBV.js";import{u as G}from"./hooks-DCxDPOZs.js";import"./sortable.esm-4pfM8QNQ.js";import"./FieldTypeIcon-B64159oL.js";const H=()=>{const{trackUsage:r}=T(),{formatMessage:o}=I(),{toggleNotification:a}=A(),{_unstableFormatAPIError:c}=N(),{isLoading:F,schema:y,model:L}=v(),{isLoading:S,error:h,list:E,edit:g}=w(),{fieldSizes:x,error:d,isLoading:_,isFetching:j}=D(void 0,{selectFromResult:e=>{const l=Object.entries(e.data?.fieldSizes??{}).reduce((n,[t,{default:i}])=>(n[t]=i,n),{});return{isFetching:e.isFetching,isLoading:e.isLoading,error:e.error,fieldSizes:l}}});k.useEffect(()=>{d&&a({type:"danger",message:c(d)})},[d,c,a]);const C=F||S||_||j,[M]=O(),P=async e=>{try{r("willSaveContentTypeLayout");const l=Object.entries(E.metadatas).reduce((t,[i,{mainField:m,...f}])=>{const z=g.metadatas[i],{__temp_key__:Q,size:V,name:$,...b}=e.layout.flatMap(p=>p.children).find(p=>p.name===i)??{};return t[i]={edit:{...z,...b},list:f},t},{}),n=await M({layouts:{edit:e.layout.map(t=>t.children.reduce((i,{name:m,size:f})=>m!==q?[...i,{name:m,size:f}]:i,[])),list:E.layout.map(t=>t.name)},settings:R(e.settings,"displayName",void 0),metadatas:l,uid:L});"data"in n?(r("didEditEditSettings"),a({type:"success",message:o({id:"notification.success.saved",defaultMessage:"Saved"})})):a({type:"danger",message:c(n.error)})}catch{a({type:"danger",message:o({id:"notification.error",defaultMessage:"An error occurred"})})}};return C?s.jsx(u.Loading,{}):d||h||!y?s.jsx(u.Error,{}):s.jsxs(s.Fragment,{children:[s.jsx(u.Title,{children:`Configure ${g.settings.displayName} Edit View`}),s.jsx(U,{onSubmit:P,attributes:y.attributes,fieldSizes:x,layout:g})]})},Y=()=>{const r=G(o=>o.admin_app.permissions.contentManager?.collectionTypesConfigurations);return s.jsx(u.Protect,{permissions:r,children:s.jsx(H,{})})};export{H as EditConfigurationPage,Y as ProtectedEditConfigurationPage};
