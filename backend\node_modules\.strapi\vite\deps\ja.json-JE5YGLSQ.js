import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/plugin-users-permissions/dist/admin/translations/ja.json.mjs
var ja = {
  "BoundRoute.title": "Bound route to",
  "EditForm.inputSelect.description.role": "新しい認証されたユーザーが選択された役割にアタッチされます。",
  "EditForm.inputSelect.label.role": "認証されたユーザーのデフォルトの役割",
  "EditForm.inputToggle.description.email": "ユーザーが異なる認証プロバイダで同じ電子メールアドレスを使用して複数のアカウントを作成できないようにします。",
  "EditForm.inputToggle.description.email-confirmation": "有効（ON）にすると、新しい登録ユーザーに確認メールが送信されます。",
  "EditForm.inputToggle.description.email-confirmation-redirection": "あなたのEメールを確認したら、リダイレクト先を選択してください。",
  "EditForm.inputToggle.description.sign-up": "あなたの電子メールを確認した後、リダイレクト先を選択しました。",
  "EditForm.inputToggle.label.email": "メールアドレスごとに1つのアカウント",
  "EditForm.inputToggle.label.email-confirmation": "Eメールの確認を有効にする",
  "EditForm.inputToggle.label.email-confirmation-redirection": "リダイレクトURL",
  "EditForm.inputToggle.label.sign-up": "申し込みを有効にする",
  "HeaderNav.link.advancedSettings": "高度な設定",
  "HeaderNav.link.emailTemplates": "メールテンプレート",
  "HeaderNav.link.providers": "プロバイダー",
  "Plugin.permissions.plugins.description": "{name} 個のプラグインに対して許可されたすべてのアクションを定義する",
  "Plugins.header.description": "ルートにバインドされたアクションのみが以下にリストされています",
  "Plugins.header.title": "権限",
  "Policies.header.hint": "アプリケーションのアクションまたはプラグインのアクションを選択し、コグアイコンをクリックしてバインドされたルートを表示します",
  "Policies.header.title": "高度な設定",
  "PopUpForm.Email.email_templates.inputDescription": "変数の使用方法がわからない場合は、{link}",
  "PopUpForm.Email.options.from.email.label": "送信者Eメール",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "送信者名",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "メッセージ",
  "PopUpForm.Email.options.object.label": "件名",
  "PopUpForm.Email.options.response_email.label": "応答メール",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "無効にすると、ユーザーはこのプロバイダを使用できなくなります。",
  "PopUpForm.Providers.enabled.label": "有効にする",
  "PopUpForm.Providers.key.label": "クライアントID",
  "PopUpForm.Providers.key.placeholder": "TEXT",
  "PopUpForm.Providers.redirectURL.front-end.label": "フロントエンドアプリへのリダイレクトURL",
  "PopUpForm.Providers.secret.label": "クライアントの秘密",
  "PopUpForm.Providers.secret.placeholder": "TEXT",
  "PopUpForm.Providers.subdomain.label": "Host URI (Subdomain)",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "メールテンプレートの編集",
  "notification.success.submit": "設定が更新されました",
  "plugin.description.long": "JWTに基づいた完全な認証プロセスでAPIを保護します。このプラグインには、ユーザーのグループ間で権限を管理できるACL戦略もあります。",
  "plugin.description.short": "JWTに基づく完全な認証プロセスでAPIを保護する",
  "plugin.name": "ロールと権限"
};
export {
  ja as default
};
//# sourceMappingURL=ja.json-JE5YGLSQ.js.map
