{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\dfghj\\\\site\\\\frontend\\\\src\\\\components\\\\DebugTest.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DebugTest() {\n  _s();\n  const [apiData, setApiData] = useState({\n    importateurs: null,\n    exportateurs: null,\n    opportunites: null,\n    loading: true,\n    errors: {}\n  });\n  useEffect(() => {\n    const testAPIs = async () => {\n      const apis = [{\n        name: 'importateurs',\n        url: 'http://localhost:1337/api/importateurs?populate=*'\n      }, {\n        name: 'exportateurs',\n        url: 'http://localhost:1337/api/exportateurs?populate=*'\n      }, {\n        name: 'opportunites',\n        url: 'http://localhost:1337/api/opportunites?populate=*'\n      }];\n      const results = {};\n      const errors = {};\n      for (const api of apis) {\n        try {\n          console.log(`Testing ${api.name} API...`);\n          const response = await fetch(api.url);\n          const data = await response.json();\n          console.log(`${api.name} response:`, data);\n          results[api.name] = data;\n        } catch (error) {\n          console.error(`Error fetching ${api.name}:`, error);\n          errors[api.name] = error.message;\n        }\n      }\n      setApiData({\n        ...results,\n        loading: false,\n        errors\n      });\n    };\n    testAPIs();\n  }, []);\n  if (apiData.loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Testing APIs...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      fontFamily: 'monospace'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"API Debug Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), Object.keys(apiData.errors).length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        color: 'red',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Errors:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this), Object.entries(apiData.errors).map(([api, error]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [api, \": \", error]\n      }, api, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Importateurs API Response:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        style: {\n          background: '#f5f5f5',\n          padding: '10px',\n          overflow: 'auto'\n        },\n        children: JSON.stringify(apiData.importateurs, null, 2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Exportateurs API Response:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        style: {\n          background: '#f5f5f5',\n          padding: '10px',\n          overflow: 'auto'\n        },\n        children: JSON.stringify(apiData.exportateurs, null, 2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Opportunites API Response:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        style: {\n          background: '#f5f5f5',\n          padding: '10px',\n          overflow: 'auto'\n        },\n        children: JSON.stringify(apiData.opportunites, null, 2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n}\n_s(DebugTest, \"Vhhg2bdjyP0LTnnp72q3lH28WME=\");\n_c = DebugTest;\nexport default DebugTest;\nvar _c;\n$RefreshReg$(_c, \"DebugTest\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "DebugTest", "_s", "apiData", "setApiData", "importateurs", "exportateurs", "opportunites", "loading", "errors", "testAPIs", "apis", "name", "url", "results", "api", "console", "log", "response", "fetch", "data", "json", "error", "message", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "fontFamily", "Object", "keys", "length", "color", "marginBottom", "entries", "map", "background", "overflow", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/dfghj/site/frontend/src/components/DebugTest.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\n\nfunction DebugTest() {\n  const [apiData, setApiData] = useState({\n    importateurs: null,\n    exportateurs: null,\n    opportunites: null,\n    loading: true,\n    errors: {}\n  });\n\n  useEffect(() => {\n    const testAPIs = async () => {\n      const apis = [\n        { name: 'importateurs', url: 'http://localhost:1337/api/importateurs?populate=*' },\n        { name: 'exportateurs', url: 'http://localhost:1337/api/exportateurs?populate=*' },\n        { name: 'opportunites', url: 'http://localhost:1337/api/opportunites?populate=*' }\n      ];\n\n      const results = {};\n      const errors = {};\n\n      for (const api of apis) {\n        try {\n          console.log(`Testing ${api.name} API...`);\n          const response = await fetch(api.url);\n          const data = await response.json();\n          console.log(`${api.name} response:`, data);\n          results[api.name] = data;\n        } catch (error) {\n          console.error(`Error fetching ${api.name}:`, error);\n          errors[api.name] = error.message;\n        }\n      }\n\n      setApiData({\n        ...results,\n        loading: false,\n        errors\n      });\n    };\n\n    testAPIs();\n  }, []);\n\n  if (apiData.loading) {\n    return <div>Testing APIs...</div>;\n  }\n\n  return (\n    <div style={{ padding: '20px', fontFamily: 'monospace' }}>\n      <h2>API Debug Test</h2>\n      \n      {Object.keys(apiData.errors).length > 0 && (\n        <div style={{ color: 'red', marginBottom: '20px' }}>\n          <h3>Errors:</h3>\n          {Object.entries(apiData.errors).map(([api, error]) => (\n            <div key={api}>{api}: {error}</div>\n          ))}\n        </div>\n      )}\n\n      <div style={{ marginBottom: '20px' }}>\n        <h3>Importateurs API Response:</h3>\n        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>\n          {JSON.stringify(apiData.importateurs, null, 2)}\n        </pre>\n      </div>\n\n      <div style={{ marginBottom: '20px' }}>\n        <h3>Exportateurs API Response:</h3>\n        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>\n          {JSON.stringify(apiData.exportateurs, null, 2)}\n        </pre>\n      </div>\n\n      <div style={{ marginBottom: '20px' }}>\n        <h3>Opportunites API Response:</h3>\n        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>\n          {JSON.stringify(apiData.opportunites, null, 2)}\n        </pre>\n      </div>\n    </div>\n  );\n}\n\nexport default DebugTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGP,QAAQ,CAAC;IACrCQ,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,CAAC;EACX,CAAC,CAAC;EAEFX,SAAS,CAAC,MAAM;IACd,MAAMY,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,MAAMC,IAAI,GAAG,CACX;QAAEC,IAAI,EAAE,cAAc;QAAEC,GAAG,EAAE;MAAoD,CAAC,EAClF;QAAED,IAAI,EAAE,cAAc;QAAEC,GAAG,EAAE;MAAoD,CAAC,EAClF;QAAED,IAAI,EAAE,cAAc;QAAEC,GAAG,EAAE;MAAoD,CAAC,CACnF;MAED,MAAMC,OAAO,GAAG,CAAC,CAAC;MAClB,MAAML,MAAM,GAAG,CAAC,CAAC;MAEjB,KAAK,MAAMM,GAAG,IAAIJ,IAAI,EAAE;QACtB,IAAI;UACFK,OAAO,CAACC,GAAG,CAAC,WAAWF,GAAG,CAACH,IAAI,SAAS,CAAC;UACzC,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAACJ,GAAG,CAACF,GAAG,CAAC;UACrC,MAAMO,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;UAClCL,OAAO,CAACC,GAAG,CAAC,GAAGF,GAAG,CAACH,IAAI,YAAY,EAAEQ,IAAI,CAAC;UAC1CN,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC,GAAGQ,IAAI;QAC1B,CAAC,CAAC,OAAOE,KAAK,EAAE;UACdN,OAAO,CAACM,KAAK,CAAC,kBAAkBP,GAAG,CAACH,IAAI,GAAG,EAAEU,KAAK,CAAC;UACnDb,MAAM,CAACM,GAAG,CAACH,IAAI,CAAC,GAAGU,KAAK,CAACC,OAAO;QAClC;MACF;MAEAnB,UAAU,CAAC;QACT,GAAGU,OAAO;QACVN,OAAO,EAAE,KAAK;QACdC;MACF,CAAC,CAAC;IACJ,CAAC;IAEDC,QAAQ,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIP,OAAO,CAACK,OAAO,EAAE;IACnB,oBAAOR,OAAA;MAAAwB,QAAA,EAAK;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACnC;EAEA,oBACE5B,OAAA;IAAK6B,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAY,CAAE;IAAAP,QAAA,gBACvDxB,OAAA;MAAAwB,QAAA,EAAI;IAAc;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEtBI,MAAM,CAACC,IAAI,CAAC9B,OAAO,CAACM,MAAM,CAAC,CAACyB,MAAM,GAAG,CAAC,iBACrClC,OAAA;MAAK6B,KAAK,EAAE;QAAEM,KAAK,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAO,CAAE;MAAAZ,QAAA,gBACjDxB,OAAA;QAAAwB,QAAA,EAAI;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACfI,MAAM,CAACK,OAAO,CAAClC,OAAO,CAACM,MAAM,CAAC,CAAC6B,GAAG,CAAC,CAAC,CAACvB,GAAG,EAAEO,KAAK,CAAC,kBAC/CtB,OAAA;QAAAwB,QAAA,GAAgBT,GAAG,EAAC,IAAE,EAACO,KAAK;MAAA,GAAlBP,GAAG;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAqB,CACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,eAED5B,OAAA;MAAK6B,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAO,CAAE;MAAAZ,QAAA,gBACnCxB,OAAA;QAAAwB,QAAA,EAAI;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC5B,OAAA;QAAK6B,KAAK,EAAE;UAAEU,UAAU,EAAE,SAAS;UAAET,OAAO,EAAE,MAAM;UAAEU,QAAQ,EAAE;QAAO,CAAE;QAAAhB,QAAA,EACtEiB,IAAI,CAACC,SAAS,CAACvC,OAAO,CAACE,YAAY,EAAE,IAAI,EAAE,CAAC;MAAC;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5B,OAAA;MAAK6B,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAO,CAAE;MAAAZ,QAAA,gBACnCxB,OAAA;QAAAwB,QAAA,EAAI;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC5B,OAAA;QAAK6B,KAAK,EAAE;UAAEU,UAAU,EAAE,SAAS;UAAET,OAAO,EAAE,MAAM;UAAEU,QAAQ,EAAE;QAAO,CAAE;QAAAhB,QAAA,EACtEiB,IAAI,CAACC,SAAS,CAACvC,OAAO,CAACG,YAAY,EAAE,IAAI,EAAE,CAAC;MAAC;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5B,OAAA;MAAK6B,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAO,CAAE;MAAAZ,QAAA,gBACnCxB,OAAA;QAAAwB,QAAA,EAAI;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnC5B,OAAA;QAAK6B,KAAK,EAAE;UAAEU,UAAU,EAAE,SAAS;UAAET,OAAO,EAAE,MAAM;UAAEU,QAAQ,EAAE;QAAO,CAAE;QAAAhB,QAAA,EACtEiB,IAAI,CAACC,SAAS,CAACvC,OAAO,CAACI,YAAY,EAAE,IAAI,EAAE,CAAC;MAAC;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1B,EAAA,CAlFQD,SAAS;AAAA0C,EAAA,GAAT1C,SAAS;AAoFlB,eAAeA,SAAS;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}