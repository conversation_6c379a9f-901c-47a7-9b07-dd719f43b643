const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["ConfigureTheView-DyZtRlOR.js","strapi-z7ApxZZq.js","strapi-D6821bIJ.css"])))=>i.map(i=>d[i]);
import{f9 as Te,j as e,$ as T,bp as je,fa as E,c7 as c,a as M,fb as $e,bt as ue,D as S,bm as Pe,bu as Qe,w as Ve,b_ as Ue,bW as ze,c1 as Oe,ea as H,fc as Ge,cc as y,e as $,L as _e,be as He,bf as qe,H as W,M as ge,fd as Ne,T as J,fe as We,eD as Ke,r as C,ff as Je,fg as Xe,p as Ye,av as K,b as be,u as P,eE as fe,fh as Ze,fi as es,fj as pe,fk as ss,aA as ye,B as I,bH as ts,aC as Ce,au as as,dR as ns,fl as os,V as is,fm as rs,fn as ls,fo as ds,fp as cs,fq as us,fr as gs,fs,P as L,I as q,a3 as ps,cg as hs,ft as ms,fu as xs,fv as B,fw as js,fx as bs,fy as ys,fz as Cs,fA as Ms,fB as Fs,bq as vs,g as Ss,b7 as ks,fC as Ls,cu as N,fD as ws,fE as Es,fF as As,k as X,fG as Rs,ec as Ds,R as Bs,Q as he,_ as Is}from"./strapi-z7ApxZZq.js";import{S as Ts}from"./SearchInput-Cmj1ynyp.js";const $s=s=>{const{id:n}=Te();return e.jsx(T,{position:"relative",zIndex:2,children:e.jsx(je,{"aria-labelledby":`${n}-title`,...s})})},Ps=(s,{pathname:n,query:a})=>{const o=[{id:null,label:{id:c("plugin.name"),defaultMessage:"Media Library"},href:s?E(n,a||{}):void 0}];return s?.parent&&typeof s?.parent!="number"&&s?.parent?.parent&&o.push([]),s?.parent&&typeof s.parent!="number"&&o.push({id:s.parent.id,label:s.parent.name,href:E(n,a||{},{folder:s.parent.id?.toString(),folderPath:s.parent.path})}),s&&o.push({id:s.id,label:s.name}),o},Qs=({selected:s,onSuccess:n})=>{const{formatMessage:a}=M(),{remove:o}=$e(),d=async()=>{await o(s),n()};return e.jsxs(ue.Root,{children:[e.jsx(ue.Trigger,{children:e.jsx(S,{variant:"danger-light",size:"S",startIcon:e.jsx(Pe,{}),children:a({id:"global.delete",defaultMessage:"Delete"})})}),e.jsx(Qe,{onConfirm:d})]})},Vs=()=>{const{formatMessage:s}=M(),{toggleNotification:n}=Ve(),a=Ue(),{post:o}=ze(),i=Oe(({destinationFolderId:p,filesAndFolders:f})=>{const l=f.reduce((g,j)=>{const{id:r,type:v}=j,h=v==="asset"?"fileIds":"folderIds";return g[h]||(g[h]=[]),g[h].push(r),g},{});return o("/upload/actions/bulk-move",{...l,destinationFolderId:p})},{onSuccess(p){const{data:{data:f}}=p;f?.files?.length>0&&(a.refetchQueries([H,"assets"],{active:!0}),a.refetchQueries([H,"asset-count"],{active:!0})),a.refetchQueries([H,"folders"],{active:!0}),n({type:"success",message:s({id:c("modal.move.success-label"),defaultMessage:"Elements have been moved successfully."})})}});return{...i,move:(p,f)=>i.mutateAsync({destinationFolderId:p,filesAndFolders:f})}},Us=({onClose:s,selected:n=[],currentFolder:a})=>{const{formatMessage:o}=M(),{data:d,isLoading:i}=Ge(),{move:u}=Vs();if(!d)return null;const p=async(l,{setErrors:g})=>{try{if(typeof l.destination!="string"){const j=l.destination.value;await u(j,n),s()}}catch(j){const r=We(j);if(r&&"errors"in r){const v=r.errors?.reduce((h,A)=>(h[A.values?.path?.length||"destination"]=A.defaultMessage,h),{});Ke(v)||g(v)}}};if(i)return e.jsx(y.Content,{children:e.jsx(y.Body,{children:e.jsx($,{justifyContent:"center",paddingTop:4,paddingBottom:4,children:e.jsx(_e,{children:o({id:c("content.isLoading"),defaultMessage:"Content is loading."})})})})});const f={destination:{value:a?.id||"",label:a?.name||d[0].label}};return e.jsx(y.Content,{children:e.jsx(He,{validateOnChange:!1,onSubmit:p,initialValues:f,children:({values:l,errors:g,setFieldValue:j})=>e.jsxs(qe,{noValidate:!0,children:[e.jsx(y.Header,{children:e.jsx(y.Title,{children:o({id:c("modal.folder.move.title"),defaultMessage:"Move elements to"})})}),e.jsx(y.Body,{children:e.jsx(W.Root,{gap:4,children:e.jsx(W.Item,{xs:12,col:12,direction:"column",alignItems:"stretch",children:e.jsxs(ge.Root,{id:"folder-destination",children:[e.jsx(ge.Label,{children:o({id:c("form.input.label.folder-location"),defaultMessage:"Location"})}),e.jsx(Ne,{options:d,onChange:r=>{j("destination",r)},defaultValue:typeof l.destination!="string"?l.destination:void 0,name:"destination",menuPortalTarget:document.querySelector("body"),inputId:"folder-destination",error:g?.destination,ariaErrorMessage:"destination-error"}),g.destination&&e.jsx(J,{variant:"pi",tag:"p",textColor:"danger600",children:g.destination})]})})})}),e.jsxs(y.Footer,{children:[e.jsx(y.Close,{children:e.jsx(S,{variant:"tertiary",name:"cancel",children:o({id:"cancel",defaultMessage:"Cancel"})})}),e.jsx(S,{type:"submit",loading:i,children:o({id:"modal.folder.move.submit",defaultMessage:"Move"})})]})]})})})},zs=({selected:s=[],onSuccess:n,currentFolder:a})=>{const{formatMessage:o}=M(),[d,i]=C.useState(!1),u=()=>{i(!1),n()};return e.jsxs(y.Root,{open:d,onOpenChange:i,children:[e.jsx(y.Trigger,{children:e.jsx(S,{variant:"secondary",size:"S",startIcon:e.jsx(Je,{}),children:o({id:"global.move",defaultMessage:"Move"})})}),e.jsx(Us,{currentFolder:a,onClose:u,selected:s})]})},Os=({selected:s=[],onSuccess:n,currentFolder:a})=>{const{formatMessage:o}=M(),d=s?.reduce(function(i,u){return u?.type==="folder"&&"files"in u&&u?.files&&"count"in u.files?i+u?.files?.count:i+1},0);return e.jsxs($,{gap:2,paddingBottom:5,children:[e.jsx(J,{variant:"epsilon",textColor:"neutral600",children:o({id:c("list.assets.selected"),defaultMessage:"{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 asset} other {# assets}} selected"},{numberFolders:s?.filter(({type:i})=>i==="folder").length,numberAssets:d})}),e.jsx(Qs,{selected:s,onSuccess:n}),e.jsx(zs,{currentFolder:a,selected:s,onSuccess:n})]})},Gs=({isFiltering:s,canCreate:n,canRead:a})=>s?{id:"list.assets-empty.title-withSearch",defaultMessage:"There are no elements with the applied filters"}:a?n?{id:"list.assets.empty-upload",defaultMessage:"Upload your first assets..."}:{id:"list.assets.empty",defaultMessage:"Media Library is empty"}:{id:"header.actions.no-permissions",defaultMessage:"No permissions to view"},_s=({canCreate:s,isFiltering:n,canRead:a,onActionClick:o})=>{const{formatMessage:d}=M(),i=Gs({isFiltering:n,canCreate:s,canRead:a});return e.jsx(Xe,{icon:a?void 0:Ye,action:s&&!n&&e.jsx(S,{variant:"secondary",startIcon:e.jsx(K,{}),onClick:o,children:d({id:c("header.actions.add-assets"),defaultMessage:"Add new assets"})}),content:d({...i,id:c(i.id)})})},Hs=()=>{const[s,n]=C.useState(!1),{formatMessage:a}=M(),{trackUsage:o}=be(),[{query:d},i]=P(),u=d?.filters?.$and||[],p=l=>{i({filters:{$and:l},page:1})},f=l=>{o("didFilterMediaLibraryElements",{location:"content-manager",filter:Object.keys(l[l.length-1])[0]}),i({filters:{$and:l},page:1})};return e.jsxs(fe.Root,{open:s,onOpenChange:n,children:[e.jsx(fe.Trigger,{children:e.jsx(S,{variant:"tertiary",startIcon:e.jsx(Ze,{}),size:"S",children:a({id:"app.utils.filters",defaultMessage:"Filters"})})}),e.jsx(es,{displayedFilters:pe,filters:u,onSubmit:f,onToggle:n}),e.jsx(ss,{appliedFilters:u,filtersSchema:pe,onRemoveFilter:p})]})},qs=({breadcrumbs:s=null,canCreate:n,folder:a=null,onToggleEditFolderDialog:o,onToggleUploadAssetDialog:d})=>{const{formatMessage:i}=M(),{pathname:u}=ye(),[{query:p}]=P(),f={...p,folder:a?.parent&&typeof a.parent!="number"&&a.parent.id?a.parent.id:void 0,folderPath:a?.parent&&typeof a.parent!="number"&&a.parent.path?a.parent.path:void 0};return e.jsx(I.Header,{title:i({id:c("plugin.name"),defaultMessage:"Media Library"}),subtitle:s&&typeof s!="boolean"&&a&&e.jsx(os,{label:i({id:c("header.breadcrumbs.nav.label"),defaultMessage:"Folders navigation"}),breadcrumbs:s,currentFolderId:a?.id}),navigationAction:a&&e.jsx(ts,{tag:as,startIcon:e.jsx(ns,{}),to:`${u}?${Ce.stringify(f,{encode:!1})}`,children:i({id:c("header.actions.folder-level-up"),defaultMessage:"Back"})}),primaryAction:n&&e.jsxs($,{gap:2,children:[e.jsx(S,{startIcon:e.jsx(K,{}),variant:"secondary",onClick:o,children:i({id:c("header.actions.add-folder"),defaultMessage:"Add new folder"})}),e.jsx(S,{startIcon:e.jsx(K,{}),onClick:d,children:i({id:c("header.actions.add-assets"),defaultMessage:"Add new assets"})})]})})},Ns=X(T)`
  height: 3.2rem;
  display: flex;
  align-items: center;
`,me=X(J)`
  max-width: 100%;
`,xe=X(T)`
  svg {
    path {
      fill: ${({theme:s})=>s.colors.neutral500};
    }
  }
`,Ws=()=>{const s=is(),{canRead:n,canCreate:a,canUpdate:o,canCopyLink:d,canDownload:i,canConfigureView:u,isLoading:p}=rs(),f=C.useRef(),{formatMessage:l}=M(),{pathname:g}=ye(),{trackUsage:j}=be(),[{query:r},v]=P(),h=!!(r._q||r.filters),[A,Me]=ls(Rs.view,B.GRID),k=A===B.GRID,{data:b,isLoading:Fe,error:ve}=ds({skipWhen:!n,query:r}),{data:Se,isLoading:ke,error:Le}=cs({enabled:n&&b?.pagination?.page===1&&!us(r),query:r}),{data:Q,isLoading:Y,error:we}=gs(r?.folder,{enabled:n&&!!r?.folder});we?.name==="NotFoundError"&&s(g);const R=Se?.map(t=>({...t,type:"folder",folderURL:E(g,r,{folder:t.id.toString(),folderPath:t.path}),isSelectable:o}))??[],m=R?.length||0,D=b?.results?.map(t=>({...t,type:"asset",isSelectable:o}))||[],x=D?.length??0,Ee=b?.pagination?.total,Ae=Y||ke||p||Fe,[Z,Re]=C.useState(!1),[ee,se]=C.useState(!1),[V,U]=C.useState(void 0),[z,te]=C.useState(void 0),[F,{selectOne:O,selectAll:ae}]=fs(["type","id"],[]),ne=F?.length>0&&F?.length!==x+m,G=()=>Re(t=>!t),oe=({created:t=!1}={})=>{t&&r?.page!=="1"&&v({...r,page:1}),se(w=>!w)},ie=(t,w)=>{t&&j("didSelectAllMediaLibraryElements"),ae(w)},re=t=>{j("didSortMediaLibraryElements",{location:"upload",sort:t}),v({sort:t})},le=t=>{te(t),se(!0)},De=t=>{te(null),oe(t),f.current&&f.current.focus()},de=t=>{t===x&&b?.pagination?.page===b?.pagination?.pageCount&&b?.pagination?.page&&b.pagination.page>1&&v({...r,page:b.pagination.page-1})},Be=()=>{ae(),de(F.length)};return Ae?e.jsx(L.Loading,{}):ve||Le?e.jsx(L.Error,{}):e.jsxs(I.Root,{children:[e.jsxs(L.Main,{children:[e.jsx(qs,{breadcrumbs:Y?null:Ps(Q,{pathname:g,query:r}),canCreate:a,onToggleEditFolderDialog:oe,onToggleUploadAssetDialog:G,folder:Q}),e.jsx(I.Action,{startActions:e.jsxs(e.Fragment,{children:[o&&k&&(x>0||m>0)&&e.jsx(Ns,{paddingLeft:2,paddingRight:2,background:"neutral0",hasRadius:!0,borderColor:"neutral200",children:e.jsx(je,{"aria-label":l({id:c("bulk.select.label"),defaultMessage:"Select all folders & assets"}),checked:ne?"indeterminate":(x>0||m>0)&&F.length===x+m,onCheckedChange:t=>ie(t,[...D,...R])})}),n&&k&&e.jsx(js,{value:r?.sort,onChangeSort:re}),n&&e.jsx(Hs,{})]}),endActions:e.jsxs(e.Fragment,{children:[u?e.jsx(xe,{paddingTop:1,paddingBottom:1,children:e.jsx(q,{tag:ps,to:{pathname:`${g}/configuration`,search:Ce.stringify(r,{encode:!1})},label:l({id:"app.links.configure-view",defaultMessage:"Configure the view"}),children:e.jsx(hs,{})})}):null,e.jsx(xe,{paddingTop:1,paddingBottom:1,children:e.jsx(q,{label:l(k?{id:c("view-switch.list"),defaultMessage:"List View"}:{id:c("view-switch.grid"),defaultMessage:"Grid View"}),onClick:()=>Me(k?B.LIST:B.GRID),children:k?e.jsx(ms,{}):e.jsx(xs,{})})}),e.jsx(Ts,{label:l({id:c("search.label"),defaultMessage:"Search for an asset"}),trackedEvent:"didSearchMediaLibraryElements",trackedEventDetails:{location:"upload"}})]})}),e.jsxs(I.Content,{children:[F.length>0&&e.jsx(Os,{currentFolder:Q,selected:F,onSuccess:Be}),m===0&&x===0&&e.jsx(_s,{canCreate:a,canRead:n,isFiltering:h,onActionClick:G}),n&&!k&&(x>0||m>0)&&e.jsx(bs,{assetCount:x,folderCount:m,indeterminate:ne,onChangeSort:re,onChangeFolder:(t,w)=>s(E(g,r,{folder:t.toString(),folderPath:w})),onEditAsset:U,onEditFolder:le,onSelectOne:O,onSelectAll:ie,rows:[...R,...D],selected:F,shouldDisableBulkSelect:!o,sortQuery:r?.sort??""}),n&&k&&e.jsxs(e.Fragment,{children:[m>0&&e.jsx(ys,{title:(h&&x>0||!h)&&l({id:c("list.folders.title"),defaultMessage:"Folders ({count})"},{count:m})||"",children:R.map(t=>{const Ie=!!F.filter(({type:_})=>_==="folder").find(_=>_.id===t.id),ce=E(g,r,{folder:t?.id.toString(),folderPath:t?.path});return e.jsx(W.Item,{col:3,direction:"column",alignItems:"stretch",children:e.jsx(Cs,{ref:z&&t.id===z.id?f:void 0,ariaLabel:t.name,id:`folder-${t.id}`,to:ce,startAction:t.isSelectable?e.jsx($s,{"data-testid":`folder-checkbox-${t.id}`,checked:Ie,onCheckedChange:()=>O(t)}):null,cardActions:e.jsx(q,{label:l({id:c("list.folder.edit"),defaultMessage:"Edit folder"}),onClick:()=>le(t),children:e.jsx(Ss,{})}),children:e.jsx(Ms,{children:e.jsx(Fs,{to:ce,children:e.jsxs($,{tag:"h2",direction:"column",alignItems:"start",maxWidth:"100%",children:[e.jsxs(me,{fontWeight:"semiBold",textColor:"neutral800",ellipsis:!0,children:[t.name,e.jsx(vs,{children:":"})]}),e.jsx(me,{tag:"span",textColor:"neutral600",variant:"pi",ellipsis:!0,children:l({id:c("list.folder.subtitle"),defaultMessage:"{folderCount, plural, =0 {# folder} one {# folder} other {# folders}}, {filesCount, plural, =0 {# asset} one {# asset} other {# assets}}"},{folderCount:t.children?.count,filesCount:t.files?.count})})]})})})})},`folder-${t.id}`)})}),x>0&&m>0&&e.jsx(T,{paddingTop:6,paddingBottom:4,children:e.jsx(ks,{})}),x>0&&e.jsx(Ls,{assets:D,onEditAsset:U,onSelectAsset:O,selectedAssets:F.filter(({type:t})=>t==="asset"),title:(!h||h&&m>0)&&b?.pagination?.page===1&&l({id:c("list.assets.title"),defaultMessage:"Assets ({count})"},{count:Ee})||""})]}),e.jsxs(N.Root,{...b?.pagination,children:[e.jsx(N.PageSize,{}),e.jsx(N.Links,{})]})]})]}),Z&&e.jsx(ws,{open:Z,onClose:G,trackedLocation:"upload",folderId:r?.folder}),ee&&e.jsx(Es,{open:ee,onClose:()=>De(),folder:z,parentFolderId:r?.folder,location:"upload"}),V&&e.jsx(As,{onClose:t=>{t===null&&de(1),U(void 0)},open:!!V,asset:V,canUpdate:o,canCopyLink:d,canDownload:i,trackedLocation:"upload"})]})},Ks=C.lazy(async()=>Is(()=>import("./ConfigureTheView-DyZtRlOR.js"),__vite__mapDeps([0,1,2])).then(s=>({default:s.ConfigureTheView}))),Ys=()=>{const{config:{isLoading:s,isError:n,data:a}}=Ds(),[{rawQuery:o},d]=P(),{formatMessage:i}=M(),u=i({id:c("plugin.name"),defaultMessage:"Media Library"});return C.useEffect(()=>{s||n||o||d({sort:a.sort,page:1,pageSize:a.pageSize})},[s,n,a,o,d]),s?e.jsx(L.Loading,{}):e.jsxs(L.Main,{children:[e.jsx(L.Title,{children:u}),o?e.jsx(C.Suspense,{fallback:e.jsx(L.Loading,{}),children:e.jsxs(Bs,{children:[e.jsx(he,{index:!0,element:e.jsx(Ws,{})}),e.jsx(he,{path:"configuration",element:e.jsx(Ks,{config:a})})]})}):null]})};export{Ys as Upload};
