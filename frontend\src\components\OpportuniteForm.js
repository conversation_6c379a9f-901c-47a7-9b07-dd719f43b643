import React, { useState, useEffect } from "react";

function OpportuniteForm() {
  // États pour les données
  const [importateurs, setImportateurs] = useState([]);
  const [exportateurs, setExportateurs] = useState([]);
  const [matchedExportateurs, setMatchedExportateurs] = useState([]);
  const [selectedExportateurs, setSelectedExportateurs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showNewImportateurForm, setShowNewImportateurForm] = useState(false);
  const [showMatching, setShowMatching] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // État du formulaire principal
  const [formData, setFormData] = useState({
    importateur: "",
    objet: "",
    pays: "",
    pays_autre: "",
    date_debut: "",
    date_fin: "",
    secteurs: []
  });

  // État du formulaire nouvel importateur
  const [newImportateur, setNewImportateur] = useState({
    societe: "",
    pays: "",
    nom_responsable: "",
    prenom_responsable: "",
    telephone_whatsapp: "",
    email: "",
    region: ""
  });

  // Listes des options
  const secteurs = [
    "Agro-alimentaire",
    "Textile",
    "IME",
    "Service",
    "Artisanat",
    "Divers"
  ];

  const pays = [
    "France", "Allemagne", "Italie", "Espagne", "Belgique", "Pays-Bas",
    "Royaume-Uni", "Suisse", "Canada", "États-Unis", "Maroc", "Algérie",
    "Libye", "Égypte", "Arabie Saoudite", "Émirats Arabes Unis", "Qatar",
    "Koweït", "Turquie", "Chine", "Japon", "Corée du Sud", "Inde", "Autre"
  ];

  // Charger les importateurs au démarrage
  useEffect(() => {
    Promise.all([
      fetch("http://localhost:1337/api/importateurs?populate=*").then(r => r.json()),
      fetch("http://localhost:1337/api/exportateurs?populate=*").then(r => r.json())
    ])
    .then(([importateursData, exportateursData]) => {
      console.log("Importateurs dans OpportuniteForm:", importateursData);
      console.log("Premier importateur structure:", importateursData.data && importateursData.data[0]);
      console.log("Exportateurs dans OpportuniteForm:", exportateursData);

      if (importateursData?.data) setImportateurs(importateursData.data);
      if (exportateursData?.data) setExportateurs(exportateursData.data);

      setLoading(false);
    })
    .catch(err => {
      console.error("Erreur lors du chargement:", err);
      setLoading(false);
    });
  }, []);

  // Fonctions utilitaires
  const isFormValid = () => {
    const paysValid = formData.pays && (formData.pays !== "Autre" || formData.pays_autre);
    return formData.importateur &&
           formData.objet &&
           paysValid &&
           formData.date_debut &&
           formData.date_fin &&
           formData.secteurs.length > 0;
  };

  const handleMatching = async () => {
    if (!isFormValid()) return;

    // Si "autre" est sélectionné, créer d'abord le nouvel importateur
    if (formData.importateur === "autre") {
      try {
        const response = await fetch("http://localhost:1337/api/importateurs", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ data: newImportateur })
        });
        const newImp = await response.json();
        setFormData({...formData, importateur: newImp.data.id});
        setImportateurs([...importateurs, newImp.data]);
        setShowNewImportateurForm(false);
      } catch (error) {
        console.error("Erreur lors de la création de l'importateur:", error);
        return;
      }
    }

    // Filtrer les exportateurs par secteur
    console.log("Secteurs sélectionnés:", formData.secteurs);
    console.log("Tous les exportateurs:", exportateurs);

    const matched = exportateurs.filter(exp => {
      console.log("Exportateur en cours:", exp);

      // Essayer différentes structures de données
      const expSecteur = exp?.attributes?.secteur_activite ||
                        exp?.secteur_activite ||
                        exp?.data?.attributes?.secteur_activite;

      console.log("Secteur de l'exportateur:", expSecteur);
      console.log("Secteur inclus?", formData.secteurs.includes(expSecteur));

      return formData.secteurs.includes(expSecteur);
    });

    console.log("Exportateurs correspondants:", matched);

    setMatchedExportateurs(matched);
    setShowMatching(true);
  };

  const handleSendOpportunity = () => {
    setShowConfirmation(true);
  };

  const getImportateurName = () => {
    if (formData.importateur === "autre") {
      return newImportateur.societe;
    }
    const imp = importateurs.find(i => (i.id || i.documentId) === formData.importateur);
    // Utiliser la même logique flexible
    return imp?.attributes?.societe ||
           imp?.societe ||
           imp?.data?.attributes?.societe ||
           "Importateur inconnu";
  };

  if (loading) return <div>Chargement...</div>;

  return (
    <div style={{ padding: "20px", maxWidth: "800px" }}>
      <h2>🔍 Formulaire Opportunité d'Importation</h2>

      {!showConfirmation ? (
        <div>
          {/* Formulaire principal */}
          <form style={{ marginBottom: "30px" }}>
            {/* Sélection Importateur */}
            <div style={{ marginBottom: "20px" }}>
              <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
                Importateur *
              </label>
              <select
                value={formData.importateur}
                onChange={(e) => {
                  setFormData({...formData, importateur: e.target.value});
                  setShowNewImportateurForm(e.target.value === "autre");
                }}
                style={{ width: "100%", padding: "8px", marginBottom: "10px" }}
                required
              >
                <option value="">-- Sélectionner un importateur --</option>
                {importateurs.map(imp => {
                  // Utiliser la même logique flexible que dans les autres composants
                  const societe = imp?.attributes?.societe ||
                                 imp?.societe ||
                                 imp?.data?.attributes?.societe ||
                                 'Nom non disponible';
                  return (
                    <option key={imp.id || imp.documentId} value={imp.id || imp.documentId}>
                      {societe}
                    </option>
                  );
                })}
                <option value="autre">➕ Autre (Ajouter un nouveau)</option>
              </select>
            </div>

            {/* Formulaire nouvel importateur (conditionnel) */}
            {showNewImportateurForm && (
              <div style={{
                border: "2px solid #007bff",
                padding: "15px",
                marginBottom: "20px",
                borderRadius: "5px",
                backgroundColor: "#f8f9fa"
              }}>
                <h4>➕ Ajouter un nouvel importateur</h4>
                <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "10px" }}>
                  <input
                    placeholder="Société *"
                    value={newImportateur.societe}
                    onChange={(e) => setNewImportateur({...newImportateur, societe: e.target.value})}
                    style={{ padding: "8px" }}
                    required
                  />
                  <input
                    placeholder="Pays *"
                    value={newImportateur.pays}
                    onChange={(e) => setNewImportateur({...newImportateur, pays: e.target.value})}
                    style={{ padding: "8px" }}
                    required
                  />
                  <input
                    placeholder="Nom responsable *"
                    value={newImportateur.nom_responsable}
                    onChange={(e) => setNewImportateur({...newImportateur, nom_responsable: e.target.value})}
                    style={{ padding: "8px" }}
                    required
                  />
                  <input
                    placeholder="Prénom responsable *"
                    value={newImportateur.prenom_responsable}
                    onChange={(e) => setNewImportateur({...newImportateur, prenom_responsable: e.target.value})}
                    style={{ padding: "8px" }}
                    required
                  />
                  <input
                    placeholder="Téléphone WhatsApp *"
                    value={newImportateur.telephone_whatsapp}
                    onChange={(e) => setNewImportateur({...newImportateur, telephone_whatsapp: e.target.value})}
                    style={{ padding: "8px" }}
                    required
                  />
                  <input
                    placeholder="Email *"
                    type="email"
                    value={newImportateur.email}
                    onChange={(e) => setNewImportateur({...newImportateur, email: e.target.value})}
                    style={{ padding: "8px" }}
                    required
                  />
                  <input
                    placeholder="Région *"
                    value={newImportateur.region}
                    onChange={(e) => setNewImportateur({...newImportateur, region: e.target.value})}
                    style={{ padding: "8px", gridColumn: "1 / -1" }}
                    required
                  />
                </div>
              </div>
            )}

            {/* Objet */}
            <div style={{ marginBottom: "20px" }}>
              <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
                Objet (Produit recherché, spécifications...) *
              </label>
              <textarea
                value={formData.objet}
                onChange={(e) => setFormData({...formData, objet: e.target.value})}
                style={{ width: "100%", padding: "8px", minHeight: "100px" }}
                placeholder="Décrivez le produit recherché, les spécifications techniques, quantités, etc."
                required
              />
            </div>

            {/* Pays de destination */}
            <div style={{ marginBottom: "20px" }}>
              <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
                Pays de destination *
              </label>
              <select
                value={formData.pays}
                onChange={(e) => setFormData({...formData, pays: e.target.value})}
                style={{ width: "100%", padding: "8px", marginBottom: "10px" }}
                required
              >
                <option value="">-- Sélectionner un pays --</option>
                {pays.map(p => (
                  <option key={p} value={p}>{p}</option>
                ))}
              </select>

              {/* Champ texte pour "Autre" pays */}
              {formData.pays === "Autre" && (
                <input
                  type="text"
                  placeholder="Précisez le pays *"
                  value={formData.pays_autre}
                  onChange={(e) => setFormData({...formData, pays_autre: e.target.value})}
                  style={{ width: "100%", padding: "8px", border: "2px solid #007bff", borderRadius: "4px" }}
                  required
                />
              )}
            </div>

            {/* Dates */}
            <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "20px", marginBottom: "20px" }}>
              <div>
                <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
                  Date début *
                </label>
                <input
                  type="date"
                  value={formData.date_debut}
                  onChange={(e) => setFormData({...formData, date_debut: e.target.value})}
                  style={{ width: "100%", padding: "8px" }}
                  required
                />
              </div>
              <div>
                <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
                  Date fin *
                </label>
                <input
                  type="date"
                  value={formData.date_fin}
                  onChange={(e) => setFormData({...formData, date_fin: e.target.value})}
                  style={{ width: "100%", padding: "8px" }}
                  required
                />
              </div>
            </div>

            {/* Secteurs (sélection multiple) */}
            <div style={{ marginBottom: "20px" }}>
              <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
                Secteurs d'activité * (sélection multiple)
              </label>
              <div style={{
                border: "1px solid #ccc",
                padding: "10px",
                borderRadius: "4px",
                display: "grid",
                gridTemplateColumns: "repeat(2, 1fr)",
                gap: "10px"
              }}>
                {secteurs.map(secteur => (
                  <label key={secteur} style={{ display: "flex", alignItems: "center" }}>
                    <input
                      type="checkbox"
                      checked={formData.secteurs.includes(secteur)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData({
                            ...formData,
                            secteurs: [...formData.secteurs, secteur]
                          });
                        } else {
                          setFormData({
                            ...formData,
                            secteurs: formData.secteurs.filter(s => s !== secteur)
                          });
                        }
                      }}
                      style={{ marginRight: "8px" }}
                    />
                    {secteur}
                  </label>
                ))}
              </div>
              {formData.secteurs.length === 0 && (
                <small style={{ color: "red" }}>Veuillez sélectionner au moins un secteur</small>
              )}
            </div>

            {/* Bouton MATCHING */}
            <button
              type="button"
              onClick={() => handleMatching()}
              disabled={!isFormValid()}
              style={{
                backgroundColor: isFormValid() ? "#28a745" : "#6c757d",
                color: "white",
                padding: "12px 30px",
                border: "none",
                borderRadius: "5px",
                fontSize: "16px",
                fontWeight: "bold",
                cursor: isFormValid() ? "pointer" : "not-allowed",
                marginRight: "10px"
              }}
            >
              🔍 MATCHING
            </button>
          </form>

          {/* Section Matching des Exportateurs */}
          {showMatching && (
            <div style={{
              border: "2px solid #28a745",
              padding: "20px",
              borderRadius: "5px",
              backgroundColor: "#f8fff9",
              marginBottom: "20px"
            }}>
              <h3>🎯 Exportateurs correspondants ({matchedExportateurs.length})</h3>

              {matchedExportateurs.length > 0 ? (
                <div>
                  <table style={{ width: "100%", borderCollapse: "collapse", marginBottom: "20px" }}>
                    <thead>
                      <tr style={{ backgroundColor: "#e9ecef" }}>
                        <th style={{ padding: "10px", border: "1px solid #ddd", textAlign: "left" }}>
                          <input
                            type="checkbox"
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedExportateurs(matchedExportateurs.map(exp => exp.id));
                              } else {
                                setSelectedExportateurs([]);
                              }
                            }}
                            style={{ marginRight: "5px" }}
                          />
                          Sélectionner
                        </th>
                        <th style={{ padding: "10px", border: "1px solid #ddd", textAlign: "left" }}>Entreprise</th>
                        <th style={{ padding: "10px", border: "1px solid #ddd", textAlign: "left" }}>Contact</th>
                        <th style={{ padding: "10px", border: "1px solid #ddd", textAlign: "left" }}>Secteur</th>
                        <th style={{ padding: "10px", border: "1px solid #ddd", textAlign: "left" }}>Localisation</th>
                      </tr>
                    </thead>
                    <tbody>
                      {matchedExportateurs.map(exp => {
                        console.log("Affichage exportateur:", exp);

                        // Utiliser la logique flexible pour accéder aux données
                        const raisonSociale = exp?.attributes?.raison_sociale ||
                                             exp?.raison_sociale ||
                                             exp?.data?.attributes?.raison_sociale ||
                                             'Nom non disponible';

                        const email = exp?.attributes?.email ||
                                     exp?.email ||
                                     exp?.data?.attributes?.email ||
                                     'Email non disponible';

                        const telephone = exp?.attributes?.telephone_siege ||
                                         exp?.telephone_siege ||
                                         exp?.attributes?.mobile ||
                                         exp?.mobile ||
                                         exp?.data?.attributes?.telephone_siege ||
                                         'Tél. non disponible';

                        const nomContact = exp?.attributes?.nom_contact ||
                                          exp?.nom_contact ||
                                          exp?.data?.attributes?.nom_contact ||
                                          '';

                        const prenomContact = exp?.attributes?.prenom_contact ||
                                             exp?.prenom_contact ||
                                             exp?.data?.attributes?.prenom_contact ||
                                             '';

                        const contact = nomContact && prenomContact ?
                                       `${prenomContact} ${nomContact}` :
                                       nomContact || prenomContact || 'Contact non disponible';

                        const secteur = exp?.attributes?.secteur_activite ||
                                       exp?.secteur_activite ||
                                       exp?.data?.attributes?.secteur_activite ||
                                       'Secteur non disponible';

                        const adresse = exp?.attributes?.adresse ||
                                       exp?.adresse ||
                                       exp?.data?.attributes?.adresse ||
                                       '';

                        // Pour la ville, essayer différentes structures
                        const ville = exp?.attributes?.ville?.data?.attributes?.nom ||
                                     exp?.attributes?.ville?.nom ||
                                     exp?.attributes?.ville ||
                                     exp?.ville?.data?.attributes?.nom ||
                                     exp?.ville?.nom ||
                                     exp?.ville ||
                                     exp?.data?.attributes?.ville ||
                                     '';

                        const localisation = ville && adresse ?
                                           `${ville} - ${adresse}` :
                                           ville || adresse || 'Localisation non disponible';

                        return (
                          <tr key={exp.id || exp.documentId}>
                            <td style={{ padding: "10px", border: "1px solid #ddd" }}>
                              <input
                                type="checkbox"
                                checked={selectedExportateurs.includes(exp.id)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setSelectedExportateurs([...selectedExportateurs, exp.id]);
                                  } else {
                                    setSelectedExportateurs(selectedExportateurs.filter(id => id !== exp.id));
                                  }
                                }}
                              />
                            </td>
                            <td style={{ padding: "10px", border: "1px solid #ddd" }}>
                              <div>
                                <strong style={{ color: "#007bff" }}>{raisonSociale}</strong>
                                <br />
                                <small style={{ color: "#6c757d" }}>
                                  {exp?.attributes?.matricule_fiscal || exp?.matricule_fiscal || 'Matricule N/A'}
                                </small>
                              </div>
                            </td>
                            <td style={{ padding: "10px", border: "1px solid #ddd" }}>
                              <div>
                                <div style={{ fontWeight: "bold" }}>{contact}</div>
                                <div style={{ fontSize: "12px", color: "#007bff" }}>{email}</div>
                                <div style={{ fontSize: "12px", color: "#28a745" }}>{telephone}</div>
                              </div>
                            </td>
                            <td style={{ padding: "10px", border: "1px solid #ddd" }}>
                              <span style={{
                                backgroundColor: "#e9ecef",
                                padding: "4px 8px",
                                borderRadius: "12px",
                                fontSize: "12px",
                                fontWeight: "bold"
                              }}>
                                {secteur}
                              </span>
                            </td>
                            <td style={{ padding: "10px", border: "1px solid #ddd" }}>
                              <div style={{ fontSize: "12px" }}>
                                {localisation}
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>

                  <button
                    onClick={handleSendOpportunity}
                    disabled={selectedExportateurs.length === 0}
                    style={{
                      backgroundColor: selectedExportateurs.length > 0 ? "#007bff" : "#6c757d",
                      color: "white",
                      padding: "12px 30px",
                      border: "none",
                      borderRadius: "5px",
                      fontSize: "16px",
                      fontWeight: "bold",
                      cursor: selectedExportateurs.length > 0 ? "pointer" : "not-allowed"
                    }}
                  >
                    📧 ENVOYER ({selectedExportateurs.length} sélectionné{selectedExportateurs.length > 1 ? 's' : ''})
                  </button>
                </div>
              ) : (
                <p style={{ color: "#856404", backgroundColor: "#fff3cd", padding: "10px", borderRadius: "4px" }}>
                  ⚠️ Aucun exportateur trouvé pour les secteurs sélectionnés : {formData.secteurs.join(", ")}
                </p>
              )}
            </div>
          )}
        </div>
      ) : (
        <div style={{
          backgroundColor: "#d4edda",
          border: "2px solid #28a745",
          borderRadius: "10px",
          padding: "30px",
          textAlign: "center"
        }}>
          <h2 style={{ color: "#155724", marginBottom: "20px" }}>
            🎉 Félicitations ! Votre opportunité a bien été soumise.
          </h2>

          <div style={{
            backgroundColor: "white",
            padding: "20px",
            borderRadius: "8px",
            textAlign: "left",
            marginBottom: "20px"
          }}>
            <h3 style={{ color: "#155724", marginBottom: "15px" }}>
              📧 Un email sera envoyé aux exportateurs sélectionnés avec le contenu suivant :
            </h3>

            <div style={{ backgroundColor: "#f8f9fa", padding: "15px", borderRadius: "5px" }}>
              <p><strong>Objet :</strong> Nouvelle Opportunité d'Importation</p>
              <br />
              <p><strong>Contenu :</strong></p>
              <ul style={{ marginLeft: "20px" }}>
                <li><strong>Importateur :</strong> {getImportateurName()}</li>
                <li><strong>Objet :</strong> {formData.objet}</li>
                <li><strong>Pays de destination :</strong> {formData.pays === "Autre" ? formData.pays_autre : formData.pays}</li>
                <li><strong>Période :</strong> {formData.date_debut} → {formData.date_fin}</li>
                <li><strong>Secteurs concernés :</strong> {formData.secteurs.join(", ")}</li>
              </ul>
              <br />
              <p><strong>Exportateurs contactés :</strong></p>
              <ul style={{ marginLeft: "20px" }}>
                {selectedExportateurs.map(expId => {
                  const exp = matchedExportateurs.find(e => e.id === expId);
                  return (
                    <li key={expId}>
                      {exp?.attributes?.email} ({exp?.attributes?.raison_sociale})
                    </li>
                  );
                })}
              </ul>
            </div>
          </div>

          <button
            onClick={() => {
              setShowConfirmation(false);
              setShowMatching(false);
              setSelectedExportateurs([]);
              setMatchedExportateurs([]);
              setFormData({
                importateur: "",
                objet: "",
                pays: "",
                pays_autre: "",
                date_debut: "",
                date_fin: "",
                secteurs: []
              });
              setNewImportateur({
                societe: "",
                pays: "",
                nom_responsable: "",
                prenom_responsable: "",
                telephone_whatsapp: "",
                email: "",
                region: ""
              });
              setShowNewImportateurForm(false);
            }}
            style={{
              backgroundColor: "#007bff",
              color: "white",
              padding: "12px 30px",
              border: "none",
              borderRadius: "5px",
              fontSize: "16px",
              fontWeight: "bold",
              cursor: "pointer"
            }}
          >
            ✨ Nouvelle Opportunité
          </button>
        </div>
      )}
    </div>
  );
}

export default OpportuniteForm;
