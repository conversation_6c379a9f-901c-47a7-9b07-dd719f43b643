import React, { useState, useEffect } from "react";

function OpportuniteForm() {
  const [opportunite, setOpportunite] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch("http://localhost:1337/api/opportunites?populate=*")
      .then((res) => res.json())
      .then((data) => {
        console.log("Opportunites API response:", data);
        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
          setOpportunite(data.data[0]); // ناخذ أول opportunite
        } else {
          console.warn("No opportunites found or unexpected response structure:", data);
          setOpportunite(null);
        }
        setLoading(false);
      })
      .catch((err) => {
        console.error("Fetch error:", err);
        setOpportunite(null);
        setLoading(false);
      });
  }, []);

  if (loading) return <div>Loading...</div>;
  if (!opportunite) return <div>Aucune opportunité trouvée.</div>;

  // باش نستعمل attributes من opportunite
  const attr = opportunite.attributes;

  // Additional safety check
  if (!attr) return <div>Erreur: données d'opportunité non disponibles.</div>;

  return (
    <div>
      <h2>Objet: {attr.objet}</h2>
      <p>Pays de destination: {attr.pays_destination}</p>
      <p>Date début: {attr.date_debut}</p>
      <p>Date fin: {attr.date_fin}</p>
      <p>Secteur: {attr.secteur}</p>

      <h3>Importateur</h3>
      {attr.importateur && attr.importateur.data ? (
        <p>Societe: {attr.importateur.data.attributes.societe}</p>
      ) : (
        <p>Aucun importateur</p>
      )}

      <h3>Exportateurs</h3>
      {attr.exportateurs && attr.exportateurs.data && attr.exportateurs.data.length > 0 ? (
        attr.exportateurs.data.map((exp) => (
          <div key={exp.id}>{exp.attributes.raison_sociale}</div>
        ))
      ) : (
        <p>Aucun exportateur</p>
      )}
    </div>
  );
}

export default OpportuniteForm;
