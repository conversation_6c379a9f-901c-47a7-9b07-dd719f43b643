import React, { useState, useEffect } from "react";

function OpportuniteForm() {
  const [opportunite, setOpportunite] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch("http://localhost:1337/api/opportunites?populate=*")
      .then((res) => res.json())
      .then((data) => {
        console.log("Opportunites API response:", data);
        console.log("First opportunite structure:", data.data && data.data[0]);
        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
          setOpportunite(data.data[0]); // ناخذ أول opportunite
        } else {
          console.warn("No opportunites found or unexpected response structure:", data);
          setOpportunite(null);
        }
        setLoading(false);
      })
      .catch((err) => {
        console.error("Fetch error:", err);
        setOpportunite(null);
        setLoading(false);
      });
  }, []);

  if (loading) return <div>Loading...</div>;
  if (!opportunite) return <div>Aucune opportunité trouvée.</div>;

  console.log("Processing opportunite:", opportunite);

  // Try different possible data structures
  const attr = opportunite.attributes || opportunite.data?.attributes || opportunite;

  // Additional safety check
  if (!attr) return <div>Erreur: données d'opportunité non disponibles.</div>;

  return (
    <div>
      <h2>Objet: {attr.objet}</h2>
      <p>Pays de destination: {attr.pays_destination}</p>
      <p>Date début: {attr.date_debut}</p>
      <p>Date fin: {attr.date_fin}</p>
      <p>Secteur: {attr.secteur}</p>

      <h3>Importateur</h3>
      {(() => {
        const importateur = attr.importateur?.data?.attributes ||
                           attr.importateur?.attributes ||
                           attr.importateur;
        return importateur?.societe ? (
          <p>Societe: {importateur.societe}</p>
        ) : (
          <p>Aucun importateur</p>
        );
      })()}

      <h3>Exportateurs</h3>
      {(() => {
        const exportateurs = attr.exportateurs?.data || attr.exportateurs || [];
        return exportateurs.length > 0 ? (
          exportateurs.map((exp) => {
            const raisonSociale = exp?.attributes?.raison_sociale ||
                                 exp?.raison_sociale ||
                                 'Nom non disponible';
            return (
              <div key={exp.id || exp.documentId || Math.random()}>
                {raisonSociale}
              </div>
            );
          })
        ) : (
          <p>Aucun exportateur</p>
        );
      })()}
    </div>
  );
}

export default OpportuniteForm;
