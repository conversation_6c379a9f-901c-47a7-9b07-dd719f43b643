{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/RoleRow.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/ListPage.tsx"], "sourcesContent": ["import { Box, Flex, IconButton, IconButtonProps, Td, Tr, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport type { AdminRole } from '../../../../../hooks/useAdminRoles';\n\ninterface RoleRowProps extends Pick<AdminRole, 'id' | 'name' | 'description' | 'usersCount'> {\n  icons: Array<Required<Pick<IconButtonProps, 'children' | 'label' | 'onClick'>>>;\n  rowIndex: number;\n  canUpdate?: boolean;\n  cursor?: string;\n}\n\nconst RoleRow = ({\n  id,\n  name,\n  description,\n  usersCount,\n  icons,\n  rowIndex,\n  canUpdate,\n  cursor,\n}: RoleRowProps) => {\n  const { formatMessage } = useIntl();\n  const [, editObject] = icons;\n\n  const usersCountText = formatMessage(\n    {\n      id: `Roles.RoleRow.user-count`,\n      defaultMessage: '{number, plural, =0 {#  user} one {#  user} other {# users}}',\n    },\n    { number: usersCount }\n  );\n\n  return (\n    <Tr\n      cursor={cursor}\n      aria-rowindex={rowIndex}\n      key={id}\n      // @ts-expect-error – the prop uses `HTMLButtonElement` but we just specify `HTMLElement`\n      onClick={canUpdate ? editObject.onClick : undefined}\n    >\n      <Td maxWidth={`13rem`}>\n        <Typography ellipsis textColor=\"neutral800\">\n          {name}\n        </Typography>\n      </Td>\n      <Td maxWidth={`25rem`}>\n        <Typography ellipsis textColor=\"neutral800\">\n          {description}\n        </Typography>\n      </Td>\n      <Td>\n        <Typography textColor=\"neutral800\">{usersCountText}</Typography>\n      </Td>\n      <Td>\n        <Flex justifyContent=\"flex-end\" onClick={(e) => e.stopPropagation()}>\n          {icons.map((icon, i) => {\n            if (icon) {\n              return (\n                <Box key={icon.label} paddingLeft={i === 0 ? 0 : 1}>\n                  <IconButton {...icon} variant=\"ghost\" />\n                </Box>\n              );\n            }\n\n            return null;\n          })}\n        </Flex>\n      </Td>\n    </Tr>\n  );\n};\n\nexport { RoleRow };\nexport type { RoleRowProps };\n", "import * as React from 'react';\n\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  Tr,\n  <PERSON>po<PERSON>,\n  <PERSON>ly<PERSON><PERSON><PERSON>,\n} from '@strapi/design-system';\nimport { Duplicate, Pencil, Plus, Trash } from '@strapi/icons';\nimport { produce } from 'immer';\nimport { useIntl } from 'react-intl';\nimport { useNavigate } from 'react-router-dom';\n\nimport { ConfirmDialog } from '../../../../components/ConfirmDialog';\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { SearchInput } from '../../../../components/SearchInput';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useAdminRoles, AdminRole } from '../../../../hooks/useAdminRoles';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useFetchClient } from '../../../../hooks/useFetchClient';\nimport { useQueryParams } from '../../../../hooks/useQueryParams';\nimport { useRBAC } from '../../../../hooks/useRBAC';\nimport { selectAdminPermissions } from '../../../../selectors';\nimport { isFetchError } from '../../../../utils/getFetchClient';\n\nimport { RoleRow, RoleRowProps } from './components/RoleRow';\n\nconst ListPage = () => {\n  const { formatMessage } = useIntl();\n  const permissions = useTypedSelector(selectAdminPermissions);\n  const { formatAPIError } = useAPIErrorHandler();\n  const { toggleNotification } = useNotification();\n  const [isWarningDeleteAllOpened, setIsWarningDeleteAllOpenend] = React.useState(false);\n  const [{ query }] = useQueryParams<{ _q?: string }>();\n  const {\n    isLoading: isLoadingForPermissions,\n    allowedActions: { canCreate, canDelete, canRead, canUpdate },\n  } = useRBAC(permissions.settings?.roles);\n\n  const { roles, refetch: refetchRoles } = useAdminRoles(\n    { filters: query?._q ? { name: { $containsi: query._q } } : undefined },\n    {\n      refetchOnMountOrArgChange: true,\n      skip: isLoadingForPermissions || !canRead,\n    }\n  );\n\n  const navigate = useNavigate();\n  const [{ roleToDelete }, dispatch] = React.useReducer(reducer, initialState);\n  const { post } = useFetchClient();\n\n  const handleDeleteData = async () => {\n    try {\n      dispatch({\n        type: 'ON_REMOVE_ROLES',\n      });\n\n      await post('/admin/roles/batch-delete', {\n        ids: [roleToDelete],\n      });\n\n      await refetchRoles();\n\n      dispatch({\n        type: 'RESET_DATA_TO_DELETE',\n      });\n    } catch (error) {\n      if (isFetchError(error)) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(error),\n        });\n      }\n    }\n  };\n\n  const handleNewRoleClick = () => navigate('new');\n\n  const handleToggleModal = () => setIsWarningDeleteAllOpenend((prev) => !prev);\n\n  const handleClickDelete = (role: AdminRole) => (e: React.MouseEvent<HTMLButtonElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (role.usersCount) {\n      toggleNotification({\n        type: 'info',\n        message: formatMessage({ id: 'Roles.ListPage.notification.delete-not-allowed' }),\n      });\n    } else {\n      dispatch({\n        type: 'SET_ROLE_TO_DELETE',\n        id: role.id,\n      });\n\n      handleToggleModal();\n    }\n  };\n\n  const handleClickDuplicate = (role: AdminRole) => (e: React.MouseEvent<HTMLButtonElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    navigate(`duplicate/${role.id}`);\n  };\n\n  const rowCount = roles.length + 1;\n  const colCount = 6;\n\n  if (isLoadingForPermissions) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Page.Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Roles',\n          }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        primaryAction={\n          canCreate ? (\n            <Button onClick={handleNewRoleClick} startIcon={<Plus />} size=\"S\">\n              {formatMessage({\n                id: 'Settings.roles.list.button.add',\n                defaultMessage: 'Add new role',\n              })}\n            </Button>\n          ) : null\n        }\n        title={formatMessage({\n          id: 'global.roles',\n          defaultMessage: 'roles',\n        })}\n        subtitle={formatMessage({\n          id: 'Settings.roles.list.description',\n          defaultMessage: 'List of roles',\n        })}\n      />\n      {canRead && (\n        <Layouts.Action\n          startActions={\n            <SearchInput\n              label={formatMessage(\n                { id: 'app.component.search.label', defaultMessage: 'Search for {target}' },\n                {\n                  target: formatMessage({\n                    id: 'global.roles',\n                    defaultMessage: 'roles',\n                  }),\n                }\n              )}\n            />\n          }\n        />\n      )}\n      {canRead && (\n        <Layouts.Content>\n          <Table\n            colCount={colCount}\n            rowCount={rowCount}\n            footer={\n              canCreate ? (\n                <TFooter cursor=\"pointer\" onClick={handleNewRoleClick} icon={<Plus />}>\n                  {formatMessage({\n                    id: 'Settings.roles.list.button.add',\n                    defaultMessage: 'Add new role',\n                  })}\n                </TFooter>\n              ) : null\n            }\n          >\n            <Thead>\n              <Tr aria-rowindex={1}>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({\n                      id: 'global.name',\n                      defaultMessage: 'Name',\n                    })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({\n                      id: 'global.description',\n                      defaultMessage: 'Description',\n                    })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({\n                      id: 'global.users',\n                      defaultMessage: 'Users',\n                    })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <VisuallyHidden>\n                    {formatMessage({\n                      id: 'global.actions',\n                      defaultMessage: 'Actions',\n                    })}\n                  </VisuallyHidden>\n                </Th>\n              </Tr>\n            </Thead>\n            <Tbody>\n              {roles?.map((role, index) => (\n                <RoleRow\n                  cursor=\"pointer\"\n                  key={role.id}\n                  id={role.id}\n                  name={role.name}\n                  description={role.description}\n                  usersCount={role.usersCount}\n                  icons={\n                    [\n                      canCreate &&\n                        ({\n                          onClick: handleClickDuplicate(role),\n                          label: formatMessage({\n                            id: 'app.utils.duplicate',\n                            defaultMessage: 'Duplicate',\n                          }),\n                          children: <Duplicate />,\n                        } satisfies RoleRowProps['icons'][number]),\n                      canUpdate &&\n                        ({\n                          onClick: () => navigate(role.id.toString()),\n                          label: formatMessage({ id: 'app.utils.edit', defaultMessage: 'Edit' }),\n                          children: <Pencil />,\n                        } satisfies RoleRowProps['icons'][number]),\n                      canDelete &&\n                        ({\n                          onClick: handleClickDelete(role),\n                          label: formatMessage({ id: 'global.delete', defaultMessage: 'Delete' }),\n                          children: <Trash />,\n                        } satisfies RoleRowProps['icons'][number]),\n                    ].filter(Boolean) as RoleRowProps['icons']\n                  }\n                  rowIndex={index + 2}\n                  canUpdate={canUpdate}\n                />\n              ))}\n            </Tbody>\n          </Table>\n        </Layouts.Content>\n      )}\n      <Dialog.Root open={isWarningDeleteAllOpened} onOpenChange={handleToggleModal}>\n        <ConfirmDialog onConfirm={handleDeleteData} />\n      </Dialog.Root>\n    </Page.Main>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Reducer\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * TODO: do we actually need this reducer? It's not doing a lot...\n */\n\ninterface State {\n  roleToDelete: null | AdminRole['id'];\n  showModalConfirmButtonLoading: boolean;\n  shouldRefetchData: boolean;\n}\n\nconst initialState = {\n  roleToDelete: null,\n  showModalConfirmButtonLoading: false,\n  shouldRefetchData: false,\n} satisfies State;\n\ninterface SetRoleToDeleteAction extends Pick<AdminRole, 'id'> {\n  type: 'SET_ROLE_TO_DELETE';\n}\n\ninterface ResetDataToDeleteAction {\n  type: 'RESET_DATA_TO_DELETE';\n}\n\ninterface OnRemoveRolesAction {\n  type: 'ON_REMOVE_ROLES';\n}\n\ninterface OnRemoveRolesSucceededAction {\n  type: 'ON_REMOVE_ROLES_SUCCEEDED';\n}\n\ntype Action =\n  | SetRoleToDeleteAction\n  | ResetDataToDeleteAction\n  | OnRemoveRolesAction\n  | OnRemoveRolesSucceededAction;\n\nconst reducer = (state: State, action: Action) =>\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case 'ON_REMOVE_ROLES': {\n        draftState.showModalConfirmButtonLoading = true;\n        break;\n      }\n      case 'ON_REMOVE_ROLES_SUCCEEDED': {\n        draftState.shouldRefetchData = true;\n        draftState.roleToDelete = null;\n        break;\n      }\n      case 'RESET_DATA_TO_DELETE': {\n        draftState.shouldRefetchData = false;\n        draftState.roleToDelete = null;\n        draftState.showModalConfirmButtonLoading = false;\n        break;\n      }\n      case 'SET_ROLE_TO_DELETE': {\n        draftState.roleToDelete = action.id;\n\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedListPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedListPage = () => {\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings?.roles.read);\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListPage />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedListPage, ListPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAMA,UAAU,CAAC,EACfC,IACAC,MACAC,aACAC,YACAC,OACAC,UACAC,WACAC,OAAM,MACO;AACb,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAAA,EAAGC,UAAAA,IAAcN;AAEvB,QAAMO,iBAAiBH,cACrB;IACER,IAAI;IACJY,gBAAgB;KAElB;IAAEC,QAAQV;EAAW,CAAA;AAGvB,aACEW,yBAACC,IAAAA;IACCR;IACAS,iBAAeX;;IAGfY,SAASX,YAAYI,WAAWO,UAAUC;;UAE1CC,wBAACC,IAAAA;QAAGC,UAAU;QACZ,cAAAF,wBAACG,YAAAA;UAAWC,UAAQ;UAACC,WAAU;UAC5BvB,UAAAA;;;UAGLkB,wBAACC,IAAAA;QAAGC,UAAU;QACZ,cAAAF,wBAACG,YAAAA;UAAWC,UAAQ;UAACC,WAAU;UAC5BtB,UAAAA;;;UAGLiB,wBAACC,IAAAA;QACC,cAAAD,wBAACG,YAAAA;UAAWE,WAAU;UAAcb,UAAAA;;;UAEtCQ,wBAACC,IAAAA;QACC,cAAAD,wBAACM,MAAAA;UAAKC,gBAAe;UAAWT,SAAS,CAACU,MAAMA,EAAEC,gBAAe;oBAC9DxB,MAAMyB,IAAI,CAACC,MAAMC,MAAAA;AAChB,gBAAID,MAAM;AACR,yBACEX,wBAACa,KAAAA;gBAAqBC,aAAaF,MAAM,IAAI,IAAI;gBAC/C,cAAAZ,wBAACe,YAAAA;kBAAY,GAAGJ;kBAAMK,SAAQ;;cADtBL,GAAAA,KAAKM,KAAK;YAIxB;AAEA,mBAAO;UACT,CAAA;;;;EA7BCpC,GAAAA,EAAAA;AAkCX;;;ACpCA,IAAMqC,WAAW,MAAA;;AACf,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,cAAcC,iBAAiBC,sBAAAA;AACrC,QAAM,EAAEC,eAAc,IAAKC,mBAAAA;AAC3B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,CAACC,0BAA0BC,4BAAAA,IAAsCC,eAAS,KAAA;AAChF,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAM,EACJC,WAAWC,yBACXC,gBAAgB,EAAEC,WAAWC,WAAWC,SAASC,UAAS,EAAE,IAC1DC,SAAQnB,iBAAYoB,aAAZpB,mBAAsBqB,KAAAA;AAElC,QAAM,EAAEA,OAAOC,SAASC,aAAY,IAAKC,cACvC;IAAEC,UAASf,+BAAOgB,MAAK;MAAEC,MAAM;QAAEC,YAAYlB,MAAMgB;MAAG;QAAMG;KAC5D;IACEC,2BAA2B;IAC3BC,MAAMlB,2BAA2B,CAACI;EACpC,CAAA;AAGF,QAAMe,WAAWC,YAAAA;AACjB,QAAM,CAAC,EAAEC,aAAY,GAAIC,QAAAA,IAAkBC,iBAAWC,SAASC,YAAAA;AAC/D,QAAM,EAAEC,KAAI,IAAKC,eAAAA;AAEjB,QAAMC,mBAAmB,YAAA;AACvB,QAAI;AACFN,eAAS;QACPO,MAAM;MACR,CAAA;AAEA,YAAMH,KAAK,6BAA6B;QACtCI,KAAK;UAACT;QAAa;MACrB,CAAA;AAEA,YAAMX,aAAAA;AAENY,eAAS;QACPO,MAAM;MACR,CAAA;IACF,SAASE,OAAO;AACd,UAAIC,aAAaD,KAAQ,GAAA;AACvBvC,2BAAmB;UACjBqC,MAAM;UACNI,SAAS3C,eAAeyC,KAAAA;QAC1B,CAAA;MACF;IACF;EACF;AAEA,QAAMG,qBAAqB,MAAMf,SAAS,KAAA;AAE1C,QAAMgB,oBAAoB,MAAMxC,6BAA6B,CAACyC,SAAS,CAACA,IAAAA;AAExE,QAAMC,oBAAoB,CAACC,SAAoB,CAACC,MAAAA;AAC9CA,MAAEC,eAAc;AAChBD,MAAEE,gBAAe;AAEjB,QAAIH,KAAKI,YAAY;AACnBlD,yBAAmB;QACjBqC,MAAM;QACNI,SAAShD,cAAc;UAAE0D,IAAI;QAAiD,CAAA;MAChF,CAAA;WACK;AACLrB,eAAS;QACPO,MAAM;QACNc,IAAIL,KAAKK;MACX,CAAA;AAEAR,wBAAAA;IACF;EACF;AAEA,QAAMS,uBAAuB,CAACN,SAAoB,CAACC,MAAAA;AACjDA,MAAEC,eAAc;AAChBD,MAAEE,gBAAe;AAEjBtB,aAAS,aAAamB,KAAKK,EAAE,EAAE;EACjC;AAEA,QAAME,WAAWrC,MAAMsC,SAAS;AAChC,QAAMC,WAAW;AAEjB,MAAI/C,yBAAyB;AAC3B,eAAOgD,yBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,0BAACF,KAAKG,MAAI;;UACRJ,yBAACC,KAAKI,OAAK;kBACRpE,cACC;UAAE0D,IAAI;UAAsBW,gBAAgB;WAC5C;UACExC,MAAM;QACR,CAAA;;UAGJkC,yBAACO,QAAQC,QAAM;QACbC,eACEvD,gBACE8C,yBAACU,QAAAA;UAAOC,SAASzB;UAAoB0B,eAAWZ,yBAACa,eAAAA,CAAAA,CAAAA;UAASC,MAAK;oBAC5D7E,cAAc;YACb0D,IAAI;YACJW,gBAAgB;UAClB,CAAA;QAEA,CAAA,IAAA;QAENS,OAAO9E,cAAc;UACnB0D,IAAI;UACJW,gBAAgB;QAClB,CAAA;QACAU,UAAU/E,cAAc;UACtB0D,IAAI;UACJW,gBAAgB;QAClB,CAAA;;MAEDlD,eACC4C,yBAACO,QAAQU,QAAM;QACbC,kBACElB,yBAACmB,aAAAA;UACCC,OAAOnF,cACL;YAAE0D,IAAI;YAA8BW,gBAAgB;aACpD;YACEe,QAAQpF,cAAc;cACpB0D,IAAI;cACJW,gBAAgB;YAClB,CAAA;UACF,CAAA;;;MAMTlD,eACC4C,yBAACO,QAAQe,SAAO;QACd,cAAAnB,0BAACoB,OAAAA;UACCxB;UACAF;UACA2B,QACEtE,gBACE8C,yBAACyB,SAAAA;YAAQC,QAAO;YAAUf,SAASzB;YAAoByC,UAAM3B,yBAACa,eAAAA,CAAAA,CAAAA;sBAC3D5E,cAAc;cACb0D,IAAI;cACJW,gBAAgB;YAClB,CAAA;UAEA,CAAA,IAAA;;gBAGNN,yBAAC4B,OAAAA;cACC,cAAAzB,0BAAC0B,IAAAA;gBAAGC,iBAAe;;sBACjB9B,yBAAC+B,IAAAA;oBACC,cAAA/B,yBAACgC,YAAAA;sBAAWC,SAAQ;sBAAQC,WAAU;gCACnCjG,cAAc;wBACb0D,IAAI;wBACJW,gBAAgB;sBAClB,CAAA;;;sBAGJN,yBAAC+B,IAAAA;oBACC,cAAA/B,yBAACgC,YAAAA;sBAAWC,SAAQ;sBAAQC,WAAU;gCACnCjG,cAAc;wBACb0D,IAAI;wBACJW,gBAAgB;sBAClB,CAAA;;;sBAGJN,yBAAC+B,IAAAA;oBACC,cAAA/B,yBAACgC,YAAAA;sBAAWC,SAAQ;sBAAQC,WAAU;gCACnCjG,cAAc;wBACb0D,IAAI;wBACJW,gBAAgB;sBAClB,CAAA;;;sBAGJN,yBAAC+B,IAAAA;oBACC,cAAA/B,yBAACmC,gBAAAA;gCACElG,cAAc;wBACb0D,IAAI;wBACJW,gBAAgB;sBAClB,CAAA;;;;;;gBAKRN,yBAACoC,OAAAA;cACE5E,UAAAA,+BAAO6E,IAAI,CAAC/C,MAAMgD,cACjBtC,yBAACuC,SAAAA;gBACCb,QAAO;gBAEP/B,IAAIL,KAAKK;gBACT7B,MAAMwB,KAAKxB;gBACX0E,aAAalD,KAAKkD;gBAClB9C,YAAYJ,KAAKI;gBACjB+C,OACE;kBACEvF,aACG;oBACCyD,SAASf,qBAAqBN,IAAAA;oBAC9B8B,OAAOnF,cAAc;sBACnB0D,IAAI;sBACJW,gBAAgB;oBAClB,CAAA;oBACAoC,cAAU1C,yBAAC2C,eAAAA,CAAAA,CAAAA;kBACb;kBACFtF,aACG;oBACCsD,SAAS,MAAMxC,SAASmB,KAAKK,GAAGiD,SAAQ,CAAA;oBACxCxB,OAAOnF,cAAc;sBAAE0D,IAAI;sBAAkBW,gBAAgB;oBAAO,CAAA;oBACpEoC,cAAU1C,yBAAC6C,eAAAA,CAAAA,CAAAA;kBACb;kBACF1F,aACG;oBACCwD,SAAStB,kBAAkBC,IAAAA;oBAC3B8B,OAAOnF,cAAc;sBAAE0D,IAAI;sBAAiBW,gBAAgB;oBAAS,CAAA;oBACrEoC,cAAU1C,yBAAC8C,cAAAA,CAAAA,CAAAA;kBACb;gBACH,EAACC,OAAOC,OAAAA;gBAEXC,UAAUX,QAAQ;gBAClBjF;cA/BKiC,GAAAA,KAAKK,EAAE;;;;;UAsCxBK,yBAACkD,OAAOC,MAAI;QAACC,MAAM1G;QAA0B2G,cAAclE;QACzD,cAAAa,yBAACsD,eAAAA;UAAcC,WAAW3E;;;;;AAIlC;AAgBA,IAAMH,eAAe;EACnBJ,cAAc;EACdmF,+BAA+B;EAC/BC,mBAAmB;AACrB;AAwBA,IAAMjF,UAAU,CAACkF,OAAcC,WAC7BC,GAAQF,OAAO,CAACG,eAAAA;AACd,UAAQF,OAAO9E,MAAI;IACjB,KAAK,mBAAmB;AACtBgF,iBAAWL,gCAAgC;AAC3C;IACF;IACA,KAAK,6BAA6B;AAChCK,iBAAWJ,oBAAoB;AAC/BI,iBAAWxF,eAAe;AAC1B;IACF;IACA,KAAK,wBAAwB;AAC3BwF,iBAAWJ,oBAAoB;AAC/BI,iBAAWxF,eAAe;AAC1BwF,iBAAWL,gCAAgC;AAC3C;IACF;IACA,KAAK,sBAAsB;AACzBK,iBAAWxF,eAAesF,OAAOhE;AAEjC;IACF;IACA;AACE,aAAOkE;EACX;AACF,CAAA;AAIgG,IAE5FC,oBAAoB,MAAA;AACxB,QAAM3H,cAAcC,iBAAiB,CAACsH,UAAUA;;AAAAA,uBAAMK,UAAU5H,YAAYoB,aAA5BmG,mBAAsClG,MAAMwG;GAAAA;AAE5F,aACEhE,yBAACC,KAAKgE,SAAO;IAAC9H;IACZ,cAAA6D,yBAAChE,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["RoleRow", "id", "name", "description", "usersCount", "icons", "rowIndex", "canUpdate", "cursor", "formatMessage", "useIntl", "editObject", "usersCountText", "defaultMessage", "number", "_jsxs", "Tr", "aria-rowindex", "onClick", "undefined", "_jsx", "Td", "max<PERSON><PERSON><PERSON>", "Typography", "ellipsis", "textColor", "Flex", "justifyContent", "e", "stopPropagation", "map", "icon", "i", "Box", "paddingLeft", "IconButton", "variant", "label", "ListPage", "formatMessage", "useIntl", "permissions", "useTypedSelector", "selectAdminPermissions", "formatAPIError", "useAPIErrorHandler", "toggleNotification", "useNotification", "isWarningDeleteAllOpened", "setIsWarningDeleteAllOpenend", "useState", "query", "useQueryParams", "isLoading", "isLoadingForPermissions", "allowedActions", "canCreate", "canDelete", "canRead", "canUpdate", "useRBAC", "settings", "roles", "refetch", "refetchRoles", "useAdminRoles", "filters", "_q", "name", "$containsi", "undefined", "refetchOnMountOrArgChange", "skip", "navigate", "useNavigate", "roleToDelete", "dispatch", "useReducer", "reducer", "initialState", "post", "useFetchClient", "handleDeleteData", "type", "ids", "error", "isFetchError", "message", "handleNewRoleClick", "handleToggleModal", "prev", "handleClickDelete", "role", "e", "preventDefault", "stopPropagation", "usersCount", "id", "handleClickDuplicate", "rowCount", "length", "col<PERSON>ount", "_jsx", "Page", "Loading", "_jsxs", "Main", "Title", "defaultMessage", "Layouts", "Header", "primaryAction", "<PERSON><PERSON>", "onClick", "startIcon", "Plus", "size", "title", "subtitle", "Action", "startActions", "SearchInput", "label", "target", "Content", "Table", "footer", "TF<PERSON>er", "cursor", "icon", "<PERSON><PERSON>", "Tr", "aria-rowindex", "Th", "Typography", "variant", "textColor", "VisuallyHidden", "Tbody", "map", "index", "RoleRow", "description", "icons", "children", "Duplicate", "toString", "Pencil", "Trash", "filter", "Boolean", "rowIndex", "Dialog", "Root", "open", "onOpenChange", "ConfirmDialog", "onConfirm", "showModalConfirmButtonLoading", "shouldRefetchData", "state", "action", "produce", "draftState", "ProtectedListPage", "admin_app", "read", "Protect"]}