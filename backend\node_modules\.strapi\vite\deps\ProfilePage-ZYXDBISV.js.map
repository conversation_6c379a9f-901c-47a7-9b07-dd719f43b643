{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/ProfilePage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { <PERSON>, Button, Flex, useNotifyAT, Grid, Typography } from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport upperFirst from 'lodash/upperFirst';\nimport { useIntl } from 'react-intl';\nimport * as yup from 'yup';\n\nimport { Form, FormHelpers } from '../components/Form';\nimport { InputRenderer } from '../components/FormInputs/Renderer';\nimport { Layouts } from '../components/Layouts/Layout';\nimport { Page } from '../components/PageHelpers';\nimport { useTypedDispatch, useTypedSelector } from '../core/store/hooks';\nimport { useAuth } from '../features/Auth';\nimport { useNotification } from '../features/Notifications';\nimport { useTracking } from '../features/Tracking';\nimport { useAPIErrorHandler } from '../hooks/useAPIErrorHandler';\nimport { AppState, setAppTheme } from '../reducer';\nimport { useIsSSOLockedQuery, useUpdateMeMutation } from '../services/auth';\nimport { isBaseQueryError } from '../utils/baseQuery';\nimport { translatedErrors } from '../utils/translatedErrors';\nimport { getDisplayName } from '../utils/users';\n\nimport { COMMON_USER_SCHEMA } from './Settings/pages/Users/<USER>/validation';\n\nimport type { UpdateMe } from '../../../shared/contracts/users';\n\nconst PROFILE_VALIDTION_SCHEMA = yup.object().shape({\n  ...COMMON_USER_SCHEMA,\n  currentPassword: yup\n    .string()\n    // @ts-expect-error – no idea why this is failing.\n    .when(['password', 'confirmPassword'], (password, confirmPassword, passSchema) => {\n      return password || confirmPassword\n        ? passSchema\n            .required({\n              id: translatedErrors.required.id,\n              defaultMessage: 'This field is required',\n            })\n            .nullable()\n        : passSchema;\n    }),\n  preferedLanguage: yup.string().nullable(),\n});\n\n/* -------------------------------------------------------------------------------------------------\n * ProfilePage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProfilePage = () => {\n  const localeNames = useTypedSelector((state) => state.admin_app.language.localeNames);\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { toggleNotification } = useNotification();\n  const { notifyStatus } = useNotifyAT();\n  const currentTheme = useTypedSelector((state) => state.admin_app.theme.currentTheme);\n  const dispatch = useTypedDispatch();\n  const {\n    _unstableFormatValidationErrors: formatValidationErrors,\n    _unstableFormatAPIError: formatApiError,\n  } = useAPIErrorHandler();\n\n  const user = useAuth('ProfilePage', (state) => state.user);\n\n  React.useEffect(() => {\n    if (user) {\n      notifyStatus(\n        formatMessage({\n          id: 'Settings.profile.form.notify.data.loaded',\n          defaultMessage: 'Your profile data has been loaded',\n        })\n      );\n    } else {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n      });\n    }\n  }, [formatMessage, notifyStatus, toggleNotification, user]);\n\n  const [updateMe, { isLoading: isSubmittingForm }] = useUpdateMeMutation();\n\n  const {\n    isLoading,\n    data: dataSSO,\n    error,\n  } = useIsSSOLockedQuery(undefined, {\n    skip: !(window.strapi.isEE && window.strapi.features.isEnabled('sso')),\n  });\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'Settings.permissions.users.sso.provider.error' }),\n      });\n    }\n  }, [error, formatMessage, toggleNotification]);\n\n  type UpdateUsersMeBody = UpdateMe.Request['body'] & {\n    confirmPassword: string;\n    currentTheme: AppState['theme']['currentTheme'];\n  };\n\n  const handleSubmit = async (\n    body: UpdateUsersMeBody,\n    { setErrors }: FormHelpers<UpdateUsersMeBody>\n  ) => {\n    const { confirmPassword: _confirmPassword, currentTheme, ...bodyRest } = body;\n    let dataToSend = bodyRest;\n\n    // The password fields are optional. If the user didn't touch them, don't send any password\n    // to the API, because an empty string would throw a validation error\n    if (dataToSend.password === '') {\n      const {\n        password: _password,\n        currentPassword: _currentPassword,\n        ...passwordRequestBodyRest\n      } = dataToSend;\n      dataToSend = passwordRequestBodyRest;\n    }\n\n    const res = await updateMe(dataToSend);\n\n    if ('data' in res) {\n      dispatch(setAppTheme(currentTheme));\n\n      trackUsage('didChangeMode', { newMode: currentTheme });\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: 'notification.success.saved', defaultMessage: 'Saved' }),\n      });\n    }\n\n    if ('error' in res) {\n      if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n        setErrors(formatValidationErrors(res.error));\n      } else if (isBaseQueryError(res.error)) {\n        toggleNotification({\n          type: 'danger',\n          message: formatApiError(res.error),\n        });\n      } else {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n        });\n      }\n    }\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  const hasLockedRole = dataSSO?.isSSOLocked ?? false;\n  const { email, firstname, lastname, username, preferedLanguage } = user ?? {};\n  const initialData = {\n    email: email ?? '',\n    firstname: firstname ?? '',\n    lastname: lastname ?? '',\n    username: username ?? '',\n    preferedLanguage,\n    currentTheme,\n    confirmPassword: '',\n    password: '',\n  };\n\n  return (\n    <Page.Main aria-busy={isSubmittingForm}>\n      <Page.Title>\n        {formatMessage({\n          id: 'Settings.profile.form.section.head.title',\n          defaultMessage: 'User profile',\n        })}\n      </Page.Title>\n      <Form\n        method=\"PUT\"\n        onSubmit={handleSubmit}\n        initialValues={initialData}\n        validationSchema={PROFILE_VALIDTION_SCHEMA}\n      >\n        {({ isSubmitting, modified }) => (\n          <>\n            <Layouts.Header\n              title={getDisplayName(user)}\n              primaryAction={\n                <Button\n                  startIcon={<Check />}\n                  loading={isSubmitting}\n                  type=\"submit\"\n                  disabled={!modified}\n                >\n                  {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n                </Button>\n              }\n            />\n            <Box paddingBottom={10}>\n              <Layouts.Content>\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n                  <UserInfoSection />\n                  {!hasLockedRole && <PasswordSection />}\n                  <PreferencesSection localeNames={localeNames} />\n                </Flex>\n              </Layouts.Content>\n            </Box>\n          </>\n        )}\n      </Form>\n    </Page.Main>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordSection\n * -----------------------------------------------------------------------------------------------*/\n\nconst PasswordSection = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"filterShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Typography variant=\"delta\" tag=\"h2\">\n          {formatMessage({\n            id: 'global.change-password',\n            defaultMessage: 'Change password',\n          })}\n        </Typography>\n        {[\n          [\n            {\n              label: formatMessage({\n                id: 'Auth.form.currentPassword.label',\n                defaultMessage: 'Current Password',\n              }),\n              name: 'currentPassword',\n              size: 6,\n              type: 'password' as const,\n            },\n          ],\n          [\n            {\n              autoComplete: 'new-password',\n              label: formatMessage({\n                id: 'global.password',\n                defaultMessage: 'Password',\n              }),\n              name: 'password',\n              size: 6,\n              type: 'password' as const,\n            },\n            {\n              autoComplete: 'new-password',\n              label: formatMessage({\n                id: 'Auth.form.confirmPassword.label',\n                defaultMessage: 'Confirm Password',\n              }),\n              name: 'confirmPassword',\n              size: 6,\n              type: 'password' as const,\n            },\n          ],\n        ].map((row, index) => (\n          <Grid.Root key={index} gap={5}>\n            {row.map(({ size, ...field }) => (\n              <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n                <InputRenderer {...field} />\n              </Grid.Item>\n            ))}\n          </Grid.Root>\n        ))}\n      </Flex>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PreferencesSection\n * -----------------------------------------------------------------------------------------------*/\n\ninterface PreferencesSectionProps {\n  localeNames: Record<string, string>;\n}\n\nconst PreferencesSection = ({ localeNames }: PreferencesSectionProps) => {\n  const { formatMessage } = useIntl();\n  const themesToDisplay = useTypedSelector((state) => state.admin_app.theme.availableThemes);\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"filterShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n          <Typography variant=\"delta\" tag=\"h2\">\n            {formatMessage({\n              id: 'Settings.profile.form.section.experience.title',\n              defaultMessage: 'Experience',\n            })}\n          </Typography>\n          <Typography>\n            {formatMessage(\n              {\n                id: 'Settings.profile.form.section.experience.interfaceLanguageHelp',\n                defaultMessage:\n                  'Preference changes will apply only to you. More information is available {here}.',\n              },\n              {\n                here: (\n                  <Box\n                    tag=\"a\"\n                    color=\"primary600\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    href=\"https://docs.strapi.io/developer-docs/latest/development/admin-customization.html#locales\"\n                  >\n                    {formatMessage({\n                      id: 'Settings.profile.form.section.experience.here',\n                      defaultMessage: 'here',\n                    })}\n                  </Box>\n                ),\n              }\n            )}\n          </Typography>\n        </Flex>\n        <Grid.Root gap={5}>\n          {[\n            {\n              hint: formatMessage({\n                id: 'Settings.profile.form.section.experience.interfaceLanguage.hint',\n                defaultMessage: 'This will only display your own interface in the chosen language.',\n              }),\n              label: formatMessage({\n                id: 'Settings.profile.form.section.experience.interfaceLanguage',\n                defaultMessage: 'Interface language',\n              }),\n              name: 'preferedLanguage',\n              options: Object.entries(localeNames).map(([value, label]) => ({\n                label,\n                value,\n              })),\n              placeholder: formatMessage({\n                id: 'global.select',\n                defaultMessage: 'Select',\n              }),\n              size: 6,\n              type: 'enumeration' as const,\n            },\n            {\n              hint: formatMessage({\n                id: 'Settings.profile.form.section.experience.mode.hint',\n                defaultMessage: 'Displays your interface in the chosen mode.',\n              }),\n              label: formatMessage({\n                id: 'Settings.profile.form.section.experience.mode.label',\n                defaultMessage: 'Interface mode',\n              }),\n              name: 'currentTheme',\n              options: [\n                {\n                  label: formatMessage({\n                    id: 'Settings.profile.form.section.experience.mode.option-system-label',\n                    defaultMessage: 'Use system settings',\n                  }),\n                  value: 'system',\n                },\n                ...themesToDisplay.map((theme) => ({\n                  label: formatMessage(\n                    {\n                      id: 'Settings.profile.form.section.experience.mode.option-label',\n                      defaultMessage: '{name} mode',\n                    },\n                    {\n                      name: formatMessage({\n                        id: theme,\n                        defaultMessage: upperFirst(theme),\n                      }),\n                    }\n                  ),\n                  value: theme,\n                })),\n              ],\n              placeholder: formatMessage({\n                id: 'components.Select.placeholder',\n                defaultMessage: 'Select',\n              }),\n              size: 6,\n              type: 'enumeration' as const,\n            },\n          ].map(({ size, ...field }) => (\n            <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n              <InputRenderer {...field} />\n            </Grid.Item>\n          ))}\n        </Grid.Root>\n      </Flex>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * UserInfoSection\n * -----------------------------------------------------------------------------------------------*/\n\nconst UserInfoSection = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"filterShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Typography variant=\"delta\" tag=\"h2\">\n          {formatMessage({\n            id: 'global.profile',\n            defaultMessage: 'Profile',\n          })}\n        </Typography>\n        <Grid.Root gap={5}>\n          {[\n            {\n              label: formatMessage({\n                id: 'Auth.form.firstname.label',\n                defaultMessage: 'First name',\n              }),\n              name: 'firstname',\n              required: true,\n              size: 6,\n              type: 'string' as const,\n            },\n            {\n              label: formatMessage({\n                id: 'Auth.form.lastname.label',\n                defaultMessage: 'Last name',\n              }),\n              name: 'lastname',\n              size: 6,\n              type: 'string' as const,\n            },\n            {\n              label: formatMessage({\n                id: 'Auth.form.email.label',\n                defaultMessage: 'Email',\n              }),\n              name: 'email',\n              required: true,\n              size: 6,\n              type: 'email' as const,\n            },\n            {\n              label: formatMessage({\n                id: 'Auth.form.username.label',\n                defaultMessage: 'Username',\n              }),\n              name: 'username',\n              size: 6,\n              type: 'string' as const,\n            },\n          ].map(({ size, ...field }) => (\n            <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n              <InputRenderer {...field} />\n            </Grid.Item>\n          ))}\n        </Grid.Root>\n      </Flex>\n    </Box>\n  );\n};\n\nexport { ProfilePage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,IAAMA,2BAA+BC,QAAM,EAAGC,MAAM;EAClD,GAAGC;EACHC,iBACGC,OAAM,EAENC,KAAK;IAAC;IAAY;KAAoB,CAACC,UAAUC,iBAAiBC,eAAAA;AACjE,WAAOF,YAAYC,kBACfC,WACGC,SAAS;MACRC,IAAIC,YAAiBF,SAASC;MAC9BE,gBAAgB;IAClB,CAAA,EACCC,SAAQ,IACXL;EACN,CAAA;EACFM,kBAAsBV,OAAM,EAAGS,SAAQ;AACzC,CAAA;AAIkG,IAE5FE,cAAc,MAAA;AAClB,QAAMC,cAAcC,iBAAiB,CAACC,UAAUA,MAAMC,UAAUC,SAASJ,WAAW;AACpF,QAAM,EAAEK,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,aAAY,IAAKC,YAAAA;AACzB,QAAMC,eAAeZ,iBAAiB,CAACC,UAAUA,MAAMC,UAAUW,MAAMD,YAAY;AACnF,QAAME,WAAWC,iBAAAA;AACjB,QAAM,EACJC,iCAAiCC,wBACjCC,yBAAyBC,eAAc,IACrCC,mBAAAA;AAEJ,QAAMC,OAAOC,QAAQ,eAAe,CAACrB,UAAUA,MAAMoB,IAAI;AAEzDE,EAAMC,gBAAU,MAAA;AACd,QAAIH,MAAM;AACRX,mBACEN,cAAc;QACZX,IAAI;QACJE,gBAAgB;MAClB,CAAA,CAAA;WAEG;AACLa,yBAAmB;QACjBiB,MAAM;QACNC,SAAStB,cAAc;UAAEX,IAAI;UAAsBE,gBAAgB;QAAmB,CAAA;MACxF,CAAA;IACF;KACC;IAACS;IAAeM;IAAcF;IAAoBa;EAAK,CAAA;AAE1D,QAAM,CAACM,UAAU,EAAEC,WAAWC,iBAAgB,CAAE,IAAIC,oBAAAA;AAEpD,QAAM,EACJF,WACAG,MAAMC,SACNC,MAAK,IACHC,oBAAoBC,QAAW;IACjCC,MAAM,EAAEC,OAAOC,OAAOC,QAAQF,OAAOC,OAAOE,SAASC,UAAU,KAAK;EACtE,CAAA;AAEAlB,EAAMC,gBAAU,MAAA;AACd,QAAIS,OAAO;AACTzB,yBAAmB;QACjBiB,MAAM;QACNC,SAAStB,cAAc;UAAEX,IAAI;QAAgD,CAAA;MAC/E,CAAA;IACF;KACC;IAACwC;IAAO7B;IAAeI;EAAmB,CAAA;AAO7C,QAAMkC,eAAe,OACnBC,MACA,EAAEC,UAAS,MAAkC;AAE7C,UAAM,EAAEtD,iBAAiBuD,kBAAkBjC,cAAAA,eAAc,GAAGkC,SAAAA,IAAaH;AACzE,QAAII,aAAaD;AAIjB,QAAIC,WAAW1D,aAAa,IAAI;AAC9B,YAAM,EACJA,UAAU2D,WACV9D,iBAAiB+D,kBACjB,GAAGC,wBAAAA,IACDH;AACJA,mBAAaG;IACf;AAEA,UAAMC,MAAM,MAAMxB,SAASoB,UAAAA;AAE3B,QAAI,UAAUI,KAAK;AACjBrC,eAASsC,YAAYxC,aAAAA,CAAAA;AAErBN,iBAAW,iBAAiB;QAAE+C,SAASzC;MAAa,CAAA;AAEpDJ,yBAAmB;QACjBiB,MAAM;QACNC,SAAStB,cAAc;UAAEX,IAAI;UAA8BE,gBAAgB;QAAQ,CAAA;MACrF,CAAA;IACF;AAEA,QAAI,WAAWwD,KAAK;AAClB,UAAIG,iBAAiBH,IAAIlB,KAAK,KAAKkB,IAAIlB,MAAMsB,SAAS,mBAAmB;AACvEX,kBAAU3B,uBAAuBkC,IAAIlB,KAAK,CAAA;MAC5C,WAAWqB,iBAAiBH,IAAIlB,KAAK,GAAG;AACtCzB,2BAAmB;UACjBiB,MAAM;UACNC,SAASP,eAAegC,IAAIlB,KAAK;QACnC,CAAA;aACK;AACLzB,2BAAmB;UACjBiB,MAAM;UACNC,SAAStB,cAAc;YAAEX,IAAI;YAAsBE,gBAAgB;UAAmB,CAAA;QACxF,CAAA;MACF;IACF;EACF;AAEA,MAAIiC,WAAW;AACb,eAAO4B,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,QAAMC,iBAAgB3B,mCAAS4B,gBAAe;AAC9C,QAAM,EAAEC,OAAOC,WAAWC,UAAUC,UAAUnE,iBAAgB,IAAKwB,QAAQ,CAAA;AAC3E,QAAM4C,cAAc;IAClBJ,OAAOA,SAAS;IAChBC,WAAWA,aAAa;IACxBC,UAAUA,YAAY;IACtBC,UAAUA,YAAY;IACtBnE;IACAe;IACAtB,iBAAiB;IACjBD,UAAU;EACZ;AAEA,aACE6E,yBAACT,KAAKU,MAAI;IAACC,aAAWvC;;UACpB2B,wBAACC,KAAKY,OAAK;kBACRjE,cAAc;UACbX,IAAI;UACJE,gBAAgB;QAClB,CAAA;;UAEF6D,wBAACc,MAAAA;QACCC,QAAO;QACPC,UAAU9B;QACV+B,eAAeR;QACfS,kBAAkB5F;QAEjB,UAAA,CAAC,EAAE6F,cAAcC,SAAQ,UACxBV,yBAAAW,6BAAA;;gBACErB,wBAACsB,QAAQC,QAAM;cACbC,OAAOC,eAAe5D,IAAAA;cACtB6D,mBACE1B,wBAAC2B,QAAAA;gBACCC,eAAW5B,wBAAC6B,eAAAA,CAAAA,CAAAA;gBACZC,SAASX;gBACTlD,MAAK;gBACL8D,UAAU,CAACX;0BAEVxE,cAAc;kBAAEX,IAAI;kBAAeE,gBAAgB;gBAAO,CAAA;;;gBAIjE6D,wBAACgC,KAAAA;cAAIC,eAAe;4BAClBjC,wBAACsB,QAAQY,SAAO;gBACd,cAAAxB,yBAACyB,MAAAA;kBAAKC,WAAU;kBAASC,YAAW;kBAAUC,KAAK;;wBACjDtC,wBAACuC,iBAAAA,CAAAA,CAAAA;oBACA,CAACpC,qBAAiBH,wBAACwC,iBAAAA,CAAAA,CAAAA;wBACpBxC,wBAACyC,oBAAAA;sBAAmBlG;;;;;;;;;;;AAStC;AAMA,IAAMiG,kBAAkB,MAAA;AACtB,QAAM,EAAE5F,cAAa,IAAKC,QAAAA;AAE1B,aACEmD,wBAACgC,KAAAA;IACCU,YAAW;IACXC,WAAS;IACTC,QAAO;IACPC,YAAY;IACZZ,eAAe;IACfa,aAAa;IACbC,cAAc;IAEd,cAAArC,yBAACyB,MAAAA;MAAKC,WAAU;MAASC,YAAW;MAAUC,KAAK;;YACjDtC,wBAACgD,YAAAA;UAAWC,SAAQ;UAAQC,KAAI;oBAC7BtG,cAAc;YACbX,IAAI;YACJE,gBAAgB;UAClB,CAAA;;QAED;UACC;YACE;cACEgH,OAAOvG,cAAc;gBACnBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACA4D,MAAM;cACNqD,MAAM;cACNnF,MAAM;YACR;UACD;UACD;YACE;cACEoF,cAAc;cACdF,OAAOvG,cAAc;gBACnBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACA4D,MAAM;cACNqD,MAAM;cACNnF,MAAM;YACR;YACA;cACEoF,cAAc;cACdF,OAAOvG,cAAc;gBACnBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACA4D,MAAM;cACNqD,MAAM;cACNnF,MAAM;YACR;UACD;QACF,EAACqF,IAAI,CAACC,KAAKC,cACVxD,wBAACyD,KAAKC,MAAI;UAAapB,KAAK;oBACzBiB,IAAID,IAAI,CAAC,EAAEF,MAAM,GAAGO,MAAO,UAC1B3D,wBAACyD,KAAKG,MAAI;YAAkBC,KAAKT;YAAMhB,WAAU;YAASC,YAAW;YACnE,cAAArC,wBAAC8D,uBAAAA;cAAe,GAAGH;;UADLA,GAAAA,MAAM5D,IAAI,CAAA;QAFdyD,GAAAA,KAAAA,CAAAA;;;;AAW1B;AAUA,IAAMf,qBAAqB,CAAC,EAAElG,YAAW,MAA2B;AAClE,QAAM,EAAEK,cAAa,IAAKC,QAAAA;AAC1B,QAAMkH,kBAAkBvH,iBAAiB,CAACC,UAAUA,MAAMC,UAAUW,MAAM2G,eAAe;AAEzF,aACEhE,wBAACgC,KAAAA;IACCU,YAAW;IACXC,WAAS;IACTC,QAAO;IACPC,YAAY;IACZZ,eAAe;IACfa,aAAa;IACbC,cAAc;IAEd,cAAArC,yBAACyB,MAAAA;MAAKC,WAAU;MAASC,YAAW;MAAUC,KAAK;;YACjD5B,yBAACyB,MAAAA;UAAKC,WAAU;UAASC,YAAW;UAAUC,KAAK;;gBACjDtC,wBAACgD,YAAAA;cAAWC,SAAQ;cAAQC,KAAI;wBAC7BtG,cAAc;gBACbX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;;gBAEF6D,wBAACgD,YAAAA;wBACEpG,cACC;gBACEX,IAAI;gBACJE,gBACE;iBAEJ;gBACE8H,UACEjE,wBAACgC,KAAAA;kBACCkB,KAAI;kBACJgB,OAAM;kBACNC,QAAO;kBACPC,KAAI;kBACJC,MAAK;4BAEJzH,cAAc;oBACbX,IAAI;oBACJE,gBAAgB;kBAClB,CAAA;;cAGN,CAAA;;;;YAIN6D,wBAACyD,KAAKC,MAAI;UAACpB,KAAK;UACb,UAAA;YACC;cACEgC,MAAM1H,cAAc;gBAClBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACAgH,OAAOvG,cAAc;gBACnBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACA4D,MAAM;cACNwE,SAASC,OAAOC,QAAQlI,WAAa+G,EAAAA,IAAI,CAAC,CAACoB,OAAOvB,KAAM,OAAM;gBAC5DA;gBACAuB;gBACF;cACAC,aAAa/H,cAAc;gBACzBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACAiH,MAAM;cACNnF,MAAM;YACR;YACA;cACEqG,MAAM1H,cAAc;gBAClBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACAgH,OAAOvG,cAAc;gBACnBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACA4D,MAAM;cACNwE,SAAS;gBACP;kBACEpB,OAAOvG,cAAc;oBACnBX,IAAI;oBACJE,gBAAgB;kBAClB,CAAA;kBACAuI,OAAO;gBACT;gBACGX,GAAAA,gBAAgBT,IAAI,CAACjG,WAAW;kBACjC8F,OAAOvG,cACL;oBACEX,IAAI;oBACJE,gBAAgB;qBAElB;oBACE4D,MAAMnD,cAAc;sBAClBX,IAAIoB;sBACJlB,oBAAgByI,kBAAAA,SAAWvH,KAAAA;oBAC7B,CAAA;kBACF,CAAA;kBAEFqH,OAAOrH;kBACT;cACD;cACDsH,aAAa/H,cAAc;gBACzBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACAiH,MAAM;cACNnF,MAAM;YACR;YACAqF,IAAI,CAAC,EAAEF,MAAM,GAAGO,MAAO,UACvB3D,wBAACyD,KAAKG,MAAI;YAAkBC,KAAKT;YAAMhB,WAAU;YAASC,YAAW;YACnE,cAAArC,wBAAC8D,uBAAAA;cAAe,GAAGH;;UADLA,GAAAA,MAAM5D,IAAI,CAAA;;;;;AAQtC;AAMA,IAAMwC,kBAAkB,MAAA;AACtB,QAAM,EAAE3F,cAAa,IAAKC,QAAAA;AAE1B,aACEmD,wBAACgC,KAAAA;IACCU,YAAW;IACXC,WAAS;IACTC,QAAO;IACPC,YAAY;IACZZ,eAAe;IACfa,aAAa;IACbC,cAAc;IAEd,cAAArC,yBAACyB,MAAAA;MAAKC,WAAU;MAASC,YAAW;MAAUC,KAAK;;YACjDtC,wBAACgD,YAAAA;UAAWC,SAAQ;UAAQC,KAAI;oBAC7BtG,cAAc;YACbX,IAAI;YACJE,gBAAgB;UAClB,CAAA;;YAEF6D,wBAACyD,KAAKC,MAAI;UAACpB,KAAK;UACb,UAAA;YACC;cACEa,OAAOvG,cAAc;gBACnBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACA4D,MAAM;cACN/D,UAAU;cACVoH,MAAM;cACNnF,MAAM;YACR;YACA;cACEkF,OAAOvG,cAAc;gBACnBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACA4D,MAAM;cACNqD,MAAM;cACNnF,MAAM;YACR;YACA;cACEkF,OAAOvG,cAAc;gBACnBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACA4D,MAAM;cACN/D,UAAU;cACVoH,MAAM;cACNnF,MAAM;YACR;YACA;cACEkF,OAAOvG,cAAc;gBACnBX,IAAI;gBACJE,gBAAgB;cAClB,CAAA;cACA4D,MAAM;cACNqD,MAAM;cACNnF,MAAM;YACR;YACAqF,IAAI,CAAC,EAAEF,MAAM,GAAGO,MAAO,UACvB3D,wBAACyD,KAAKG,MAAI;YAAkBC,KAAKT;YAAMhB,WAAU;YAASC,YAAW;YACnE,cAAArC,wBAAC8D,uBAAAA;cAAe,GAAGH;;UADLA,GAAAA,MAAM5D,IAAI,CAAA;;;;;AAQtC;", "names": ["PROFILE_VALIDTION_SCHEMA", "object", "shape", "COMMON_USER_SCHEMA", "currentPassword", "string", "when", "password", "confirmPassword", "passSchema", "required", "id", "translatedErrors", "defaultMessage", "nullable", "preferedLanguage", "ProfilePage", "localeNames", "useTypedSelector", "state", "admin_app", "language", "formatMessage", "useIntl", "trackUsage", "useTracking", "toggleNotification", "useNotification", "notify<PERSON><PERSON><PERSON>", "useNotifyAT", "currentTheme", "theme", "dispatch", "useTypedDispatch", "_unstableFormatValidationErrors", "formatValidationErrors", "_unstableFormatAPIError", "formatApiError", "useAPIErrorHandler", "user", "useAuth", "React", "useEffect", "type", "message", "updateMe", "isLoading", "isSubmittingForm", "useUpdateMeMutation", "data", "dataSSO", "error", "useIsSSOLockedQuery", "undefined", "skip", "window", "strapi", "isEE", "features", "isEnabled", "handleSubmit", "body", "setErrors", "_confirmPassword", "bodyRest", "dataToSend", "_password", "_currentPassword", "passwordRequestBodyRest", "res", "setAppTheme", "newMode", "isBaseQueryError", "name", "_jsx", "Page", "Loading", "hasLockedRole", "isSSOLocked", "email", "firstname", "lastname", "username", "initialData", "_jsxs", "Main", "aria-busy", "Title", "Form", "method", "onSubmit", "initialValues", "validationSchema", "isSubmitting", "modified", "_Fragment", "Layouts", "Header", "title", "getDisplayName", "primaryAction", "<PERSON><PERSON>", "startIcon", "Check", "loading", "disabled", "Box", "paddingBottom", "Content", "Flex", "direction", "alignItems", "gap", "UserInfoSection", "PasswordSection", "PreferencesSection", "background", "hasRadius", "shadow", "paddingTop", "paddingLeft", "paddingRight", "Typography", "variant", "tag", "label", "size", "autoComplete", "map", "row", "index", "Grid", "Root", "field", "<PERSON><PERSON>", "col", "InputR<PERSON><PERSON>", "themesToDisplay", "availableThemes", "here", "color", "target", "rel", "href", "hint", "options", "Object", "entries", "value", "placeholder", "upperFirst"]}