import{a as n,j as t,P as s,B as a,ar as o,as as r,s as i,at as d,au as c,av as u}from"./strapi-z7ApxZZq.js";const y=()=>{const{formatMessage:e}=n();return t.jsxs(s.Main,{children:[t.jsx(<PERSON><PERSON>,{title:e({id:o("header.name"),defaultMessage:"Content"})}),t.jsx(a.Content,{children:t.jsx(r,{action:t.jsx(d,{tag:c,variant:"secondary",startIcon:t.jsx(u,{}),to:"/plugins/content-type-builder/content-types/create-content-type",children:e({id:"app.components.HomePage.create",defaultMessage:"Create your first Content-type"})}),content:e({id:"content-manager.pages.NoContentType.text",defaultMessage:"You don't have any content yet, we recommend you to create your first Content-Type."}),hasRadius:!0,icon:t.jsx(i,{width:"16rem"}),shadow:"tableShadow"})})]})};export{y as NoContentType};
