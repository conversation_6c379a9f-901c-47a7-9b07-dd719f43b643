import{r as l,a as m,bC as c,j as e,e as u,b8 as p,q as f,D as x,bJ as g}from"./strapi-z7ApxZZq.js";import{i as h}from"./isNil-HHOJ63lS.js";const w=l.forwardRef((i,r)=>{const{formatMessage:s}=m(),{license:a,isError:o,isLoading:n}=c(),{permittedSeats:d,shouldStopCreate:t}=a??{};return o||n?null:e.jsxs(u,{gap:2,children:[!h(d)&&t&&e.jsx(p,{label:s({id:"Settings.application.admin-seats.at-limit-tooltip",defaultMessage:"At limit: add seats to invite more users"}),side:"left",children:e.jsx(f,{width:"1.4rem",height:"1.4rem",fill:"danger500"})}),e.jsx(x,{ref:r,"data-testid":"create-user-button",startIcon:e.jsx(g,{}),size:"S",disabled:t,...i,children:s({id:"Settings.permissions.users.create",defaultMessage:"Invite new user"})})]})});export{w as CreateActionEE};
