import React, { useState, useEffect } from "react";

export default function ExportateurForm() {
  const [exportateurs, setExportateurs] = useState([]);
  const [formData, setFormData] = useState({
    raison_sociale: "",
    nom_contact: "",
    prenom_contact: "",
    matricule_fiscal: "",
    effectif: "",
    forme_juridique: "",
    forme_juridique_autre: "",
    statut: "",
    totalement_exportatrice: false,
    partiellement_exportatrice: false,
    adresse: "",
    gouvernorat: "",
    ville: "",
    code_postal: "",
    telephone_siege: "",
    mobile: "",
    email: "",
    secteur_activite: "",
  });

  useEffect(() => {
    fetch("http://localhost:1337/api/exportateurs?populate=*")
      .then((res) => res.json())
      .then((data) => setExportateurs(data.data))
      .catch((err) => console.error(err));
  }, []);

  function handleChange(e) {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  }

  function handleSubmit(e) {
    e.preventDefault();

    fetch("http://localhost:1337/api/exportateurs", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ data: formData }),
    })
      .then((res) => res.json())
      .then((data) => {
        setExportateurs([...exportateurs, data.data]);
        setFormData({
          raison_sociale: "",
          nom_contact: "",
          prenom_contact: "",
          matricule_fiscal: "",
          effectif: "",
          forme_juridique: "",
          forme_juridique_autre: "",
          statut: "",
          totalement_exportatrice: false,
          partiellement_exportatrice: false,
          adresse: "",
          gouvernorat: "",
          ville: "",
          code_postal: "",
          telephone_siege: "",
          mobile: "",
          email: "",
          secteur_activite: "",
        });
      })
      .catch((err) => console.error(err));
  }

  return (
    <div>
      <h2>Liste des Exportateurs</h2>
      <ul>
        {exportateurs.map((exp) => (
          <li key={exp.id}>{exp.attributes.raison_sociale}</li>
        ))}
      </ul>

      <h3>Ajouter un Exportateur</h3>
      <form onSubmit={handleSubmit}>
        <input
          name="raison_sociale"
          placeholder="Raison Sociale"
          value={formData.raison_sociale}
          onChange={handleChange}
          required
        />
        <input
          name="nom_contact"
          placeholder="Nom du Contact"
          value={formData.nom_contact}
          onChange={handleChange}
          required
        />
        <input
          name="prenom_contact"
          placeholder="Prénom du Contact"
          value={formData.prenom_contact}
          onChange={handleChange}
          required
        />
        <input
          name="matricule_fiscal"
          placeholder="Matricule Fiscal"
          value={formData.matricule_fiscal}
          onChange={handleChange}
          required
        />
        <input
          name="effectif"
          type="number"
          placeholder="Effectif"
          value={formData.effectif}
          onChange={handleChange}
          required
        />
        <select
          name="forme_juridique"
          value={formData.forme_juridique}
          onChange={handleChange}
          required
        >
          <option value="">--Forme Juridique--</option>
          <option value="S.A">S.A</option>
          <option value="S.A.R.L">S.A.R.L</option>
          <option value="S.U.A.R.L">S.U.A.R.L</option>
          <option value="Autre">Autre</option>
        </select>
        {formData.forme_juridique === "Autre" && (
          <input
            name="forme_juridique_autre"
            placeholder="Précisez la forme juridique"
            value={formData.forme_juridique_autre}
            onChange={handleChange}
          />
        )}
        <select
          name="statut"
          value={formData.statut}
          onChange={handleChange}
          required
        >
          <option value="">--Statut--</option>
          <option value="Résidente">Résidente</option>
          <option value="Non résidente">Non résidente</option>
        </select>
        <label>
          <input
            type="checkbox"
            name="totalement_exportatrice"
            checked={formData.totalement_exportatrice}
            onChange={handleChange}
          />
          Totalement Exportatrice
        </label>
        <label>
          <input
            type="checkbox"
            name="partiellement_exportatrice"
            checked={formData.partiellement_exportatrice}
            onChange={handleChange}
          />
          Partiellement Exportatrice
        </label>
        <input
          name="adresse"
          placeholder="Adresse"
          value={formData.adresse}
          onChange={handleChange}
          required
        />
        <input
          name="gouvernorat"
          placeholder="Gouvernorat"
          value={formData.gouvernorat}
          onChange={handleChange}
          required
        />
        <input
          name="ville"
          placeholder="Ville"
          value={formData.ville}
          onChange={handleChange}
          required
        />
        <input
          name="code_postal"
          placeholder="Code Postal"
          value={formData.code_postal}
          onChange={handleChange}
          required
        />
        <input
          name="telephone_siege"
          placeholder="Téléphone siège"
          value={formData.telephone_siege}
          onChange={handleChange}
          required
        />
        <input
          name="mobile"
          placeholder="Mobile"
          value={formData.mobile}
          onChange={handleChange}
        />
        <input
          name="email"
          placeholder="Email"
          type="email"
          value={formData.email}
          onChange={handleChange}
          required
        />
        <select
          name="secteur_activite"
          value={formData.secteur_activite}
          onChange={handleChange}
          required
        >
          <option value="">--Secteur d'activité--</option>
          <option value="Agro-alimentaire">Agro-alimentaire</option>
          <option value="Textile">Textile</option>
          <option value="IME">IME</option>
          <option value="Service">Service</option>
          <option value="Artisanat">Artisanat</option>
          <option value="Divers">Divers</option>
        </select>
        <button type="submit">Ajouter</button>
      </form>
    </div>
  );
}
