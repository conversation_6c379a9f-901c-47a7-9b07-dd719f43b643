import React, { useState, useEffect } from "react";

export default function ExportateurForm() {
  const [exportateurs, setExportateurs] = useState([]);
  const [formData, setFormData] = useState({
    raisonSociale: "",
    contact: "",
    matriculeFiscal: "",
    effectif: "",
    formeJuridique: "",
    formeJuridiqueAutre: "",
    statut: "",
    totalExportatrice: false,
    partielleExportatrice: false,
    adresse: "",
    gouvernorat: "",
    ville: "",
    codePostal: "",
    telephoneSiege: "",
    mobile: "",
    email: "",
    secteurActivite: "",
    produitsExportes: "",
  });

  useEffect(() => {
    fetch("http://localhost:1337/api/exportateurs?populate=*")
      .then((res) => res.json())
      .then((data) => setExportateurs(data.data))
      .catch((err) => console.error(err));
  }, []);

  function handleChange(e) {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  }

  function handleSubmit(e) {
    e.preventDefault();

    fetch("http://localhost:1337/api/exportateurs", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ data: formData }),
    })
      .then((res) => res.json())
      .then((data) => {
        setExportateurs([...exportateurs, data.data]);
        setFormData({
          raisonSociale: "",
          contact: "",
          matriculeFiscal: "",
          effectif: "",
          formeJuridique: "",
          formeJuridiqueAutre: "",
          statut: "",
          totalExportatrice: false,
          partielleExportatrice: false,
          adresse: "",
          gouvernorat: "",
          ville: "",
          codePostal: "",
          telephoneSiege: "",
          mobile: "",
          email: "",
          secteurActivite: "",
          produitsExportes: "",
        });
      })
      .catch((err) => console.error(err));
  }

  return (
    <div>
      <h2>Liste des Exportateurs</h2>
      <ul>
        {exportateurs.map((exp) => (
          <li key={exp.id}>{exp.attributes.raisonSociale}</li>
        ))}
      </ul>

      <h3>Ajouter un Exportateur</h3>
      <form onSubmit={handleSubmit}>
        <input
          name="raisonSociale"
          placeholder="Raison Sociale"
          value={formData.raisonSociale}
          onChange={handleChange}
          required
        />
        <input
          name="contact"
          placeholder="Contact"
          value={formData.contact}
          onChange={handleChange}
          required
        />
        <input
          name="matriculeFiscal"
          placeholder="Matricule Fiscal"
          value={formData.matriculeFiscal}
          onChange={handleChange}
          required
        />
        <input
          name="effectif"
          type="number"
          placeholder="Effectif"
          value={formData.effectif}
          onChange={handleChange}
          required
        />
        <select
          name="formeJuridique"
          value={formData.formeJuridique}
          onChange={handleChange}
          required
        >
          <option value="">--Forme Juridique--</option>
          <option value="S.A">S.A</option>
          <option value="S.A.R.L">S.A.R.L</option>
          <option value="S.U.A.R.L">S.U.A.R.L</option>
          <option value="Autre">Autre</option>
        </select>
        {formData.formeJuridique === "Autre" && (
          <input
            name="formeJuridiqueAutre"
            placeholder="Précisez la forme juridique"
            value={formData.formeJuridiqueAutre}
            onChange={handleChange}
          />
        )}
        <select
          name="statut"
          value={formData.statut}
          onChange={handleChange}
          required
        >
          <option value="">--Statut--</option>
          <option value="Résidente">Résidente</option>
          <option value="Non Résidente">Non Résidente</option>
        </select>
        <label>
          <input
            type="checkbox"
            name="totalExportatrice"
            checked={formData.totalExportatrice}
            onChange={handleChange}
          />
          Totalement Exportatrice
        </label>
        <label>
          <input
            type="checkbox"
            name="partielleExportatrice"
            checked={formData.partielleExportatrice}
            onChange={handleChange}
          />
          Partiellement Exportatrice
        </label>
        <input
          name="adresse"
          placeholder="Adresse"
          value={formData.adresse}
          onChange={handleChange}
          required
        />
        <input
          name="gouvernorat"
          placeholder="Gouvernorat"
          value={formData.gouvernorat}
          onChange={handleChange}
          required
        />
        <input
          name="ville"
          placeholder="Ville"
          value={formData.ville}
          onChange={handleChange}
          required
        />
        <input
          name="codePostal"
          placeholder="Code Postal"
          value={formData.codePostal}
          onChange={handleChange}
          required
        />
        <input
          name="telephoneSiege"
          placeholder="Téléphone siège"
          value={formData.telephoneSiege}
          onChange={handleChange}
          required
        />
        <input
          name="mobile"
          placeholder="Mobile"
          value={formData.mobile}
          onChange={handleChange}
          required
        />
        <input
          name="email"
          placeholder="Email"
          type="email"
          value={formData.email}
          onChange={handleChange}
          required
        />
        <input
          name="secteurActivite"
          placeholder="Secteur d'activité"
          value={formData.secteurActivite}
          onChange={handleChange}
          required
        />
        <input
          name="produitsExportes"
          placeholder="Produits Exportés"
          value={formData.produitsExportes}
          onChange={handleChange}
        />
        <button type="submit">Ajouter</button>
      </form>
    </div>
  );
}
