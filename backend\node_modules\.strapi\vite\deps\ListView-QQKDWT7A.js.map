{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/TransferTokens/ListView.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport * as qs from 'qs';\nimport { useIntl } from 'react-intl';\nimport { Link, useNavigate } from 'react-router-dom';\n\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useTracking } from '../../../../features/Tracking';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useOnce } from '../../../../hooks/useOnce';\nimport { useRBAC } from '../../../../hooks/useRBAC';\nimport {\n  useDeleteTransferTokenMutation,\n  useGetTransferTokensQuery,\n} from '../../../../services/transferTokens';\nimport { TRANSFER_TOKEN_TYPE } from '../../components/Tokens/constants';\nimport { Table } from '../../components/Tokens/Table';\n\nimport type { Data } from '@strapi/types';\n\nconst tableHeaders = [\n  {\n    name: 'name',\n    label: {\n      id: 'Settings.tokens.ListView.headers.name',\n      defaultMessage: 'Name',\n    },\n    sortable: true,\n  },\n  {\n    name: 'description',\n    label: {\n      id: 'Settings.tokens.ListView.headers.description',\n      defaultMessage: 'Description',\n    },\n    sortable: false,\n  },\n  {\n    name: 'createdAt',\n    label: {\n      id: 'Settings.tokens.ListView.headers.createdAt',\n      defaultMessage: 'Created at',\n    },\n    sortable: false,\n  },\n  {\n    name: 'lastUsedAt',\n    label: {\n      id: 'Settings.tokens.ListView.headers.lastUsedAt',\n      defaultMessage: 'Last used',\n    },\n    sortable: false,\n  },\n] as const;\n\n/* -------------------------------------------------------------------------------------------------\n * ListView\n * -----------------------------------------------------------------------------------------------*/\n\nconst ListView = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['transfer-tokens']\n  );\n  const {\n    isLoading: isLoadingRBAC,\n    allowedActions: { canCreate, canDelete, canUpdate, canRead },\n  } = useRBAC(permissions);\n  const navigate = useNavigate();\n  const { trackUsage } = useTracking();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  React.useEffect(() => {\n    navigate({ search: qs.stringify({ sort: 'name:ASC' }, { encode: false }) });\n  }, [navigate]);\n\n  useOnce(() => {\n    trackUsage('willAccessTokenList', {\n      tokenType: TRANSFER_TOKEN_TYPE,\n    });\n  });\n\n  const headers = tableHeaders.map((header) => ({\n    ...header,\n    label: formatMessage(header.label),\n  }));\n\n  const {\n    data: transferTokens = [],\n    isLoading: isLoadingTokens,\n    error,\n  } = useGetTransferTokensQuery(undefined, {\n    skip: !canRead,\n  });\n\n  React.useEffect(() => {\n    if (transferTokens) {\n      trackUsage('didAccessTokenList', {\n        number: transferTokens.length,\n        tokenType: TRANSFER_TOKEN_TYPE,\n      });\n    }\n  }, [trackUsage, transferTokens]);\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  const [deleteToken] = useDeleteTransferTokenMutation();\n\n  const handleDelete = async (id: Data.ID) => {\n    try {\n      const res = await deleteToken(id);\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n      }\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n      });\n    }\n  };\n\n  const isLoading = isLoadingTokens || isLoadingRBAC;\n\n  return (\n    <>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Transfer Tokens',\n          }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        title={formatMessage({\n          id: 'Settings.transferTokens.title',\n          defaultMessage: 'Transfer Tokens',\n        })}\n        subtitle={formatMessage({\n          id: 'Settings.transferTokens.description',\n          defaultMessage: '\"List of generated transfer tokens\"', // TODO change this message\n        })}\n        primaryAction={\n          canCreate ? (\n            <LinkButton\n              role=\"button\"\n              tag={Link}\n              data-testid=\"create-transfer-token-button\"\n              startIcon={<Plus />}\n              size=\"S\"\n              onClick={() =>\n                trackUsage('willAddTokenFromList', {\n                  tokenType: TRANSFER_TOKEN_TYPE,\n                })\n              }\n              to=\"/settings/transfer-tokens/create\"\n            >\n              {formatMessage({\n                id: 'Settings.transferTokens.create',\n                defaultMessage: 'Create new Transfer Token',\n              })}\n            </LinkButton>\n          ) : undefined\n        }\n      />\n      {!canRead ? (\n        <Page.NoPermissions />\n      ) : (\n        <Page.Main aria-busy={isLoading}>\n          <Layouts.Content>\n            {transferTokens.length > 0 && (\n              <Table\n                permissions={{ canRead, canDelete, canUpdate }}\n                headers={headers}\n                isLoading={isLoading}\n                onConfirmDelete={handleDelete}\n                tokens={transferTokens}\n                tokenType={TRANSFER_TOKEN_TYPE}\n              />\n            )}\n            {canCreate && transferTokens.length === 0 ? (\n              <EmptyStateLayout\n                action={\n                  <LinkButton\n                    tag={Link}\n                    variant=\"secondary\"\n                    startIcon={<Plus />}\n                    to=\"/settings/transfer-tokens/create\"\n                  >\n                    {formatMessage({\n                      id: 'Settings.transferTokens.addNewToken',\n                      defaultMessage: 'Add new Transfer Token',\n                    })}\n                  </LinkButton>\n                }\n                icon={<EmptyDocuments width=\"16rem\" />}\n                content={formatMessage({\n                  id: 'Settings.transferTokens.addFirstToken',\n                  defaultMessage: 'Add your first Transfer Token',\n                })}\n              />\n            ) : null}\n            {!canCreate && transferTokens.length === 0 ? (\n              <EmptyStateLayout\n                icon={<EmptyDocuments width=\"16rem\" />}\n                content={formatMessage({\n                  id: 'Settings.transferTokens.emptyStateLayout',\n                  defaultMessage: 'You don’t have any content yet...',\n                })}\n              />\n            ) : null}\n          </Layouts.Content>\n        </Page.Main>\n      )}\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedListView\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedListView = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['transfer-tokens'].main\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListView />\n    </Page.Protect>\n  );\n};\n\nexport { ListView, ProtectedListView };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,IAAMA,eAAe;EACnB;IACEC,MAAM;IACNC,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,UAAU;EACZ;EACA;IACEJ,MAAM;IACNC,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,UAAU;EACZ;EACA;IACEJ,MAAM;IACNC,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,UAAU;EACZ;EACA;IACEJ,MAAM;IACNC,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,UAAU;EACZ;AACD;AAIiG,IAE5FC,WAAW,MAAA;AACf,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,uBAAMC,UAAUH,YAAYI,aAA5BF,mBAAuC;GAAkB;AAEtE,QAAM,EACJG,WAAWC,eACXC,gBAAgB,EAAEC,WAAWC,WAAWC,WAAWC,QAAO,EAAE,IAC1DC,QAAQZ,WAAAA;AACZ,QAAMa,WAAWC,YAAAA;AACjB,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpDC,EAAMC,gBAAU,MAAA;AACdR,aAAS;MAAES,QAAWC,aAAU;QAAEC,MAAM;SAAc;QAAEC,QAAQ;MAAM,CAAA;IAAG,CAAA;KACxE;IAACZ;EAAS,CAAA;AAEba,UAAQ,MAAA;AACNX,eAAW,uBAAuB;MAChCY,WAAWC;IACb,CAAA;EACF,CAAA;AAEA,QAAMC,UAAUxC,aAAayC,IAAI,CAACC,YAAY;IAC5C,GAAGA;IACHxC,OAAOK,cAAcmC,OAAOxC,KAAK;IACnC;AAEA,QAAM,EACJyC,MAAMC,iBAAiB,CAAA,GACvB5B,WAAW6B,iBACXC,MAAK,IACHC,0BAA0BC,QAAW;IACvCC,MAAM,CAAC3B;EACT,CAAA;AAEAS,EAAMC,gBAAU,MAAA;AACd,QAAIY,gBAAgB;AAClBlB,iBAAW,sBAAsB;QAC/BwB,QAAQN,eAAeO;QACvBb,WAAWC;MACb,CAAA;IACF;KACC;IAACb;IAAYkB;EAAe,CAAA;AAE/Bb,EAAMC,gBAAU,MAAA;AACd,QAAIc,OAAO;AACTrC,yBAAmB;QACjB2C,MAAM;QACNC,SAASxB,eAAeiB,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAOjB;IAAgBpB;EAAmB,CAAA;AAE9C,QAAM,CAAC6C,WAAAA,IAAeC,+BAAAA;AAEtB,QAAMC,eAAe,OAAOrD,OAAAA;AAC1B,QAAI;AACF,YAAMsD,MAAM,MAAMH,YAAYnD,EAAAA;AAE9B,UAAI,WAAWsD,KAAK;AAClBhD,2BAAmB;UACjB2C,MAAM;UACNC,SAASxB,eAAe4B,IAAIX,KAAK;QACnC,CAAA;MACF;IACF,QAAQ;AACNrC,yBAAmB;QACjB2C,MAAM;QACNC,SAAS9C,cAAc;UAAEJ,IAAI;UAAsBC,gBAAgB;QAAmB,CAAA;MACxF,CAAA;IACF;EACF;AAEA,QAAMY,YAAY6B,mBAAmB5B;AAErC,aACEyC,yBAAAC,6BAAA;;UACEC,wBAACC,KAAKC,OAAK;kBACRvD,cACC;UAAEJ,IAAI;UAAsBC,gBAAgB;WAC5C;UACEH,MAAM;QACR,CAAA;;UAGJ2D,wBAACG,QAAQC,QAAM;QACbC,OAAO1D,cAAc;UACnBJ,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACA8D,UAAU3D,cAAc;UACtBJ,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACA+D,eACEhD,gBACEyC,wBAACQ,YAAAA;UACCC,MAAK;UACLC,KAAKC;UACLC,eAAY;UACZC,eAAWb,wBAACc,eAAAA,CAAAA,CAAAA;UACZC,MAAK;UACLC,SAAS,MACPlD,WAAW,wBAAwB;YACjCY,WAAWC;UACb,CAAA;UAEFsC,IAAG;oBAEFtE,cAAc;YACbJ,IAAI;YACJC,gBAAgB;UAClB,CAAA;QAEA4C,CAAAA,IAAAA;;MAGP,CAAC1B,cACAsC,wBAACC,KAAKiB,eAEN,CAAA,CAAA,QAAAlB,wBAACC,KAAKkB,MAAI;QAACC,aAAWhE;sBACpB0C,yBAACK,QAAQkB,SAAO;;YACbrC,eAAeO,SAAS,SACvBS,wBAACsB,OAAAA;cACCvE,aAAa;gBAAEW;gBAASF;gBAAWC;cAAU;cAC7CmB;cACAxB;cACAmE,iBAAiB3B;cACjB4B,QAAQxC;cACRN,WAAWC;;YAGdpB,aAAayB,eAAeO,WAAW,QACtCS,wBAACyB,kBAAAA;cACCC,YACE1B,wBAACQ,YAAAA;gBACCE,KAAKC;gBACLgB,SAAQ;gBACRd,eAAWb,wBAACc,eAAAA,CAAAA,CAAAA;gBACZG,IAAG;0BAEFtE,cAAc;kBACbJ,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;cAGJoF,UAAM5B,wBAAC6B,cAAAA;gBAAeC,OAAM;;cAC5BC,SAASpF,cAAc;gBACrBJ,IAAI;gBACJC,gBAAgB;cAClB,CAAA;YAEA,CAAA,IAAA;YACH,CAACe,aAAayB,eAAeO,WAAW,QACvCS,wBAACyB,kBAAAA;cACCG,UAAM5B,wBAAC6B,cAAAA;gBAAeC,OAAM;;cAC5BC,SAASpF,cAAc;gBACrBJ,IAAI;gBACJC,gBAAgB;cAClB,CAAA;YAEA,CAAA,IAAA;;;;;;AAMhB;AAIkG,IAE5FwF,oBAAoB,MAAA;AACxB,QAAMjF,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,uBAAMC,UAAUH,YAAYI,aAA5BF,mBAAuC,mBAAmBgF;GAAAA;AAGvE,aACEjC,wBAACC,KAAKiC,SAAO;IAACnF;IACZ,cAAAiD,wBAACtD,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["tableHeaders", "name", "label", "id", "defaultMessage", "sortable", "ListView", "formatMessage", "useIntl", "toggleNotification", "useNotification", "permissions", "useTypedSelector", "state", "admin_app", "settings", "isLoading", "isLoadingRBAC", "allowedActions", "canCreate", "canDelete", "canUpdate", "canRead", "useRBAC", "navigate", "useNavigate", "trackUsage", "useTracking", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "React", "useEffect", "search", "stringify", "sort", "encode", "useOnce", "tokenType", "TRANSFER_TOKEN_TYPE", "headers", "map", "header", "data", "transferTokens", "isLoadingTokens", "error", "useGetTransferTokensQuery", "undefined", "skip", "number", "length", "type", "message", "deleteToken", "useDeleteTransferTokenMutation", "handleDelete", "res", "_jsxs", "_Fragment", "_jsx", "Page", "Title", "Layouts", "Header", "title", "subtitle", "primaryAction", "LinkButton", "role", "tag", "Link", "data-testid", "startIcon", "Plus", "size", "onClick", "to", "NoPermissions", "Main", "aria-busy", "Content", "Table", "onConfirmDelete", "tokens", "EmptyStateLayout", "action", "variant", "icon", "EmptyDocuments", "width", "content", "ProtectedListView", "main", "Protect"]}