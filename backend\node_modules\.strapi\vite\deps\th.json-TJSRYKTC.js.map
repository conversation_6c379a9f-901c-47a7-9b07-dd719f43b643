{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/th.json.mjs"], "sourcesContent": ["var groups = \"กลุ่ม\";\nvar models = \"ชนิดของคอลเล็กชัน\";\nvar pageNotFound = \"ไม่พบหน้า\";\nvar th = {\n    \"EditRelations.title\": \"ข้อมูลเชิงสัมพันธ์\",\n    \"api.id\": \"รหัส API\",\n    \"components.AddFilterCTA.add\": \"ตัวกรอง\",\n    \"components.AddFilterCTA.hide\": \"ตัวกรอง\",\n    \"components.DraggableAttr.edit\": \"คลิกเพื่อแก้ไข\",\n    \"components.DynamicZone.pick-compo\": \"เลือกหนึ่งคอมโพเนนต์\",\n    \"components.DynamicZone.required\": \"ต้องการคอมโพเนนต์\",\n    \"components.EmptyAttributesBlock.button\": \"ไปที่หน้าค่าติดตั้ง\",\n    \"components.EmptyAttributesBlock.description\": \"คุณสามารถเปลี่ยนค่าติดตั้งของคุณ\",\n    \"components.FieldItem.linkToComponentLayout\": \"ตั้งค่าโครงร่างของคอมโพเนนต์\",\n    \"components.FilterOptions.button.apply\": \"นำไปใช้\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"นำไปใช้\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"ล้างข้อมูลทั้งหมด\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"ตั้งค่าเงื่อนไขเพื่อใช้เพื่อกรองรายการต่างๆ\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"ตัวกรอง\",\n    \"components.FiltersPickWrapper.hide\": \"ซ่อน\",\n    \"components.LimitSelect.itemsPerPage\": \"ไอเท็มต่อเพจ\",\n    \"components.NotAllowedInput.text\": \"ไม่มีสิทธิในการดูฟิลด์นี้\",\n    \"components.Search.placeholder\": \"ค้นหารายการ...\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"กำหนดวิธีที่มุมมองแก้ไขจะมีลักษณะเหมือน\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"กำหนดค่าติดตั้งของมุมมองรายการ\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"กำหนดคอนฟิกมุมมอง - {name}\",\n    \"components.TableDelete.delete\": \"ลบทั้งหมด\",\n    \"components.TableDelete.deleteSelected\": \"ลบที่เลือกไว้\",\n    \"components.TableEmpty.withFilters\": \"ไม่มี {contentType} ตามตัวกรองที่ใช้ ...\",\n    \"components.TableEmpty.withSearch\": \"ไม่มี {contentType} ที่สอดคล้องกับการค้นหา ({search})...\",\n    \"components.TableEmpty.withoutFilter\": \"ไม่มี {contentType}...\",\n    \"components.empty-repeatable\": \"ยังไม่มีข้อมูล คลิกที่ปุ่มด้านล่างเพื่อเพิ่มข้อมูลเข้าไป\",\n    \"components.notification.info.maximum-requirement\": \"คุณได้มาถึงจำนวนสูงสุดของฟิลด์แล้ว\",\n    \"components.notification.info.minimum-requirement\": \"ฟิลด์ถูกเพิ่มให้ตรงกับข้อกำหนดต่ำสุด\",\n    \"components.reset-entry\": \"รีเซ็ตรายการ\",\n    \"components.uid.apply\": \"สมัคร\",\n    \"components.uid.available\": \"ที่พร้อมใช้งาน\",\n    \"components.uid.regenerate\": \"สร้างใหม่อีกครั้ง\",\n    \"components.uid.suggested\": \"ค่าแนะนำ\",\n    \"components.uid.unavailable\": \"ไม่ว่าง\",\n    \"containers.Edit.Link.Layout\": \"กำหนดคอนฟิกโครงร่าง\",\n    \"containers.Edit.Link.Model\": \"แก้ไขชนิดของคอลเล็กชัน\",\n    \"containers.Edit.addAnItem\": \"เพิ่มไอเท็ม...\",\n    \"containers.Edit.clickToJump\": \"คลิกเพื่อข้ามไปยังรายการ\",\n    \"containers.Edit.delete\": \"ลบ\",\n    \"containers.Edit.editing\": \"กำลังแก้ไข...\",\n    \"containers.Edit.pluginHeader.title.new\": \"สร้างรายการ\",\n    \"containers.Edit.reset\": \"รีเซ็ต\",\n    \"containers.Edit.returnList\": \"กลับไปที่รายการ\",\n    \"containers.Edit.seeDetails\": \"รายละเอียด\",\n    \"containers.Edit.submit\": \"บันทึก\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"แก้ไขฟิลด์\",\n    \"containers.EditView.notification.errors\": \"ฟอร์มมีข้อผิดพลาดบางอย่าง\",\n    \"containers.Home.introduction\": \"หากต้องการแก้ไขรายการของคุณให้ไปยังลิงก์ที่ระบุเฉพาะในเมนูด้านซ้าย ปลั๊กอินนี้ไม่มีวิธีที่เหมาะสมในการแก้ไขการตั้งค่าและยังคงอยู่ภายใต้การพัฒนา\",\n    \"containers.Home.pluginHeaderDescription\": \"จัดการรายการของคุณโดยใช้อินเตอร์เฟสที่ทรงพลังและสวยงาม\",\n    \"containers.Home.pluginHeaderTitle\": \"ตัวจัดการเนื้อหา\",\n    \"containers.List.errorFetchRecords\": \"ข้อผิดพลาด\",\n    \"containers.list.displayedFields\": \"ฟิลด์ที่แสดง\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"แก้ไขเลเบล\",\n    \"containers.SettingPage.add.field\": \"แทรกฟิลด์อื่น\",\n    \"containers.SettingPage.attributes\": \"ฟิลด์ Attributes\",\n    \"containers.SettingPage.attributes.description\": \"กำหนดลำดับของแอ็ตทริบิวต์\",\n    \"containers.SettingPage.editSettings.description\": \"ลาก & ปล่อยฟิลด์เพื่อสร้างโครงร่าง\",\n    \"containers.SettingPage.editSettings.entry.title\": \"ชื่อรายการ\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"ตั้งค่าฟิลด์ที่แสดงของรายการของคุณ\",\n    \"containers.SettingPage.editSettings.title\": \"มุมมองแก้ไข (ค่าติดตั้ง)\",\n    \"containers.SettingPage.layout\": \"โครงร่าง\",\n    \"containers.SettingPage.listSettings.description\": \"กำหนดคอนฟิกอ็อพชันสำหรับชนิดคอลเล็กชันนี้\",\n    \"containers.SettingPage.listSettings.title\": \"มุมมองรายการ (การตั้งค่า)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"กำหนดคอนฟิกค่าติดตั้งเฉพาะสำหรับชนิดคอลเล็กชันนี้\",\n    \"containers.SettingPage.settings\": \"การตั้งค่า\",\n    \"containers.SettingPage.view\": \"ดู\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Content Manager - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"กำหนดค่าค่าติดตั้งเฉพาะ\",\n    \"containers.SettingsPage.Block.contentType.title\": \"ชนิดของคอลเล็กชัน\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"กำหนดคอนฟิกค่าพื้นฐานสำหรับชนิดคอลเล็กชันของคุณ\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"ทั่วไป\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"กำหนดคอนฟิกค่าติดตั้งสำหรับชนิดและกลุ่มคอลเล็กชันของคุณทั้งหมด\",\n    \"containers.SettingsView.list.subtitle\": \"กำหนดคอนฟิกโครงร่างและการแสดงชนิดคอลเล็กชันและกลุ่มของคุณ\",\n    \"containers.SettingsView.list.title\": \"แสดงการตั้งค่า\",\n    \"emptyAttributes.button\": \"ไปที่ตัวสร้างชนิดคอลเล็กชัน\",\n    \"emptyAttributes.description\": \"เพิ่มฟิลด์แรกของคุณลงในชนิดคอลเล็กชันของคุณ\",\n    \"emptyAttributes.title\": \"ยังไม่มีฟิลด์\",\n    \"error.attribute.key.taken\": \"ค่านี้มีอยู่แล้ว\",\n    \"error.attribute.sameKeyAndName\": \"ไม่สามารถเท่ากับ\",\n    \"error.attribute.taken\": \"ชื่อฟิลด์นี้มีอยู่แล้ว\",\n    \"error.contentTypeName.taken\": \"ชื่อนี้มีอยู่แล้ว\",\n    \"error.model.fetch\": \"มีข้อผิดพลาดเกิดขึ้นระหว่างการดึงข้อมูลคอนฟิกโมเดล\",\n    \"error.record.create\": \"มีข้อผิดพลาดเกิดขึ้นระหว่างการสร้างเร็กคอร์ด\",\n    \"error.record.delete\": \"มีข้อผิดพลาดเกิดขึ้นระหว่างการลบเร็กคอร์ด\",\n    \"error.record.fetch\": \"มีข้อผิดพลาดเกิดขึ้นระหว่างการดึงข้อมูลเร็กคอร์ด\",\n    \"error.record.update\": \"มีข้อผิดพลาดเกิดขึ้นระหว่างการอัพเดตเร็กคอร์ด\",\n    \"error.records.count\": \"มีข้อผิดพลาดเกิดขึ้นระหว่างการดึงข้อมูลจำนวนนับเร็กคอร์ด\",\n    \"error.records.fetch\": \"มีข้อผิดพลาดเกิดขึ้นระหว่างการดึงข้อมูลเร็กคอร์ด\",\n    \"error.schema.generation\": \"มีข้อผิดพลาดเกิดขึ้นระหว่างการสร้างสกีมา\",\n    \"error.validation.json\": \"นี่ไม่ใช่ JSON ้\",\n    \"error.validation.max\": \"ค่านั้นสูงเกินไป\",\n    \"error.validation.maxLength\": \"ค่ามีขนาดยาวเกินไป\",\n    \"error.validation.min\": \"ค่านี้ต่ำเกินไป\",\n    \"error.validation.minLength\": \"ค่านี้สั้นเกินไป\",\n    \"error.validation.minSupMax\": \"ไม่สามารถมากกว่า\",\n    \"error.validation.regex\": \"ค่าไม่ตรงกับ regex\",\n    \"error.validation.required\": \"ค่าที่ป้อนนี้จำเป็นต้องมี\",\n    \"form.Input.bulkActions\": \"เปิดใช้งานแอ็คชันขนาดใหญ่\",\n    \"form.Input.defaultSort\": \"แอ็ตทริบิวต์การเรียงเริ่มต้น\",\n    \"form.Input.description\": \"รายละเอียด\",\n    \"form.Input.description.placeholder\": \"แสดงชื่อในโปรไฟล์\",\n    \"form.Input.editable\": \"ฟิลด์แก้ไขได้\",\n    \"form.Input.filters\": \"เปิดใช้งานตัวกรอง\",\n    \"form.Input.label\": \"เลเบล\",\n    \"form.Input.label.inputDescription\": \"ค่านี้แทนที่เลเบลที่แสดงในส่วนหัวของตาราง\",\n    \"form.Input.pageEntries\": \"รายการต่อหน้า\",\n    \"form.Input.pageEntries.inputDescription\": \"หมายเหตุ: คุณสามารถแทนที่ค่านี้ในหน้าค่าติดตั้งชนิดคอลเล็กชัน\",\n    \"form.Input.placeholder\": \"ตัวอย่าง\",\n    \"form.Input.placeholder.placeholder\": \"ค่าของฉัน\",\n    \"form.Input.search\": \"เปิดใช้งานการค้นหา\",\n    \"form.Input.search.field\": \"เปิดใช้งานการค้นหาบนฟิลด์นี้\",\n    \"form.Input.sort.field\": \"เปิดใช้งานการเรียงลำดับบนฟิลด์นี้\",\n    \"form.Input.wysiwyg\": \"แสดงเป็น WYSIWYG\",\n    \"global.displayedFields\": \"ฟิลด์ที่แสดง\",\n    groups: groups,\n    \"groups.numbered\": \"กลุ่ม ({number})\",\n    models: models,\n    \"models.numbered\": \"ชนิดคอลเล็กชัน ({number})\",\n    \"notification.error.displayedFields\": \"คุณต้องการฟิลด์ที่แสดงอย่างน้อยหนึ่งฟิลด์\",\n    \"notification.error.relationship.fetch\": \"มีข้อผิดพลาดเกิดขึ้นระหว่างการดึงข้อมูลความสัมพันธ์\",\n    \"notification.info.SettingPage.disableSort\": \"คุณต้องมีหนึ่งแอ็ตทริบิวต์ที่มีการเรียงลำดับที่อนุญาต\",\n    \"notification.info.minimumFields\": \"คุณต้องมีการแสดงฟิลด์อย่างน้อยหนึ่งฟิลด์\",\n    \"notification.upload.error\": \"มีข้อผิดพลาดเกิดขึ้นขณะอัพโหลดไฟล์ของคุณ\",\n    pageNotFound: pageNotFound,\n    \"permissions.not-allowed.create\": \"คุณไม่ได้รับอนุญาตให้สร้างเอกสาร\",\n    \"permissions.not-allowed.update\": \"คุณไม่ได้รับอนุญาตให้ดูเอกสารนี้\",\n    \"plugin.description.long\": \"วิธีที่รวดเร็วในการดูแก้ไขและลบข้อมูลในฐานข้อมูลของคุณ\",\n    \"plugin.description.short\": \"วิธีที่รวดเร็วในการดูแก้ไขและลบข้อมูลในฐานข้อมูลของคุณ\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"คุณแน่ใจว่าต้องการลบรายการนี้หรือไม่?\",\n    \"popUpWarning.bodyMessage.contentType.delete.all\": \"คุณแน่ใจว่าคุณต้องการลบรายการเหล่านี้หรือไม่?\",\n    \"popUpWarning.warning.cancelAllSettings\": \"คุณแน่ใจว่าต้องการยกเลิกการแก้ไขของคุณหรือไม่?\",\n    \"popUpWarning.warning.updateAllSettings\": \"สิ่งนี้จะปรับเปลี่ยนค่าติดตั้งทั้งหมดของคุณ\",\n    \"success.record.delete\": \"ลบแล้ว\",\n    \"success.record.save\": \"ถูกบันทึก\"\n};\n\nexport { th as default, groups, models, pageNotFound };\n//# sourceMappingURL=th.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,uBAAuB;AAAA,EACvB,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,uBAAuB;AAC3B;", "names": []}