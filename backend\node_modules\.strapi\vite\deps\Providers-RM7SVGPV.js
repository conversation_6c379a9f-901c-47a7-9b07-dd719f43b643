import {
  require_prop_types
} from "./chunk-ZYDILIPQ.js";
import "./chunk-WHSGG4JJ.js";
import "./chunk-AK5CFNWL.js";
import "./chunk-3AV7AZT7.js";
import "./chunk-TQNFYFKF.js";
import "./chunk-RCW4U6TM.js";
import "./chunk-7FKQZHO7.js";
import "./chunk-WYIFJTEX.js";
import "./chunk-CKNI3O63.js";
import "./chunk-UVPT7Z34.js";
import "./chunk-VH65KUX4.js";
import "./chunk-WJB72J7I.js";
import "./chunk-YGWPNWDV.js";
import "./chunk-ONVRVBQ5.js";
import "./chunk-WRTY2ETJ.js";
import "./chunk-PDGPTUUZ.js";
import "./chunk-PD7BZCZJ.js";
import "./chunk-4KYMBCCA.js";
import "./chunk-6RRYDBGC.js";
import "./chunk-7UKZKMUX.js";
import "./chunk-UIEUQC33.js";
import "./chunk-6LY4MOO2.js";
import "./chunk-S4UKOROC.js";
import "./chunk-MJKVBYJW.js";
import "./chunk-BSVBHESH.js";
import "./chunk-JVGUDHAQ.js";
import "./chunk-HIZVCZYI.js";
import "./chunk-H4GPAAVJ.js";
import "./chunk-MLDZODJM.js";
import "./chunk-C7H2BX76.js";
import "./chunk-SYWYLB7I.js";
import "./chunk-OXKL46WM.js";
import "./chunk-GXEE7KZL.js";
import "./chunk-6EC7MKBK.js";
import "./chunk-XNACAI67.js";
import {
  useMutation,
  useQuery,
  useQueryClient
} from "./chunk-55Q6LON3.js";
import "./chunk-QVDAYSOU.js";
import "./chunk-A34HN3WE.js";
import "./chunk-APY4KZ5L.js";
import "./chunk-AGIQEOXA.js";
import "./chunk-OOAHAGSN.js";
import "./chunk-BEZCWAXF.js";
import "./chunk-MFYBRT3Z.js";
import "./chunk-73ABBQBL.js";
import "./chunk-WNXMQTNQ.js";
import "./chunk-VJ3LKUI5.js";
import {
  useFetchClient
} from "./chunk-3OKC5SXR.js";
import "./chunk-ITIYAEI7.js";
import "./chunk-K4LYEYIN.js";
import "./chunk-XGEFBNZK.js";
import "./chunk-YW3XCEFV.js";
import "./chunk-MBK4V2X7.js";
import {
  require_isEmpty
} from "./chunk-YJEURQPS.js";
import "./chunk-UCU7ROGU.js";
import "./chunk-GGK2TLCV.js";
import "./chunk-K65KIEAL.js";
import "./chunk-F5JI4FJS.js";
import {
  Form,
  Formik
} from "./chunk-TFPYBHFW.js";
import "./chunk-MMKYPT4K.js";
import "./chunk-U63NXCTP.js";
import "./chunk-SGQXSZWC.js";
import {
  errorsTrads
} from "./chunk-IFOFBKTA.js";
import {
  create3 as create,
  create4 as create2,
  create6 as create3,
  require_upperFirst
} from "./chunk-7US6K6XC.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-DZIOEGNZ.js";
import "./chunk-L3WQFZOD.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-67LA7IEH.js";
import "./chunk-UHKKWDMK.js";
import "./chunk-GM54BMM2.js";
import "./chunk-2DNMQP4H.js";
import "./chunk-L32VSWBJ.js";
import "./chunk-XVW7MCOW.js";
import "./chunk-2U7FOQNK.js";
import {
  useRBAC
} from "./chunk-WIFIVZU3.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import "./chunk-T3UNFN7Y.js";
import {
  Layouts
} from "./chunk-DETWX3NC.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  Page,
  useAPIErrorHandler
} from "./chunk-FTSHQ5RF.js";
import "./chunk-RZWN45NY.js";
import "./chunk-FOUXGIF2.js";
import "./chunk-TXOJWASW.js";
import "./chunk-BUMITFEB.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-PW6GS6S3.js";
import {
  Breadcrumbs,
  Button,
  Crumb,
  Field,
  Flex,
  Grid,
  IconButton,
  Modal,
  Table,
  Tbody,
  Td,
  TextInput,
  Th,
  Thead,
  Toggle,
  Tr,
  Typography,
  VisuallyHidden,
  useCollator,
  useIntl
} from "./chunk-Y6YT4U2T.js";
import "./chunk-5ZC4PE57.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1v
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  PERMISSIONS,
  getTrad
} from "./chunk-DJJSG3NG.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Providers/index.mjs
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_upperFirst = __toESM(require_upperFirst(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/FormModal/index.mjs
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var import_react2 = __toESM(require_react(), 1);
var import_prop_types2 = __toESM(require_prop_types(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/FormModal/Input/index.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);
var Input = ({ description, disabled, intlLabel, error, name, onChange, placeholder, providerToEditName, type, value }) => {
  const { formatMessage } = useIntl();
  const inputValue = name === "noName" ? `${window.strapi.backendURL}/api/connect/${providerToEditName}/callback` : value;
  const label = formatMessage({
    id: intlLabel.id,
    defaultMessage: intlLabel.defaultMessage
  }, {
    provider: providerToEditName,
    ...intlLabel.values
  });
  const hint = description ? formatMessage({
    id: description.id,
    defaultMessage: description.defaultMessage
  }, {
    provider: providerToEditName,
    ...description.values
  }) : "";
  if (type === "bool") {
    return (0, import_jsx_runtime.jsxs)(Field.Root, {
      hint,
      name,
      children: [
        (0, import_jsx_runtime.jsx)(Field.Label, {
          children: label
        }),
        (0, import_jsx_runtime.jsx)(Toggle, {
          "aria-label": name,
          checked: value,
          disabled,
          offLabel: formatMessage({
            id: "app.components.ToggleCheckbox.off-label",
            defaultMessage: "Off"
          }),
          onLabel: formatMessage({
            id: "app.components.ToggleCheckbox.on-label",
            defaultMessage: "On"
          }),
          onChange: (e) => {
            onChange({
              target: {
                name,
                value: e.target.checked
              }
            });
          }
        }),
        (0, import_jsx_runtime.jsx)(Field.Hint, {})
      ]
    });
  }
  const formattedPlaceholder = placeholder ? formatMessage({
    id: placeholder.id,
    defaultMessage: placeholder.defaultMessage
  }, {
    ...placeholder.values
  }) : "";
  const errorMessage = error ? formatMessage({
    id: error,
    defaultMessage: error
  }) : "";
  return (0, import_jsx_runtime.jsxs)(Field.Root, {
    error: errorMessage,
    name,
    children: [
      (0, import_jsx_runtime.jsx)(Field.Label, {
        children: label
      }),
      (0, import_jsx_runtime.jsx)(TextInput, {
        disabled,
        onChange,
        placeholder: formattedPlaceholder,
        type,
        value: inputValue
      }),
      (0, import_jsx_runtime.jsx)(Field.Error, {})
    ]
  });
};
Input.defaultProps = {
  description: null,
  disabled: false,
  error: "",
  placeholder: null,
  value: ""
};
Input.propTypes = {
  description: import_prop_types.default.shape({
    id: import_prop_types.default.string.isRequired,
    defaultMessage: import_prop_types.default.string.isRequired,
    values: import_prop_types.default.object
  }),
  disabled: import_prop_types.default.bool,
  error: import_prop_types.default.string,
  intlLabel: import_prop_types.default.shape({
    id: import_prop_types.default.string.isRequired,
    defaultMessage: import_prop_types.default.string.isRequired,
    values: import_prop_types.default.object
  }).isRequired,
  name: import_prop_types.default.string.isRequired,
  onChange: import_prop_types.default.func.isRequired,
  placeholder: import_prop_types.default.shape({
    id: import_prop_types.default.string.isRequired,
    defaultMessage: import_prop_types.default.string.isRequired,
    values: import_prop_types.default.object
  }),
  providerToEditName: import_prop_types.default.string.isRequired,
  type: import_prop_types.default.string.isRequired,
  value: import_prop_types.default.oneOfType([
    import_prop_types.default.bool,
    import_prop_types.default.string
  ])
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/components/FormModal/index.mjs
var FormModal = ({ headerBreadcrumbs, initialData, isSubmiting, layout, isOpen, onSubmit, onToggle, providerToEditName }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime2.jsx)(Modal.Root, {
    open: isOpen,
    onOpenChange: onToggle,
    children: (0, import_jsx_runtime2.jsxs)(Modal.Content, {
      children: [
        (0, import_jsx_runtime2.jsx)(Modal.Header, {
          children: (0, import_jsx_runtime2.jsx)(Breadcrumbs, {
            label: headerBreadcrumbs.join(", "),
            children: headerBreadcrumbs.map((crumb, index, arr) => (0, import_jsx_runtime2.jsx)(Crumb, {
              isCurrent: index === arr.length - 1,
              children: crumb
            }, crumb))
          })
        }),
        (0, import_jsx_runtime2.jsx)(Formik, {
          onSubmit: (values) => onSubmit(values),
          initialValues: initialData,
          validationSchema: layout.schema,
          validateOnChange: false,
          children: ({ errors, handleChange, values }) => {
            return (0, import_jsx_runtime2.jsxs)(Form, {
              children: [
                (0, import_jsx_runtime2.jsx)(Modal.Body, {
                  children: (0, import_jsx_runtime2.jsx)(Flex, {
                    direction: "column",
                    alignItems: "stretch",
                    gap: 1,
                    children: (0, import_jsx_runtime2.jsx)(Grid.Root, {
                      gap: 5,
                      children: layout.form.map((row) => {
                        return row.map((input) => {
                          return (0, import_jsx_runtime2.jsx)(Grid.Item, {
                            col: input.size,
                            xs: 12,
                            direction: "column",
                            alignItems: "stretch",
                            children: (0, import_jsx_runtime2.jsx)(Input, {
                              ...input,
                              error: errors[input.name],
                              onChange: handleChange,
                              value: values[input.name],
                              providerToEditName
                            })
                          }, input.name);
                        });
                      })
                    })
                  })
                }),
                (0, import_jsx_runtime2.jsxs)(Modal.Footer, {
                  children: [
                    (0, import_jsx_runtime2.jsx)(Button, {
                      variant: "tertiary",
                      onClick: onToggle,
                      type: "button",
                      children: formatMessage({
                        id: "app.components.Button.cancel",
                        defaultMessage: "Cancel"
                      })
                    }),
                    (0, import_jsx_runtime2.jsx)(Button, {
                      type: "submit",
                      loading: isSubmiting,
                      children: formatMessage({
                        id: "global.save",
                        defaultMessage: "Save"
                      })
                    })
                  ]
                })
              ]
            });
          }
        })
      ]
    })
  });
};
FormModal.defaultProps = {
  initialData: null,
  providerToEditName: null
};
FormModal.propTypes = {
  headerBreadcrumbs: import_prop_types2.default.arrayOf(import_prop_types2.default.string).isRequired,
  initialData: import_prop_types2.default.object,
  layout: import_prop_types2.default.shape({
    form: import_prop_types2.default.arrayOf(import_prop_types2.default.array),
    schema: import_prop_types2.default.object
  }).isRequired,
  isOpen: import_prop_types2.default.bool.isRequired,
  isSubmiting: import_prop_types2.default.bool.isRequired,
  onSubmit: import_prop_types2.default.func.isRequired,
  onToggle: import_prop_types2.default.func.isRequired,
  providerToEditName: import_prop_types2.default.string
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Providers/index.mjs
var import_isEmpty2 = __toESM(require_isEmpty(), 1);

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Providers/utils/forms.mjs
var import_isEmpty = __toESM(require_isEmpty(), 1);
var callbackLabel = {
  id: getTrad("PopUpForm.Providers.redirectURL.front-end.label"),
  defaultMessage: "The redirect URL to your front-end app"
};
var callbackPlaceholder = {
  id: "http://www.client-app.com",
  defaultMessage: "http://www.client-app.com"
};
var enabledDescription = {
  id: getTrad("PopUpForm.Providers.enabled.description"),
  defaultMessage: "If disabled, users won't be able to use this provider."
};
var enabledLabel = {
  id: getTrad("PopUpForm.Providers.enabled.label"),
  defaultMessage: "Enable"
};
var keyLabel = {
  id: getTrad("PopUpForm.Providers.key.label"),
  defaultMessage: "Client ID"
};
var hintLabel = {
  id: getTrad("PopUpForm.Providers.redirectURL.label"),
  defaultMessage: "The redirect URL to add in your {provider} application configurations"
};
var textPlaceholder = {
  id: getTrad("PopUpForm.Providers.key.placeholder"),
  defaultMessage: "TEXT"
};
var secretLabel = {
  id: getTrad("PopUpForm.Providers.secret.label"),
  defaultMessage: "Client Secret"
};
var CALLBACK_REGEX = /^$|^[a-z][a-z0-9+.-]*:\/\/[^\s/$.?#](?:[^\s]*[^\s/$.?#])?$/i;
var SUBDOMAIN_REGEX = /^(([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+)(:\d+)?(\/\S*)?$/i;
var forms = {
  email: {
    form: [
      [
        {
          intlLabel: enabledLabel,
          name: "enabled",
          type: "bool",
          description: enabledDescription,
          size: 6
        }
      ]
    ],
    schema: create3().shape({
      enabled: create().required(errorsTrads.required.id)
    })
  },
  providers: {
    form: [
      [
        {
          intlLabel: enabledLabel,
          name: "enabled",
          type: "bool",
          description: enabledDescription,
          size: 6,
          validations: {
            required: true
          }
        }
      ],
      [
        {
          intlLabel: keyLabel,
          name: "key",
          type: "text",
          placeholder: textPlaceholder,
          size: 12,
          validations: {
            required: true
          }
        }
      ],
      [
        {
          intlLabel: secretLabel,
          name: "secret",
          type: "text",
          placeholder: textPlaceholder,
          size: 12,
          validations: {
            required: true
          }
        }
      ],
      [
        {
          intlLabel: callbackLabel,
          placeholder: callbackPlaceholder,
          name: "callback",
          type: "text",
          size: 12,
          validations: {
            required: true
          }
        }
      ],
      [
        {
          intlLabel: hintLabel,
          name: "noName",
          type: "text",
          validations: {},
          size: 12,
          disabled: true
        }
      ]
    ],
    schema: create3().shape({
      enabled: create().required(errorsTrads.required.id),
      key: create2().when("enabled", {
        is: true,
        then: create2().required(errorsTrads.required.id),
        otherwise: create2()
      }),
      secret: create2().when("enabled", {
        is: true,
        then: create2().required(errorsTrads.required.id),
        otherwise: create2()
      }),
      callback: create2().when("enabled", {
        is: true,
        then: create2().matches(CALLBACK_REGEX, errorsTrads.regex.id).required(errorsTrads.required.id),
        otherwise: create2()
      })
    })
  },
  providersWithSubdomain: {
    form: [
      [
        {
          intlLabel: enabledLabel,
          name: "enabled",
          type: "bool",
          description: enabledDescription,
          size: 6,
          validations: {
            required: true
          }
        }
      ],
      [
        {
          intlLabel: keyLabel,
          name: "key",
          type: "text",
          placeholder: textPlaceholder,
          size: 12,
          validations: {
            required: true
          }
        }
      ],
      [
        {
          intlLabel: secretLabel,
          name: "secret",
          type: "text",
          placeholder: textPlaceholder,
          size: 12,
          validations: {
            required: true
          }
        }
      ],
      [
        {
          intlLabel: {
            id: getTrad({
              id: "PopUpForm.Providers.jwksurl.label"
            }),
            defaultMessage: "JWKS URL"
          },
          name: "jwksurl",
          type: "text",
          placeholder: textPlaceholder,
          size: 12,
          validations: {
            required: false
          }
        }
      ],
      [
        {
          intlLabel: {
            id: getTrad("PopUpForm.Providers.subdomain.label"),
            defaultMessage: "Host URI (Subdomain)"
          },
          name: "subdomain",
          type: "text",
          placeholder: {
            id: getTrad("PopUpForm.Providers.subdomain.placeholder"),
            defaultMessage: "my.subdomain.com"
          },
          size: 12,
          validations: {
            required: true
          }
        }
      ],
      [
        {
          intlLabel: callbackLabel,
          placeholder: callbackPlaceholder,
          name: "callback",
          type: "text",
          size: 12,
          validations: {
            required: true
          }
        }
      ],
      [
        {
          intlLabel: hintLabel,
          name: "noName",
          type: "text",
          validations: {},
          size: 12,
          disabled: true
        }
      ]
    ],
    schema: create3().shape({
      enabled: create().required(errorsTrads.required.id),
      key: create2().when("enabled", {
        is: true,
        then: create2().required(errorsTrads.required.id),
        otherwise: create2()
      }),
      secret: create2().when("enabled", {
        is: true,
        then: create2().required(errorsTrads.required.id),
        otherwise: create2()
      }),
      subdomain: create2().when("enabled", {
        is: true,
        then: create2().matches(SUBDOMAIN_REGEX, errorsTrads.regex.id).required(errorsTrads.required.id),
        otherwise: create2()
      }),
      callback: create2().when("enabled", {
        is: true,
        then: create2().matches(CALLBACK_REGEX, errorsTrads.regex.id).required(errorsTrads.required.id),
        otherwise: create2()
      })
    })
  }
};

// node_modules/@strapi/plugin-users-permissions/dist/admin/pages/Providers/index.mjs
var ProvidersPage = () => {
  const { formatMessage, locale } = useIntl();
  const queryClient = useQueryClient();
  const { trackUsage } = useTracking();
  const [isOpen, setIsOpen] = React.useState(false);
  const [providerToEditName, setProviderToEditName] = React.useState(null);
  const { toggleNotification } = useNotification();
  const { get, put } = useFetchClient();
  const { formatAPIError } = useAPIErrorHandler();
  const formatter = useCollator(locale, {
    sensitivity: "base"
  });
  const { isLoading: isLoadingPermissions, allowedActions: { canUpdate } } = useRBAC({
    update: PERMISSIONS.updateProviders
  });
  const { isLoading: isLoadingData, data } = useQuery([
    "users-permissions",
    "get-providers"
  ], async () => {
    const { data: data2 } = await get("/users-permissions/providers");
    return data2;
  }, {
    initialData: {}
  });
  const submitMutation = useMutation((body) => put("/users-permissions/providers", body), {
    async onSuccess() {
      await queryClient.invalidateQueries([
        "users-permissions",
        "get-providers"
      ]);
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTrad("notification.success.submit")
        })
      });
      trackUsage("didEditAuthenticationProvider");
      handleToggleModal();
    },
    onError(error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    },
    refetchActive: false
  });
  const providers = Object.entries(data).reduce((acc, [name, provider]) => {
    const { icon, enabled, subdomain } = provider;
    acc.push({
      name,
      icon: icon === "envelope" ? [
        "fas",
        "envelope"
      ] : [
        "fab",
        icon
      ],
      enabled,
      subdomain
    });
    return acc;
  }, []).sort((a, b) => formatter.compare(a.name, b.name));
  const isLoading = isLoadingData || isLoadingPermissions;
  const isProviderWithSubdomain = React.useMemo(() => {
    if (!providerToEditName) {
      return false;
    }
    const providerToEdit = providers.find((obj) => obj.name === providerToEditName);
    return !!(providerToEdit == null ? void 0 : providerToEdit.subdomain);
  }, [
    providers,
    providerToEditName
  ]);
  const layoutToRender = React.useMemo(() => {
    if (providerToEditName === "email") {
      return forms.email;
    }
    if (isProviderWithSubdomain) {
      return forms.providersWithSubdomain;
    }
    return forms.providers;
  }, [
    providerToEditName,
    isProviderWithSubdomain
  ]);
  const handleToggleModal = () => {
    setIsOpen((prev) => !prev);
  };
  const handleClickEdit = (provider) => {
    if (canUpdate) {
      setProviderToEditName(provider.name);
      handleToggleModal();
    }
  };
  const handleSubmit = async (values) => {
    trackUsage("willEditAuthenticationProvider");
    submitMutation.mutate({
      providers: {
        ...data,
        [providerToEditName]: values
      }
    });
  };
  if (isLoading) {
    return (0, import_jsx_runtime3.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime3.jsxs)(Layouts.Root, {
    children: [
      (0, import_jsx_runtime3.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: formatMessage({
            id: getTrad("HeaderNav.link.providers"),
            defaultMessage: "Providers"
          })
        })
      }),
      (0, import_jsx_runtime3.jsxs)(Page.Main, {
        children: [
          (0, import_jsx_runtime3.jsx)(Layouts.Header, {
            title: formatMessage({
              id: getTrad("HeaderNav.link.providers"),
              defaultMessage: "Providers"
            })
          }),
          (0, import_jsx_runtime3.jsx)(Layouts.Content, {
            children: (0, import_jsx_runtime3.jsxs)(Table, {
              colCount: 3,
              rowCount: providers.length + 1,
              children: [
                (0, import_jsx_runtime3.jsx)(Thead, {
                  children: (0, import_jsx_runtime3.jsxs)(Tr, {
                    children: [
                      (0, import_jsx_runtime3.jsx)(Th, {
                        children: (0, import_jsx_runtime3.jsx)(Typography, {
                          variant: "sigma",
                          textColor: "neutral600",
                          children: formatMessage({
                            id: "global.name",
                            defaultMessage: "Name"
                          })
                        })
                      }),
                      (0, import_jsx_runtime3.jsx)(Th, {
                        children: (0, import_jsx_runtime3.jsx)(Typography, {
                          variant: "sigma",
                          textColor: "neutral600",
                          children: formatMessage({
                            id: getTrad("Providers.status"),
                            defaultMessage: "Status"
                          })
                        })
                      }),
                      (0, import_jsx_runtime3.jsx)(Th, {
                        children: (0, import_jsx_runtime3.jsx)(Typography, {
                          variant: "sigma",
                          children: (0, import_jsx_runtime3.jsx)(VisuallyHidden, {
                            children: formatMessage({
                              id: "global.settings",
                              defaultMessage: "Settings"
                            })
                          })
                        })
                      })
                    ]
                  })
                }),
                (0, import_jsx_runtime3.jsx)(Tbody, {
                  children: providers.map((provider) => (0, import_jsx_runtime3.jsxs)(Tr, {
                    onClick: () => canUpdate ? handleClickEdit(provider) : void 0,
                    children: [
                      (0, import_jsx_runtime3.jsx)(Td, {
                        width: "45%",
                        children: (0, import_jsx_runtime3.jsx)(Typography, {
                          fontWeight: "semiBold",
                          textColor: "neutral800",
                          children: provider.name
                        })
                      }),
                      (0, import_jsx_runtime3.jsx)(Td, {
                        width: "65%",
                        children: (0, import_jsx_runtime3.jsx)(Typography, {
                          textColor: provider.enabled ? "success600" : "danger600",
                          "data-testid": `enable-${provider.name}`,
                          children: provider.enabled ? formatMessage({
                            id: "global.enabled",
                            defaultMessage: "Enabled"
                          }) : formatMessage({
                            id: "global.disabled",
                            defaultMessage: "Disabled"
                          })
                        })
                      }),
                      (0, import_jsx_runtime3.jsx)(Td, {
                        onClick: (e) => e.stopPropagation(),
                        children: canUpdate && (0, import_jsx_runtime3.jsx)(IconButton, {
                          onClick: () => handleClickEdit(provider),
                          variant: "ghost",
                          label: "Edit",
                          children: (0, import_jsx_runtime3.jsx)(ForwardRef$1v, {})
                        })
                      })
                    ]
                  }, provider.name))
                })
              ]
            })
          })
        ]
      }),
      (0, import_jsx_runtime3.jsx)(FormModal, {
        initialData: data[providerToEditName],
        isOpen,
        isSubmiting: submitMutation.isLoading,
        layout: layoutToRender,
        headerBreadcrumbs: [
          formatMessage({
            id: getTrad("PopUpForm.header.edit.providers"),
            defaultMessage: "Edit Provider"
          }),
          (0, import_upperFirst.default)(providerToEditName)
        ],
        onToggle: handleToggleModal,
        onSubmit: handleSubmit,
        providerToEditName
      })
    ]
  });
};
var ProtectedProvidersPage = () => (0, import_jsx_runtime3.jsx)(Page.Protect, {
  permissions: PERMISSIONS.readProviders,
  children: (0, import_jsx_runtime3.jsx)(ProvidersPage, {})
});
export {
  ProvidersPage,
  ProtectedProvidersPage as default
};
//# sourceMappingURL=Providers-RM7SVGPV.js.map
