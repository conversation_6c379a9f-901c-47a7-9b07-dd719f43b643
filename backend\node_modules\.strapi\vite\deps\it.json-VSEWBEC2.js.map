{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/it.json.mjs"], "sourcesContent": ["var configurations = \"configurazioni\";\nvar from = \"da\";\nvar it = {\n    \"attribute.boolean\": \"Booleano\",\n    \"attribute.boolean.description\": \"Sì o no, 1 o 0, vero o falso\",\n    \"attribute.component\": \"Componente\",\n    \"attribute.component.description\": \"Gruppo di campi che puoi ripetere o riutilizzare\",\n    \"attribute.date\": \"Data\",\n    \"attribute.date.description\": \"Un selettore di date con ore, minuti e secondi\",\n    \"attribute.datetime\": \"Data e Ora\",\n    \"attribute.dynamiczone\": \"Zona dinamica\",\n    \"attribute.dynamiczone.description\": \"Scegli un componente dinamicamente durante la modifica del contenuto \",\n    \"attribute.email\": \"Email\",\n    \"attribute.email.description\": \"Un campo Email con validazione del formato\",\n    \"attribute.enumeration\": \"Enumerazione\",\n    \"attribute.enumeration.description\": \"Scegli il valore da una lista precompilata\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"Dati in formato JSON\",\n    \"attribute.media\": \"Media\",\n    \"attribute.media.description\": \"File come immagini, video, ecc.\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"Numero\",\n    \"attribute.number.description\": \"Numeri (interi, float, decimali)\",\n    \"attribute.password\": \"Password\",\n    \"attribute.password.description\": \"Campo Password crittografato\",\n    \"attribute.relation\": \"Relazione\",\n    \"attribute.relation.description\": \"Per collegare una Collezione\",\n    \"attribute.richtext\": \"Testo formattato\",\n    \"attribute.richtext.description\": \"Un editor di testo con comandi per la formattazione\",\n    \"attribute.text\": \"Testo\",\n    \"attribute.text.description\": \"Testo semplice come titolo o descrizione\",\n    \"attribute.time\": \"Orario\",\n    \"attribute.timestamp\": \"Timestamp\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Identificatore univoco\",\n    \"button.attributes.add.another\": \"Aggiungi altro campo\",\n    \"button.component.add\": \"Aggiungi componente\",\n    \"button.component.create\": \"Crea nuovo componente\",\n    \"button.model.create\": \"Crea nuova Collezione\",\n    \"button.single-types.create\": \"Crea nuova Entità singola\",\n    \"component.repeatable\": \"(ripetibile)\",\n    \"components.componentSelect.no-component-available\": \"Hai già aggiunto tutti i componenti\",\n    \"components.componentSelect.no-component-available.with-search\": \"Nessun componente con questa chiave di ricerca\",\n    \"components.componentSelect.value-component\": \"{number} componenti selezionati (digita per cercare un componente)\",\n    \"components.componentSelect.value-components\": \"{number} componenti selezionati\",\n    configurations: configurations,\n    \"contentType.collectionName.description\": \"Utile quando il nome della Collezione differisce dal nome della tabella del DB\",\n    \"contentType.collectionName.label\": \"Nome della Collezione\",\n    \"contentType.displayName.label\": \"Nome visualizzato\",\n    \"contentType.kind.change.warning\": \"Hai cambiato il genere di questo Tipo di Contenuto. Le API verranno ripristinate (route, controller e services verranno sovrascritti).\",\n    \"error.attributeName.reserved-name\": \"Questo nome non può essere utilizzato nel tuo Tipo di Contenuto perché potrebbe danneggiare altre funzionalità\",\n    \"error.contentTypeName.reserved-name\": \"Questo nome non può essere utilizzato nel tuo progetto perché potrebbe danneggiare altre funzionalità\",\n    \"error.validation.enum-duplicate\": \"Valori duplicati non ammessi\",\n    \"error.validation.minSupMax\": \"Non può essere maggiore\",\n    \"error.validation.regex\": \"Regex non valida\",\n    \"error.validation.relation.targetAttribute-taken\": \"Questo nome già esiste nella destinazione\",\n    \"form.attribute.component.option.add\": \"Aggiungi componente\",\n    \"form.attribute.component.option.create\": \"Crea nuovo componente\",\n    \"form.attribute.component.option.create.description\": \"I componenti sono condivisi tra Tipi e componenti, sono disponibili e accessibili da ovunque.\",\n    \"form.attribute.component.option.repeatable\": \"Componente ripetibile\",\n    \"form.attribute.component.option.repeatable.description\": \"Utile per istanze multiple (liste) come ingredienti, meta tag, ecc...\",\n    \"form.attribute.component.option.reuse-existing\": \"Usa componente esistente\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Riutilizza un componente già creato per mantenere i dati consistenti tra i vari Tipi di Contenuto\",\n    \"form.attribute.component.option.single\": \"Componente singolo\",\n    \"form.attribute.component.option.single.description\": \"Utile per raggruppare campi correlati come quelli di un indirizzo\",\n    \"form.attribute.item.customColumnName\": \"Nome della colonna personalizzato\",\n    \"form.attribute.item.customColumnName.description\": \"Utile per rinominare le colonne del database e mantenere consistente il formato delle risposte API\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Nome del campo\",\n    \"form.attribute.item.enumeration.graphql\": \"Override del nome GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Consente di ignorare l'impostazione predefinita del nome generato per GraphQL\",\n    \"form.attribute.item.enumeration.placeholder\": \"Es:\\nmattina\\nmezzogiorno\\nsera\",\n    \"form.attribute.item.enumeration.rules\": \"Valori (un valore per riga)\",\n    \"form.attribute.item.maximum\": \"Valore massimo\",\n    \"form.attribute.item.maximumLength\": \"Lunghezza massima\",\n    \"form.attribute.item.minimum\": \"Valore minimo\",\n    \"form.attribute.item.minimumLength\": \"Lunghezza minima\",\n    \"form.attribute.item.number.type\": \"Formato del numero\",\n    \"form.attribute.item.number.type.biginteger\": \"intero grande (es: 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"decimale (es: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"float (es: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"intero (es: 10)\",\n    \"form.attribute.item.privateField\": \"Campo privato\",\n    \"form.attribute.item.privateField.description\": \"Questo campo non sarà presente nelle risposte API\",\n    \"form.attribute.item.requiredField\": \"Campo obbligatorio\",\n    \"form.attribute.item.requiredField.description\": \"Non sarai in grado di creare una voce se questo campo è vuoto\",\n    \"form.attribute.item.text.regex\": \"Schema RegExp\",\n    \"form.attribute.item.text.regex.description\": \"Il testo di una Espressione Regolare\",\n    \"form.attribute.item.uniqueField\": \"Campo univoco\",\n    \"form.attribute.item.uniqueField.description\": \"Non sarai in grado di creare una voce, se c'è una voce esistente con valore identico\",\n    \"form.attribute.media.allowed-types\": \"Seleziona i tipi di media permessi\",\n    \"form.attribute.media.allowed-types.option-files\": \"File\",\n    \"form.attribute.media.allowed-types.option-images\": \"Immagini\",\n    \"form.attribute.media.allowed-types.option-videos\": \"Video\",\n    \"form.attribute.media.option.multiple\": \"Media multipli\",\n    \"form.attribute.media.option.multiple.description\": \"Utile per slider, caroselli o download multipli\",\n    \"form.attribute.media.option.single\": \"Media singolo\",\n    \"form.attribute.media.option.single.description\": \"Utile per avatar, foto profilo o copertina\",\n    \"form.attribute.settings.default\": \"Valore di Default\",\n    \"form.attribute.text.option.long-text\": \"Testo lungo\",\n    \"form.attribute.text.option.long-text.description\": \"Utile per descrizioni o biografie. La ricerca esatta è disabilitata.\",\n    \"form.attribute.text.option.short-text\": \"Testo breve\",\n    \"form.attribute.text.option.short-text.description\": \"Utile per titoli, nomi, link (URL). Potrai eseguire una ricerca esatta sul campo.\",\n    \"form.button.add-components-to-dynamiczone\": \"Aggiungi componenti alla zona\",\n    \"form.button.add-field\": \"Aggiungi altro campo\",\n    \"form.button.add-first-field-to-created-component\": \"Aggiungi un primo campo al componente\",\n    \"form.button.add.field.to.collectionType\": \"Aggiungi un altro campo a questa Collezione\",\n    \"form.button.add.field.to.component\": \"Aggiungi un altro campo a questo componente\",\n    \"form.button.add.field.to.contentType\": \"Aggiungi un altro campo a questo Tipo di Contenuto\",\n    \"form.button.add.field.to.singleType\": \"Aggiungi un altro campo a questa Entità singola\",\n    \"form.button.cancel\": \"Annulla\",\n    \"form.button.collection-type.description\": \"Utile per istanze multiple come articoli, prodotti, commenti, ecc...\",\n    \"form.button.configure-component\": \"Configura componente\",\n    \"form.button.configure-view\": \"Configura vista\",\n    \"form.button.select-component\": \"Seleziona un componente\",\n    \"form.button.single-type.description\": \"Indicato per entità uniche come home page, chi siamo, ecc...\",\n    from: from,\n    \"modalForm.attribute.form.base.name.description\": \"Spazi non ammessi per il nome dell'attributo\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"Es: slug, urlSeo, urlCanonico\",\n    \"modalForm.attribute.target-field\": \"Campo collegato\",\n    \"modalForm.attributes.select-component\": \"Seleziona un componente\",\n    \"modalForm.attributes.select-components\": \"Seleziona i componenti\",\n    \"modalForm.component.header-create\": \"Crea un componente\",\n    \"modalForm.components.create-component.category.label\": \"Seleziona una categoria o inserisci un nome per crearne una nuova\",\n    \"modalForm.components.icon.label\": \"Icona\",\n    \"modalForm.editCategory.base.name.description\": \"Spazi non ammessi per il nome della categoria\",\n    \"modalForm.header-edit\": \"Modifica {name}\",\n    \"modalForm.header.categories\": \"Categorie\",\n    \"modalForm.header.back\": \"Indietro\",\n    \"modalForm.singleType.header-create\": \"Crea una Entità singola\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Aggiungi nuovo componente alla zona dinamica\",\n    \"modalForm.sub-header.attribute.create\": \"Aggiungi nuovo campo {type}\",\n    \"modalForm.sub-header.attribute.create.step\": \"Aggiungi nuovo componente ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Modifica {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Seleziona un campo per la tua Collezione\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Seleziona un campo per il tuo componente\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Seleziona un campo per la tua Entità singola\",\n    \"modelPage.attribute.relation-polymorphic\": \"Relazione (polimorfica)\",\n    \"modelPage.attribute.relationWith\": \"Relazione con\",\n    \"notification.info.autoreaload-disable\": \"La funzionalità autoReload è richiesta per usare questo plugin. Avvia il tuo server con `strapi develop`\",\n    \"notification.info.creating.notSaved\": \"Per favore, salva il tuo lavoro prima di creare nuovi Tipi di Contenuto o componenti\",\n    \"plugin.description.long\": \"Modella la struttura dei dati delle tue API. Crea nuovi campi e relazioni in maniera visuale. I file vengono automaticamente creati e aggiornati nel tuo progetto.\",\n    \"plugin.description.short\": \"Modella la struttura dei dati delle tue API.\",\n    \"popUpForm.navContainer.advanced\": \"Impostazioni avanzate\",\n    \"popUpForm.navContainer.base\": \"Impostazioni di base\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"Sei sicuro di voler annullare le tue modifiche?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Sei sicuro di voler annullare le tue modifiche? Alcuni componenti sono stati creati o modificati...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"Sei sicuro di voler eliminare questa categoria? Verranno cancellati anche tutti i suoi componenti.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"Sei sicuro di voler eliminare questo componente?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Sei sicuro di voler eliminare questa Collezione?\",\n    \"popUpWarning.draft-publish.button.confirm\": \"Sì, disabilita\",\n    \"popUpWarning.draft-publish.message\": \"Se disabiliti il sistema Bozza/pubblicato, le tue bozze verranno eliminate.\",\n    \"popUpWarning.draft-publish.second-message\": \"Sei sicuro di volerlo disabilitare?\",\n    \"prompt.unsaved\": \"Sei sicuro di voler uscire? Tutte le modifiche verranno perdute.\",\n    \"relation.attributeName.placeholder\": \"Es: autore, categoria, tag\",\n    \"relation.manyToMany\": \"ha e appartiene a molti\",\n    \"relation.manyToOne\": \"ha molti\",\n    \"relation.manyWay\": \"ha molti\",\n    \"relation.oneToMany\": \"appartiene a molti\",\n    \"relation.oneToOne\": \"ha e appartiene a un\",\n    \"relation.oneWay\": \"ha un\"\n};\n\nexport { configurations, it as default, from };\n//# sourceMappingURL=it.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC;AAAA,EACA,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACvB;", "names": []}