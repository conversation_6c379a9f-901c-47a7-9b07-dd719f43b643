{"version": 3, "sources": ["../../../@dnd-kit/modifiers/src/createSnapModifier.ts", "../../../@dnd-kit/modifiers/src/restrictToHorizontalAxis.ts", "../../../@dnd-kit/modifiers/src/utilities/restrictToBoundingRect.ts", "../../../@dnd-kit/modifiers/src/restrictToParentElement.ts", "../../../@dnd-kit/modifiers/src/restrictToFirstScrollableAncestor.ts", "../../../@dnd-kit/modifiers/src/restrictToVerticalAxis.ts", "../../../@dnd-kit/modifiers/src/restrictToWindowEdges.ts", "../../../@dnd-kit/modifiers/src/snapCenterToCursor.ts", "../../../@strapi/content-type-builder/admin/src/icons/Curve.tsx", "../../../@strapi/content-type-builder/admin/src/utils/getAttributeDisplayedType.ts", "../../../@strapi/content-type-builder/admin/src/components/ComponentRow.tsx", "../../../@strapi/content-type-builder/admin/src/components/ComponentList.tsx", "../../../@strapi/content-type-builder/admin/src/components/DisplayedType.tsx", "../../../@strapi/content-type-builder/admin/src/components/ComponentCard/ComponentIcon/ComponentIcon.tsx", "../../../@strapi/content-type-builder/admin/src/components/ComponentCard/ComponentCard.tsx", "../../../@strapi/content-type-builder/admin/src/components/DynamicZoneList.tsx", "../../../@strapi/content-type-builder/admin/src/components/AttributeRow.tsx", "../../../@strapi/content-type-builder/admin/src/components/Footers.tsx", "../../../@strapi/content-type-builder/admin/src/components/List.tsx", "../../../@strapi/content-type-builder/admin/src/pages/ListView/LinkToCMSettingsView.tsx", "../../../@strapi/content-type-builder/admin/src/pages/ListView/ListView.tsx"], "sourcesContent": ["import type {Modifier} from '@dnd-kit/core';\n\nexport function createSnapModifier(gridSize: number): Modifier {\n  return ({transform}) => ({\n    ...transform,\n    x: Math.ceil(transform.x / gridSize) * gridSize,\n    y: Math.ceil(transform.y / gridSize) * gridSize,\n  });\n}\n", "import type {Modifier} from '@dnd-kit/core';\n\nexport const restrictToHorizontalAxis: Modifier = ({transform}) => {\n  return {\n    ...transform,\n    y: 0,\n  };\n};\n", "import type {ClientRect} from '@dnd-kit/core';\nimport type {Transform} from '@dnd-kit/utilities';\n\nexport function restrictToBoundingRect(\n  transform: Transform,\n  rect: ClientRect,\n  boundingRect: ClientRect\n): Transform {\n  const value = {\n    ...transform,\n  };\n\n  if (rect.top + transform.y <= boundingRect.top) {\n    value.y = boundingRect.top - rect.top;\n  } else if (\n    rect.bottom + transform.y >=\n    boundingRect.top + boundingRect.height\n  ) {\n    value.y = boundingRect.top + boundingRect.height - rect.bottom;\n  }\n\n  if (rect.left + transform.x <= boundingRect.left) {\n    value.x = boundingRect.left - rect.left;\n  } else if (\n    rect.right + transform.x >=\n    boundingRect.left + boundingRect.width\n  ) {\n    value.x = boundingRect.left + boundingRect.width - rect.right;\n  }\n\n  return value;\n}\n", "import type {Modifier} from '@dnd-kit/core';\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToParentElement: Modifier = ({\n  containerNodeRect,\n  draggingNodeRect,\n  transform,\n}) => {\n  if (!draggingNodeRect || !containerNodeRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(transform, draggingNodeRect, containerNodeRect);\n};\n", "import type {Modifier} from '@dnd-kit/core';\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToFirstScrollableAncestor: Modifier = ({\n  draggingNodeRect,\n  transform,\n  scrollableAncestorRects,\n}) => {\n  const firstScrollableAncestorRect = scrollableAncestorRects[0];\n\n  if (!draggingNodeRect || !firstScrollableAncestorRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(\n    transform,\n    draggingNodeRect,\n    firstScrollableAncestorRect\n  );\n};\n", "import type {Modifier} from '@dnd-kit/core';\n\nexport const restrictToVerticalAxis: Modifier = ({transform}) => {\n  return {\n    ...transform,\n    x: 0,\n  };\n};\n", "import type {Modifier} from '@dnd-kit/core';\n\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToWindowEdges: Modifier = ({\n  transform,\n  draggingNodeRect,\n  windowRect,\n}) => {\n  if (!draggingNodeRect || !windowRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(transform, draggingNodeRect, windowRect);\n};\n", "import type {Modifier} from '@dnd-kit/core';\nimport {getEventCoordinates} from '@dnd-kit/utilities';\n\nexport const snapCenterToCursor: Modifier = ({\n  activatorEvent,\n  draggingNodeRect,\n  transform,\n}) => {\n  if (draggingNodeRect && activatorEvent) {\n    const activatorCoordinates = getEventCoordinates(activatorEvent);\n\n    if (!activatorCoordinates) {\n      return transform;\n    }\n\n    const offsetX = activatorCoordinates.x - draggingNodeRect.left;\n    const offsetY = activatorCoordinates.y - draggingNodeRect.top;\n\n    return {\n      ...transform,\n      x: transform.x + offsetX - draggingNodeRect.width / 2,\n      y: transform.y + offsetY - draggingNodeRect.height / 2,\n    };\n  }\n\n  return transform;\n};\n", "import { Box } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nconst StyledBox = styled(Box)`\n  position: absolute;\n  left: -3.4rem;\n  top: 0px;\n\n  &:before {\n    content: '';\n    width: 0.4rem;\n    height: 1.2rem;\n    background: ${({ theme, color }) => theme.colors[color!]};\n    display: block;\n  }\n`;\n\nconst Svg = styled.svg`\n  position: relative;\n  flex-shrink: 0;\n  transform: translate(-0.5px, -1px);\n\n  * {\n    fill: ${({ theme, color }) => theme.colors[color!]};\n  }\n`;\n\ninterface CurveProps {\n  color: string;\n}\n\nexport const Curve = (props: CurveProps) => (\n  <StyledBox>\n    <Svg\n      width=\"20\"\n      height=\"23\"\n      viewBox=\"0 0 20 23\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M7.02477 14.7513C8.65865 17.0594 11.6046 18.6059 17.5596 18.8856C18.6836 18.9384 19.5976 19.8435 19.5976 20.9688V20.9688C19.5976 22.0941 18.6841 23.0125 17.5599 22.9643C10.9409 22.6805 6.454 20.9387 3.75496 17.1258C0.937988 13.1464 0.486328 7.39309 0.486328 0.593262H4.50974C4.50974 7.54693 5.06394 11.9813 7.02477 14.7513Z\"\n      />\n    </Svg>\n  </StyledBox>\n);\n", "export const getAttributeDisplayedType = (type: string) => {\n  let displayedType;\n\n  switch (type) {\n    case 'date':\n    case 'datetime':\n    case 'time':\n    case 'timestamp':\n      displayedType = 'date';\n      break;\n    case 'integer':\n    case 'biginteger':\n    case 'decimal':\n    case 'float':\n      displayedType = 'number';\n      break;\n    case 'string':\n    case 'text':\n      displayedType = 'text';\n      break;\n    case '':\n      displayedType = 'relation';\n      break;\n    default:\n      displayedType = type;\n  }\n\n  return displayedType;\n};\n", "import { Box } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nexport const ComponentRow = styled(Box)<{\n  $isFromDynamicZone?: boolean;\n  $isChildOfDynamicZone?: boolean;\n}>`\n  &.component-row,\n  &.dynamiczone-row {\n    position: relative;\n\n    > ul:first-of-type {\n      padding: 0 0 0 104px;\n      position: relative;\n\n      &::before {\n        content: '';\n        width: 0.4rem;\n        height: ${({ $isFromDynamicZone }) =>\n          $isFromDynamicZone ? 'calc(100% - 65px)' : 'calc(100%)'};\n        position: absolute;\n        left: 7rem;\n        border-radius: 4px;\n\n        ${({ $isFromDynamicZone, $isChildOfDynamicZone, theme }) => {\n          if ($isChildOfDynamicZone) {\n            return `background-color: ${theme.colors.primary200};`;\n          }\n\n          if ($isFromDynamicZone) {\n            return `background-color: ${theme.colors.primary200};`;\n          }\n\n          return `background: ${theme.colors.neutral150};`;\n        }}\n      }\n    }\n  }\n\n  &.dynamiczone-row > ul:first-of-type {\n    padding: 0;\n  }\n`;\n", "import { Box } from '@strapi/design-system';\nimport get from 'lodash/get';\n\nimport { ComponentRow } from './ComponentRow';\nimport { useDataManager } from './DataManager/useDataManager';\nimport { List } from './List';\n\nimport type { UID } from '@strapi/types';\n\ninterface ComponentListProps {\n  component: UID.Component;\n  firstLoopComponentUid?: UID.Component | null;\n  isFromDynamicZone?: boolean;\n}\n\nexport const ComponentList = ({\n  component,\n  isFromDynamicZone = false,\n  firstLoopComponentUid,\n}: ComponentListProps) => {\n  const { components } = useDataManager();\n  const type = get(components, component);\n\n  return (\n    <ComponentRow $isChildOfDynamicZone={isFromDynamicZone} className=\"component-row\">\n      <List\n        type={type}\n        firstLoopComponentUid={firstLoopComponentUid || component}\n        isFromDynamicZone={isFromDynamicZone}\n        isSub\n        secondLoopComponentUid={firstLoopComponentUid ? component : null}\n      />\n    </ComponentRow>\n  );\n};\n", "import { useIntl } from 'react-intl';\n\nimport { getTrad } from '../utils/getTrad';\n\ntype DisplayedTypeProps = {\n  type: string;\n  customField?: string | null;\n  repeatable?: boolean;\n  multiple?: boolean;\n};\n\nexport const DisplayedType = ({\n  type,\n  customField = null,\n  repeatable = false,\n  multiple = false,\n}: DisplayedTypeProps) => {\n  const { formatMessage } = useIntl();\n\n  let readableType = type;\n\n  if (['integer', 'biginteger', 'float', 'decimal'].includes(type)) {\n    readableType = 'number';\n  } else if (['string'].includes(type)) {\n    readableType = 'text';\n  }\n\n  if (customField) {\n    return formatMessage({\n      id: getTrad('attribute.customField'),\n      defaultMessage: 'Custom field',\n    });\n  }\n\n  return (\n    <>\n      {repeatable &&\n        formatMessage({\n          id: getTrad('component.repeatable'),\n          defaultMessage: 'Repeatable',\n        })}\n      {multiple &&\n        formatMessage({\n          id: getTrad('media.multiple'),\n          defaultMessage: 'Multiple',\n        })}\n      &nbsp;\n      {formatMessage({\n        id: getTrad(`attribute.${readableType}`),\n        defaultMessage: type,\n      })}\n    </>\n  );\n};\n", "import { Flex } from '@strapi/design-system';\n\nimport { COMPONENT_ICONS } from '../../IconPicker/constants';\n\ninterface ComponentIconProps {\n  isActive?: boolean;\n  icon?: keyof typeof COMPONENT_ICONS;\n}\n\nexport const ComponentIcon = ({ isActive = false, icon = 'dashboard' }: ComponentIconProps) => {\n  const Icon = COMPONENT_ICONS[icon] || COMPONENT_ICONS.dashboard;\n\n  return (\n    <Flex\n      alignItems=\"center\"\n      background={isActive ? 'primary200' : 'neutral200'}\n      justifyContent=\"center\"\n      height={8}\n      width={8}\n      borderRadius=\"50%\"\n    >\n      <Icon height=\"2rem\" width=\"2rem\" />\n    </Flex>\n  );\n};\n", "import { Box, Flex, Typography } from '@strapi/design-system';\nimport { Cross } from '@strapi/icons';\nimport get from 'lodash/get';\nimport { styled } from 'styled-components';\n\nimport { useDataManager } from '../DataManager/useDataManager';\n\nimport { ComponentIcon } from './ComponentIcon';\n\nimport type { Internal, Struct } from '@strapi/types';\n\ninterface ComponentCardProps {\n  component: string;\n  dzName: string;\n  index: number;\n  isActive?: boolean;\n  isInDevelopmentMode?: boolean;\n  onClick?: () => void;\n  forTarget: Struct.ModelType;\n  targetUid: Internal.UID.Schema;\n  disabled?: boolean;\n}\n\nconst CloseButton = styled(Box)`\n  position: absolute;\n  display: none;\n  top: 5px;\n  right: 0.8rem;\n\n  svg {\n    width: 1rem;\n    height: 1rem;\n\n    path {\n      fill: ${({ theme }) => theme.colors.primary600};\n    }\n  }\n`;\n\nconst ComponentBox = styled(Flex)`\n  width: 14rem;\n  height: 8rem;\n  position: relative;\n  border: 1px solid ${({ theme }) => theme.colors.neutral200};\n  background: ${({ theme }) => theme.colors.neutral100};\n  border-radius: ${({ theme }) => theme.borderRadius};\n  max-width: 100%;\n\n  &.active,\n  &:focus,\n  &:hover {\n    border: 1px solid ${({ theme }) => theme.colors.primary200};\n    background: ${({ theme }) => theme.colors.primary100};\n    color: ${({ theme }) => theme.colors.primary600};\n\n    ${CloseButton} {\n      display: block;\n    }\n\n    /* > ComponentIcon */\n    > div:first-child {\n      background: ${({ theme }) => theme.colors.primary200};\n      color: ${({ theme }) => theme.colors.primary600};\n\n      svg {\n        path {\n          fill: ${({ theme }) => theme.colors.primary600};\n        }\n      }\n    }\n  }\n`;\n\nexport const ComponentCard = ({\n  component,\n  dzName,\n  index,\n  isActive = false,\n  isInDevelopmentMode = false,\n  onClick,\n  forTarget,\n  targetUid,\n  disabled,\n}: ComponentCardProps) => {\n  const { components, removeComponentFromDynamicZone } = useDataManager();\n  const type = get(components, component);\n  const { icon, displayName } = type?.info || {};\n\n  const onClose = (e: any) => {\n    e.stopPropagation();\n    removeComponentFromDynamicZone({\n      forTarget,\n      targetUid,\n      dzName,\n      componentToRemoveIndex: index,\n    });\n  };\n\n  return (\n    <ComponentBox\n      alignItems=\"center\"\n      direction=\"column\"\n      className={isActive ? 'active' : ''}\n      borderRadius=\"borderRadius\"\n      justifyContent=\"center\"\n      paddingLeft={4}\n      paddingRight={4}\n      shrink={0}\n      onClick={onClick}\n      role=\"tab\"\n      tabIndex={isActive ? 0 : -1}\n      cursor=\"pointer\"\n      aria-selected={isActive}\n      aria-controls={`dz-${dzName}-panel-${index}`}\n      id={`dz-${dzName}-tab-${index}`}\n    >\n      <ComponentIcon icon={icon} isActive={isActive} />\n\n      <Box marginTop={1} maxWidth=\"100%\">\n        <Typography variant=\"pi\" fontWeight=\"bold\" ellipsis>\n          {displayName}\n        </Typography>\n      </Box>\n\n      {isInDevelopmentMode && !disabled && (\n        <CloseButton cursor=\"pointer\" tag=\"button\" onClick={onClose}>\n          <Cross />\n        </CloseButton>\n      )}\n    </ComponentBox>\n  );\n};\n", "import { ComponentType, useState } from 'react';\n\nimport { Box, Flex, Typography } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { getTrad } from '../utils/getTrad';\n\nimport { ComponentCard } from './ComponentCard';\nimport { ComponentList } from './ComponentList';\nimport { ComponentRow } from './ComponentRow';\nimport { useDataManager } from './DataManager/useDataManager';\n\nimport type { Internal, Struct } from '@strapi/types';\n\ninterface DynamicZoneListProps {\n  addComponent: (name?: string) => void;\n  components: Array<Internal.UID.Component>;\n  customRowComponent?: ComponentType<any>;\n  name?: string;\n  forTarget: Struct.ModelType;\n  targetUid: Internal.UID.Schema;\n  disabled?: boolean;\n}\n\nconst StyledAddIcon = styled(Plus)<{ disabled?: boolean }>`\n  width: 3.2rem;\n  height: 3.2rem;\n  padding: 0.9rem;\n  border-radius: 6.4rem;\n  background: ${({ theme, disabled }) =>\n    disabled ? theme.colors.neutral100 : theme.colors.primary100};\n  path {\n    fill: ${({ theme, disabled }) =>\n      disabled ? theme.colors.neutral600 : theme.colors.primary600};\n  }\n`;\n\nconst ComponentStack = styled(Flex)`\n  flex-shrink: 0;\n  width: 14rem;\n  height: 8rem;\n  justify-content: center;\n  align-items: center;\n`;\n\nexport const DynamicZoneList = ({\n  components = [],\n  addComponent,\n  name,\n  forTarget,\n  targetUid,\n  disabled = false,\n}: DynamicZoneListProps) => {\n  const { isInDevelopmentMode } = useDataManager();\n  const [activeTab, setActiveTab] = useState(0);\n  const { formatMessage } = useIntl();\n\n  const toggle = (tab: number) => {\n    if (activeTab !== tab) {\n      setActiveTab(tab);\n    }\n  };\n\n  const handleClickAdd = () => {\n    addComponent(name);\n  };\n\n  return (\n    <ComponentRow className=\"dynamiczone-row\" $isFromDynamicZone>\n      <Box>\n        <Box padding={2} paddingLeft=\"104px\">\n          <Flex role=\"tablist\" gap={2} wrap=\"wrap\">\n            {isInDevelopmentMode && (\n              <button\n                type=\"button\"\n                onClick={handleClickAdd}\n                disabled={disabled}\n                style={{\n                  cursor: disabled ? 'not-allowed' : 'pointer',\n                }}\n              >\n                <ComponentStack direction=\"column\" alignItems=\"stretch\" gap={1}>\n                  <StyledAddIcon disabled={disabled} />\n                  <Typography\n                    variant=\"pi\"\n                    fontWeight=\"bold\"\n                    textColor={disabled ? 'neutral600' : 'primary600'}\n                  >\n                    {formatMessage({\n                      id: getTrad('button.component.add'),\n                      defaultMessage: 'Add a component',\n                    })}\n                  </Typography>\n                </ComponentStack>\n              </button>\n            )}\n            {components.map((component, index) => {\n              return (\n                <ComponentCard\n                  key={component}\n                  dzName={name || ''}\n                  index={index}\n                  component={component}\n                  isActive={activeTab === index}\n                  isInDevelopmentMode={isInDevelopmentMode}\n                  onClick={() => toggle(index)}\n                  forTarget={forTarget}\n                  targetUid={targetUid}\n                  disabled={disabled}\n                />\n              );\n            })}\n          </Flex>\n        </Box>\n        <Box>\n          {components.map((component, index) => {\n            return (\n              <Box\n                id={`dz-${name}-panel-${index}`}\n                role=\"tabpanel\"\n                aria-labelledby={`dz-${name}-tab-${index}`}\n                key={component}\n                style={{ display: activeTab === index ? 'block' : 'none' }}\n              >\n                <ComponentList isFromDynamicZone component={component} key={component} />\n              </Box>\n            );\n          })}\n        </Box>\n      </Box>\n    </ComponentRow>\n  );\n};\n", "import { forwardRef, memo, useState } from 'react';\n\nimport { ConfirmDialog } from '@strapi/admin/strapi-admin';\nimport { Box, Flex, IconButton, Typography, Link, Badge, Dialog } from '@strapi/design-system';\nimport { ChevronDown, Drag, Lock, Pencil, Trash } from '@strapi/icons';\nimport get from 'lodash/get';\nimport upperFirst from 'lodash/upperFirst';\nimport { useIntl } from 'react-intl';\nimport { Link as NavLink } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { Curve } from '../icons/Curve';\nimport { checkDependentRows } from '../utils/conditions';\nimport { getAttributeDisplayedType } from '../utils/getAttributeDisplayedType';\nimport { getRelationType } from '../utils/getRelationType';\nimport { getTrad } from '../utils/getTrad';\n\nimport { AttributeIcon } from './AttributeIcon';\nimport { ComponentList } from './ComponentList';\nimport { useDataManager } from './DataManager/useDataManager';\nimport { DisplayedType } from './DisplayedType';\nimport { DynamicZoneList } from './DynamicZoneList';\nimport { useFormModalNavigation } from './FormModalNavigation/useFormModalNavigation';\nimport { StatusBadge } from './Status';\n\nimport type { AnyAttribute, Component, ContentType } from '../types';\nimport type { DraggableAttributes, DraggableSyntheticListeners } from '@dnd-kit/core';\nimport type { UID } from '@strapi/types';\n\nexport const GridWrapper = styled(Flex)<{ $isOverlay?: boolean; $isDragging?: boolean }>`\n  justify-content: space-between;\n\n  border-top: ${({ theme, $isOverlay }) =>\n    $isOverlay ? 'none' : `1px solid ${theme.colors.neutral150}`};\n\n  padding-top: ${({ theme }) => theme.spaces[4]};\n  padding-bottom: ${({ theme }) => theme.spaces[4]};\n\n  opacity: ${({ $isDragging }) => ($isDragging ? 0 : 1)};\n  align-items: center;\n`;\n\nexport type AttributeRowProps = {\n  item: {\n    id: string;\n    index: number;\n  } & AnyAttribute;\n  firstLoopComponentUid?: UID.Component | null;\n  isFromDynamicZone?: boolean;\n  addComponentToDZ?: () => void;\n  secondLoopComponentUid?: UID.Component | null;\n  type: ContentType | Component;\n  isDragging?: boolean;\n  style?: Record<string, unknown>;\n  listeners?: DraggableSyntheticListeners;\n  attributes?: DraggableAttributes;\n  isOverlay?: boolean;\n  handleRef?: (element: HTMLElement | null) => void;\n};\n\nconst StyledAttributeRow = styled(Box)`\n  list-style: none;\n  list-style-type: none;\n`;\n\nexport const AttributeRow = forwardRef<HTMLLIElement, AttributeRowProps>((props, ref) => {\n  const { style, ...rest } = props;\n\n  return (\n    <StyledAttributeRow\n      tag=\"li\"\n      ref={ref}\n      {...props.attributes}\n      style={style}\n      background=\"neutral0\"\n      shadow={props.isOverlay ? 'filterShadow' : 'none'}\n      aria-label={props.item.name}\n    >\n      <MemoizedRow {...rest} />\n    </StyledAttributeRow>\n  );\n});\n\nconst MemoizedRow = memo((props: Omit<AttributeRowProps, 'style'>) => {\n  const {\n    item,\n    firstLoopComponentUid,\n    isFromDynamicZone,\n    addComponentToDZ,\n    secondLoopComponentUid,\n    type,\n    isDragging,\n    isOverlay,\n    handleRef,\n    listeners,\n  } = props;\n  const shouldHideNestedInfos = isOverlay || isDragging;\n\n  const [isOpen, setIsOpen] = useState<boolean>(true);\n\n  const isTypeDeleted = type.status === 'REMOVED';\n\n  const { contentTypes, removeAttribute, isInDevelopmentMode } = useDataManager();\n  const { onOpenModalEditField, onOpenModalEditCustomField } = useFormModalNavigation();\n\n  const { formatMessage } = useIntl();\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n\n  const isDeleted = item.status === 'REMOVED';\n\n  const isMorph = item.type === 'relation' && item.relation.includes('morph');\n  const ico = ['integer', 'biginteger', 'float', 'decimal'].includes(item.type)\n    ? 'number'\n    : item.type;\n\n  const targetContentType = item.type === 'relation' ? get(contentTypes, item.target) : null;\n  const isPluginContentType = get(targetContentType, 'plugin');\n\n  const src = 'target' in item && item.target ? 'relation' : ico;\n\n  const handleDelete = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    const dependentRows = checkDependentRows(contentTypes, item.name);\n    if (dependentRows.length > 0) {\n      setShowConfirmDialog(true);\n    } else {\n      removeAttribute({\n        forTarget: type.modelType,\n        targetUid: type.uid,\n        attributeToRemoveName: item.name,\n      });\n    }\n  };\n\n  const handleConfirmDelete = () => {\n    removeAttribute({\n      forTarget: type.modelType,\n      targetUid: type.uid,\n      attributeToRemoveName: item.name,\n    });\n    setShowConfirmDialog(false);\n  };\n\n  const handleCancelDelete = () => {\n    setShowConfirmDialog(false);\n  };\n\n  const handleClick = () => {\n    if (isMorph) {\n      return;\n    }\n\n    if (item.configurable !== false) {\n      const editTargetUid = (secondLoopComponentUid || firstLoopComponentUid || type.uid)!;\n\n      const attributeType = getAttributeDisplayedType(item.type);\n      const step = item.type === 'component' ? '2' : null;\n\n      if (item.customField) {\n        onOpenModalEditCustomField({\n          forTarget: type.modelType,\n          targetUid: editTargetUid,\n          attributeName: item.name,\n          attributeType,\n          customFieldUid: item.customField,\n        });\n      } else {\n        onOpenModalEditField({\n          forTarget: type.modelType,\n          targetUid: editTargetUid,\n          attributeName: item.name,\n          attributeType,\n          step,\n        });\n      }\n    }\n  };\n\n  let loopNumber;\n\n  if (secondLoopComponentUid && firstLoopComponentUid) {\n    loopNumber = 2;\n  } else if (firstLoopComponentUid) {\n    loopNumber = 1;\n  } else {\n    loopNumber = 0;\n  }\n\n  const canEdit = !isTypeDeleted && !isDeleted;\n  const canDelete = !isTypeDeleted && !isDeleted;\n\n  const cursor = isTypeDeleted || isDeleted ? 'not-allowed' : 'move';\n\n  const canClick = isInDevelopmentMode && item.configurable !== false && !isMorph && canEdit;\n\n  return (\n    <>\n      <GridWrapper\n        $isOverlay={isOverlay}\n        $isDragging={isDragging}\n        onClick={canClick ? handleClick : undefined}\n        paddingLeft={4}\n        paddingRight={4}\n      >\n        <Flex alignItems=\"center\" overflow=\"hidden\" gap={2}>\n          {loopNumber !== 0 && !isOverlay && (\n            <Curve color={isFromDynamicZone ? 'primary200' : 'neutral150'} />\n          )}\n          {isInDevelopmentMode && (\n            <IconButton\n              cursor={cursor}\n              role=\"Handle\"\n              ref={handleRef}\n              {...listeners}\n              variant=\"ghost\"\n              withTooltip={false}\n              label={`${formatMessage({\n                id: 'app.utils.drag',\n                defaultMessage: 'Drag',\n              })} ${item.name}`}\n              disabled={isTypeDeleted || isDeleted}\n            >\n              <Drag />\n            </IconButton>\n          )}\n          <Flex gap={4}>\n            <Flex gap={4} alignItems=\"center\">\n              <AttributeIcon type={src} customField={item.customField} />\n              <Typography\n                textColor=\"neutral800\"\n                fontWeight=\"bold\"\n                textDecoration={isDeleted ? 'line-through' : 'none'}\n                ellipsis\n                overflow=\"hidden\"\n              >\n                {item.name}\n                {'required' in item && item.required && (\n                  <Typography textColor=\"danger600\">*&nbsp;</Typography>\n                )}\n              </Typography>\n            </Flex>\n            <Flex>\n              <Typography textColor=\"neutral600\">\n                <DisplayedType\n                  type={item.type}\n                  customField={item.customField}\n                  repeatable={'repeatable' in item && item.repeatable}\n                  multiple={'multiple' in item && item.multiple}\n                />\n                {'conditions' in item &&\n                  item.conditions &&\n                  Object.keys(item.conditions).length > 0 && <Badge margin={4}>conditional</Badge>}\n                {item.type === 'relation' && (\n                  <>\n                    &nbsp;({getRelationType(item.relation, item.targetAttribute)})&nbsp;\n                    {targetContentType &&\n                      formatMessage({\n                        id: getTrad('modelPage.attribute.with'),\n                        defaultMessage: 'with',\n                      })}\n                    &nbsp;\n                    {targetContentType && (\n                      <Link\n                        onClick={(e) => e.stopPropagation()}\n                        tag={NavLink}\n                        to={`/plugins/content-type-builder/content-types/${targetContentType.uid}`}\n                      >\n                        {upperFirst(targetContentType.info.displayName)}\n                      </Link>\n                    )}\n                    {isPluginContentType &&\n                      `(${formatMessage({\n                        id: getTrad(`from`),\n                        defaultMessage: 'from',\n                      })}: ${isPluginContentType})`}\n                  </>\n                )}\n                {item.type === 'component' && <ComponentLink uid={item.component} />}\n              </Typography>\n            </Flex>\n          </Flex>\n        </Flex>\n\n        <Box>\n          <Flex justifyContent=\"flex-end\" gap={1} onClick={(e) => e.stopPropagation()}>\n            <>\n              <Box>{item.status && <StatusBadge status={item.status} />}</Box>\n              {['component', 'dynamiczone'].includes(item.type) && (\n                <IconButton\n                  onClick={(e) => {\n                    e.preventDefault();\n                    e.stopPropagation();\n\n                    if (isOpen) {\n                      setIsOpen(false);\n                    } else {\n                      setIsOpen(true);\n                    }\n                  }}\n                  aria-expanded={isOpen}\n                  label={formatMessage({\n                    id: 'app.utils.toggle',\n                    defaultMessage: 'Toggle',\n                  })}\n                  variant=\"ghost\"\n                  withTooltip={false}\n                >\n                  <ChevronDown\n                    aria-hidden\n                    fill=\"neutral500\"\n                    style={{\n                      transform: `rotate(${isOpen ? '0deg' : '-90deg'})`,\n                      transition: 'transform 0.5s',\n                    }}\n                  />\n                </IconButton>\n              )}\n              {isInDevelopmentMode && item.configurable !== false ? (\n                <>\n                  {!isMorph && (\n                    <IconButton\n                      onClick={handleClick}\n                      label={`${formatMessage({\n                        id: 'app.utils.edit',\n                        defaultMessage: 'Edit',\n                      })} ${item.name}`}\n                      variant=\"ghost\"\n                      disabled={!canEdit}\n                    >\n                      <Pencil />\n                    </IconButton>\n                  )}\n                  <IconButton\n                    onClick={handleDelete}\n                    label={`${formatMessage({\n                      id: 'global.delete',\n                      defaultMessage: 'Delete',\n                    })} ${item.name}`}\n                    variant=\"ghost\"\n                    disabled={!canDelete}\n                  >\n                    <Trash />\n                  </IconButton>\n                  <Dialog.Root open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>\n                    <ConfirmDialog onConfirm={handleConfirmDelete} onCancel={handleCancelDelete}>\n                      <Box>\n                        <Typography>\n                          {formatMessage({\n                            id: getTrad(\n                              'popUpWarning.bodyMessage.delete-attribute-with-conditions'\n                            ),\n                            defaultMessage:\n                              'The following fields have conditions that depend on this field: ',\n                          })}\n                          <Typography fontWeight=\"bold\">\n                            {checkDependentRows(contentTypes, item.name)\n                              .map(({ attribute }) => attribute)\n                              .join(', ')}\n                          </Typography>\n                          {formatMessage({\n                            id: getTrad(\n                              'popUpWarning.bodyMessage.delete-attribute-with-conditions-end'\n                            ),\n                            defaultMessage: '. Are you sure you want to delete it?',\n                          })}\n                        </Typography>\n                      </Box>\n                    </ConfirmDialog>\n                  </Dialog.Root>\n                </>\n              ) : (\n                <Flex padding={2}>\n                  <Lock fill=\"neutral500\" />\n                </Flex>\n              )}\n            </>\n          </Flex>\n        </Box>\n      </GridWrapper>\n\n      <SubRow $shouldHideNestedInfos={shouldHideNestedInfos} $isOpen={isOpen}>\n        {item.type === 'component' && (\n          <ComponentList\n            {...item}\n            isFromDynamicZone={isFromDynamicZone}\n            firstLoopComponentUid={firstLoopComponentUid}\n          />\n        )}\n\n        {item.type === 'dynamiczone' && (\n          <DynamicZoneList\n            {...item}\n            disabled={isTypeDeleted || item.status === 'REMOVED'}\n            addComponent={addComponentToDZ!}\n            forTarget={type.modelType}\n            targetUid={type.uid}\n          />\n        )}\n      </SubRow>\n    </>\n  );\n});\n\nconst SubRow = styled(Box)<{ $isOpen: boolean; $shouldHideNestedInfos?: boolean }>`\n  display: ${({ $shouldHideNestedInfos }) => ($shouldHideNestedInfos ? 'none' : 'block')};\n  max-height: ${({ $isOpen }) => ($isOpen ? '9999px' : '0px')};\n  overflow: hidden;\n\n  transition: ${({ $isOpen }) =>\n    $isOpen ? 'max-height 1s ease-in-out' : 'max-height 0.5s cubic-bezier(0, 1, 0, 1)'};\n`;\n\nconst ComponentLink = ({ uid }: { uid: UID.Component }) => {\n  const { components } = useDataManager();\n  const type = get(components, uid);\n\n  return (\n    <>\n      &nbsp;(\n      <Link\n        onClick={(e) => e.stopPropagation()}\n        tag={NavLink}\n        to={`/plugins/content-type-builder/component-categories/${type.category}/${type.uid}`}\n      >\n        {upperFirst(type.info.displayName)}\n      </Link>\n      )\n    </>\n  );\n};\n", "import type { ReactNode } from 'react';\n\nimport { Box, Divider, Flex, TFooterProps, Typography } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nconst IconBox = styled(Box)`\n  height: 2.4rem;\n  width: 2.4rem;\n  border-radius: 50%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n\n  svg {\n    height: 1rem;\n    width: 1rem;\n  }\n\n  svg path {\n    fill: ${({ theme, color }) => theme.colors[`${color}600`]};\n  }\n`;\n\nconst ButtonBox = styled(Box)`\n  border-radius: 0 0 ${({ theme }) => theme.borderRadius} ${({ theme }) => theme.borderRadius};\n  display: block;\n  width: 100%;\n  border: none;\n  position: relative;\n`;\n\ninterface NestedTFooterProps extends TFooterProps {\n  color: string;\n  children: ReactNode;\n  icon: ReactNode;\n  onClick?: () => void;\n}\n\nexport const NestedTFooter = ({ children, icon, color, ...props }: NestedTFooterProps) => {\n  return (\n    <ButtonBox\n      paddingBottom={4}\n      paddingTop={4}\n      paddingLeft=\"6rem\"\n      tag=\"button\"\n      type=\"button\"\n      {...props}\n    >\n      <Flex>\n        <IconBox color={color} aria-hidden background={`${color}200`}>\n          {icon}\n        </IconBox>\n        <Box paddingLeft={3}>\n          <Typography variant=\"pi\" fontWeight=\"bold\" textColor={`${color}600`}>\n            {children}\n          </Typography>\n        </Box>\n      </Flex>\n    </ButtonBox>\n  );\n};\n\nexport const TFooter = ({ children, icon, color, ...props }: TFooterProps & { color: string }) => {\n  return (\n    <div>\n      <Divider />\n      <ButtonBox tag=\"button\" background={`${color}100`} padding={5} {...props}>\n        <Flex>\n          <IconBox color={color} aria-hidden background={`${color}200`}>\n            {icon}\n          </IconBox>\n          <Box paddingLeft={3}>\n            <Typography variant=\"pi\" fontWeight=\"bold\" textColor={`${color}600`}>\n              {children}\n            </Typography>\n          </Box>\n        </Flex>\n      </ButtonBox>\n    </div>\n  );\n};\n", "import { useState } from 'react';\n\nimport {\n  DndContext,\n  closestCenter,\n  PointerSensor,\n  useSensor,\n  useSensors,\n  DragEndEvent,\n  DragStartEvent,\n  DragOverlay,\n  UniqueIdentifier,\n} from '@dnd-kit/core';\nimport { restrictToVerticalAxis } from '@dnd-kit/modifiers';\nimport { SortableContext, verticalListSortingStrategy, useSortable } from '@dnd-kit/sortable';\nimport { CSS } from '@dnd-kit/utilities';\nimport { useTracking } from '@strapi/admin/strapi-admin';\nimport { Box, Button, EmptyStateLayout } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { createPortal } from 'react-dom';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { getTrad } from '../utils/getTrad';\n\nimport { AttributeRow, type AttributeRowProps } from './AttributeRow';\nimport { useDataManager } from './DataManager/useDataManager';\nimport { NestedTFooter, TFooter } from './Footers';\nimport { useFormModalNavigation } from './FormModalNavigation/useFormModalNavigation';\n\nimport type { Component, ContentType } from '../types';\nimport type { UID } from '@strapi/types';\n\nexport const ListGrid = styled(Box)`\n  white-space: nowrap;\n  list-style: none;\n  list-style-type: none;\n`;\n\ntype ListProps = {\n  addComponentToDZ?: () => void;\n  firstLoopComponentUid?: UID.Component | null;\n  isFromDynamicZone?: boolean;\n  isMain?: boolean;\n  secondLoopComponentUid?: UID.Component | null;\n  isSub?: boolean;\n  type: ContentType | Component;\n};\n\nconst SortableRow = (props: AttributeRowProps) => {\n  const { isInDevelopmentMode } = useDataManager();\n\n  const {\n    isDragging,\n    attributes,\n    listeners,\n    setNodeRef,\n    transform,\n    transition,\n    setActivatorNodeRef,\n  } = useSortable({\n    disabled:\n      !isInDevelopmentMode || props.item.status === 'REMOVED' || props.type.status === 'REMOVED',\n    id: props.item.id,\n    data: { index: props.item.index },\n  });\n\n  const style = {\n    transform: CSS.Transform.toString({\n      x: transform?.x ?? 0,\n      y: transform?.y ?? 0,\n      scaleX: 1,\n      scaleY: 1,\n    }),\n    transition,\n  };\n\n  return (\n    <AttributeRow\n      ref={setNodeRef}\n      handleRef={setActivatorNodeRef}\n      isDragging={isDragging}\n      attributes={attributes}\n      listeners={listeners}\n      style={style}\n      {...props}\n    />\n  );\n};\n\nexport const List = ({\n  addComponentToDZ,\n  firstLoopComponentUid,\n  isFromDynamicZone = false,\n  isMain = false,\n  isSub = false,\n  secondLoopComponentUid,\n  type,\n}: ListProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { isInDevelopmentMode, moveAttribute } = useDataManager();\n  const { onOpenModalAddField } = useFormModalNavigation();\n\n  const items = type?.attributes.map((item, index) => {\n    return {\n      id: `${type.uid}_${item.name}`,\n      index,\n      ...item,\n    };\n  });\n\n  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);\n\n  const isDeleted = type?.status === 'REMOVED';\n\n  const sensors = useSensors(useSensor(PointerSensor));\n\n  function handlerDragStart({ active }: DragStartEvent) {\n    if (!active) {\n      return;\n    }\n\n    setActiveId(active.id);\n  }\n\n  function handleDragEnd(event: DragEndEvent) {\n    const { active, over } = event;\n\n    setActiveId(null);\n\n    if (over) {\n      if (active.id !== over.id) {\n        moveAttribute({\n          forTarget: type.modelType,\n          targetUid: type.uid,\n          from: active.data.current!.index,\n          to: over.data.current!.index,\n        });\n      }\n    }\n  }\n\n  const activeItem = items.find((item) => item.id === activeId);\n\n  const onClickAddField = () => {\n    if (isDeleted) {\n      return;\n    }\n\n    trackUsage('hasClickedCTBAddFieldBanner');\n\n    onOpenModalAddField({ forTarget: type?.modelType, targetUid: type.uid });\n  };\n\n  if (type?.attributes.length === 0 && isMain) {\n    return (\n      <EmptyStateLayout\n        action={\n          <Button onClick={onClickAddField} size=\"L\" startIcon={<Plus />} variant=\"secondary\">\n            {formatMessage({\n              id: getTrad('table.button.no-fields'),\n              defaultMessage: 'Add new field',\n            })}\n          </Button>\n        }\n        content={formatMessage(\n          type.modelType === 'contentType'\n            ? {\n                id: getTrad('table.content.no-fields.collection-type'),\n                defaultMessage: 'Add your first field to this Collection-Type',\n              }\n            : {\n                id: getTrad('table.content.no-fields.component'),\n                defaultMessage: 'Add your first field to this component',\n              }\n        )}\n        hasRadius\n        icon={<EmptyDocuments width=\"16rem\" />}\n      />\n    );\n  }\n\n  return (\n    <DndContext\n      sensors={sensors}\n      collisionDetection={closestCenter}\n      onDragEnd={handleDragEnd}\n      onDragStart={handlerDragStart}\n      onDragCancel={() => setActiveId(null)}\n      modifiers={[restrictToVerticalAxis]}\n    >\n      <ListGrid tag=\"ul\">\n        {createPortal(\n          <DragOverlay zIndex={10}>\n            {activeItem && (\n              <AttributeRow\n                isOverlay\n                item={activeItem}\n                firstLoopComponentUid={firstLoopComponentUid}\n                isFromDynamicZone={isFromDynamicZone}\n                secondLoopComponentUid={secondLoopComponentUid}\n                type={type}\n                addComponentToDZ={addComponentToDZ}\n              />\n            )}\n          </DragOverlay>,\n          document.body\n        )}\n        <SortableContext items={items} strategy={verticalListSortingStrategy}>\n          {items.map((item) => {\n            return (\n              <SortableRow\n                key={item.id}\n                item={item}\n                firstLoopComponentUid={firstLoopComponentUid}\n                isFromDynamicZone={isFromDynamicZone}\n                secondLoopComponentUid={secondLoopComponentUid}\n                type={type}\n                addComponentToDZ={addComponentToDZ}\n              />\n            );\n          })}\n        </SortableContext>\n      </ListGrid>\n\n      {isMain && isInDevelopmentMode && (\n        <TFooter\n          cursor={isDeleted ? 'normal' : 'pointer'}\n          icon={<Plus />}\n          onClick={onClickAddField}\n          color={isDeleted ? 'neutral' : 'primary'}\n        >\n          {formatMessage({\n            id: getTrad(\n              `form.button.add.field.to.${type.modelType === 'component' ? 'component' : type.kind}`\n            ),\n            defaultMessage: 'Add another field',\n          })}\n        </TFooter>\n      )}\n      {isSub && isInDevelopmentMode && (\n        <NestedTFooter\n          cursor={isDeleted ? 'normal' : 'pointer'}\n          icon={<Plus />}\n          onClick={onClickAddField}\n          color={isFromDynamicZone && !isDeleted ? 'primary' : 'neutral'}\n        >\n          {formatMessage({\n            id: getTrad(`form.button.add.field.to.component`),\n            defaultMessage: 'Add another field',\n          })}\n        </NestedTFooter>\n      )}\n    </DndContext>\n  );\n};\n", "import { memo } from 'react';\n\nimport { type Permission, useRBAC } from '@strapi/admin/strapi-admin';\nimport { Button } from '@strapi/design-system';\nimport { ListPlus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useNavigate } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport type { Component, ContentType } from '../../types';\n\nconst cmPermissions: Record<string, Permission[]> = {\n  collectionTypesConfigurations: [\n    {\n      action: 'plugin::content-manager.collection-types.configure-view',\n      subject: null,\n    },\n  ],\n  componentsConfigurations: [\n    {\n      action: 'plugin::content-manager.components.configure-layout',\n      subject: null,\n    },\n  ],\n  singleTypesConfigurations: [\n    {\n      action: 'plugin::content-manager.single-types.configure-view',\n      subject: null,\n    },\n  ],\n};\n\nconst getPermission = (type: Component | ContentType) => {\n  if (type.modelType === 'contentType') {\n    if (type.kind === 'singleType') {\n      return cmPermissions.singleTypesConfigurations;\n    }\n\n    return cmPermissions.collectionTypesConfigurations;\n  }\n\n  return cmPermissions.componentsConfigurations;\n};\n\ninterface LinkToCMSettingsViewProps {\n  disabled: boolean;\n  type: Component | ContentType;\n}\n\nconst getLink = (type: Component | ContentType) => {\n  switch (type.modelType) {\n    case 'contentType':\n      switch (type.kind) {\n        case 'singleType':\n          return `/content-manager/single-types/${type.uid}/configurations/edit`;\n        case 'collectionType':\n          return `/content-manager/collection-types/${type.uid}/configurations/edit`;\n      }\n    case 'component':\n      return `/content-manager/components/${type.uid}/configurations/edit`;\n  }\n};\n\nconst StyledButton = styled(Button)`\n  white-space: nowrap;\n`;\n\nexport const LinkToCMSettingsView = memo(({ disabled, type }: LinkToCMSettingsViewProps) => {\n  const { formatMessage } = useIntl();\n  const navigate = useNavigate();\n  const permissionsToApply = getPermission(type);\n\n  const label = formatMessage({\n    id: 'content-type-builder.form.button.configure-view',\n    defaultMessage: 'Configure the view',\n  });\n\n  const handleClick = () => {\n    if (disabled) {\n      return false;\n    }\n\n    const link = getLink(type);\n\n    navigate(link);\n\n    return false;\n  };\n\n  const { isLoading, allowedActions } = useRBAC(permissionsToApply);\n\n  if (isLoading) {\n    return null;\n  }\n\n  if (!allowedActions.canConfigureView && !allowedActions.canConfigureLayout) {\n    return null;\n  }\n\n  return (\n    <StyledButton\n      startIcon={<ListPlus />}\n      variant=\"tertiary\"\n      onClick={handleClick}\n      disabled={disabled}\n    >\n      {label}\n    </StyledButton>\n  );\n});\n", "/* eslint-disable import/no-default-export */\nimport { useTracking, Layouts } from '@strapi/admin/strapi-admin';\nimport { Box, Button, Flex, Typography } from '@strapi/design-system';\nimport { Information, Pencil, Plus } from '@strapi/icons';\nimport upperFirst from 'lodash/upperFirst';\nimport { useIntl } from 'react-intl';\nimport { Navigate, useParams } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { useDataManager } from '../../components/DataManager/useDataManager';\nimport { useFormModalNavigation } from '../../components/FormModalNavigation/useFormModalNavigation';\nimport { List } from '../../components/List';\nimport { getTrad } from '../../utils/getTrad';\n\nimport { LinkToCMSettingsView } from './LinkToCMSettingsView';\n\nimport type { Internal } from '@strapi/types';\n\nconst LayoutsHeaderCustom = styled(Layouts.Header)`\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n`;\n\nconst ListView = () => {\n  const { isInDevelopmentMode, contentTypes, components, isLoading } = useDataManager();\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n\n  const { contentTypeUid, componentUid } = useParams<{\n    contentTypeUid: Internal.UID.ContentType;\n    componentUid: Internal.UID.Component;\n  }>();\n\n  const { onOpenModalAddComponentsToDZ, onOpenModalAddField, onOpenModalEditSchema } =\n    useFormModalNavigation();\n\n  const type = contentTypeUid\n    ? contentTypes[contentTypeUid]\n    : componentUid\n      ? components[componentUid]\n      : null;\n\n  if (isLoading) {\n    return null;\n  }\n\n  if (!type) {\n    const allowedEndpoints = Object.values(contentTypes)\n      .filter((ct) => ct.visible === true && !ct.plugin)\n      .map((ct) => ct.uid)\n      .sort();\n\n    if (allowedEndpoints.length > 0) {\n      return <Navigate to={`/plugins/content-type-builder/content-types/${allowedEndpoints[0]}`} />;\n    }\n\n    return <Navigate to=\"/plugins/content-type-builder/content-types/create-content-type\" />;\n  }\n\n  const isFromPlugin = 'plugin' in type && type?.plugin !== undefined;\n\n  const forTarget = contentTypeUid ? 'contentType' : 'component';\n\n  const label = type?.info?.displayName ?? '';\n\n  const canEdit = isInDevelopmentMode && !isFromPlugin;\n\n  const handleClickAddComponentToDZ = (dynamicZoneTarget?: string) => {\n    onOpenModalAddComponentsToDZ({ dynamicZoneTarget, targetUid: type.uid });\n  };\n\n  const onEdit = () => {\n    if ('kind' in type) {\n      if (type?.kind === 'collectionType') {\n        trackUsage('willEditNameOfContentType');\n      }\n\n      if (type?.kind === 'singleType') {\n        trackUsage('willEditNameOfSingleType');\n      }\n\n      onOpenModalEditSchema({\n        modalType: forTarget,\n        forTarget: forTarget,\n        targetUid: type.uid,\n        kind: type?.kind,\n      });\n\n      return;\n    }\n\n    onOpenModalEditSchema({\n      modalType: forTarget,\n      forTarget: forTarget,\n      targetUid: type.uid,\n    });\n  };\n\n  const addNewFieldLabel = formatMessage({\n    id: getTrad('table.button.no-fields'),\n    defaultMessage: 'Add new field',\n  });\n\n  const addAnotherFieldLabel = formatMessage({\n    id: getTrad('button.attributes.add.another'),\n    defaultMessage: 'Add another field',\n  });\n\n  const isDeleted = type.status === 'REMOVED';\n\n  const primaryAction = isInDevelopmentMode && (\n    <Flex gap={2}>\n      <LinkToCMSettingsView\n        key=\"link-to-cm-settings-view\"\n        type={type}\n        disabled={type.status === 'NEW' || isDeleted}\n      />\n      <Button\n        startIcon={<Pencil />}\n        variant=\"tertiary\"\n        onClick={onEdit}\n        disabled={!canEdit || isDeleted}\n      >\n        {formatMessage({\n          id: 'app.utils.edit',\n          defaultMessage: 'Edit',\n        })}\n      </Button>\n      <Button\n        startIcon={<Plus />}\n        variant=\"secondary\"\n        minWidth=\"max-content\"\n        onClick={() => {\n          onOpenModalAddField({ forTarget, targetUid: type.uid });\n        }}\n        disabled={isDeleted}\n      >\n        {type.attributes.length === 0 ? addNewFieldLabel : addAnotherFieldLabel}\n      </Button>\n    </Flex>\n  );\n\n  return (\n    <>\n      {isDeleted && (\n        <Flex background=\"danger100\" justifyContent={'center'} padding={4}>\n          <Flex gap={2}>\n            <Information fill=\"danger600\" height=\"2rem\" width=\"2rem\" />\n            <Typography>\n              {formatMessage(\n                {\n                  id: getTrad('table.warning.deleted'),\n                  defaultMessage: `This {kind} has been deleted`,\n                },\n                {\n                  kind: type.modelType === 'contentType' ? 'Content Type' : 'Component',\n                }\n              )}\n            </Typography>\n          </Flex>\n        </Flex>\n      )}\n      <LayoutsHeaderCustom id=\"title\" primaryAction={primaryAction} title={upperFirst(label)} />\n      <Layouts.Content>\n        <Box\n          background=\"neutral0\"\n          shadow=\"filterShadow\"\n          hasRadius\n          overflow=\"auto\"\n          borderColor=\"neutral150\"\n        >\n          <List type={type} addComponentToDZ={handleClickAddComponentToDZ} isMain />\n        </Box>\n      </Layouts.Content>\n    </>\n  );\n};\n\nexport default ListView;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IKEaA,yBAAmC,UAAA;MAAC;IAACC;;AAChD,SAAO;IACL,GAAGA;IACHC,GAAG;;AAEN;;;;;;;;;;;;;AGJD,IAAMC,YAAYC,GAAOC,GAAAA;;;;;;;;;kBASP,CAAC,EAAEC,OAAOC,MAAK,MAAOD,MAAME,OAAOD,KAAAA,CAAO;;;;AAK5D,IAAME,MAAML,GAAOM;;;;;;YAMP,CAAC,EAAEJ,OAAOC,MAAK,MAAOD,MAAME,OAAOD,KAAAA,CAAO;;;AAQzCI,IAAAA,QAAQ,CAACC,cACpBC,wBAACV,WAAAA;EACC,cAAAU,wBAACJ,KAAAA;IACCK,OAAM;IACNC,QAAO;IACPC,SAAQ;IACRC,MAAK;IACLC,OAAM;IACL,GAAGN;IAEJ,cAAAC,wBAACM,QAAAA;MACCC,UAAS;MACTC,UAAS;MACTC,GAAE;;;AAIR,CAAA;;;AChDK,IAAMC,4BAA4B,CAACC,SAAAA;AACxC,MAAIC;AAEJ,UAAQD,MAAAA;IACN,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACHC,sBAAgB;AAChB;IACF,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACHA,sBAAgB;AAChB;IACF,KAAK;IACL,KAAK;AACHA,sBAAgB;AAChB;IACF,KAAK;AACHA,sBAAgB;AAChB;IACF;AACEA,sBAAgBD;EACpB;AAEA,SAAOC;AACT;;;;;;;ACzBaC,IAAAA,eAAeC,GAAOC,GAAAA;;;;;;;;;;;;kBAejB,CAAC,EAAEC,mBAAkB,MAC7BA,qBAAqB,sBAAsB,YAAa;;;;;UAKxD,CAAC,EAAEA,oBAAoBC,uBAAuBC,MAAK,MAAE;AACrD,MAAID,uBAAuB;AACzB,WAAO,qBAAqBC,MAAMC,OAAOC,UAAU;EACrD;AAEA,MAAIJ,oBAAoB;AACtB,WAAO,qBAAqBE,MAAMC,OAAOC,UAAU;EACrD;AAEA,SAAO,eAAeF,MAAMC,OAAOE,UAAU;AAC/C,CAAE;;;;;;;;;;;ACnBH,IAAMC,gBAAgB,CAAC,EAC5BC,WACAC,oBAAoB,OACpBC,sBAAqB,MACF;AACnB,QAAM,EAAEC,WAAU,IAAKC,eAAAA;AACvB,QAAMC,WAAOC,WAAAA,SAAIH,YAAYH,SAAAA;AAE7B,aACEO,yBAACC,cAAAA;IAAaC,uBAAuBR;IAAmBS,WAAU;IAChE,cAAAH,yBAACI,MAAAA;MACCN;MACAH,uBAAuBA,yBAAyBF;MAChDC;MACAW,OAAK;MACLC,wBAAwBX,wBAAwBF,YAAY;;;AAIpE;;;;ACvBac,IAAAA,gBAAgB,CAAC,EAC5BC,MACAC,cAAc,MACdC,aAAa,OACbC,WAAW,MAAK,MACG;AACnB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,MAAIC,eAAeN;AAEnB,MAAI;IAAC;IAAW;IAAc;IAAS;IAAWO,SAASP,IAAO,GAAA;AAChEM,mBAAe;EACjB,WAAW;IAAC;IAAUC,SAASP,IAAO,GAAA;AACpCM,mBAAe;EACjB;AAEA,MAAIL,aAAa;AACf,WAAOG,cAAc;MACnBI,IAAIC,QAAQ,uBAAA;MACZC,gBAAgB;IAClB,CAAA;EACF;AAEA,aACEC,0BAAAC,8BAAA;;MACGV,cACCE,cAAc;QACZI,IAAIC,QAAQ,sBAAA;QACZC,gBAAgB;MAClB,CAAA;MACDP,YACCC,cAAc;QACZI,IAAIC,QAAQ,gBAAA;QACZC,gBAAgB;MAClB,CAAA;MAAG;MAEJN,cAAc;QACbI,IAAIC,QAAQ,aAAaH,YAAAA,EAAc;QACvCI,gBAAgBV;MAClB,CAAA;;;AAGN;;;;;;;;;;;;AC5CO,IAAMa,gBAAgB,CAAC,EAAEC,WAAW,OAAOC,OAAO,YAAW,MAAsB;AACxF,QAAMC,OAAOC,gBAAgBF,IAAK,KAAIE,gBAAgBC;AAEtD,aACEC,yBAACC,MAAAA;IACCC,YAAW;IACXC,YAAYR,WAAW,eAAe;IACtCS,gBAAe;IACfC,QAAQ;IACRC,OAAO;IACPC,cAAa;IAEb,cAAAP,yBAACH,MAAAA;MAAKQ,QAAO;MAAOC,OAAM;;;AAGhC;;;ACDA,IAAME,cAAcC,GAAOC,GAAAA;;;;;;;;;;;cAWb,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;;AAKpD,IAAMC,eAAeL,GAAOM,IAAAA;;;;sBAIN,CAAC,EAAEJ,MAAK,MAAOA,MAAMC,OAAOI,UAAU;gBAC5C,CAAC,EAAEL,MAAK,MAAOA,MAAMC,OAAOK,UAAU;mBACnC,CAAC,EAAEN,MAAK,MAAOA,MAAMO,YAAY;;;;;;wBAM5B,CAAC,EAAEP,MAAK,MAAOA,MAAMC,OAAOO,UAAU;kBAC5C,CAAC,EAAER,MAAK,MAAOA,MAAMC,OAAOQ,UAAU;aAC3C,CAAC,EAAET,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;MAE7CL,WAAY;;;;;;oBAME,CAAC,EAAEG,MAAK,MAAOA,MAAMC,OAAOO,UAAU;eAC3C,CAAC,EAAER,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;;kBAInC,CAAC,EAAEF,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;;;;;AAOjD,IAAMQ,gBAAgB,CAAC,EAC5BC,WACAC,QACAC,OACAC,WAAW,OACXC,sBAAsB,OACtBC,SACAC,WACAC,WACAC,SAAQ,MACW;AACnB,QAAM,EAAEC,YAAYC,+BAA8B,IAAKC,eAAAA;AACvD,QAAMC,WAAOC,YAAAA,SAAIJ,YAAYT,SAAAA;AAC7B,QAAM,EAAEc,MAAMC,YAAW,KAAKH,6BAAMI,SAAQ,CAAA;AAE5C,QAAMC,UAAU,CAACC,MAAAA;AACfA,MAAEC,gBAAe;AACjBT,mCAA+B;MAC7BJ;MACAC;MACAN;MACAmB,wBAAwBlB;IAC1B,CAAA;EACF;AAEA,aACEmB,0BAAC7B,cAAAA;IACC8B,YAAW;IACXC,WAAU;IACVC,WAAWrB,WAAW,WAAW;IACjCP,cAAa;IACb6B,gBAAe;IACfC,aAAa;IACbC,cAAc;IACdC,QAAQ;IACRvB;IACAwB,MAAK;IACLC,UAAU3B,WAAW,IAAI;IACzB4B,QAAO;IACPC,iBAAe7B;IACf8B,iBAAe,MAAMhC,MAAAA,UAAgBC,KAAAA;IACrCgC,IAAI,MAAMjC,MAAAA,QAAcC,KAAAA;;UAExBiC,yBAACC,eAAAA;QAActB;QAAYX;;UAE3BgC,yBAAC/C,KAAAA;QAAIiD,WAAW;QAAGC,UAAS;QAC1B,cAAAH,yBAACI,YAAAA;UAAWC,SAAQ;UAAKC,YAAW;UAAOC,UAAQ;UAChD3B,UAAAA;;;MAIJX,uBAAuB,CAACI,gBACvB2B,yBAACjD,aAAAA;QAAY6C,QAAO;QAAUY,KAAI;QAAStC,SAASY;QAClD,cAAAkB,yBAACS,eAAAA,CAAAA,CAAAA;;;;AAKX;;;ACzGA,IAAMC,gBAAgBC,GAAOC,aAAAA;;;;;gBAKb,CAAC,EAAEC,OAAOC,SAAQ,MAC9BA,WAAWD,MAAME,OAAOC,aAAaH,MAAME,OAAOE,UAAU;;YAEpD,CAAC,EAAEJ,OAAOC,SAAQ,MACxBA,WAAWD,MAAME,OAAOG,aAAaL,MAAME,OAAOI,UAAU;;;AAIlE,IAAMC,iBAAiBT,GAAOU,IAAAA;;;;;;;IAQjBC,kBAAkB,CAAC,EAC9BC,aAAa,CAAA,GACbC,cACAC,MACAC,WACAC,WACAb,WAAW,MAAK,MACK;AACrB,QAAM,EAAEc,oBAAmB,IAAKC,eAAAA;AAChC,QAAM,CAACC,WAAWC,YAAa,QAAGC,uBAAS,CAAA;AAC3C,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,SAAS,CAACC,QAAAA;AACd,QAAIN,cAAcM,KAAK;AACrBL,mBAAaK,GAAAA;IACf;EACF;AAEA,QAAMC,iBAAiB,MAAA;AACrBb,iBAAaC,IAAAA;EACf;AAEA,aACEa,yBAACC,cAAAA;IAAaC,WAAU;IAAkBC,oBAAkB;IAC1D,cAAAC,0BAACC,KAAAA;;YACCL,yBAACK,KAAAA;UAAIC,SAAS;UAAGC,aAAY;UAC3B,cAAAH,0BAACrB,MAAAA;YAAKyB,MAAK;YAAUC,KAAK;YAAGC,MAAK;;cAC/BpB,2BACCU,yBAACW,UAAAA;gBACCC,MAAK;gBACLC,SAASd;gBACTvB;gBACAsC,OAAO;kBACLC,QAAQvC,WAAW,gBAAgB;gBACrC;gBAEA,cAAA4B,0BAACtB,gBAAAA;kBAAekC,WAAU;kBAASC,YAAW;kBAAUR,KAAK;;wBAC3DT,yBAAC5B,eAAAA;sBAAcI;;wBACfwB,yBAACkB,YAAAA;sBACCC,SAAQ;sBACRC,YAAW;sBACXC,WAAW7C,WAAW,eAAe;gCAEpCmB,cAAc;wBACb2B,IAAIC,QAAQ,sBAAA;wBACZC,gBAAgB;sBAClB,CAAA;;;;;cAKPvC,WAAWwC,IAAI,CAACC,WAAWC,UAAAA;AAC1B,2BACE3B,yBAAC4B,eAAAA;kBAECC,QAAQ1C,QAAQ;kBAChBwC;kBACAD;kBACAI,UAAUtC,cAAcmC;kBACxBrC;kBACAuB,SAAS,MAAMhB,OAAO8B,KAAAA;kBACtBvC;kBACAC;kBACAb;gBATKkD,GAAAA,SAAAA;cAYX,CAAA;;;;YAGJ1B,yBAACK,KAAAA;oBACEpB,WAAWwC,IAAI,CAACC,WAAWC,UAAAA;AAC1B,uBACE3B,yBAACK,KAAAA;cACCiB,IAAI,MAAMnC,IAAAA,UAAcwC,KAAAA;cACxBnB,MAAK;cACLuB,mBAAiB,MAAM5C,IAAAA,QAAYwC,KAAAA;cAEnCb,OAAO;gBAAEkB,SAASxC,cAAcmC,QAAQ,UAAU;cAAO;cAEzD,cAAA3B,yBAACiC,eAAAA;gBAAcC,mBAAiB;gBAACR;cAA2BA,GAAAA,SAAAA;YAHvDA,GAAAA,SAAAA;UAMX,CAAA;;;;;AAKV;;;ACzGaS,IAAAA,cAAcC,GAAOC,IAAAA;;;gBAGlB,CAAC,EAAEC,OAAOC,WAAU,MAChCA,aAAa,SAAS,aAAaD,MAAME,OAAOC,UAAU,EAAE;;iBAE/C,CAAC,EAAEH,MAAK,MAAOA,MAAMI,OAAO,CAAA,CAAE;oBAC3B,CAAC,EAAEJ,MAAK,MAAOA,MAAMI,OAAO,CAAA,CAAE;;aAErC,CAAC,EAAEC,YAAW,MAAQA,cAAc,IAAI,CAAG;;;AAsBxD,IAAMC,qBAAqBR,GAAOS,GAAAA;;;;AAKrBC,IAAAA,mBAAeC,0BAA6C,CAACC,OAAOC,QAAAA;AAC/E,QAAM,EAAEC,OAAO,GAAGC,KAAAA,IAASH;AAE3B,aACEI,yBAACR,oBAAAA;IACCS,KAAI;IACJJ;IACC,GAAGD,MAAMM;IACVJ;IACAK,YAAW;IACXC,QAAQR,MAAMS,YAAY,iBAAiB;IAC3CC,cAAYV,MAAMW,KAAKC;IAEvB,cAAAR,yBAACS,aAAAA;MAAa,GAAGV;;;AAGvB,CAAG;AAEH,IAAMU,kBAAcC,oBAAK,CAACd,UAAAA;AACxB,QAAM,EACJW,MACAI,uBACAC,mBACAC,kBACAC,wBACAC,MACAC,YACAX,WACAY,WACAC,UAAS,IACPtB;AACJ,QAAMuB,wBAAwBd,aAAaW;AAE3C,QAAM,CAACI,QAAQC,SAAU,QAAGC,wBAAkB,IAAA;AAE9C,QAAMC,gBAAgBR,KAAKS,WAAW;AAEtC,QAAM,EAAEC,cAAcC,iBAAiBC,oBAAmB,IAAKC,eAAAA;AAC/D,QAAM,EAAEC,sBAAsBC,2BAA0B,IAAKC,uBAAAA;AAE7D,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,mBAAmBC,oBAAqB,QAAGb,wBAAS,KAAA;AAE3D,QAAMc,YAAY7B,KAAKiB,WAAW;AAElC,QAAMa,UAAU9B,KAAKQ,SAAS,cAAcR,KAAK+B,SAASC,SAAS,OAAA;AACnE,QAAMC,MAAM;IAAC;IAAW;IAAc;IAAS;EAAU,EAACD,SAAShC,KAAKQ,IAAI,IACxE,WACAR,KAAKQ;AAET,QAAM0B,oBAAoBlC,KAAKQ,SAAS,iBAAa2B,YAAAA,SAAIjB,cAAclB,KAAKoC,MAAM,IAAI;AACtF,QAAMC,0BAAsBF,YAAAA,SAAID,mBAAmB,QAAA;AAEnD,QAAMI,MAAM,YAAYtC,QAAQA,KAAKoC,SAAS,aAAaH;AAE3D,QAAMM,eAAe,CAACC,MAAAA;AACpBA,MAAEC,gBAAe;AACjB,UAAMC,gBAAgBC,mBAAmBzB,cAAclB,KAAKC,IAAI;AAChE,QAAIyC,cAAcE,SAAS,GAAG;AAC5BhB,2BAAqB,IAAA;WAChB;AACLT,sBAAgB;QACd0B,WAAWrC,KAAKsC;QAChBC,WAAWvC,KAAKwC;QAChBC,uBAAuBjD,KAAKC;MAC9B,CAAA;IACF;EACF;AAEA,QAAMiD,sBAAsB,MAAA;AAC1B/B,oBAAgB;MACd0B,WAAWrC,KAAKsC;MAChBC,WAAWvC,KAAKwC;MAChBC,uBAAuBjD,KAAKC;IAC9B,CAAA;AACA2B,yBAAqB,KAAA;EACvB;AAEA,QAAMuB,qBAAqB,MAAA;AACzBvB,yBAAqB,KAAA;EACvB;AAEA,QAAMwB,cAAc,MAAA;AAClB,QAAItB,SAAS;AACX;IACF;AAEA,QAAI9B,KAAKqD,iBAAiB,OAAO;AAC/B,YAAMC,gBAAiB/C,0BAA0BH,yBAAyBI,KAAKwC;AAE/E,YAAMO,gBAAgBC,0BAA0BxD,KAAKQ,IAAI;AACzD,YAAMiD,OAAOzD,KAAKQ,SAAS,cAAc,MAAM;AAE/C,UAAIR,KAAK0D,aAAa;AACpBnC,mCAA2B;UACzBsB,WAAWrC,KAAKsC;UAChBC,WAAWO;UACXK,eAAe3D,KAAKC;UACpBsD;UACAK,gBAAgB5D,KAAK0D;QACvB,CAAA;aACK;AACLpC,6BAAqB;UACnBuB,WAAWrC,KAAKsC;UAChBC,WAAWO;UACXK,eAAe3D,KAAKC;UACpBsD;UACAE;QACF,CAAA;MACF;IACF;EACF;AAEA,MAAII;AAEJ,MAAItD,0BAA0BH,uBAAuB;AACnDyD,iBAAa;EACf,WAAWzD,uBAAuB;AAChCyD,iBAAa;SACR;AACLA,iBAAa;EACf;AAEA,QAAMC,UAAU,CAAC9C,iBAAiB,CAACa;AACnC,QAAMkC,YAAY,CAAC/C,iBAAiB,CAACa;AAErC,QAAMmC,SAAShD,iBAAiBa,YAAY,gBAAgB;AAE5D,QAAMoC,WAAW7C,uBAAuBpB,KAAKqD,iBAAiB,SAAS,CAACvB,WAAWgC;AAEnF,aACEI,0BAAAC,8BAAA;;UACED,0BAAC1F,aAAAA;QACCI,YAAYkB;QACZd,aAAayB;QACb2D,SAASH,WAAWb,cAAciB;QAClCC,aAAa;QACbC,cAAc;;cAEdL,0BAACxF,MAAAA;YAAK8F,YAAW;YAASC,UAAS;YAASC,KAAK;;cAC9Cb,eAAe,KAAK,CAAC/D,iBACpBL,yBAACkF,OAAAA;gBAAMC,OAAOvE,oBAAoB,eAAe;;cAElDe,2BACC3B,yBAACoF,YAAAA;gBACCb;gBACAc,MAAK;gBACLxF,KAAKoB;gBACJ,GAAGC;gBACJoE,SAAQ;gBACRC,aAAa;gBACbC,OAAO,GAAGxD,cAAc;kBACtByD,IAAI;kBACJC,gBAAgB;gBAClB,CAAA,CAAA,IAAMnF,KAAKC,IAAI;gBACfmF,UAAUpE,iBAAiBa;gBAE3B,cAAApC,yBAAC4F,eAAAA,CAAAA,CAAAA;;kBAGLnB,0BAACxF,MAAAA;gBAAKgG,KAAK;;sBACTR,0BAACxF,MAAAA;oBAAKgG,KAAK;oBAAGF,YAAW;;0BACvB/E,yBAAC6F,eAAAA;wBAAc9E,MAAM8B;wBAAKoB,aAAa1D,KAAK0D;;0BAC5CQ,0BAACqB,YAAAA;wBACCC,WAAU;wBACVC,YAAW;wBACXC,gBAAgB7D,YAAY,iBAAiB;wBAC7C8D,UAAQ;wBACRlB,UAAS;;0BAERzE,KAAKC;0BACL,cAAcD,QAAQA,KAAK4F,gBAC1BnG,yBAAC8F,YAAAA;4BAAWC,WAAU;4BAAY,UAAA;;;;;;sBAIxC/F,yBAACf,MAAAA;oBACC,cAAAwF,0BAACqB,YAAAA;sBAAWC,WAAU;;4BACpB/F,yBAACoG,eAAAA;0BACCrF,MAAMR,KAAKQ;0BACXkD,aAAa1D,KAAK0D;0BAClBoC,YAAY,gBAAgB9F,QAAQA,KAAK8F;0BACzCC,UAAU,cAAc/F,QAAQA,KAAK+F;;wBAEtC,gBAAgB/F,QACfA,KAAKgG,cACLC,OAAOC,KAAKlG,KAAKgG,UAAU,EAAEpD,SAAS,SAAKnD,yBAAC0G,OAAAA;0BAAMC,QAAQ;0BAAG,UAAA;;wBAC9DpG,KAAKQ,SAAS,kBACb0D,0BAAAC,8BAAA;;4BAAE;4BACQkC,gBAAgBrG,KAAK+B,UAAU/B,KAAKsG,eAAe;4BAAE;4BAC5DpE,qBACCT,cAAc;8BACZyD,IAAIqB,QAAQ,0BAAA;8BACZpB,gBAAgB;4BAClB,CAAA;4BAAG;4BAEJjD,yBACCzC,yBAAC+G,OAAAA;8BACCpC,SAAS,CAAC5B,MAAMA,EAAEC,gBAAe;8BACjC/C,KAAK+G;8BACLC,IAAI,+CAA+CxE,kBAAkBc,GAAG;4CAEvE2D,kBAAAA,SAAWzE,kBAAkB0E,KAAKC,WAAW;;4BAGjDxE,uBACC,IAAIZ,cAAc;8BAChByD,IAAIqB,QAAQ,MAAM;8BAClBpB,gBAAgB;4BAClB,CAAA,CAAA,KAAO9C,mBAAoB;;;wBAGhCrC,KAAKQ,SAAS,mBAAef,yBAACqH,eAAAA;0BAAc9D,KAAKhD,KAAK+G;;;;;;;;;cAM/DtH,yBAACP,KAAAA;YACC,cAAAO,yBAACf,MAAAA;cAAKsI,gBAAe;cAAWtC,KAAK;cAAGN,SAAS,CAAC5B,MAAMA,EAAEC,gBAAe;4BACvEyB,0BAAAC,8BAAA;;sBACE1E,yBAACP,KAAAA;8BAAKc,KAAKiB,cAAUxB,yBAACwH,aAAAA;sBAAYhG,QAAQjB,KAAKiB;;;kBAC9C;oBAAC;oBAAa;kBAAc,EAACe,SAAShC,KAAKQ,IAAI,SAC9Cf,yBAACoF,YAAAA;oBACCT,SAAS,CAAC5B,MAAAA;AACRA,wBAAE0E,eAAc;AAChB1E,wBAAEC,gBAAe;AAEjB,0BAAI5B,QAAQ;AACVC,kCAAU,KAAA;6BACL;AACLA,kCAAU,IAAA;sBACZ;oBACF;oBACAqG,iBAAetG;oBACfoE,OAAOxD,cAAc;sBACnByD,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;oBACAJ,SAAQ;oBACRC,aAAa;oBAEb,cAAAvF,yBAAC2H,eAAAA;sBACCC,eAAW;sBACXC,MAAK;sBACL/H,OAAO;wBACLgI,WAAW,UAAU1G,SAAS,SAAS,QAAA;wBACvC2G,YAAY;sBACd;;;kBAILpG,uBAAuBpB,KAAKqD,iBAAiB,YAC5Ca,0BAAAC,8BAAA;;sBACG,CAACrC,eACArC,yBAACoF,YAAAA;wBACCT,SAAShB;wBACT6B,OAAO,GAAGxD,cAAc;0BACtByD,IAAI;0BACJC,gBAAgB;wBAClB,CAAA,CAAA,IAAMnF,KAAKC,IAAI;wBACf8E,SAAQ;wBACRK,UAAU,CAACtB;wBAEX,cAAArE,yBAACgI,eAAAA,CAAAA,CAAAA;;0BAGLhI,yBAACoF,YAAAA;wBACCT,SAAS7B;wBACT0C,OAAO,GAAGxD,cAAc;0BACtByD,IAAI;0BACJC,gBAAgB;wBAClB,CAAA,CAAA,IAAMnF,KAAKC,IAAI;wBACf8E,SAAQ;wBACRK,UAAU,CAACrB;wBAEX,cAAAtE,yBAACiI,cAAAA,CAAAA,CAAAA;;0BAEHjI,yBAACkI,OAAOC,MAAI;wBAACC,MAAMlG;wBAAmBmG,cAAclG;wBAClD,cAAAnC,yBAACsI,eAAAA;0BAAcC,WAAW9E;0BAAqB+E,UAAU9E;0BACvD,cAAA1D,yBAACP,KAAAA;4BACC,cAAAgF,0BAACqB,YAAAA;;gCACE9D,cAAc;kCACbyD,IAAIqB,QACF,2DAAA;kCAEFpB,gBACE;gCACJ,CAAA;oCACA1F,yBAAC8F,YAAAA;kCAAWE,YAAW;kCACpB9C,UAAAA,mBAAmBzB,cAAclB,KAAKC,IAAI,EACxCiI,IAAI,CAAC,EAAEC,UAAS,MAAOA,SAAAA,EACvBC,KAAK,IAAA;;gCAET3G,cAAc;kCACbyD,IAAIqB,QACF,+DAAA;kCAEFpB,gBAAgB;gCAClB,CAAA;;;;;;;2BAOV1F,yBAACf,MAAAA;oBAAK2J,SAAS;oBACb,cAAA5I,yBAAC6I,eAAAA;sBAAKhB,MAAK;;;;;;;;;UAQvBpD,0BAACqE,QAAAA;QAAOC,wBAAwB5H;QAAuB6H,SAAS5H;;UAC7Db,KAAKQ,SAAS,mBACbf,yBAACiJ,eAAAA;YACE,GAAG1I;YACJK;YACAD;;UAIHJ,KAAKQ,SAAS,qBACbf,yBAACkJ,iBAAAA;YACE,GAAG3I;YACJoF,UAAUpE,iBAAiBhB,KAAKiB,WAAW;YAC3C2H,cAActI;YACduC,WAAWrC,KAAKsC;YAChBC,WAAWvC,KAAKwC;;;;;;AAM5B,CAAA;AAEA,IAAMuF,SAAS9J,GAAOS,GAAAA;aACT,CAAC,EAAEsJ,uBAAsB,MAAQA,yBAAyB,SAAS,OAAS;gBACzE,CAAC,EAAEC,QAAO,MAAQA,UAAU,WAAW,KAAO;;;gBAG9C,CAAC,EAAEA,QAAO,MACtBA,UAAU,8BAA8B,0CAA2C;;AAGvF,IAAM3B,gBAAgB,CAAC,EAAE9D,IAAG,MAA0B;AACpD,QAAM,EAAE6F,WAAU,IAAKxH,eAAAA;AACvB,QAAMb,WAAO2B,YAAAA,SAAI0G,YAAY7F,GAAAA;AAE7B,aACEkB,0BAAAC,8BAAA;;MAAE;UAEA1E,yBAAC+G,OAAAA;QACCpC,SAAS,CAAC5B,MAAMA,EAAEC,gBAAe;QACjC/C,KAAK+G;QACLC,IAAI,sDAAsDlG,KAAKsI,QAAQ,IAAItI,KAAKwC,GAAG;sBAElF2D,kBAAAA,SAAWnG,KAAKoG,KAAKC,WAAW;;MAC5B;;;AAIb;;;;ACxaA,IAAMkC,UAAUC,GAAOC,GAAAA;;;;;;;;;;;;;;YAcX,CAAC,EAAEC,OAAOC,MAAK,MAAOD,MAAME,OAAO,GAAGD,KAAAA,KAAU,CAAC;;;AAI7D,IAAME,YAAYL,GAAOC,GAAAA;uBACF,CAAC,EAAEC,MAAK,MAAOA,MAAMI,YAAY,IAAI,CAAC,EAAEJ,MAAK,MAAOA,MAAMI,YAAY;;;;;;AActF,IAAMC,gBAAgB,CAAC,EAAEC,UAAUC,MAAMN,OAAO,GAAGO,MAA2B,MAAA;AACnF,aACEC,yBAACN,WAAAA;IACCO,eAAe;IACfC,YAAY;IACZC,aAAY;IACZC,KAAI;IACJC,MAAK;IACJ,GAAGN;IAEJ,cAAAO,0BAACC,MAAAA;;YACCP,yBAACZ,SAAAA;UAAQI;UAAcgB,eAAW;UAACC,YAAY,GAAGjB,KAAM;UACrDM,UAAAA;;YAEHE,yBAACV,KAAAA;UAAIa,aAAa;UAChB,cAAAH,yBAACU,YAAAA;YAAWC,SAAQ;YAAKC,YAAW;YAAOC,WAAW,GAAGrB,KAAM;YAC5DK;;;;;;AAMb;AAEO,IAAMiB,UAAU,CAAC,EAAEjB,UAAUC,MAAMN,OAAO,GAAGO,MAAyC,MAAA;AAC3F,aACEO,0BAACS,OAAAA;;UACCf,yBAACgB,SAAAA,CAAAA,CAAAA;UACDhB,yBAACN,WAAAA;QAAUU,KAAI;QAASK,YAAY,GAAGjB,KAAM;QAAMyB,SAAS;QAAI,GAAGlB;QACjE,cAAAO,0BAACC,MAAAA;;gBACCP,yBAACZ,SAAAA;cAAQI;cAAcgB,eAAW;cAACC,YAAY,GAAGjB,KAAM;cACrDM,UAAAA;;gBAEHE,yBAACV,KAAAA;cAAIa,aAAa;cAChB,cAAAH,yBAACU,YAAAA;gBAAWC,SAAQ;gBAAKC,YAAW;gBAAOC,WAAW,GAAGrB,KAAM;gBAC5DK;;;;;;;;AAOf;;;AC9CaqB,IAAAA,WAAWC,GAAOC,GAAAA;;;;;AAgB/B,IAAMC,cAAc,CAACC,UAAAA;AACnB,QAAM,EAAEC,oBAAmB,IAAKC,eAAAA;AAEhC,QAAM,EACJC,YACAC,YACAC,WACAC,YACAC,WACAC,YACAC,oBAAmB,IACjBC,YAAY;IACdC,UACE,CAACV,uBAAuBD,MAAMY,KAAKC,WAAW,aAAab,MAAMc,KAAKD,WAAW;IACnFE,IAAIf,MAAMY,KAAKG;IACfC,MAAM;MAAEC,OAAOjB,MAAMY,KAAKK;IAAM;EAClC,CAAA;AAEA,QAAMC,QAAQ;IACZX,WAAWY,IAAIC,UAAUC,SAAS;MAChCC,IAAGf,uCAAWe,MAAK;MACnBC,IAAGhB,uCAAWgB,MAAK;MACnBC,QAAQ;MACRC,QAAQ;IACV,CAAA;IACAjB;EACF;AAEA,aACEkB,yBAACC,cAAAA;IACCC,KAAKtB;IACLuB,WAAWpB;IACXN;IACAC;IACAC;IACAa;IACC,GAAGlB;;AAGV;AAEO,IAAM8B,OAAO,CAAC,EACnBC,kBACAC,uBACAC,oBAAoB,OACpBC,SAAS,OACTC,QAAQ,OACRC,wBACAtB,KAAI,MACM;AACV,QAAM,EAAEuB,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEvC,qBAAqBwC,cAAa,IAAKvC,eAAAA;AAC/C,QAAM,EAAEwC,oBAAmB,IAAKC,uBAAAA;AAEhC,QAAMC,QAAQ9B,6BAAMV,WAAWyC,IAAI,CAACjC,MAAMK,UAAAA;AACxC,WAAO;MACLF,IAAI,GAAGD,KAAKgC,GAAG,IAAIlC,KAAKmC,IAAI;MAC5B9B;MACA,GAAGL;IACL;EACF;AAEA,QAAM,CAACoC,UAAUC,WAAY,QAAGC,wBAAkC,IAAA;AAElE,QAAMC,aAAYrC,6BAAMD,YAAW;AAEnC,QAAMuC,UAAUC,WAAWC,UAAUC,aAAAA,CAAAA;AAErC,WAASC,iBAAiB,EAAEC,OAAM,GAAkB;AAClD,QAAI,CAACA,QAAQ;AACX;IACF;AAEAR,gBAAYQ,OAAO1C,EAAE;EACvB;AAEA,WAAS2C,cAAcC,OAAmB;AACxC,UAAM,EAAEF,QAAQG,KAAI,IAAKD;AAEzBV,gBAAY,IAAA;AAEZ,QAAIW,MAAM;AACR,UAAIH,OAAO1C,OAAO6C,KAAK7C,IAAI;AACzB0B,sBAAc;UACZoB,WAAW/C,KAAKgD;UAChBC,WAAWjD,KAAKgC;UAChBkB,MAAMP,OAAOzC,KAAKiD,QAAShD;UAC3BiD,IAAIN,KAAK5C,KAAKiD,QAAShD;QACzB,CAAA;MACF;IACF;EACF;AAEA,QAAMkD,aAAavB,MAAMwB,KAAK,CAACxD,SAASA,KAAKG,OAAOiC,QAAAA;AAEpD,QAAMqB,kBAAkB,MAAA;AACtB,QAAIlB,WAAW;AACb;IACF;AAEAZ,eAAW,6BAAA;AAEXG,wBAAoB;MAAEmB,WAAW/C,6BAAMgD;MAAWC,WAAWjD,KAAKgC;IAAI,CAAA;EACxE;AAEA,OAAIhC,6BAAMV,WAAWkE,YAAW,KAAKpC,QAAQ;AAC3C,eACER,yBAAC6C,kBAAAA;MACCC,YACE9C,yBAAC+C,QAAAA;QAAOC,SAASL;QAAiBM,MAAK;QAAIC,eAAWlD,yBAACmD,eAAAA,CAAAA,CAAAA;QAASC,SAAQ;kBACrEzC,cAAc;UACbtB,IAAIgE,QAAQ,wBAAA;UACZC,gBAAgB;QAClB,CAAA;;MAGJC,SAAS5C,cACPvB,KAAKgD,cAAc,gBACf;QACE/C,IAAIgE,QAAQ,yCAAA;QACZC,gBAAgB;UAElB;QACEjE,IAAIgE,QAAQ,mCAAA;QACZC,gBAAgB;MAClB,CAAA;MAENE,WAAS;MACTC,UAAMzD,yBAAC0D,cAAAA;QAAeC,OAAM;;;EAGlC;AAEA,aACEC,0BAACC,YAAAA;IACCnC;IACAoC,oBAAoBC;IACpBC,WAAWhC;IACXiC,aAAanC;IACboC,cAAc,MAAM3C,YAAY,IAAA;IAChC4C,WAAW;MAACC;IAAuB;;UAEnCR,0BAAC1F,UAAAA;QAASmG,KAAI;;cACXC,mCACCtE,yBAACuE,aAAAA;YAAYC,QAAQ;YAClB/B,UAAAA,kBACCzC,yBAACC,cAAAA;cACCwE,WAAS;cACTvF,MAAMuD;cACNnC;cACAC;cACAG;cACAtB;cACAiB;;UAINqE,CAAAA,GAAAA,SAASC,IAAI;cAEf3E,yBAAC4E,iBAAAA;YAAgB1D;YAAc2D,UAAUC;sBACtC5D,MAAMC,IAAI,CAACjC,SAAAA;AACV,yBACEc,yBAAC3B,aAAAA;gBAECa;gBACAoB;gBACAC;gBACAG;gBACAtB;gBACAiB;cANKnB,GAAAA,KAAKG,EAAE;YASlB,CAAA;;;;MAIHmB,UAAUjC,2BACTyB,yBAAC+E,SAAAA;QACCC,QAAQvD,YAAY,WAAW;QAC/BgC,UAAMzD,yBAACmD,eAAAA,CAAAA,CAAAA;QACPH,SAASL;QACTsC,OAAOxD,YAAY,YAAY;kBAE9Bd,cAAc;UACbtB,IAAIgE,QACF,4BAA4BjE,KAAKgD,cAAc,cAAc,cAAchD,KAAK8F,IAAI,EAAE;UAExF5B,gBAAgB;QAClB,CAAA;;MAGH7C,SAASlC,2BACRyB,yBAACmF,eAAAA;QACCH,QAAQvD,YAAY,WAAW;QAC/BgC,UAAMzD,yBAACmD,eAAAA,CAAAA,CAAAA;QACPH,SAASL;QACTsC,OAAO1E,qBAAqB,CAACkB,YAAY,YAAY;kBAEpDd,cAAc;UACbtB,IAAIgE,QAAQ,oCAAoC;UAChDC,gBAAgB;QAClB,CAAA;;;;AAKV;;;;;ACtPA,IAAM8B,gBAA8C;EAClDC,+BAA+B;IAC7B;MACEC,QAAQ;MACRC,SAAS;IACX;EACD;EACDC,0BAA0B;IACxB;MACEF,QAAQ;MACRC,SAAS;IACX;EACD;EACDE,2BAA2B;IACzB;MACEH,QAAQ;MACRC,SAAS;IACX;EACD;AACH;AAEA,IAAMG,gBAAgB,CAACC,SAAAA;AACrB,MAAIA,KAAKC,cAAc,eAAe;AACpC,QAAID,KAAKE,SAAS,cAAc;AAC9B,aAAOT,cAAcK;IACvB;AAEA,WAAOL,cAAcC;EACvB;AAEA,SAAOD,cAAcI;AACvB;AAOA,IAAMM,UAAU,CAACH,SAAAA;AACf,UAAQA,KAAKC,WAAS;IACpB,KAAK;AACH,cAAQD,KAAKE,MAAI;QACf,KAAK;AACH,iBAAO,iCAAiCF,KAAKI,GAAG;QAClD,KAAK;AACH,iBAAO,qCAAqCJ,KAAKI,GAAG;MACxD;IACF,KAAK;AACH,aAAO,+BAA+BJ,KAAKI,GAAG;EAClD;AACF;AAEA,IAAMC,eAAeC,GAAOC,MAAAA;;;AAIrB,IAAMC,2BAAuBC,oBAAK,CAAC,EAAEC,UAAUV,KAAI,MAA6B;AACrF,QAAM,EAAEW,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,WAAWC,YAAAA;AACjB,QAAMC,qBAAqBhB,cAAcC,IAAAA;AAEzC,QAAMgB,QAAQL,cAAc;IAC1BM,IAAI;IACJC,gBAAgB;EAClB,CAAA;AAEA,QAAMC,cAAc,MAAA;AAClB,QAAIT,UAAU;AACZ,aAAO;IACT;AAEA,UAAMU,OAAOjB,QAAQH,IAAAA;AAErBa,aAASO,IAAAA;AAET,WAAO;EACT;AAEA,QAAM,EAAEC,WAAWC,eAAc,IAAKC,QAAQR,kBAAAA;AAE9C,MAAIM,WAAW;AACb,WAAO;EACT;AAEA,MAAI,CAACC,eAAeE,oBAAoB,CAACF,eAAeG,oBAAoB;AAC1E,WAAO;EACT;AAEA,aACEC,0BAACrB,cAAAA;IACCsB,eAAWD,0BAACE,eAAAA,CAAAA,CAAAA;IACZC,SAAQ;IACRC,SAASX;IACTT;IAECM,UAAAA;;AAGP,CAAG;;;AC3FH,IAAMe,sBAAsBC,GAAOC,QAAQC,MAAM;;;;;AAMjD,IAAMC,WAAW,MAAA;;AACf,QAAM,EAAEC,qBAAqBC,cAAcC,YAAYC,UAAS,IAAKC,eAAAA;AACrE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AAEvB,QAAM,EAAEC,gBAAgBC,aAAY,IAAKC,UAAAA;AAKzC,QAAM,EAAEC,8BAA8BC,qBAAqBC,sBAAqB,IAC9EC,uBAAAA;AAEF,QAAMC,OAAOP,iBACTR,aAAaQ,cAAAA,IACbC,eACER,WAAWQ,YAAAA,IACX;AAEN,MAAIP,WAAW;AACb,WAAO;EACT;AAEA,MAAI,CAACa,MAAM;AACT,UAAMC,mBAAmBC,OAAOC,OAAOlB,YAAAA,EACpCmB,OAAO,CAACC,OAAOA,GAAGC,YAAY,QAAQ,CAACD,GAAGE,MAAM,EAChDC,IAAI,CAACH,OAAOA,GAAGI,GAAG,EAClBC,KAAI;AAEP,QAAIT,iBAAiBU,SAAS,GAAG;AAC/B,iBAAOC,0BAACC,UAAAA;QAASC,IAAI,+CAA+Cb,iBAAiB,CAAA,CAAE;;IACzF;AAEA,eAAOW,0BAACC,UAAAA;MAASC,IAAG;;EACtB;AAEA,QAAMC,eAAe,YAAYf,SAAQA,6BAAMO,YAAWS;AAE1D,QAAMC,YAAYxB,iBAAiB,gBAAgB;AAEnD,QAAMyB,UAAQlB,kCAAMmB,SAANnB,mBAAYoB,gBAAe;AAEzC,QAAMC,UAAUrC,uBAAuB,CAAC+B;AAExC,QAAMO,8BAA8B,CAACC,sBAAAA;AACnC3B,iCAA6B;MAAE2B;MAAmBC,WAAWxB,KAAKS;IAAI,CAAA;EACxE;AAEA,QAAMgB,SAAS,MAAA;AACb,QAAI,UAAUzB,MAAM;AAClB,WAAIA,6BAAM0B,UAAS,kBAAkB;AACnCnC,mBAAW,2BAAA;MACb;AAEA,WAAIS,6BAAM0B,UAAS,cAAc;AAC/BnC,mBAAW,0BAAA;MACb;AAEAO,4BAAsB;QACpB6B,WAAWV;QACXA;QACAO,WAAWxB,KAAKS;QAChBiB,MAAM1B,6BAAM0B;MACd,CAAA;AAEA;IACF;AAEA5B,0BAAsB;MACpB6B,WAAWV;MACXA;MACAO,WAAWxB,KAAKS;IAClB,CAAA;EACF;AAEA,QAAMmB,mBAAmBvC,cAAc;IACrCwC,IAAIC,QAAQ,wBAAA;IACZC,gBAAgB;EAClB,CAAA;AAEA,QAAMC,uBAAuB3C,cAAc;IACzCwC,IAAIC,QAAQ,+BAAA;IACZC,gBAAgB;EAClB,CAAA;AAEA,QAAME,YAAYjC,KAAKkC,WAAW;AAElC,QAAMC,gBAAgBnD,2BACpBoD,2BAACC,MAAAA;IAAKC,KAAK;;UACT1B,0BAAC2B,sBAAAA;QAECvC;QACAwC,UAAUxC,KAAKkC,WAAW,SAASD;MAF/B,GAAA,0BAAA;UAINrB,0BAAC6B,QAAAA;QACCC,eAAW9B,0BAAC+B,eAAAA,CAAAA,CAAAA;QACZC,SAAQ;QACRC,SAASpB;QACTe,UAAU,CAACnB,WAAWY;kBAErB5C,cAAc;UACbwC,IAAI;UACJE,gBAAgB;QAClB,CAAA;;UAEFnB,0BAAC6B,QAAAA;QACCC,eAAW9B,0BAACkC,eAAAA,CAAAA,CAAAA;QACZF,SAAQ;QACRG,UAAS;QACTF,SAAS,MAAA;AACPhD,8BAAoB;YAAEoB;YAAWO,WAAWxB,KAAKS;UAAI,CAAA;QACvD;QACA+B,UAAUP;QAETjC,UAAAA,KAAKgD,WAAWrC,WAAW,IAAIiB,mBAAmBI;;;;AAKzD,aACEI,2BAAAa,+BAAA;;MACGhB,iBACCrB,0BAACyB,MAAAA;QAAKa,YAAW;QAAYC,gBAAgB;QAAUC,SAAS;QAC9D,cAAAhB,2BAACC,MAAAA;UAAKC,KAAK;;gBACT1B,0BAACyC,eAAAA;cAAYC,MAAK;cAAYC,QAAO;cAAOC,OAAM;;gBAClD5C,0BAAC6C,YAAAA;wBACEpE,cACC;gBACEwC,IAAIC,QAAQ,uBAAA;gBACZC,gBAAgB;iBAElB;gBACEL,MAAM1B,KAAK0D,cAAc,gBAAgB,iBAAiB;cAC5D,CAAA;;;;;UAMV9C,0BAACjC,qBAAAA;QAAoBkD,IAAG;QAAQM;QAA8BwB,WAAOC,mBAAAA,SAAW1C,KAAAA;;UAChFN,0BAAC/B,QAAQgF,SAAO;QACd,cAAAjD,0BAACkD,KAAAA;UACCZ,YAAW;UACXa,QAAO;UACPC,WAAS;UACTC,UAAS;UACTC,aAAY;UAEZ,cAAAtD,0BAACuD,MAAAA;YAAKnE;YAAYoE,kBAAkB9C;YAA6B+C,QAAM;;;;;;AAKjF;", "names": ["restrictToVerticalAxis", "transform", "x", "StyledBox", "styled", "Box", "theme", "color", "colors", "Svg", "svg", "Curve", "props", "_jsx", "width", "height", "viewBox", "fill", "xmlns", "path", "fillRule", "clipRule", "d", "getAttributeDisplayedType", "type", "displayedType", "ComponentRow", "styled", "Box", "$isFromDynamicZone", "$isChildOfDynamicZone", "theme", "colors", "primary200", "neutral150", "ComponentList", "component", "isFromDynamicZone", "firstLoopComponentUid", "components", "useDataManager", "type", "get", "_jsx", "ComponentRow", "$isChildOfDynamicZone", "className", "List", "isSub", "secondLoopComponentUid", "DisplayedType", "type", "customField", "repeatable", "multiple", "formatMessage", "useIntl", "readableType", "includes", "id", "getTrad", "defaultMessage", "_jsxs", "_Fragment", "ComponentIcon", "isActive", "icon", "Icon", "COMPONENT_ICONS", "dashboard", "_jsx", "Flex", "alignItems", "background", "justifyContent", "height", "width", "borderRadius", "CloseButton", "styled", "Box", "theme", "colors", "primary600", "ComponentBox", "Flex", "neutral200", "neutral100", "borderRadius", "primary200", "primary100", "ComponentCard", "component", "dzName", "index", "isActive", "isInDevelopmentMode", "onClick", "for<PERSON><PERSON><PERSON>", "targetUid", "disabled", "components", "removeComponentFromDynamicZone", "useDataManager", "type", "get", "icon", "displayName", "info", "onClose", "e", "stopPropagation", "componentToRemoveIndex", "_jsxs", "alignItems", "direction", "className", "justifyContent", "paddingLeft", "paddingRight", "shrink", "role", "tabIndex", "cursor", "aria-selected", "aria-controls", "id", "_jsx", "ComponentIcon", "marginTop", "max<PERSON><PERSON><PERSON>", "Typography", "variant", "fontWeight", "ellipsis", "tag", "Cross", "StyledAddIcon", "styled", "Plus", "theme", "disabled", "colors", "neutral100", "primary100", "neutral600", "primary600", "ComponentStack", "Flex", "DynamicZoneList", "components", "addComponent", "name", "for<PERSON><PERSON><PERSON>", "targetUid", "isInDevelopmentMode", "useDataManager", "activeTab", "setActiveTab", "useState", "formatMessage", "useIntl", "toggle", "tab", "handleClickAdd", "_jsx", "ComponentRow", "className", "$isFromDynamicZone", "_jsxs", "Box", "padding", "paddingLeft", "role", "gap", "wrap", "button", "type", "onClick", "style", "cursor", "direction", "alignItems", "Typography", "variant", "fontWeight", "textColor", "id", "getTrad", "defaultMessage", "map", "component", "index", "ComponentCard", "dzName", "isActive", "aria-<PERSON>by", "display", "ComponentList", "isFromDynamicZone", "GridWrapper", "styled", "Flex", "theme", "$isOverlay", "colors", "neutral150", "spaces", "$isDragging", "StyledAttributeRow", "Box", "AttributeRow", "forwardRef", "props", "ref", "style", "rest", "_jsx", "tag", "attributes", "background", "shadow", "isOverlay", "aria-label", "item", "name", "MemoizedRow", "memo", "firstLoopComponentUid", "isFromDynamicZone", "addComponentToDZ", "secondLoopComponentUid", "type", "isDragging", "handleRef", "listeners", "shouldHideNestedInfos", "isOpen", "setIsOpen", "useState", "isTypeDeleted", "status", "contentTypes", "removeAttribute", "isInDevelopmentMode", "useDataManager", "onOpenModalEditField", "onOpenModalEditCustomField", "useFormModalNavigation", "formatMessage", "useIntl", "showConfirmDialog", "setShowConfirmDialog", "isDeleted", "isMorph", "relation", "includes", "ico", "targetContentType", "get", "target", "isPluginContentType", "src", "handleDelete", "e", "stopPropagation", "dependentRows", "checkDependentRows", "length", "for<PERSON><PERSON><PERSON>", "modelType", "targetUid", "uid", "attributeToRemoveName", "handleConfirmDelete", "handleCancelDelete", "handleClick", "configurable", "editTargetUid", "attributeType", "getAttributeDisplayedType", "step", "customField", "attributeName", "customFieldUid", "loopNumber", "canEdit", "canDelete", "cursor", "canClick", "_jsxs", "_Fragment", "onClick", "undefined", "paddingLeft", "paddingRight", "alignItems", "overflow", "gap", "Curve", "color", "IconButton", "role", "variant", "withTooltip", "label", "id", "defaultMessage", "disabled", "Drag", "AttributeIcon", "Typography", "textColor", "fontWeight", "textDecoration", "ellipsis", "required", "DisplayedType", "repeatable", "multiple", "conditions", "Object", "keys", "Badge", "margin", "getRelationType", "targetAttribute", "getTrad", "Link", "NavLink", "to", "upperFirst", "info", "displayName", "ComponentLink", "component", "justifyContent", "StatusBadge", "preventDefault", "aria-expanded", "ChevronDown", "aria-hidden", "fill", "transform", "transition", "Pencil", "Trash", "Dialog", "Root", "open", "onOpenChange", "ConfirmDialog", "onConfirm", "onCancel", "map", "attribute", "join", "padding", "Lock", "SubRow", "$shouldHideNestedInfos", "$isOpen", "ComponentList", "DynamicZoneList", "addComponent", "components", "category", "IconBox", "styled", "Box", "theme", "color", "colors", "ButtonBox", "borderRadius", "NestedTFooter", "children", "icon", "props", "_jsx", "paddingBottom", "paddingTop", "paddingLeft", "tag", "type", "_jsxs", "Flex", "aria-hidden", "background", "Typography", "variant", "fontWeight", "textColor", "TF<PERSON>er", "div", "Divider", "padding", "ListGrid", "styled", "Box", "SortableRow", "props", "isInDevelopmentMode", "useDataManager", "isDragging", "attributes", "listeners", "setNodeRef", "transform", "transition", "setActivatorNodeRef", "useSortable", "disabled", "item", "status", "type", "id", "data", "index", "style", "CSS", "Transform", "toString", "x", "y", "scaleX", "scaleY", "_jsx", "AttributeRow", "ref", "handleRef", "List", "addComponentToDZ", "firstLoopComponentUid", "isFromDynamicZone", "is<PERSON><PERSON>", "isSub", "secondLoopComponentUid", "formatMessage", "useIntl", "trackUsage", "useTracking", "moveAttribute", "onOpenModalAddField", "useFormModalNavigation", "items", "map", "uid", "name", "activeId", "setActiveId", "useState", "isDeleted", "sensors", "useSensors", "useSensor", "PointerSensor", "handlerDragStart", "active", "handleDragEnd", "event", "over", "for<PERSON><PERSON><PERSON>", "modelType", "targetUid", "from", "current", "to", "activeItem", "find", "onClickAddField", "length", "EmptyStateLayout", "action", "<PERSON><PERSON>", "onClick", "size", "startIcon", "Plus", "variant", "getTrad", "defaultMessage", "content", "hasRadius", "icon", "EmptyDocuments", "width", "_jsxs", "DndContext", "collisionDetection", "closestCenter", "onDragEnd", "onDragStart", "onDragCancel", "modifiers", "restrictToVerticalAxis", "tag", "createPortal", "DragOverlay", "zIndex", "isOverlay", "document", "body", "SortableContext", "strategy", "verticalListSortingStrategy", "TF<PERSON>er", "cursor", "color", "kind", "NestedTFooter", "cmPermissions", "collectionTypesConfigurations", "action", "subject", "componentsConfigurations", "singleTypesConfigurations", "getPermission", "type", "modelType", "kind", "getLink", "uid", "StyledButton", "styled", "<PERSON><PERSON>", "LinkToCMSettingsView", "memo", "disabled", "formatMessage", "useIntl", "navigate", "useNavigate", "permissionsToApply", "label", "id", "defaultMessage", "handleClick", "link", "isLoading", "allowedActions", "useRBAC", "canConfigureView", "canConfigureLayout", "_jsx", "startIcon", "ListPlus", "variant", "onClick", "LayoutsHeaderCustom", "styled", "Layouts", "Header", "ListView", "isInDevelopmentMode", "contentTypes", "components", "isLoading", "useDataManager", "formatMessage", "useIntl", "trackUsage", "useTracking", "contentTypeUid", "componentUid", "useParams", "onOpenModalAddComponentsToDZ", "onOpenModalAddField", "onOpenModalEditSchema", "useFormModalNavigation", "type", "allowedEndpoints", "Object", "values", "filter", "ct", "visible", "plugin", "map", "uid", "sort", "length", "_jsx", "Navigate", "to", "isFromPlugin", "undefined", "for<PERSON><PERSON><PERSON>", "label", "info", "displayName", "canEdit", "handleClickAddComponentToDZ", "dynamicZoneTarget", "targetUid", "onEdit", "kind", "modalType", "addNewFieldLabel", "id", "getTrad", "defaultMessage", "addAnotherFieldLabel", "isDeleted", "status", "primaryAction", "_jsxs", "Flex", "gap", "LinkToCMSettingsView", "disabled", "<PERSON><PERSON>", "startIcon", "Pencil", "variant", "onClick", "Plus", "min<PERSON><PERSON><PERSON>", "attributes", "_Fragment", "background", "justifyContent", "padding", "Information", "fill", "height", "width", "Typography", "modelType", "title", "upperFirst", "Content", "Box", "shadow", "hasRadius", "overflow", "borderColor", "List", "addComponentToDZ", "is<PERSON><PERSON>"]}