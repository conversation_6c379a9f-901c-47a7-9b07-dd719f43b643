const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["strapi-z7ApxZZq.js","strapi-D6821bIJ.css"])))=>i.map(i=>d[i]);
import{r as b,a4 as I,ck as v,a7 as w,n as T,cl as A,bi as D,_ as B,a as E,b as C,aA as O,j as e,cm as m,b7 as U,cn as R,k as G,a6 as H,aw as K,P,aB as F,B as V,co as $}from"./strapi-z7ApxZZq.js";import{s as W}from"./sortBy-DX26BOXv.js";import{s as q}from"./selectors-B6uMLQu7.js";import"./_baseMap-BaaWdrf-.js";import"./_baseEach-ZnnftuGj.js";const z=n=>n.map(i=>{const l=i.links.map(r=>({...r,isDisplayed:!1}));return{...i,links:l}}),J=()=>{const[{isLoading:n,menu:i},l]=b.useState({isLoading:!0,menu:[]}),r=I("useSettingsMenu",s=>s.checkUserHasPermissions),f=v("useSettingsMenu",s=>s.shouldUpdateStrapi),u=w("useSettingsMenu",s=>s.settings),c=T(q),h=b.useMemo(()=>A(),[]),{admin:a,global:t}=D(h,async()=>(await B(async()=>{const{SETTINGS_LINKS_EE:s}=await import("./strapi-z7ApxZZq.js").then(o=>o.iM);return{SETTINGS_LINKS_EE:s}},__vite__mapDeps([0,1]))).SETTINGS_LINKS_EE(),{combine(s,o){return{admin:[...o.admin,...s.admin],global:[...s.global,...o.global]}},defaultValue:{admin:[],global:[]}}),S=b.useCallback(s=>{if(!s.id)throw new Error("The settings menu item must have an id attribute.");return{...s,permissions:c.settings?.[s.id]?.main??[]}},[c.settings]);return b.useEffect(()=>{const s=async()=>{const _=await(k=>Promise.all(k.reduce((g,x,L)=>{const j=x.links.map(async(M,p)=>({hasPermission:(await r(M.permissions)).length>0,sectionIndex:L,linkIndex:p}));return[...g,...j]},[])))(y);l(k=>({...k,isLoading:!1,menu:y.map((g,x)=>({...g,links:g.links.map((L,j)=>{const M=_.find(p=>p.sectionIndex===x&&p.linkIndex===j);return{...L,isDisplayed:!!M?.hasPermission}})}))}))},{global:o,...N}=u,y=z([{...o,links:W([...o.links,...t.map(S)],d=>d.id).map(d=>({...d,hasNotification:d.id==="000-application-infos"&&f}))},{id:"permissions",intlLabel:{id:"Settings.permissions",defaultMessage:"Administration Panel"},links:a.map(S)},...Object.values(N)]);s()},[a,t,u,f,S,r]),{isLoading:n,menu:i.map(s=>({...s,links:s.links.filter(o=>o.isDisplayed)}))}},Q=G(H)`
  border-radius: 50%;
  padding: ${({theme:n})=>n.spaces[2]};
  height: 2rem;
`,X=({menu:n})=>{const{formatMessage:i}=E(),{trackUsage:l}=C(),{pathname:r}=O(),u=n.filter(a=>!a.links.every(t=>t.isDisplayed===!1)).map(a=>({...a,title:a.intlLabel,links:a.links.map(t=>({...t,title:t.intlLabel,name:t.id}))})),c=i({id:"global.settings",defaultMessage:"Settings"}),h=a=>()=>{l("willNavigate",{from:r,to:a})};return e.jsxs(m.Main,{"aria-label":c,children:[e.jsx(m.Header,{label:c}),e.jsx(U,{background:"neutral150",marginBottom:5}),e.jsx(m.Sections,{children:u.map(a=>e.jsx(m.Section,{label:i(a.intlLabel),children:a.links.map(t=>e.jsx(m.Link,{to:t.to,onClick:h(t.to),label:i(t.intlLabel),endAction:e.jsxs(e.Fragment,{children:[t?.licenseOnly&&e.jsx(R,{fill:"primary600",width:"1.5rem",height:"1.5rem"}),t?.hasNotification&&e.jsx(Q,{"aria-label":"Notification",backgroundColor:"primary600",textColor:"neutral0",children:"1"})]})},t.id))},a.id))})]})},as=()=>{const n=K("/settings/:settingId/*"),{formatMessage:i}=E(),{isLoading:l,menu:r}=J();return l?e.jsx(P.Loading,{}):n?.params.settingId?e.jsxs(V.Root,{sideNav:e.jsx(X,{menu:r}),children:[e.jsx(P.Title,{children:i({id:"global.settings",defaultMessage:"Settings"})}),e.jsx($,{})]}):e.jsx(F,{to:"application-infos"})};export{as as Layout};
