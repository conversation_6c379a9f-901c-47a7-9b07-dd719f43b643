{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/ru.json.mjs"], "sourcesContent": ["var ru = {\n    \"BoundRoute.title\": \"Связать путь с\",\n    \"EditForm.inputSelect.description.role\": \"При регистрации пользователи будут иметь выбранную роль.\",\n    \"EditForm.inputSelect.label.role\": \"Роль по умолчанию для новых пользователей\",\n    \"EditForm.inputToggle.description.email\": \"Запретить пользователю создавать несколько учётных записей, используя один и тот же адрес электронной почты, у разных поставщиков аутентификации.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"Если включено (ON), при регистрации пользователи будут получать письмо для подтверждения адреса электронной почты.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Укажите URL-адрес для перенаправления пользователей после подтверждения адреса электронной почты.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"URL-адрес страницы для сброса пароля учётной записи пользователя\",\n    \"EditForm.inputToggle.description.sign-up\": \"Если выключено (OFF), процесс регистрации пользователей запрещен. Никто не может зарегистрироваться, независимо от провайдера.\",\n    \"EditForm.inputToggle.label.email\": \"Одна учётная запись на один адрес электронной почты\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Включить подтверждение по электронной почте\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"URL-адрес для перенаправления\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"Страница сброса пароля\",\n    \"EditForm.inputToggle.label.sign-up\": \"Включить регистрации\",\n    \"EditForm.inputToggle.placeholder.email-confirmation-redirection\": \"например: https://yourfrontend.com/email-confirmation-redirection\",\n    \"EditForm.inputToggle.placeholder.email-reset-password\": \"например: https://yourfrontend.com/reset-password\",\n    \"EditPage.form.roles\": \"Сведения о роли\",\n    \"Email.template.data.loaded\": \"Шаблоны автоматических писем для электронной почты были загружены\",\n    \"Email.template.email_confirmation\": \"Адреса электронной почты с письмом о подтверждении\",\n    \"Email.template.form.edit.label\": \"Редактировать шаблон\",\n    \"Email.template.table.action.label\": \"действие\",\n    \"Email.template.table.icon.label\": \"иконка\",\n    \"Email.template.table.name.label\": \"название\",\n    \"Form.advancedSettings.data.loaded\": \"Данные расширенных настроек были загружены\",\n    \"HeaderNav.link.advancedSettings\": \"Расширенные настройки\",\n    \"HeaderNav.link.emailTemplates\": \"Шаблоны писем\",\n    \"HeaderNav.link.providers\": \"Провайдеры\",\n    \"Plugin.permissions.plugins.description\": \"Определите все разрешенные действия для плагина {name}.\",\n    \"Plugins.header.description\": \"Ниже перечислены только действия, связанные с путём.\",\n    \"Plugins.header.title\": \"Разрешения\",\n    \"Policies.header.hint\": \"Выберите действия приложения или плагина и нажмите на значок шестерёнки, чтобы отобразить связанный путь\",\n    \"Policies.header.title\": \"Расширенные настройки\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"Если вы не уверены, как использовать переменные — {link}\",\n    \"PopUpForm.Email.link.documentation\": \"ознакомьтесь с нашей документацией.\",\n    \"PopUpForm.Email.options.from.email.label\": \"Электронная почта отправителя\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Имя отправителя\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n    \"PopUpForm.Email.options.message.label\": \"Сообщение\",\n    \"PopUpForm.Email.options.object.label\": \"Тема\",\n    \"PopUpForm.Email.options.object.placeholder\": \"Пожалуйста, подтвердите свой адрес электронной почты для %APP_NAME%\",\n    \"PopUpForm.Email.options.response_email.label\": \"Электронная почта для ответов\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"Если этот параметр отключен, пользователи не смогут использовать этого поставщика.\",\n    \"PopUpForm.Providers.enabled.label\": \"Включено\",\n    \"PopUpForm.Providers.key.label\": \"ID клиента\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"URL-адрес перенаправления на ваше приложение\",\n    \"PopUpForm.Providers.redirectURL.label\": \"URL-адрес перенаправления, который нужно добавить в {provider} конфигурации приложения\",\n    \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.subdomain.label\": \"Хост URI (Поддомен)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Редактировать шаблон письма\",\n    \"PopUpForm.header.edit.providers\": \"Редактировать провайдера\",\n    \"Providers.data.loaded\": \"Провайдеры были загружены\",\n    \"Providers.status\": \"Статус\",\n    \"Roles.empty\": \"У вас пока нет никаких ролей.\",\n    \"Roles.empty.search\": \"Ни одна роль не соответствует поисковому запросу.\",\n    \"Settings.roles.deleted\": \"Роль удалена\",\n    \"Settings.roles.edited\": \"Роль отредактирована\",\n    \"Settings.section-label\": \"Плагин «Пользователи и Разрешения»\",\n    \"components.Input.error.validation.email\": \"Неверный адрес электронной почты\",\n    \"components.Input.error.validation.json\": \"Это не соответствует формату JSON\",\n    \"components.Input.error.validation.max\": \"Значение слишком велико.\",\n    \"components.Input.error.validation.maxLength\": \"Значение слишком длинное.\",\n    \"components.Input.error.validation.min\": \"Значение слишком мало.\",\n    \"components.Input.error.validation.minLength\": \"Значение слишком короткое.\",\n    \"components.Input.error.validation.minSupMax\": \"Не может быть выше\",\n    \"components.Input.error.validation.regex\": \"Значение не соответствует регулярному выражению.\",\n    \"components.Input.error.validation.required\": \"Это значение является обязательным.\",\n    \"components.Input.error.validation.unique\": \"Это значение уже используется.\",\n    \"notification.success.submit\": \"Настройки были обновлены\",\n    \"page.title\": \"Настройки — Роли\",\n    \"plugin.description.long\": \"Защитите свой API с помощью полноценного процесса аутентификации, основанного на JWT. Этот плагин также имеет настройки стратегии ACL, которые позволяют вам управлять разрешениями между группами пользователей.\",\n    \"plugin.description.short\": \"Защитите свой API с помощью полноценного процесса аутентификации, основанного на JWT.\",\n    \"plugin.name\": \"Пользователи и Разрешения\",\n    \"popUpWarning.button.cancel\": \"Отменить\",\n    \"popUpWarning.button.confirm\": \"Подтвердить\",\n    \"popUpWarning.title\": \"Пожалуйста подтвердите\",\n    \"popUpWarning.warning.cancel\": \"Вы уверены, что хотите отменить свои изменения?\"\n};\n\nexport { ru as default };\n//# sourceMappingURL=ru.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,+BAA+B;AACnC;", "names": []}