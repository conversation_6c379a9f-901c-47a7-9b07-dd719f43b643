{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/pt-BR.json.mjs"], "sourcesContent": ["var Analytics = \"Monitoramento\";\nvar Documentation = \"Documentação\";\nvar Email = \"E-mail\";\nvar Password = \"Senha\";\nvar Provider = \"Provedor\";\nvar ResetPasswordToken = \"Redefinir o token de senha\";\nvar Role = \"Função\";\nvar light = \"Claro\";\nvar dark = \"Escuro\";\nvar Username = \"Nome de usuário\";\nvar Users = \"Usuários\";\nvar anErrorOccurred = \"Ops! Algo deu errado. Por favor, tente novamente.\";\nvar clearLabel = \"Limpar\";\nvar or = \"OU\";\nvar skipToContent = \"Pular para o conteúdo\";\nvar submit = \"Enviar\";\nvar ptBR = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Sua conta foi suspensa.\",\n    \"Auth.components.Oops.text.admin\": \"Se isso foi um erro, por favor, contate seu administrador.\",\n    \"Auth.components.Oops.title\": \"Ops...\",\n    \"Auth.form.active.label\": \"Ativo\",\n    \"Auth.form.button.forgot-password\": \"Enviar e-mail\",\n    \"Auth.form.button.go-home\": \"VOLTAR PARA O INÍCIO\",\n    \"Auth.form.button.login\": \"Entrar\",\n    \"Auth.form.button.login.providers.error\": \"Não foi possível conectar você pelo provedor selecionado.\",\n    \"Auth.form.button.login.strapi\": \"Entrar com Strapi\",\n    \"Auth.form.button.password-recovery\": \"Recuperação de Senha\",\n    \"Auth.form.button.register\": \"Pronto para começar\",\n    \"Auth.form.confirmPassword.label\": \"Confirmação de senha\",\n    \"Auth.form.currentPassword.label\": \"Senha atual\",\n    \"Auth.form.email.label\": \"E-mail\",\n    \"Auth.form.email.placeholder\": \"<EMAIL>\",\n    \"Auth.form.error.blocked\": \"Sua conta foi bloqueada pelo administrador.\",\n    \"Auth.form.error.code.provide\": \"Código incorreto fornecido.\",\n    \"Auth.form.error.confirmed\": \"O email da sua conta não foi confirmado.\",\n    \"Auth.form.error.email.invalid\": \"Este email é inválido.\",\n    \"Auth.form.error.email.provide\": \"Por favor, forneça seu nome de usuário ou seu e-mail.\",\n    \"Auth.form.error.email.taken\": \"O email já foi utilizado\",\n    \"Auth.form.error.invalid\": \"Identificador ou senha inválida.\",\n    \"Auth.form.error.params.provide\": \"Params incorretos fornecidos.\",\n    \"Auth.form.error.password.format\": \"Sua senha não pode conter o símbolo` $ `mais de três vezes.\",\n    \"Auth.form.error.password.local\": \"Este usuário nunca definiu uma senha local, por favor faça o login através do provedor usado durante a criação da conta.\",\n    \"Auth.form.error.password.matching\": \"As senhas não coincidem.\",\n    \"Auth.form.error.password.provide\": \"Por favor, forneça sua senha\",\n    \"Auth.form.error.ratelimit\": \"Muitas tentativas, tente novamente em um minuto.\",\n    \"Auth.form.error.user.not-exist\": \"Este e-mail não existe.\",\n    \"Auth.form.error.username.taken\": \"Nome de usuário já foi obtido\",\n    \"Auth.form.firstname.label\": \"Primeiro nome\",\n    \"Auth.form.firstname.placeholder\": \"ex: Kai\",\n    \"Auth.form.forgot-password.email.label\": \"Digite seu email\",\n    \"Auth.form.forgot-password.email.label.success\": \"E-mail enviado com sucesso para\",\n    \"Auth.form.lastname.label\": \"Último nome\",\n    \"Auth.form.lastname.placeholder\": \"ex: Doe\",\n    \"Auth.form.password.hide-password\": \"Esconder senha\",\n    \"Auth.form.password.hint\": \"A senha deve conter pelo menos 8 caracteres, 1 letra maiúscula, 1 letra minúscula, e 1 número\",\n    \"Auth.form.password.show-password\": \"Mostrar senha\",\n    \"Auth.form.register.news.label\": \"Mantenha-me atualizado sobre os novos recursos e as próximas melhorias (ao fazer isso, você aceita os {terms} e a {policy}).\",\n    \"Auth.form.register.subtitle\": \"Suas credenciais são utilizadas somente para autenticar você ao painel de admin. Todos os dados serão salvos na sua própria base de dados.\",\n    \"Auth.form.rememberMe.label\": \"Lembre-se de mim\",\n    \"Auth.form.username.label\": \"Nome de usuário\",\n    \"Auth.form.username.placeholder\": \"Kai Doe\",\n    \"Auth.form.welcome.subtitle\": \"Entrar na sua conta do Strapi\",\n    \"Auth.form.welcome.title\": \"Bem-vindo(a)!\",\n    \"Auth.link.forgot-password\": \"Esqueceu sua senha?\",\n    \"Auth.link.ready\": \"Pronto para logar?\",\n    \"Auth.link.signin\": \"Entrar\",\n    \"Auth.link.signin.account\": \"Já tem uma conta?\",\n    \"Auth.login.sso.divider\": \"Ou entre com\",\n    \"Auth.login.sso.loading\": \"Carregando provedores...\",\n    \"Auth.login.sso.subtitle\": \"Entre na sua conta com SSO\",\n    \"Auth.privacy-policy-agreement.policy\": \"política de privacidade\",\n    \"Auth.privacy-policy-agreement.terms\": \"termos\",\n    \"Auth.reset-password.title\": \"Redefinir senha\",\n    \"Content Manager\": \"Gerenciador de conteúdo\",\n    \"Content Type Builder\": \"Criador de Tipos de Conteúdo\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Enviar arquivos\",\n    \"HomePage.head.title\": \"Pagina inicial\",\n    \"HomePage.roadmap\": \"Veja nosso roadmap\",\n    \"HomePage.welcome.congrats\": \"Parabéns!\",\n    \"HomePage.welcome.congrats.content\": \"Você está logado como o primeiro administrador. Para descobrir os recursos avançados fornecidos pelo Strapi,\",\n    \"HomePage.welcome.congrats.content.bold\": \"nós recomendados que você crie o seu primeiro Tipo de Conteúdo.\",\n    \"Media Library\": \"Biblioteca de Mídia\",\n    \"New entry\": \"Novo registro\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Funções e Permissões\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Algumas funções não puderam ser removidos por estarem associadas a alguns usuários\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"A função não pode ser removida se ainda estiver associada a algum usuário\",\n    \"Roles.RoleRow.select-all\": \"Selecione {name} para ações em massa\",\n    \"Roles.RoleRow.user-count\": \"{number, plural, =0 {#  user} um {#  user} outros {# users}}\",\n    \"Roles.components.List.empty.withSearch\": \"Não existe uma função correspondente à busca ({search})...\",\n    \"Settings.PageTitle\": \"Configurações - {name}\",\n    \"Settings.apiTokens.addFirstToken\": \"Adicione sua primeira chave de API\",\n    \"Settings.apiTokens.addNewToken\": \"Adicionar nova chave de API\",\n    \"Settings.tokens.copy.editMessage\": \"Por motivos de segurança, você só poderá ver sua chave uma única vez.\",\n    \"Settings.tokens.copy.editTitle\": \"Essa chave não está mais acessível.\",\n    \"Settings.tokens.copy.lastWarning\": \"Certifique-se de copiar esta chave, pois não será possível vê-la novamente!\",\n    \"Settings.apiTokens.create\": \"Adicionar\",\n    \"Settings.apiTokens.description\": \"lista de chaves geradas para consumo da API\",\n    \"Settings.apiTokens.emptyStateLayout\": \"Você não tem nada aqui ainda...\",\n    \"Settings.tokens.notification.copied\": \"Chave copiada pra área de transferência.\",\n    \"Settings.apiTokens.title\": \"Chaves de API\",\n    \"Settings.tokens.types.full-access\": \"Acesso total\",\n    \"Settings.tokens.types.read-only\": \"Somente leitura\",\n    \"Settings.application.description\": \"Informações globais do painel administrativo\",\n    \"Settings.application.edition-title\": \"edição atual\",\n    \"Settings.application.get-help\": \"Ajuda\",\n    \"Settings.application.link-pricing\": \"Ver todos os preços\",\n    \"Settings.application.link-upgrade\": \"Atualize seu painel administrativo\",\n    \"Settings.application.node-version\": \"versão do node\",\n    \"Settings.application.strapi-version\": \"versão do strapi\",\n    \"Settings.application.strapiVersion\": \"versão do strapi\",\n    \"Settings.application.title\": \"Visão geral\",\n    \"Settings.application.customization\": \"Customização\",\n    \"Settings.application.customization.carousel.title\": \"Logo\",\n    \"Settings.application.customization.carousel.change-action\": \"Alterar logotipo\",\n    \"Settings.application.customization.carousel.reset-action\": \"Redefinir logotipo\",\n    \"Settings.application.customization.carousel-slide.label\": \"Slide do logotipo\",\n    \"Settings.application.customization.carousel-hint\": \"Alterar o logotipo do painel de administração (Dimensão máxima: {dimension}x{dimension}, Tamanho máx.: {size}KB)\",\n    \"Settings.application.customization.modal.cancel\": \"Cancelar\",\n    \"Settings.application.customization.modal.upload\": \"Enviar\",\n    \"Settings.application.customization.modal.tab.label\": \"Como você deseja enviar seus arquivos?\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"Do computador\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"Dimensão máxima: {dimension}x{dimension}, Tamanho máximo: {size}KB\",\n    \"Settings.application.customization.modal.upload.error-format\": \"Formato incorreto (formatos aceitos: jpeg, jpg, png, svg).\",\n    \"Settings.application.customization.modal.upload.error-size\": \"O arquivo enviado é muito grande (dimensão máxima: {dimension}x{dimension}, tamanho máximo do arquivo: {size}KB)\",\n    \"Settings.application.customization.modal.upload.error-network\": \"Erro ao enviar o arquivo\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"Procurar arquivo\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"Arraste e solte o arquivo aqui ou\",\n    \"Settings.application.customization.modal.upload.from-url\": \"Da URL\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n    \"Settings.application.customization.modal.upload.next\": \"Próximo\",\n    \"Settings.application.customization.modal.pending\": \"Logotipo pendente\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"Escolha outro logotipo\",\n    \"Settings.application.customization.modal.pending.title\": \"Logotipo pronto para ser enviado\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"Gerencie o logotipo escolhido antes de fazer o upload\",\n    \"Settings.application.customization.modal.pending.upload\": \"Enviar\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"imagem\",\n    \"Settings.error\": \"Erro\",\n    \"Settings.global\": \"Configurações Globais\",\n    \"Settings.permissions\": \"Painel administrativo\",\n    \"Settings.permissions.category\": \"Configurações de permissão da categoria {category}\",\n    \"Settings.permissions.category.plugins\": \"Configurações de permissão da extensão {category}\",\n    \"Settings.permissions.conditions.anytime\": \"A qualquer hora\",\n    \"Settings.permissions.conditions.apply\": \"Aplicar\",\n    \"Settings.permissions.conditions.can\": \"Pode\",\n    \"Settings.permissions.conditions.conditions\": \"Definir condições\",\n    \"Settings.permissions.conditions.links\": \"Links\",\n    \"Settings.permissions.conditions.no-actions\": \"Você precisa selecionar ações (criar, ler, editar, ...) antes de definir uma condição.\",\n    \"Settings.permissions.conditions.none-selected\": \"A qualquer hora\",\n    \"Settings.permissions.conditions.or\": \"OU\",\n    \"Settings.permissions.conditions.when\": \"Quando\",\n    \"Settings.permissions.select-all-by-permission\": \"Selecionar todas as permissões de {label}\",\n    \"Settings.permissions.select-by-permission\": \"Selecionar permissão de {label}\",\n    \"Settings.permissions.users.create\": \"Convidar novo usuário\",\n    \"Settings.permissions.users.email\": \"E-mail\",\n    \"Settings.permissions.users.firstname\": \"Primeiro nome\",\n    \"Settings.permissions.users.lastname\": \"Último nome\",\n    \"Settings.permissions.users.form.sso\": \"Conectar com SSO\",\n    \"Settings.permissions.users.form.sso.description\": \"Quando ativado, usuários podem se conectar com SSO\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Todos os usuários com acesso ao painel administrativo\",\n    \"Settings.permissions.users.tabs.label\": \"Permissões de Abas\",\n    \"Settings.profile.form.notify.data.loaded\": \"Os dados do seu pefil foram carregados\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Limpar a linguagem da interface selecionada\",\n    \"Settings.profile.form.section.experience.here\": \"aqui\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Linguagem da interface\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Somente sua interface será exibida com a linguagem selecionada.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"A nova linguagem seleciona só será exibida a você. Por favor, leia essa {documentation} para disponibilizar outras linguagens para sua equipe.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Modo de interface\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Selecione o modo de interface que você deseja usar\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"modo {name}\",\n    light: light,\n    dark: dark,\n    \"Settings.profile.form.section.experience.title\": \"Experiência\",\n    \"Settings.profile.form.section.head.title\": \"Perfil do usuário\",\n    \"Settings.profile.form.section.profile.page.title\": \"Página do perfil\",\n    \"Settings.roles.create.description\": \"Defina as permissões desse papel\",\n    \"Settings.roles.create.title\": \"Criar uma função\",\n    \"Settings.roles.created\": \"Função criada\",\n    \"Settings.roles.edit.title\": \"Editar uma função\",\n    \"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# usuários} um {# usuário} outros {# usuários}} com esse papel\",\n    \"Settings.roles.form.created\": \"Criado\",\n    \"Settings.roles.form.description\": \"Nome e descrição da função\",\n    \"Settings.roles.form.permission.property-label\": \"Permissões de {label}\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Permissões de campos\",\n    \"Settings.roles.form.permissions.create\": \"Criar\",\n    \"Settings.roles.form.permissions.delete\": \"Remover\",\n    \"Settings.roles.form.permissions.publish\": \"Publicar\",\n    \"Settings.roles.form.permissions.read\": \"Ler\",\n    \"Settings.roles.form.permissions.update\": \"Editar\",\n    \"Settings.roles.list.button.add\": \"Adicionar novo papel\",\n    \"Settings.roles.list.description\": \"lista de papéis\",\n    \"Settings.roles.title.singular\": \"função\",\n    \"Settings.sso.description\": \"Configurações da funcionalidade de Single Sign-On.\",\n    \"Settings.sso.form.defaultRole.description\": \"Isso irá atribuir o novo usuário ao papel selecionado\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"Você precisa de permissão para ver os papéis administrativos\",\n    \"Settings.sso.form.defaultRole.label\": \"Função padrão\",\n    \"Settings.sso.form.registration.description\": \"Criar novo usuário ao logar por SSO e nenhuma conta existe previamente\",\n    \"Settings.sso.form.registration.label\": \"Inscrição automática\",\n    \"Settings.sso.title\": \"Single Sign-On\",\n    \"Settings.webhooks.create\": \"Criar um webhook\",\n    \"Settings.webhooks.create.header\": \"Adicionar um novo header\",\n    \"Settings.webhooks.created\": \"Webhook criado\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Esse evento só está disponível para conteúdos com a funcionalidade de Rascunho/Publicado habilitada\",\n    \"Settings.webhooks.events.create\": \"Criar\",\n    \"Settings.webhooks.events.update\": \"Atualizar\",\n    \"Settings.webhooks.form.events\": \"Eventos\",\n    \"Settings.webhooks.form.headers\": \"Headers\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"Remover header {number}\",\n    \"Settings.webhooks.key\": \"Chave\",\n    \"Settings.webhooks.list.button.add\": \"Adicionar novo webhook\",\n    \"Settings.webhooks.list.description\": \"Receba notificações de mudanças POST.\",\n    \"Settings.webhooks.list.empty.description\": \"Adicione o seu primeiro a essa lista.\",\n    \"Settings.webhooks.list.empty.link\": \"Veja nossa documentação\",\n    \"Settings.webhooks.list.empty.title\": \"Nenhum webhook adicionado ainda\",\n    \"Settings.webhooks.list.th.actions\": \"ações\",\n    \"Settings.webhooks.list.th.status\": \"estado\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhooks\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# elemento} other {# elementos}} selecionado(s)\",\n    \"Settings.webhooks.trigger\": \"Disparo\",\n    \"Settings.webhooks.trigger.cancel\": \"Cancelar disparo\",\n    \"Settings.webhooks.trigger.pending\": \"Pendente…\",\n    \"Settings.webhooks.trigger.save\": \"Por favor salve para disparar\",\n    \"Settings.webhooks.trigger.success\": \"Sucesso!\",\n    \"Settings.webhooks.trigger.success.label\": \"Disparo realizado com sucesso\",\n    \"Settings.webhooks.trigger.test\": \"Disparo de teste\",\n    \"Settings.webhooks.trigger.title\": \"Salvar antes do Disparo\",\n    \"Settings.webhooks.value\": \"Valor\",\n    \"Usecase.back-end\": \"Desenvolvedor Back-end\",\n    \"Usecase.button.skip\": \"Pular esta pergunta\",\n    \"Usecase.content-creator\": \"Criador de Conteúdo\",\n    \"Usecase.front-end\": \"Desenvolvedor Front-end\",\n    \"Usecase.full-stack\": \"Desenvolvedor Full-stack\",\n    \"Usecase.input.work-type\": \"Qual é o seu tipo de trabalho?\",\n    \"Usecase.notification.success.project-created\": \"Projeto criado com sucesso\",\n    \"Usecase.other\": \"Outro\",\n    \"Usecase.title\": \"Conte-nos um pouco mais sobre você\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Usuários & Permissões\",\n    \"Users.components.List.empty\": \"Não há usuários...\",\n    \"Users.components.List.empty.withFilters\": \"Não há usuários correspondentes aos filtros...\",\n    \"Users.components.List.empty.withSearch\": \"Não há usuários correspondentes à busca ({search})...\",\n    \"admin.pages.MarketPlacePage.head\": \"Loja - Extensões\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"Você está offline\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"Você precisa estar online para ver as extensões\",\n    \"admin.pages.MarketPlacePage.plugins\": \"Extensões\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"Copiar comando de instalação\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"Comando de instalação pronto para ser colado em seu terminal\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"Saber mais\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"Saiba mais sobre {pluginName}\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"Saber mais\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"Instalado\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Criado por Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Extensão verificada por Strapi\",\n    \"admin.pages.MarketPlacePage.providers\": \"Fornecedores\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"Limpar busca\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"Sem resultado para \\\"{target}\\\"\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"Pesquisar\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Submeta sua extensão\",\n    \"admin.pages.MarketPlacePage.submit.provider.link\": \"Submeta provedor\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Faça mais com o Strapi\",\n    \"admin.pages.MarketPlacePage.tab-group.label\": \"Plugins e Provedores para Strapi\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"Falta-lhe um plugin?\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"Diga-nos qual extensão você está procurando e informaremos nossos desenvolvedores de extensões da comunidade caso eles estejam em busca de inspiração!\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Copiar pra área de transferência\",\n    \"app.component.search.label\": \"Buscar por {target}\",\n    \"app.component.table.duplicate\": \"Duplicar {target}\",\n    \"app.component.table.edit\": \"Editar {target}\",\n    \"app.component.table.select.one-entry\": \"Selecionar {target}\",\n    \"app.components.BlockLink.blog\": \"Blog\",\n    \"app.components.BlockLink.blog.content\": \"Leia as últimas notícias do Strapi e seu ecossistema.\",\n    \"app.components.BlockLink.code\": \"Códigos de Exemplo\",\n    \"app.components.BlockLink.code.content\": \"Aprenda testando projetos desenvolvidos pela comunidade.\",\n    \"app.components.BlockLink.documentation.content\": \"Descubra os conceitos essenciais, guias e instruções.\",\n    \"app.components.BlockLink.tutorial\": \"Tutoriais\",\n    \"app.components.BlockLink.tutorial.content\": \"Siga o passo-a-passo para usar e customizar o Strapi.\",\n    \"app.components.Button.cancel\": \"Cancelar\",\n    \"app.components.Button.confirm\": \"Confirmar\",\n    \"app.components.Button.reset\": \"Resetar\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Em breve\",\n    \"app.components.ConfirmDialog.title\": \"Confirmação\",\n    \"app.components.DownloadInfo.download\": \"Transferência em andamento...\",\n    \"app.components.DownloadInfo.text\": \"Isto poderá levar alguns minutos. Obrigado pela sua paciência\",\n    \"app.components.EmptyAttributes.title\": \"Ainda não existem campos\",\n    \"app.components.EmptyStateLayout.content-document\": \"Nenhum conteúdo encontrado\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"Você não tem permissão para acessar esse conteúdo\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>Crie e gerencie todo o conteúdo aqui no Gerenciador de Conteúdos.</p><p>Ex: Levando ainda mais o exemplo do site do Blog, pode-se escrever um artigo, salvá-lo e publicá-lo como quiser.</p><p>💡 Dica rápida - Não se esqueça de clicar em publicar no conteúdo que você criar.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ Crie seu primeiro conteúdo\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>Incrível, falta um último passo!</p><b>🚀  Ver conteúdo em ação</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"Teste a API\",\n    \"app.components.GuidedTour.CM.success.title\": \"Passo 2: Concluído ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>Os tipos de coleção ajudam a gerenciar várias entradas, os tipos únicos são adequados para gerenciar apenas uma entrada.</p> <p>Ex: Para um site de blog, os artigos seriam um tipo de coleção, enquanto uma página inicial seria um tipo único.</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"Criar um Tipo de Coleção\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 Criar um primeiro tipo de coleção\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>Bom trabalho!</p><b>⚡️ O que você gostaria de compartilhar com o mundo?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"Passo 1: Concluído ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>Gere um token de autenticação aqui e recupere o conteúdo que você acabou de criar.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"Gerar um token de API\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Visualizar conteúdo em ação \",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>Visualize o conteúdo em ação fazendo uma solicitação HTTP:</p><ul><li><p>Para esta URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Com o cabeçalho: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Para mais formas de interagir com o conteúdo, consulte a <documentationLink>documentação</documentationLink>.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"Voltar para a página inicial\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"Passo 3: Concluído ✅\",\n    \"app.components.GuidedTour.create-content\": \"Crie um conteúdo\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ O que você gostaria de compartilhar com o mundo?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"Ir para o Criador de tipos de conteúdo\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 Construir a estrutura de conteúdo\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"Testar a API\",\n    \"app.components.GuidedTour.skip\": \"Pular o tutorial\",\n    \"app.components.GuidedTour.title\": \"3 passos para começar\",\n    \"app.components.HomePage.button.blog\": \"Veja mais no blog\",\n    \"app.components.HomePage.community\": \"Nossa comunidade na web\",\n    \"app.components.HomePage.community.content\": \"Converse com membros da equipe, colaboradores e desenvolvedores em diversos canais.\",\n    \"app.components.HomePage.create\": \"Crie seu primeiro Tipo de Conteúdo\",\n    \"app.components.HomePage.roadmap\": \"Veja nosso roadmap\",\n    \"app.components.HomePage.welcome\": \"Bem-vindo(a) a bordo 👋\",\n    \"app.components.HomePage.welcome.again\": \"Bem-vindo(a) 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"Estamos muito felizes em tê-lo(a) como um membro da nossa comunidade. Estamos sempre querendo saber sua opinião, então fique a vontade em nos enviar uma mensagem em privado no \",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Desejamos que você esteja progredindo em seu projeto... Fique por dentro das últimas novidades sobre o Strapi. Estamos sempre dando o nosso melhor para melhorar o produto sempre baseando-se em sua opinião.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"problemas.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" ou aponte \",\n    \"app.components.ImgPreview.hint\": \"Arraste e solte o seu arquivo sobre a área ou {browse} um arquivo para fazer o envio\",\n    \"app.components.ImgPreview.hint.browse\": \"selecione\",\n    \"app.components.InputFile.newFile\": \"Adicionar um novo arquivo\",\n    \"app.components.InputFileDetails.open\": \"Abrir numa nova aba\",\n    \"app.components.InputFileDetails.originalName\": \"Nome original:\",\n    \"app.components.InputFileDetails.remove\": \"Remova este arquivo\",\n    \"app.components.InputFileDetails.size\": \"Tamanho:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Pode demorar alguns segundos para baixar e instalar a extensão.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Baixando...\",\n    \"app.components.InstallPluginPage.description\": \"Estenda seu aplicativo sem esforço.\",\n    \"app.components.LeftMenu.collapse\": \"Recolher barra de navegação\",\n    \"app.components.LeftMenu.expand\": \"Expandir barra de navegação\",\n    \"app.components.LeftMenu.general\": \"Geral\",\n    \"app.components.LeftMenu.logout\": \"Sair\",\n    \"app.components.LeftMenu.logo.alt\": \"logo da aplicação\",\n    \"app.components.LeftMenu.plugins\": \"Extensões\",\n    \"app.components.LeftMenu.trialCountdown\": \"Sua prova termina em {date}.\",\n    \"app.components.LeftMenu.navbrand.title\": \"Painel do Strapi\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"Local de trabalho\",\n    \"app.components.LeftMenuFooter.help\": \"Ajuda\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Mantido por \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Tipos de coleção\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Configurações\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Geral\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Nenhuma extensão instalada ainda\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Extensões\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Tipos singulares\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"Pode demorar alguns segundos para desinstalar a extensão.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Desinstalando\",\n    \"app.components.ListPluginsPage.description\": \"Lista de extensões instaladas no projeto.\",\n    \"app.components.ListPluginsPage.head.title\": \"Lista de extensões\",\n    \"app.components.Logout.logout\": \"Sair\",\n    \"app.components.Logout.profile\": \"Perfil\",\n    \"app.components.MarketplaceBanner\": \"Descubra extensões da comunidade, e muito mais para dar início ao seu projeto, em Strapi Awesome.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"um logo de foguete do strapi\",\n    \"app.components.MarketplaceBanner.link\": \"Ver agora\",\n    \"app.components.NotFoundPage.back\": \"Voltar à página inicial\",\n    \"app.components.NotFoundPage.description\": \"Não encontrado\",\n    \"app.components.Official\": \"Oficial\",\n    \"app.components.Onboarding.help.button\": \"Botão de ajuda\",\n    \"app.components.Onboarding.label.completed\": \"% concluído\",\n    \"app.components.Onboarding.title\": \"Vídeos de Introdução\",\n    \"app.components.PluginCard.Button.label.download\": \"Baixar\",\n    \"app.components.PluginCard.Button.label.install\": \"Já instalado\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"O recurso autoReload precisa estar ativado. Por favor, inicie seu aplicativo com `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Eu compreendo!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Por motivos de segurança, uma extensão só pode ser baixada no ambiente de desenvolvimento.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Não é possível baixar\",\n    \"app.components.PluginCard.compatible\": \"Compatível com a sua aplicação\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Compatível com a comunidade\",\n    \"app.components.PluginCard.more-details\": \"Mais detalhes\",\n    \"app.components.ToggleCheckbox.off-label\": \"Desativado\",\n    \"app.components.ToggleCheckbox.on-label\": \"Ativado\",\n    \"app.components.Users.MagicLink.connect\": \"Copie e compartilhe esse link para dar acesso ao usuário\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Envie esse link para o usuário. O primeiro login pode ser feito por um provedor de SSO\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Detalhes do usuário\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Papéis do usuário\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Um usuário pode ter um ou mais papéis\",\n    \"app.components.Users.SortPicker.button-label\": \"Ordenar por\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"E-mail (A a Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"E-mail (Z a A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Primeiro nome (A a Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Primeiro nome (Z a A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Último nome (A a Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Último nome (Z a A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Nome de usuário (A a Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Nome de usuário (Z a A)\",\n    \"app.components.listPlugins.button\": \"Adicionar nova Extensão\",\n    \"app.components.listPlugins.title.none\": \"Nenhuma extensão instalada\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Ocorreu um erro ao desinstalar extensão\",\n    \"app.containers.App.notification.error.init\": \"Ocorreu um erro ao solicitar a API\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Se você não receber esse link, por favor contate seu administrador.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Pode levar alguns minutos para receber seu link de recuperação de senha.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"E-mail enviado\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Ativo\",\n    \"app.containers.Users.EditPage.header.label\": \"Editar {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Editar usuário\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Papéis atribuídos\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Convidar usuário\",\n    \"app.links.configure-view\": \"Configurar a visualização\",\n    \"app.page.not.found\": \"Ops! Não conseguimos encontrar a página que você está procurando...\",\n    \"app.static.links.cheatsheet\": \"CheatSheet\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Adicionar filtro\",\n    \"app.utils.close-label\": \"Fechar\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"Duplicar\",\n    \"app.utils.edit\": \"Editar\",\n    \"app.utils.errors.file-too-big.message\": \"O arquivo é muito grande\",\n    \"app.utils.filter-value\": \"Filtrar valor\",\n    \"app.utils.filters\": \"Filtros\",\n    \"app.utils.notify.data-loaded\": \"{target} foi carregado\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Publicar\",\n    \"app.utils.select-all\": \"Selecionar todos\",\n    \"app.utils.select-field\": \"Selecionar campo\",\n    \"app.utils.select-filter\": \"Selecionar filtro\",\n    \"app.utils.unpublish\": \"Despublicar\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Esse conteúdo está sendo construído e voltará dentro de algumas semanas!\",\n    \"component.Input.error.validation.integer\": \"O valor deve ser um inteiro\",\n    \"components.AutoReloadBlocker.description\": \"Execute o Strapi com um dos seguintes comandos:\",\n    \"components.AutoReloadBlocker.header\": \"Auto recarregamento é necessário para esta extensão.\",\n    \"components.ErrorBoundary.title\": \"Algo deu errado...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"contém\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"contém (não diferencia maiúsculas de minúsculas)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"termina com\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"termina com (não diferencia maiúsculas de minúsculas)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"é igual a\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"é igual a (não diferencia maiúsculas de minúsculas)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"é maior que\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"é maior que ou igual a\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"é menor que\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"é menor que ou igual a\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"é diferente de\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"é diferente de (não diferencia maiúsculas de minúsculas)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"não contém\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"não contém (não diferencia maiúsculas de minúsculas)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"não é vazio\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"é vazio\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"começa com\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"começa com (não diferencia maiúsculas de minúsculas)\",\n    \"components.Input.error.attribute.key.taken\": \"Este valor já existe\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Não pode ser igual\",\n    \"components.Input.error.attribute.taken\": \"O nome deste campo já existe\",\n    \"components.Input.error.contain.lowercase\": \"A senha deve conter pelo menos uma letra minúscula\",\n    \"components.Input.error.contain.number\": \"A senha deve conter pelo menos um número\",\n    \"components.Input.error.contain.uppercase\": \"A senha deve conter pelo menos uma letra maiúscula\",\n    \"components.Input.error.contentTypeName.taken\": \"Este tipo de conteúdo já existe\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"As senhas não conferem\",\n    \"components.Input.error.validation.email\": \"Isto não é um endereço de e-mail\",\n    \"components.Input.error.validation.json\": \"Isto não corresponde ao formato JSON\",\n    \"components.Input.error.validation.lowercase\": \"O valor deve ser um texto com letras minúsculas\",\n    \"components.Input.error.validation.max\": \"O valor é muito alto {max}.\",\n    \"components.Input.error.validation.maxLength\": \"O valor é muito longo {max}.\",\n    \"components.Input.error.validation.min\": \"O valor é muito baixo {min}.\",\n    \"components.Input.error.validation.minLength\": \"O valor é muito curto {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Não pode ser superior\",\n    \"components.Input.error.validation.regex\": \"O valor não corresponde ao regex.\",\n    \"components.Input.error.validation.required\": \"Este valor é obrigatório.\",\n    \"components.Input.error.validation.unique\": \"Este valor já foi usado.\",\n    \"components.InputSelect.option.placeholder\": \"Escolha aqui\",\n    \"components.ListRow.empty\": \"Não existe nenhum registro para ser exibido\",\n    \"components.NotAllowedInput.text\": \"Sem permissões para ver esse campo\",\n    \"components.OverlayBlocker.description\": \"Você está usando um recurso que precisa que o servidor seja reiniciado. Por favor, aguarde até que o servidor esteja totalmente reiniciado.\",\n    \"components.OverlayBlocker.description.serverError\": \"O servidor deve ter sido reiniciado. Verifique seus registros no terminal.\",\n    \"components.OverlayBlocker.title\": \"Aguardando pela reinicialização...\",\n    \"components.OverlayBlocker.title.serverError\": \"A reinicialização levou mais tempo que o esperado\",\n    \"components.PageFooter.select\": \"registros por página\",\n    \"components.ProductionBlocker.description\": \"Por motivos de segurança, temos que desativar esta extensão em outros ambientes.\",\n    \"components.ProductionBlocker.header\": \"Esta extensão está disponível apenas em modo de desenvolvimento.\",\n    \"components.Search.placeholder\": \"Buscar...\",\n    \"components.TableHeader.sort\": \"Ordenar por {label}\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Modo de edição\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Modo de visualização\",\n    \"components.Wysiwyg.collapse\": \"Fechar\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Título H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Título H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Título H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Título H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Título H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Título H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Adicionar um título\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"caracteres\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Expandir\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Arraste e solte arquivos, cole na área de transferência ou {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"Selecione-os\",\n    \"components.pagination.go-to\": \"Ir pra página {page}\",\n    \"components.pagination.go-to-next\": \"Ir pra próxima página\",\n    \"components.pagination.go-to-previous\": \"Ir pra página anterior\",\n    \"components.pagination.remaining-links\": \"E {number} outros links\",\n    \"components.popUpWarning.button.cancel\": \"Não, cancelar\",\n    \"components.popUpWarning.button.confirm\": \"Sim, confirmar\",\n    \"components.popUpWarning.message\": \"Tem certeza que deseja remover isso?\",\n    \"components.popUpWarning.title\": \"Por favor, confirme\",\n    \"form.button.continue\": \"Continuar\",\n    \"form.button.done\": \"Pronto\",\n    \"global.search\": \"Pesquisar\",\n    \"global.actions\": \"Ações\",\n    \"global.active\": \"Ativo\",\n    \"global.inactive\": \"Inativo\",\n    \"global.back\": \"Voltar\",\n    \"global.cancel\": \"Cancelar\",\n    \"global.change-password\": \"Alterar senha\",\n    \"global.content-manager\": \"Gerenciador de Conteúdo\",\n    \"global.continue\": \"Continuar\",\n    \"global.delete\": \"Deletar\",\n    \"global.delete-target\": \"Deletar {target}\",\n    \"global.description\": \"Descrição\",\n    \"global.details\": \"Detalhes\",\n    \"global.disabled\": \"Desabilitado\",\n    \"global.documentation\": \"Documentação\",\n    \"global.enabled\": \"Habilitado\",\n    \"global.finish\": \"Finalizar\",\n    \"global.marketplace\": \"Loja\",\n    \"global.name\": \"Nome\",\n    \"global.none\": \"Nenhum\",\n    \"global.password\": \"Senha\",\n    \"global.plugins\": \"Extensões\",\n    \"global.plugins.content-manager\": \"Gerenciador de Conteúdo\",\n    \"global.plugins.content-manager.description\": \"Maneira rápida de ver, editar e excluir os dados em seu banco de dados.\",\n    \"global.plugins.content-type-builder\": \"Criador de Tipos de Conteúdo\",\n    \"global.plugins.content-type-builder.description\": \"Modele a estrutura de dados da sua API. Crie novos campos e relações em apenas um minuto. Os arquivos são criados e atualizados automaticamente em seu projeto.\",\n    \"global.plugins.email\": \"Email\",\n    \"global.plugins.email.description\": \"Configure seu aplicativo para enviar emails.\",\n    \"global.plugins.upload\": \"Biblioteca de mídia\",\n    \"global.plugins.upload.description\": \"Gerenciamento de arquivos de mídia.\",\n    \"global.plugins.graphql\": \"GraphQL\",\n    \"global.plugins.graphql.description\": \"Adiciona o endpoint GraphQL com métodos de API padrão.\",\n    \"global.plugins.documentation\": \"Documentação\",\n    \"global.plugins.documentation.description\": \"Crie um documento OpenAPI e visualize sua API com SWAGGER UI.\",\n    \"global.plugins.i18n\": \"Internacionalização\",\n    \"global.plugins.i18n.description\": \"Esta extensão permite criar, ler e atualizar conteúdo em diferentes idiomas, tanto do Painel Administrativo quanto da API.\",\n    \"global.plugins.sentry\": \"Sentry\",\n    \"global.plugins.sentry.description\": \"Envie eventos de erro do Strapi para o Sentry.\",\n    \"global.plugins.users-permissions\": \"Funções e permissões\",\n    \"global.plugins.users-permissions.description\": \"Proteja sua API com um processo de autenticação completo baseado em JWT. Esta extensão também vem com uma estratégia de ACL que permite gerenciar as permissões entre os grupos de usuários.\",\n    \"global.profile\": \"Perfil\",\n    \"global.prompt.unsaved\": \"Você tem certeza que deseja sair desta página? Todas as suas modificações serão perdidas\",\n    \"global.reset-password\": \"Redefinir senha\",\n    \"global.roles\": \"Funções\",\n    \"global.save\": \"Salvar\",\n    \"global.see-more\": \"Ver mais\",\n    \"global.select\": \"Selecionar\",\n    \"global.select-all-entries\": \"Selecionar todas as entradas\",\n    \"global.settings\": \"Configurações\",\n    \"global.strapi-super-admin\": \"Super Admin\",\n    \"global.strapi-editor\": \"Editor\",\n    \"global.strapi-author\": \"Autor\",\n    \"global.table.header.email\": \"Email\",\n    \"global.table.header.firstname\": \"Nome\",\n    \"global.table.header.isActive\": \"Status do usuário\",\n    \"global.table.header.lastname\": \"Sobrenome\",\n    \"global.table.header.roles\": \"Funções\",\n    \"global.table.header.username\": \"Usuário\",\n    \"global.type\": \"Tipo\",\n    \"global.users\": \"Usuários\",\n    \"notification.contentType.relations.conflict\": \"Tipo de conteúdo tem relacionamentos conflitantes\",\n    \"notification.default.title\": \"Informação:\",\n    \"notification.error\": \"Ocorreu um erro\",\n    \"notification.error.layout\": \"Não foi possível recuperar o layout\",\n    \"notification.form.error.fields\": \"O formulário contém alguns erros\",\n    \"notification.form.success.fields\": \"Mudanças salvas\",\n    \"notification.link-copied\": \"Link copiado pra área de transferência\",\n    \"notification.permission.not-allowed-read\": \"Você não tem permissão para ver esse documento\",\n    \"notification.success.delete\": \"O item foi removido\",\n    \"notification.success.saved\": \"Salvo\",\n    \"notification.success.title\": \"Sucesso:\",\n    \"notification.version.update.message\": \"Uma nova versão do Strapi está disponível!\",\n    \"notification.warning.title\": \"Aviso:\",\n    \"notification.warning.404\": \"404 - Página não encontrada\",\n    or: or,\n    \"request.error.model.unknown\": \"Este modelo não existe\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, ptBR as default, light, or, skipToContent, submit };\n//# sourceMappingURL=pt-BR.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,OAAO;AAAA,EACP;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,sCAAsC;AAAA,EACtC,qDAAqD;AAAA,EACrD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,2DAA2D;AAAA,EAC3D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,8DAA8D;AAAA,EAC9D,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,mEAAmE;AAAA,EACnE,0DAA0D;AAAA,EAC1D,6DAA6D;AAAA,EAC7D,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}