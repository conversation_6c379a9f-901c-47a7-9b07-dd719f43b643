{"version": 3, "sources": ["../../../@strapi/email/admin/src/utils/getYupInnerErrors.ts", "../../../@strapi/email/admin/src/utils/schema.ts", "../../../@strapi/email/admin/src/pages/Settings.tsx"], "sourcesContent": ["import type { MessageDescriptor, PrimitiveType } from 'react-intl';\nimport type { ValidationError } from 'yup';\n\ninterface TranslationMessage extends MessageDescriptor {\n  values?: Record<string, PrimitiveType>;\n}\n\nconst extractValuesFromYupError = (\n  errorType?: string | undefined,\n  errorParams?: Record<string, any> | undefined\n) => {\n  if (!errorType || !errorParams) {\n    return {};\n  }\n\n  return {\n    [errorType]: errorParams[errorType],\n  };\n};\n\nconst getYupInnerErrors = (error: ValidationError) =>\n  (error?.inner || []).reduce<Record<string, TranslationMessage>>((acc, currentError) => {\n    if (currentError.path) {\n      acc[currentError.path.split('[').join('.').split(']').join('')] = {\n        id: currentError.message,\n        defaultMessage: currentError.message,\n        values: extractValuesFromYupError(currentError?.type, currentError?.params),\n      };\n    }\n\n    return acc;\n  }, {});\n\nexport { getYupInnerErrors };\n", "import { translatedErrors } from '@strapi/admin/strapi-admin';\nimport * as yup from 'yup';\n\nexport const schema = yup.object().shape({\n  email: yup.string().email(translatedErrors.email.id).required(translatedErrors.required.id),\n});\n", "import * as React from 'react';\n\nimport { Page, useNotification, useFetchClient, Layouts } from '@strapi/admin/strapi-admin';\nimport {\n  Box,\n  Button,\n  Flex,\n  Grid,\n  SingleSelectOption,\n  SingleSelect,\n  TextInput,\n  Typography,\n  Field,\n} from '@strapi/design-system';\nimport { Mail } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useQuery, useMutation } from 'react-query';\nimport { styled } from 'styled-components';\nimport { ValidationError } from 'yup';\n\nimport { PERMISSIONS } from '../constants';\nimport { getYupInnerErrors } from '../utils/getYupInnerErrors';\nimport { schema } from '../utils/schema';\n\nimport type { EmailSettings } from '../../../shared/types';\n\nconst DocumentationLink = styled.a`\n  color: ${({ theme }) => theme.colors.primary600};\n`;\n\ninterface MutationBody {\n  to: string;\n}\n\nexport const ProtectedSettingsPage = () => (\n  <Page.Protect permissions={PERMISSIONS.settings}>\n    <SettingsPage />\n  </Page.Protect>\n);\n\nconst SettingsPage = () => {\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const { get, post } = useFetchClient();\n\n  const [testAddress, setTestAddress] = React.useState('');\n  const [isTestAddressValid, setIsTestAddressValid] = React.useState(false);\n\n  // TODO: I'm not sure how to type this. I think it should be Record<string, TranslationMessage> but that type is defined in the helper-plugin\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  const [formErrors, setFormErrors] = React.useState<Record<string, any>>({});\n\n  const { data, isLoading } = useQuery(['email', 'settings'], async () => {\n    const res = await get<EmailSettings>('/email/settings');\n    const {\n      data: { config },\n    } = res;\n\n    return config;\n  });\n\n  const mutation = useMutation<void, Error, MutationBody>(\n    async (body) => {\n      await post('/email/test', body);\n    },\n    {\n      onError() {\n        toggleNotification!({\n          type: 'danger',\n          message: formatMessage(\n            {\n              id: 'email.Settings.email.plugin.notification.test.error',\n              defaultMessage: 'Failed to send a test mail to {to}',\n            },\n            { to: testAddress }\n          ),\n        });\n      },\n      onSuccess() {\n        toggleNotification!({\n          type: 'success',\n          message: formatMessage(\n            {\n              id: 'email.Settings.email.plugin.notification.test.success',\n              defaultMessage: 'Email test succeeded, check the {to} mailbox',\n            },\n            { to: testAddress }\n          ),\n        });\n      },\n      retry: false,\n    }\n  );\n\n  React.useEffect(() => {\n    schema\n      .validate({ email: testAddress }, { abortEarly: false })\n      .then(() => setIsTestAddressValid(true))\n      .catch(() => setIsTestAddressValid(false));\n  }, [testAddress]);\n\n  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    setTestAddress(() => event.target.value);\n  };\n\n  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {\n    event.preventDefault();\n\n    try {\n      await schema.validate({ email: testAddress }, { abortEarly: false });\n    } catch (error) {\n      if (error instanceof ValidationError) {\n        setFormErrors(getYupInnerErrors(error));\n      }\n    }\n\n    mutation.mutate({ to: testAddress });\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Page.Main labelledBy=\"title\" aria-busy={isLoading || mutation.isLoading}>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: formatMessage({\n              id: 'email.Settings.email.plugin.title',\n              defaultMessage: 'Configuration',\n            }),\n          }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        id=\"title\"\n        title={formatMessage({\n          id: 'email.Settings.email.plugin.title',\n          defaultMessage: 'Configuration',\n        })}\n        subtitle={formatMessage({\n          id: 'email.Settings.email.plugin.subTitle',\n          defaultMessage: 'Test the settings for the Email plugin',\n        })}\n      />\n\n      <Layouts.Content>\n        {data && (\n          <form onSubmit={handleSubmit}>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={7}>\n              <Box\n                background=\"neutral0\"\n                hasRadius\n                shadow=\"filterShadow\"\n                paddingTop={6}\n                paddingBottom={6}\n                paddingLeft={7}\n                paddingRight={7}\n              >\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                  <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n                    <Typography variant=\"delta\" tag=\"h2\">\n                      {formatMessage({\n                        id: 'email.Settings.email.plugin.title.config',\n                        defaultMessage: 'Configuration',\n                      })}\n                    </Typography>\n                    <Typography>\n                      {formatMessage(\n                        {\n                          id: 'email.Settings.email.plugin.text.configuration',\n                          defaultMessage:\n                            'The plugin is configured through the {file} file, checkout this {link} for the documentation.',\n                        },\n                        {\n                          file: './config/plugins.js',\n                          link: (\n                            <DocumentationLink\n                              href=\"https://docs.strapi.io/developer-docs/latest/plugins/email.html\"\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                            >\n                              {formatMessage({\n                                id: 'email.link',\n                                defaultMessage: 'Link',\n                              })}\n                            </DocumentationLink>\n                          ),\n                        }\n                      )}\n                    </Typography>\n                  </Flex>\n\n                  <Grid.Root gap={5}>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root name=\"shipper-email\">\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'email.Settings.email.plugin.label.defaultFrom',\n                            defaultMessage: 'Default sender email',\n                          })}\n                        </Field.Label>\n                        <TextInput\n                          placeholder={formatMessage({\n                            id: 'email.Settings.email.plugin.placeholder.defaultFrom',\n                            defaultMessage: \"ex: Strapi No-Reply '<'<EMAIL>'>'\",\n                          })}\n                          disabled\n                          value={data.settings.defaultFrom}\n                        />\n                      </Field.Root>\n                    </Grid.Item>\n\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root name=\"response-email\">\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'email.Settings.email.plugin.label.defaultReplyTo',\n                            defaultMessage: 'Default response email',\n                          })}\n                        </Field.Label>\n                        <TextInput\n                          placeholder={formatMessage({\n                            id: 'email.Settings.email.plugin.placeholder.defaultReplyTo',\n                            defaultMessage: `ex: Strapi '<'<EMAIL>'>'`,\n                          })}\n                          disabled\n                          value={data.settings.defaultReplyTo}\n                        />\n                      </Field.Root>\n                    </Grid.Item>\n\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root name=\"email-provider\">\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'email.Settings.email.plugin.label.provider',\n                            defaultMessage: 'Email provider',\n                          })}\n                        </Field.Label>\n                        <SingleSelect disabled value={data.provider}>\n                          <SingleSelectOption value={data.provider}>\n                            {data.provider}\n                          </SingleSelectOption>\n                        </SingleSelect>\n                      </Field.Root>\n                    </Grid.Item>\n                  </Grid.Root>\n                </Flex>\n              </Box>\n\n              <Flex\n                alignItems=\"stretch\"\n                background=\"neutral0\"\n                direction=\"column\"\n                gap={4}\n                hasRadius\n                shadow=\"filterShadow\"\n                paddingTop={6}\n                paddingBottom={6}\n                paddingLeft={7}\n                paddingRight={7}\n              >\n                <Typography variant=\"delta\" tag=\"h2\">\n                  {formatMessage({\n                    id: 'email.Settings.email.plugin.title.test',\n                    defaultMessage: 'Test email delivery',\n                  })}\n                </Typography>\n\n                <Grid.Root gap={5}>\n                  <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                    <Field.Root\n                      name=\"test-address\"\n                      error={\n                        formErrors.email?.id &&\n                        formatMessage({\n                          id: `email.${formErrors.email?.id}`,\n                          defaultMessage: 'This is not a valid email',\n                        })\n                      }\n                    >\n                      <Field.Label>\n                        {formatMessage({\n                          id: 'email.Settings.email.plugin.label.testAddress',\n                          defaultMessage: 'Recipient email',\n                        })}\n                      </Field.Label>\n                      <TextInput\n                        onChange={handleChange}\n                        value={testAddress}\n                        placeholder={formatMessage({\n                          id: 'email.Settings.email.plugin.placeholder.testAddress',\n                          defaultMessage: 'ex: <EMAIL>',\n                        })}\n                      />\n                    </Field.Root>\n                  </Grid.Item>\n                  <Grid.Item col={7} s={12} direction=\"column\" alignItems=\"start\">\n                    <Button\n                      loading={mutation.isLoading}\n                      disabled={!isTestAddressValid}\n                      type=\"submit\"\n                      startIcon={<Mail />}\n                    >\n                      {formatMessage({\n                        id: 'email.Settings.email.plugin.button.test-email',\n                        defaultMessage: 'Send test email',\n                      })}\n                    </Button>\n                  </Grid.Item>\n                </Grid.Root>\n              </Flex>\n            </Flex>\n          </form>\n        )}\n      </Layouts.Content>\n    </Page.Main>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAMA,4BAA4B,CAChCC,WACAC,gBAAAA;AAEA,MAAI,CAACD,aAAa,CAACC,aAAa;AAC9B,WAAO,CAAA;EACT;AAEA,SAAO;IACL,CAACD,SAAU,GAAEC,YAAYD,SAAU;EACrC;AACF;AAEA,IAAME,oBAAoB,CAACC,YACxBA,+BAAOC,UAAS,CAAA,GAAIC,OAA2C,CAACC,KAAKC,iBAAAA;AACpE,MAAIA,aAAaC,MAAM;AACrBF,QAAIC,aAAaC,KAAKC,MAAM,GAAKC,EAAAA,KAAK,GAAA,EAAKD,MAAM,GAAA,EAAKC,KAAK,EAAA,CAAA,IAAO;MAChEC,IAAIJ,aAAaK;MACjBC,gBAAgBN,aAAaK;MAC7BE,QAAQf,0BAA0BQ,6CAAcQ,MAAMR,6CAAcS,MAAAA;IACtE;EACF;AAEA,SAAOV;AACT,GAAG,CAAA,CAAC;;;IC5BOW,SAAaC,QAAM,EAAGC,MAAM;EACvCC,OAAWC,OAAM,EAAGD,MAAME,YAAiBF,MAAMG,EAAE,EAAEC,SAASF,YAAiBE,SAASD,EAAE;AAC5F,CAAG;;;ACqBH,IAAME,oBAAoBC,GAAOC;WACtB,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;AAOpCC,IAAAA,wBAAwB,UACnCC,wBAACC,KAAKC,SAAO;EAACC,aAAaC,YAAYC;EACrC,cAAAL,wBAACM,cAAAA,CAAAA,CAAAA;AAEH,CAAA;AAEF,IAAMA,eAAe,MAAA;;AACnB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,KAAKC,KAAI,IAAKC,eAAAA;AAEtB,QAAM,CAACC,aAAaC,cAAAA,IAAwBC,eAAS,EAAA;AACrD,QAAM,CAACC,oBAAoBC,qBAAAA,IAA+BF,eAAS,KAAA;AAInE,QAAM,CAACG,YAAYC,aAAAA,IAAuBJ,eAA8B,CAAA,CAAC;AAEzE,QAAM,EAAEK,MAAMC,UAAS,IAAKC,SAAS;IAAC;IAAS;KAAa,YAAA;AAC1D,UAAMC,MAAM,MAAMb,IAAmB,iBAAA;AACrC,UAAM,EACJU,MAAM,EAAEI,OAAM,EAAE,IACdD;AAEJ,WAAOC;EACT,CAAA;AAEA,QAAMC,WAAWC,YACf,OAAOC,SAAAA;AACL,UAAMhB,KAAK,eAAegB,IAAAA;KAE5B;IACEC,UAAAA;AACEtB,yBAAoB;QAClBuB,MAAM;QACNC,SAAStB,cACP;UACEuB,IAAI;UACJC,gBAAgB;WAElB;UAAEC,IAAIpB;QAAY,CAAA;MAEtB,CAAA;IACF;IACAqB,YAAAA;AACE5B,yBAAoB;QAClBuB,MAAM;QACNC,SAAStB,cACP;UACEuB,IAAI;UACJC,gBAAgB;WAElB;UAAEC,IAAIpB;QAAY,CAAA;MAEtB,CAAA;IACF;IACAsB,OAAO;EACT,CAAA;AAGFC,EAAMC,gBAAU,MAAA;AACdC,WACGC,SAAS;MAAEC,OAAO3B;OAAe;MAAE4B,YAAY;KAC/CC,EAAAA,KAAK,MAAMzB,sBAAsB,IAAA,CAAA,EACjC0B,MAAM,MAAM1B,sBAAsB,KAAA,CAAA;KACpC;IAACJ;EAAY,CAAA;AAEhB,QAAM+B,eAAe,CAACC,UAAAA;AACpB/B,mBAAe,MAAM+B,MAAMC,OAAOC,KAAK;EACzC;AAEA,QAAMC,eAAe,OAAOH,UAAAA;AAC1BA,UAAMI,eAAc;AAEpB,QAAI;AACF,YAAMX,OAAOC,SAAS;QAAEC,OAAO3B;SAAe;QAAE4B,YAAY;MAAM,CAAA;IACpE,SAASS,OAAO;AACd,UAAIA,iBAAiBC,iBAAiB;AACpChC,sBAAciC,kBAAkBF,KAAAA,CAAAA;MAClC;IACF;AAEAzB,aAAS4B,OAAO;MAAEpB,IAAIpB;IAAY,CAAA;EACpC;AAEA,MAAIQ,WAAW;AACb,eAAOtB,wBAACC,KAAKsD,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,yBAACvD,KAAKwD,MAAI;IAACC,YAAW;IAAQC,aAAWrC,aAAaI,SAASJ;;UAC7DtB,wBAACC,KAAK2D,OAAK;kBACRnD,cACC;UAAEuB,IAAI;UAAsBC,gBAAgB;WAC5C;UACE4B,MAAMpD,cAAc;YAClBuB,IAAI;YACJC,gBAAgB;UAClB,CAAA;QACF,CAAA;;UAGJjC,wBAAC8D,QAAQC,QAAM;QACb/B,IAAG;QACHgC,OAAOvD,cAAc;UACnBuB,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACAgC,UAAUxD,cAAc;UACtBuB,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAGFjC,wBAAC8D,QAAQI,SAAO;QACb7C,UAAAA,YACCrB,wBAACmE,QAAAA;UAAKC,UAAUnB;UACd,cAAAO,yBAACa,MAAAA;YAAKC,WAAU;YAASC,YAAW;YAAUC,KAAK;;kBACjDxE,wBAACyE,KAAAA;gBACCC,YAAW;gBACXC,WAAS;gBACTC,QAAO;gBACPC,YAAY;gBACZC,eAAe;gBACfC,aAAa;gBACbC,cAAc;gBAEd,cAAAxB,yBAACa,MAAAA;kBAAKC,WAAU;kBAASC,YAAW;kBAAUC,KAAK;;wBACjDhB,yBAACa,MAAAA;sBAAKC,WAAU;sBAASC,YAAW;sBAAUC,KAAK;;4BACjDxE,wBAACiF,YAAAA;0BAAWC,SAAQ;0BAAQC,KAAI;oCAC7B1E,cAAc;4BACbuB,IAAI;4BACJC,gBAAgB;0BAClB,CAAA;;4BAEFjC,wBAACiF,YAAAA;oCACExE,cACC;4BACEuB,IAAI;4BACJC,gBACE;6BAEJ;4BACEmD,MAAM;4BACNC,UACErF,wBAACP,mBAAAA;8BACC6F,MAAK;8BACLvC,QAAO;8BACPwC,KAAI;wCAEH9E,cAAc;gCACbuB,IAAI;gCACJC,gBAAgB;8BAClB,CAAA;;0BAGN,CAAA;;;;wBAKNuB,yBAACgC,KAAKC,MAAI;sBAACjB,KAAK;;4BACdxE,wBAACwF,KAAKE,MAAI;0BAACC,KAAK;0BAAGC,GAAG;0BAAItB,WAAU;0BAASC,YAAW;wCACtDf,yBAACqC,MAAMJ,MAAI;4BAAC5B,MAAK;;kCACf7D,wBAAC6F,MAAMC,OAAK;0CACTrF,cAAc;kCACbuB,IAAI;kCACJC,gBAAgB;gCAClB,CAAA;;kCAEFjC,wBAAC+F,WAAAA;gCACCC,aAAavF,cAAc;kCACzBuB,IAAI;kCACJC,gBAAgB;gCAClB,CAAA;gCACAgE,UAAQ;gCACRjD,OAAO3B,KAAKhB,SAAS6F;;;;;4BAK3BlG,wBAACwF,KAAKE,MAAI;0BAACC,KAAK;0BAAGC,GAAG;0BAAItB,WAAU;0BAASC,YAAW;wCACtDf,yBAACqC,MAAMJ,MAAI;4BAAC5B,MAAK;;kCACf7D,wBAAC6F,MAAMC,OAAK;0CACTrF,cAAc;kCACbuB,IAAI;kCACJC,gBAAgB;gCAClB,CAAA;;kCAEFjC,wBAAC+F,WAAAA;gCACCC,aAAavF,cAAc;kCACzBuB,IAAI;kCACJC,gBAAgB;gCAClB,CAAA;gCACAgE,UAAQ;gCACRjD,OAAO3B,KAAKhB,SAAS8F;;;;;4BAK3BnG,wBAACwF,KAAKE,MAAI;0BAACC,KAAK;0BAAGC,GAAG;0BAAItB,WAAU;0BAASC,YAAW;wCACtDf,yBAACqC,MAAMJ,MAAI;4BAAC5B,MAAK;;kCACf7D,wBAAC6F,MAAMC,OAAK;0CACTrF,cAAc;kCACbuB,IAAI;kCACJC,gBAAgB;gCAClB,CAAA;;kCAEFjC,wBAACoG,cAAAA;gCAAaH,UAAQ;gCAACjD,OAAO3B,KAAKgF;gCACjC,cAAArG,wBAACsG,oBAAAA;kCAAmBtD,OAAO3B,KAAKgF;kCAC7BhF,UAAAA,KAAKgF;;;;;;;;;;;kBASpB7C,yBAACa,MAAAA;gBACCE,YAAW;gBACXG,YAAW;gBACXJ,WAAU;gBACVE,KAAK;gBACLG,WAAS;gBACTC,QAAO;gBACPC,YAAY;gBACZC,eAAe;gBACfC,aAAa;gBACbC,cAAc;;sBAEdhF,wBAACiF,YAAAA;oBAAWC,SAAQ;oBAAQC,KAAI;8BAC7B1E,cAAc;sBACbuB,IAAI;sBACJC,gBAAgB;oBAClB,CAAA;;sBAGFuB,yBAACgC,KAAKC,MAAI;oBAACjB,KAAK;;0BACdxE,wBAACwF,KAAKE,MAAI;wBAACC,KAAK;wBAAGC,GAAG;wBAAItB,WAAU;wBAASC,YAAW;sCACtDf,yBAACqC,MAAMJ,MAAI;0BACT5B,MAAK;0BACLV,SACEhC,gBAAWsB,UAAXtB,mBAAkBa,OAClBvB,cAAc;4BACZuB,IAAI,UAASb,gBAAWsB,UAAXtB,mBAAkBa,EAAAA;4BAC/BC,gBAAgB;0BAClB,CAAA;;gCAGFjC,wBAAC6F,MAAMC,OAAK;wCACTrF,cAAc;gCACbuB,IAAI;gCACJC,gBAAgB;8BAClB,CAAA;;gCAEFjC,wBAAC+F,WAAAA;8BACCQ,UAAU1D;8BACVG,OAAOlC;8BACPkF,aAAavF,cAAc;gCACzBuB,IAAI;gCACJC,gBAAgB;8BAClB,CAAA;;;;;0BAINjC,wBAACwF,KAAKE,MAAI;wBAACC,KAAK;wBAAGC,GAAG;wBAAItB,WAAU;wBAASC,YAAW;wBACtD,cAAAvE,wBAACwG,QAAAA;0BACCC,SAAS/E,SAASJ;0BAClB2E,UAAU,CAAChF;0BACXa,MAAK;0BACL4E,eAAW1G,wBAAC2G,eAAAA,CAAAA,CAAAA;oCAEXlG,cAAc;4BACbuB,IAAI;4BACJC,gBAAgB;0BAClB,CAAA;;;;;;;;;;;;;AAWtB;", "names": ["extractValuesFromYupError", "errorType", "errorParams", "getYupInnerErrors", "error", "inner", "reduce", "acc", "currentError", "path", "split", "join", "id", "message", "defaultMessage", "values", "type", "params", "schema", "object", "shape", "email", "string", "translatedErrors", "id", "required", "DocumentationLink", "styled", "a", "theme", "colors", "primary600", "ProtectedSettingsPage", "_jsx", "Page", "Protect", "permissions", "PERMISSIONS", "settings", "SettingsPage", "toggleNotification", "useNotification", "formatMessage", "useIntl", "get", "post", "useFetchClient", "testAddress", "setTestAddress", "useState", "isTestAddressValid", "setIsTestAddressValid", "formErrors", "setFormErrors", "data", "isLoading", "useQuery", "res", "config", "mutation", "useMutation", "body", "onError", "type", "message", "id", "defaultMessage", "to", "onSuccess", "retry", "React", "useEffect", "schema", "validate", "email", "abort<PERSON><PERSON><PERSON>", "then", "catch", "handleChange", "event", "target", "value", "handleSubmit", "preventDefault", "error", "ValidationError", "getYupInnerErrors", "mutate", "Loading", "_jsxs", "Main", "labelledBy", "aria-busy", "Title", "name", "Layouts", "Header", "title", "subtitle", "Content", "form", "onSubmit", "Flex", "direction", "alignItems", "gap", "Box", "background", "hasRadius", "shadow", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "Typography", "variant", "tag", "file", "link", "href", "rel", "Grid", "Root", "<PERSON><PERSON>", "col", "s", "Field", "Label", "TextInput", "placeholder", "disabled", "defaultFrom", "defaultReplyTo", "SingleSelect", "provider", "SingleSelectOption", "onChange", "<PERSON><PERSON>", "loading", "startIcon", "Mail"]}