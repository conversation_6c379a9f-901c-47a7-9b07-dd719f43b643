import{a as S,n as I,z as A,bC as E,j as e,H as C,T as i,e as r,b8 as M,q as R,bH as d,bI as l}from"./strapi-z7ApxZZq.js";import{s as b}from"./selectors-B6uMLQu7.js";const T="https://strapi.io/billing/request-seats",_="https://strapi.io/billing/manage-seats",B=()=>{const{formatMessage:s}=S(),{settings:c}=I(b),{isLoading:a,allowedActions:{canRead:m,canCreate:g,canUpdate:p,canDelete:u}}=A(c?.users??{}),{license:n,isError:x,isLoading:f}=E({enabled:!a&&m&&g&&p&&u});if(x||(a||f)||!n)return null;const{licenseLimitStatus:h,enforcementUserCount:o,permittedSeats:t,type:L}=n;return t?e.jsxs(<PERSON><PERSON>Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:[e.jsx(i,{variant:"sigma",textColor:"neutral600",children:s({id:"Settings.application.admin-seats",defaultMessage:"Admin seats"})}),e.jsxs(r,{gap:2,children:[e.jsx(r,{children:e.jsx(i,{tag:"p",children:s({id:"Settings.application.ee.admin-seats.count",defaultMessage:"<text>{enforcementUserCount}</text>/{permittedSeats}"},{permittedSeats:t,enforcementUserCount:o,text:j=>e.jsx(i,{fontWeight:"semiBold",textColor:o>t?"danger500":void 0,children:j})})})}),h==="OVER_LIMIT"&&e.jsx(M,{label:s({id:"Settings.application.ee.admin-seats.at-limit-tooltip",defaultMessage:"At limit: add seats to invite more users"}),children:e.jsx(R,{width:"1.4rem",height:"1.4rem",fill:"danger500"})})]}),L==="gold"?e.jsx(d,{href:T,endIcon:e.jsx(l,{}),children:s({id:"Settings.application.ee.admin-seats.support",defaultMessage:"Contact sales"})}):e.jsx(d,{href:_,isExternal:!0,endIcon:e.jsx(l,{}),children:s({id:"Settings.application.ee.admin-seats.add-seats",defaultMessage:"Manage seats"})})]}):null};export{B as AdminSeatInfoEE};
