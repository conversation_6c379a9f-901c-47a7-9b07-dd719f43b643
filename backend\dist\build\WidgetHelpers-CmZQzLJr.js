import{a as r,j as e,e as o,L as a,p as i,T as s,q as d,s as g}from"./strapi-z7ApxZZq.js";const l=({children:n})=>{const{formatMessage:t}=r();return e.jsx(o,{height:"100%",justifyContent:"center",alignItems:"center",children:e.jsx(a,{children:n??t({id:"HomePage.widget.loading",defaultMessage:"Loading widget content"})})})},c=({children:n})=>{const{formatMessage:t}=r();return e.jsxs(o,{height:"100%",direction:"column",justifyContent:"center",alignItems:"center",gap:2,children:[e.jsx(d,{width:"3.2rem",height:"3.2rem",fill:"danger600"}),e.jsx(s,{variant:"delta",children:t({id:"global.error",defaultMessage:"Something went wrong"})}),e.jsx(s,{textColor:"neutral600",children:n??t({id:"HomePage.widget.error",defaultMessage:"Couldn't load widget content."})})]})},h=({children:n})=>{const{formatMessage:t}=r();return e.jsxs(o,{height:"100%",direction:"column",justifyContent:"center",alignItems:"center",gap:6,children:[e.jsx(g,{width:"16rem",height:"8.8rem"}),e.jsx(s,{textColor:"neutral600",children:n??t({id:"HomePage.widget.no-data",defaultMessage:"No content found."})})]})},m=({children:n})=>{const{formatMessage:t}=r();return e.jsxs(o,{height:"100%",direction:"column",justifyContent:"center",alignItems:"center",gap:6,children:[e.jsx(i,{width:"16rem",height:"8.8rem"}),e.jsx(s,{textColor:"neutral600",children:n??t({id:"HomePage.widget.no-permissions",defaultMessage:"You don’t have the permission to see this widget"})})]})},f={Loading:l,Error:c,NoData:h,NoPermissions:m};export{f as W};
