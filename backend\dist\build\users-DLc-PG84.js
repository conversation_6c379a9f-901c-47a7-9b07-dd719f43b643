const e=({firstname:t,lastname:n,username:r,email:s}={})=>r||(t?`${t} ${n??""}`.trim():s??""),o=(t={})=>t?.firstname&&t?.lastname?`${t.firstname.substring(0,1)}${t.lastname.substring(0,1)}`:e(t).split(" ").map(n=>n.substring(0,1)).join("").substring(0,1).toUpperCase(),c=async t=>{if(!t||!t.email)return null;try{return await i(t.email)}catch{return null}},a=t=>[...new Uint8Array(t)].map(n=>n.toString(16).padStart(2,"0")).join(""),i=async t=>{const n=new TextEncoder().encode(t),r=await crypto.subtle.digest("SHA-256",n);return a(r)};export{o as a,e as g,c as h};
