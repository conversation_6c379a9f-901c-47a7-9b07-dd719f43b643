import{bZ as e,aP as j,aQ as d,a$ as A,aR as R,j as a,a as L,w as P,aM as I,b_ as z,bW as N,v as $,z as C,b$ as p,c0 as B,c1 as H,P as o,C as U,B as u,D,E as O,$ as Q,e as k,T as q,H as m,aU as W}from"./strapi-z7ApxZZq.js";const G=[{label:{id:e("EditForm.inputToggle.label.email"),defaultMessage:"One account per email address"},hint:{id:e("EditForm.inputToggle.description.email"),defaultMessage:"Disallow the user to create multiple accounts using the same email address with different authentication providers."},name:"unique_email",type:"boolean",size:12},{label:{id:e("EditForm.inputToggle.label.sign-up"),defaultMessage:"Enable sign-ups"},hint:{id:e("EditForm.inputToggle.description.sign-up"),defaultMessage:"When disabled (OFF), the registration process is forbidden. No one can subscribe anymore no matter the used provider."},name:"allow_register",type:"boolean",size:12},{label:{id:e("EditForm.inputToggle.label.email-reset-password"),defaultMessage:"Reset password page"},hint:{id:e("EditForm.inputToggle.description.email-reset-password"),defaultMessage:"URL of your application's reset password page."},placeholder:{id:e("EditForm.inputToggle.placeholder.email-reset-password"),defaultMessage:"ex: https://youtfrontend.com/reset-password"},name:"email_reset_password",type:"string",size:12},{label:{id:e("EditForm.inputToggle.label.email-confirmation"),defaultMessage:"Enable email confirmation"},hint:{id:e("EditForm.inputToggle.description.email-confirmation"),defaultMessage:"When enabled (ON), new registered users receive a confirmation email."},name:"email_confirmation",type:"boolean",size:12},{label:{id:e("EditForm.inputToggle.label.email-confirmation-redirection"),defaultMessage:"Redirection url"},hint:{id:e("EditForm.inputToggle.description.email-confirmation-redirection"),defaultMessage:"After you confirmed your email, choose where you will be redirected."},placeholder:{id:e("EditForm.inputToggle.placeholder.email-confirmation-redirection"),defaultMessage:"ex: https://youtfrontend.com/email-confirmation"},name:"email_confirmation_redirection",type:"string",size:12}],g=new RegExp("(^$)|((.+:\\/\\/.*)(d*)\\/?(.*))"),V=j().shape({email_confirmation_redirection:A().when("email_confirmation",{is:!0,then:d().matches(g).required(),otherwise:d().nullable()}),email_reset_password:d().matches(g,{id:R.regex.id,defaultMessage:"This is not a valid URL"}).nullable()}),J=()=>a.jsx(o.Protect,{permissions:p.readAdvancedSettings,children:a.jsx(X,{})}),X=()=>{const{formatMessage:t}=L(),{toggleNotification:r}=P(),{notifyStatus:h}=I(),f=z(),{get:b,put:v}=N(),{formatAPIError:y}=$(),{isLoading:M,allowedActions:{canUpdate:S}}=C({update:p.updateAdvancedSettings}),{isLoading:w,data:l}=B(["users-permissions","advanced"],async()=>{const{data:i}=await b("/users-permissions/advanced");return i},{onSuccess(){h(t({id:e("Form.advancedSettings.data.loaded"),defaultMessage:"Advanced settings data has been loaded"}))},onError(){r({type:"danger",message:t({id:e("notification.error"),defaultMessage:"An error occured"})})}}),x=M||w,c=H(i=>v("/users-permissions/advanced",i),{async onSuccess(){await f.invalidateQueries(["users-permissions","advanced"]),r({type:"success",message:t({id:e("notification.success.saved"),defaultMessage:"Saved"})})},onError(i){r({type:"danger",message:y(i)})},refetchActive:!0}),{isLoading:E}=c,F=async i=>{c.mutate({...i,email_confirmation_redirection:i.email_confirmation?i.email_confirmation_redirection:""})};return x?a.jsx(o.Loading,{}):a.jsxs(o.Main,{"aria-busy":E,children:[a.jsx(o.Title,{children:t({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:t({id:e("HeaderNav.link.advancedSettings"),defaultMessage:"Advanced Settings"})})}),a.jsx(U,{onSubmit:F,initialValues:l.settings,validationSchema:V,children:({values:i,isSubmitting:_,modified:T})=>a.jsxs(a.Fragment,{children:[a.jsx(u.Header,{title:t({id:e("HeaderNav.link.advancedSettings"),defaultMessage:"Advanced Settings"}),primaryAction:a.jsx(D,{loading:_,type:"submit",disabled:!T||!S,startIcon:a.jsx(O,{}),size:"S",children:t({id:"global.save",defaultMessage:"Save"})})}),a.jsx(u.Content,{children:a.jsx(Q,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:a.jsxs(k,{direction:"column",alignItems:"stretch",gap:4,children:[a.jsx(q,{variant:"delta",tag:"h2",children:t({id:"global.settings",defaultMessage:"Settings"})}),a.jsx(m.Root,{gap:6,children:[{label:{id:e("EditForm.inputSelect.label.role"),defaultMessage:"Default role for authenticated users"},hint:{id:e("EditForm.inputSelect.description.role"),defaultMessage:"It will attach the new authenticated user to the selected role."},options:l.roles.map(n=>({label:n.name,value:n.type})),name:"default_role",size:6,type:"enumeration"},...G].map(({size:n,...s})=>a.jsx(m.Item,{col:n,direction:"column",alignItems:"stretch",children:a.jsx(W,{...s,disabled:s.name==="email_confirmation_redirection"&&i.email_confirmation===!1,label:t(s.label),hint:s.hint?t(s.hint):void 0,placeholder:s.placeholder?t(s.placeholder):void 0})},s.name))})]})})})]})})]})};export{X as AdvancedSettingsPage,J as ProtectedAdvancedSettingsPage};
