{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\dfghj\\\\site\\\\frontend\\\\src\\\\components\\\\OpportuniteForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction OpportuniteForm() {\n  _s();\n  // États pour les données\n  const [importateurs, setImportateurs] = useState([]);\n  const [exportateurs, setExportateurs] = useState([]);\n  const [matchedExportateurs, setMatchedExportateurs] = useState([]);\n  const [selectedExportateurs, setSelectedExportateurs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showNewImportateurForm, setShowNewImportateurForm] = useState(false);\n  const [showMatching, setShowMatching] = useState(false);\n  const [showConfirmation, setShowConfirmation] = useState(false);\n\n  // État du formulaire principal\n  const [formData, setFormData] = useState({\n    importateur: \"\",\n    objet: \"\",\n    pays: \"\",\n    date_debut: \"\",\n    date_fin: \"\",\n    secteurs: []\n  });\n\n  // État du formulaire nouvel importateur\n  const [newImportateur, setNewImportateur] = useState({\n    societe: \"\",\n    pays: \"\",\n    nom_responsable: \"\",\n    prenom_responsable: \"\",\n    telephone_whatsapp: \"\",\n    email: \"\",\n    region: \"\"\n  });\n\n  // Listes des options\n  const secteurs = [\"Agro-alimentaire\", \"Textile\", \"IME\", \"Service\", \"Artisanat\", \"Divers\"];\n  const pays = [\"France\", \"Allemagne\", \"Italie\", \"Espagne\", \"Belgique\", \"Pays-Bas\", \"Royaume-Uni\", \"Suisse\", \"Canada\", \"États-Unis\", \"Maroc\", \"Algérie\", \"Libye\", \"Égypte\", \"Arabie Saoudite\", \"Émirats Arabes Unis\", \"Qatar\", \"Koweït\", \"Turquie\", \"Chine\", \"Japon\", \"Corée du Sud\", \"Inde\", \"Autre\"];\n\n  // Charger les importateurs au démarrage\n  useEffect(() => {\n    Promise.all([fetch(\"http://localhost:1337/api/importateurs?populate=*\").then(r => r.json()), fetch(\"http://localhost:1337/api/exportateurs?populate=*\").then(r => r.json())]).then(([importateursData, exportateursData]) => {\n      console.log(\"Importateurs:\", importateursData);\n      console.log(\"Exportateurs:\", exportateursData);\n      if (importateursData !== null && importateursData !== void 0 && importateursData.data) setImportateurs(importateursData.data);\n      if (exportateursData !== null && exportateursData !== void 0 && exportateursData.data) setExportateurs(exportateursData.data);\n      setLoading(false);\n    }).catch(err => {\n      console.error(\"Erreur lors du chargement:\", err);\n      setLoading(false);\n    });\n  }, []);\n\n  // Fonctions utilitaires\n  const isFormValid = () => {\n    return formData.importateur && formData.objet && formData.pays && formData.date_debut && formData.date_fin && formData.secteurs.length > 0;\n  };\n  const handleMatching = async () => {\n    if (!isFormValid()) return;\n\n    // Si \"autre\" est sélectionné, créer d'abord le nouvel importateur\n    if (formData.importateur === \"autre\") {\n      try {\n        const response = await fetch(\"http://localhost:1337/api/importateurs\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            data: newImportateur\n          })\n        });\n        const newImp = await response.json();\n        setFormData({\n          ...formData,\n          importateur: newImp.data.id\n        });\n        setImportateurs([...importateurs, newImp.data]);\n        setShowNewImportateurForm(false);\n      } catch (error) {\n        console.error(\"Erreur lors de la création de l'importateur:\", error);\n        return;\n      }\n    }\n\n    // Filtrer les exportateurs par secteur\n    const matched = exportateurs.filter(exp => {\n      var _exp$attributes;\n      const expSecteur = (_exp$attributes = exp.attributes) === null || _exp$attributes === void 0 ? void 0 : _exp$attributes.secteur_activite;\n      return formData.secteurs.includes(expSecteur);\n    });\n    setMatchedExportateurs(matched);\n    setShowMatching(true);\n  };\n  const handleSendOpportunity = () => {\n    setShowConfirmation(true);\n  };\n  const getImportateurName = () => {\n    var _imp$attributes;\n    if (formData.importateur === \"autre\") {\n      return newImportateur.societe;\n    }\n    const imp = importateurs.find(i => i.id === formData.importateur);\n    return (imp === null || imp === void 0 ? void 0 : (_imp$attributes = imp.attributes) === null || _imp$attributes === void 0 ? void 0 : _imp$attributes.societe) || \"Importateur inconnu\";\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 126,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: \"20px\",\n      maxWidth: \"800px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"\\uD83D\\uDD0D Formulaire Opportunit\\xE9 d'Importation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), !showConfirmation ? /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        style: {\n          marginBottom: \"30px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: \"block\",\n              marginBottom: \"5px\",\n              fontWeight: \"bold\"\n            },\n            children: \"Importateur *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: formData.importateur,\n            onChange: e => {\n              setFormData({\n                ...formData,\n                importateur: e.target.value\n              });\n              setShowNewImportateurForm(e.target.value === \"autre\");\n            },\n            style: {\n              width: \"100%\",\n              padding: \"8px\",\n              marginBottom: \"10px\"\n            },\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"-- S\\xE9lectionner un importateur --\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this), importateurs.map(imp => {\n              var _imp$attributes2, _imp$data, _imp$data$attributes;\n              // Utiliser la même logique flexible que dans les autres composants\n              const societe = (imp === null || imp === void 0 ? void 0 : (_imp$attributes2 = imp.attributes) === null || _imp$attributes2 === void 0 ? void 0 : _imp$attributes2.societe) || (imp === null || imp === void 0 ? void 0 : imp.societe) || (imp === null || imp === void 0 ? void 0 : (_imp$data = imp.data) === null || _imp$data === void 0 ? void 0 : (_imp$data$attributes = _imp$data.attributes) === null || _imp$data$attributes === void 0 ? void 0 : _imp$data$attributes.societe) || 'Nom non disponible';\n              return /*#__PURE__*/_jsxDEV(\"option\", {\n                value: imp.id || imp.documentId,\n                children: societe\n              }, imp.id || imp.documentId, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 21\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"autre\",\n              children: \"\\u2795 Autre (Ajouter un nouveau)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 13\n        }, this), showNewImportateurForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            border: \"2px solid #007bff\",\n            padding: \"15px\",\n            marginBottom: \"20px\",\n            borderRadius: \"5px\",\n            backgroundColor: \"#f8f9fa\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\u2795 Ajouter un nouvel importateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"grid\",\n              gridTemplateColumns: \"1fr 1fr\",\n              gap: \"10px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"Soci\\xE9t\\xE9 *\",\n              value: newImportateur.societe,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                societe: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"Pays *\",\n              value: newImportateur.pays,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                pays: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"Nom responsable *\",\n              value: newImportateur.nom_responsable,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                nom_responsable: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"Pr\\xE9nom responsable *\",\n              value: newImportateur.prenom_responsable,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                prenom_responsable: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"T\\xE9l\\xE9phone WhatsApp *\",\n              value: newImportateur.telephone_whatsapp,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                telephone_whatsapp: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"Email *\",\n              type: \"email\",\n              value: newImportateur.email,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                email: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"R\\xE9gion *\",\n              value: newImportateur.region,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                region: e.target.value\n              }),\n              style: {\n                padding: \"8px\",\n                gridColumn: \"1 / -1\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: \"block\",\n              marginBottom: \"5px\",\n              fontWeight: \"bold\"\n            },\n            children: \"Objet (Produit recherch\\xE9, sp\\xE9cifications...) *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: formData.objet,\n            onChange: e => setFormData({\n              ...formData,\n              objet: e.target.value\n            }),\n            style: {\n              width: \"100%\",\n              padding: \"8px\",\n              minHeight: \"100px\"\n            },\n            placeholder: \"D\\xE9crivez le produit recherch\\xE9, les sp\\xE9cifications techniques, quantit\\xE9s, etc.\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: \"block\",\n              marginBottom: \"5px\",\n              fontWeight: \"bold\"\n            },\n            children: \"Pays de destination *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: formData.pays,\n            onChange: e => setFormData({\n              ...formData,\n              pays: e.target.value\n            }),\n            style: {\n              width: \"100%\",\n              padding: \"8px\"\n            },\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"-- S\\xE9lectionner un pays --\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 17\n            }, this), pays.map(p => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: p,\n              children: p\n            }, p, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"grid\",\n            gridTemplateColumns: \"1fr 1fr\",\n            gap: \"20px\",\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: \"block\",\n                marginBottom: \"5px\",\n                fontWeight: \"bold\"\n              },\n              children: \"Date d\\xE9but *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: formData.date_debut,\n              onChange: e => setFormData({\n                ...formData,\n                date_debut: e.target.value\n              }),\n              style: {\n                width: \"100%\",\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: \"block\",\n                marginBottom: \"5px\",\n                fontWeight: \"bold\"\n              },\n              children: \"Date fin *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: formData.date_fin,\n              onChange: e => setFormData({\n                ...formData,\n                date_fin: e.target.value\n              }),\n              style: {\n                width: \"100%\",\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: \"block\",\n              marginBottom: \"5px\",\n              fontWeight: \"bold\"\n            },\n            children: \"Secteurs d'activit\\xE9 * (s\\xE9lection multiple)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              border: \"1px solid #ccc\",\n              padding: \"10px\",\n              borderRadius: \"4px\",\n              display: \"grid\",\n              gridTemplateColumns: \"repeat(2, 1fr)\",\n              gap: \"10px\"\n            },\n            children: secteurs.map(secteur => /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: \"flex\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: formData.secteurs.includes(secteur),\n                onChange: e => {\n                  if (e.target.checked) {\n                    setFormData({\n                      ...formData,\n                      secteurs: [...formData.secteurs, secteur]\n                    });\n                  } else {\n                    setFormData({\n                      ...formData,\n                      secteurs: formData.secteurs.filter(s => s !== secteur)\n                    });\n                  }\n                },\n                style: {\n                  marginRight: \"8px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 21\n              }, this), secteur]\n            }, secteur, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this), formData.secteurs.length === 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n            style: {\n              color: \"red\"\n            },\n            children: \"Veuillez s\\xE9lectionner au moins un secteur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => handleMatching(),\n          disabled: !isFormValid(),\n          style: {\n            backgroundColor: isFormValid() ? \"#28a745\" : \"#6c757d\",\n            color: \"white\",\n            padding: \"12px 30px\",\n            border: \"none\",\n            borderRadius: \"5px\",\n            fontSize: \"16px\",\n            fontWeight: \"bold\",\n            cursor: isFormValid() ? \"pointer\" : \"not-allowed\",\n            marginRight: \"10px\"\n          },\n          children: \"\\uD83D\\uDD0D MATCHING\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 11\n      }, this), showMatching && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          border: \"2px solid #28a745\",\n          padding: \"20px\",\n          borderRadius: \"5px\",\n          backgroundColor: \"#f8fff9\",\n          marginBottom: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"\\uD83C\\uDFAF Exportateurs correspondants (\", matchedExportateurs.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 15\n        }, this), matchedExportateurs.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"table\", {\n            style: {\n              width: \"100%\",\n              borderCollapse: \"collapse\",\n              marginBottom: \"20px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: {\n                  backgroundColor: \"#e9ecef\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"10px\",\n                    border: \"1px solid #ddd\",\n                    textAlign: \"left\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    onChange: e => {\n                      if (e.target.checked) {\n                        setSelectedExportateurs(matchedExportateurs.map(exp => exp.id));\n                      } else {\n                        setSelectedExportateurs([]);\n                      }\n                    },\n                    style: {\n                      marginRight: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 27\n                  }, this), \"S\\xE9lectionner\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"10px\",\n                    border: \"1px solid #ddd\",\n                    textAlign: \"left\"\n                  },\n                  children: \"Raison Sociale\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"10px\",\n                    border: \"1px solid #ddd\",\n                    textAlign: \"left\"\n                  },\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"10px\",\n                    border: \"1px solid #ddd\",\n                    textAlign: \"left\"\n                  },\n                  children: \"Secteur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"10px\",\n                    border: \"1px solid #ddd\",\n                    textAlign: \"left\"\n                  },\n                  children: \"Ville\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: matchedExportateurs.map(exp => {\n                var _exp$attributes2, _exp$attributes3, _exp$attributes4, _exp$attributes5, _exp$attributes5$vill, _exp$attributes5$vill2, _exp$attributes5$vill3;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: \"10px\",\n                      border: \"1px solid #ddd\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: selectedExportateurs.includes(exp.id),\n                      onChange: e => {\n                        if (e.target.checked) {\n                          setSelectedExportateurs([...selectedExportateurs, exp.id]);\n                        } else {\n                          setSelectedExportateurs(selectedExportateurs.filter(id => id !== exp.id));\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 394,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: \"10px\",\n                      border: \"1px solid #ddd\"\n                    },\n                    children: ((_exp$attributes2 = exp.attributes) === null || _exp$attributes2 === void 0 ? void 0 : _exp$attributes2.raison_sociale) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 407,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: \"10px\",\n                      border: \"1px solid #ddd\"\n                    },\n                    children: ((_exp$attributes3 = exp.attributes) === null || _exp$attributes3 === void 0 ? void 0 : _exp$attributes3.email) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 410,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: \"10px\",\n                      border: \"1px solid #ddd\"\n                    },\n                    children: ((_exp$attributes4 = exp.attributes) === null || _exp$attributes4 === void 0 ? void 0 : _exp$attributes4.secteur_activite) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: \"10px\",\n                      border: \"1px solid #ddd\"\n                    },\n                    children: ((_exp$attributes5 = exp.attributes) === null || _exp$attributes5 === void 0 ? void 0 : (_exp$attributes5$vill = _exp$attributes5.ville) === null || _exp$attributes5$vill === void 0 ? void 0 : (_exp$attributes5$vill2 = _exp$attributes5$vill.data) === null || _exp$attributes5$vill2 === void 0 ? void 0 : (_exp$attributes5$vill3 = _exp$attributes5$vill2.attributes) === null || _exp$attributes5$vill3 === void 0 ? void 0 : _exp$attributes5$vill3.nom) || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 27\n                  }, this)]\n                }, exp.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 25\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSendOpportunity,\n            disabled: selectedExportateurs.length === 0,\n            style: {\n              backgroundColor: selectedExportateurs.length > 0 ? \"#007bff\" : \"#6c757d\",\n              color: \"white\",\n              padding: \"12px 30px\",\n              border: \"none\",\n              borderRadius: \"5px\",\n              fontSize: \"16px\",\n              fontWeight: \"bold\",\n              cursor: selectedExportateurs.length > 0 ? \"pointer\" : \"not-allowed\"\n            },\n            children: [\"\\uD83D\\uDCE7 ENVOYER (\", selectedExportateurs.length, \" s\\xE9lectionn\\xE9\", selectedExportateurs.length > 1 ? 's' : '', \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 17\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: \"#856404\",\n            backgroundColor: \"#fff3cd\",\n            padding: \"10px\",\n            borderRadius: \"4px\"\n          },\n          children: [\"\\u26A0\\uFE0F Aucun exportateur trouv\\xE9 pour les secteurs s\\xE9lectionn\\xE9s : \", formData.secteurs.join(\", \")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 357,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: \"#d4edda\",\n        border: \"2px solid #28a745\",\n        borderRadius: \"10px\",\n        padding: \"30px\",\n        textAlign: \"center\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: \"#155724\",\n          marginBottom: \"20px\"\n        },\n        children: \"\\uD83C\\uDF89 F\\xE9licitations ! Votre opportunit\\xE9 a bien \\xE9t\\xE9 soumise.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: \"white\",\n          padding: \"20px\",\n          borderRadius: \"8px\",\n          textAlign: \"left\",\n          marginBottom: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: \"#155724\",\n            marginBottom: \"15px\"\n          },\n          children: \"\\uD83D\\uDCE7 Un email sera envoy\\xE9 aux exportateurs s\\xE9lectionn\\xE9s avec le contenu suivant :\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: \"#f8f9fa\",\n            padding: \"15px\",\n            borderRadius: \"5px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Objet :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 18\n            }, this), \" Nouvelle Opportunit\\xE9 d'Importation\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Contenu :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 18\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              marginLeft: \"20px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Importateur :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 21\n              }, this), \" \", getImportateurName()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Objet :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 478,\n                columnNumber: 21\n              }, this), \" \", formData.objet]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Pays de destination :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 21\n              }, this), \" \", formData.pays]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"P\\xE9riode :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 21\n              }, this), \" \", formData.date_debut, \" \\u2192 \", formData.date_fin]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Secteurs concern\\xE9s :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 21\n              }, this), \" \", formData.secteurs.join(\", \")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Exportateurs contact\\xE9s :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 18\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              marginLeft: \"20px\"\n            },\n            children: selectedExportateurs.map(expId => {\n              var _exp$attributes6, _exp$attributes7;\n              const exp = matchedExportateurs.find(e => e.id === expId);\n              return /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [exp === null || exp === void 0 ? void 0 : (_exp$attributes6 = exp.attributes) === null || _exp$attributes6 === void 0 ? void 0 : _exp$attributes6.email, \" (\", exp === null || exp === void 0 ? void 0 : (_exp$attributes7 = exp.attributes) === null || _exp$attributes7 === void 0 ? void 0 : _exp$attributes7.raison_sociale, \")\"]\n              }, expId, true, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 461,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          setShowConfirmation(false);\n          setShowMatching(false);\n          setSelectedExportateurs([]);\n          setMatchedExportateurs([]);\n          setFormData({\n            importateur: \"\",\n            objet: \"\",\n            pays: \"\",\n            date_debut: \"\",\n            date_fin: \"\",\n            secteurs: []\n          });\n          setNewImportateur({\n            societe: \"\",\n            pays: \"\",\n            nom_responsable: \"\",\n            prenom_responsable: \"\",\n            telephone_whatsapp: \"\",\n            email: \"\",\n            region: \"\"\n          });\n          setShowNewImportateurForm(false);\n        },\n        style: {\n          backgroundColor: \"#007bff\",\n          color: \"white\",\n          padding: \"12px 30px\",\n          border: \"none\",\n          borderRadius: \"5px\",\n          fontSize: \"16px\",\n          fontWeight: \"bold\",\n          cursor: \"pointer\"\n        },\n        children: \"\\u2728 Nouvelle Opportunit\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n}\n_s(OpportuniteForm, \"LIX3izY56JlFFZuSWIKSCerDm9Q=\");\n_c = OpportuniteForm;\nexport default OpportuniteForm;\nvar _c;\n$RefreshReg$(_c, \"OpportuniteForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "OpportuniteForm", "_s", "importateurs", "setImportateurs", "exportateurs", "setExportateurs", "matchedExportateurs", "setMatchedExportateurs", "selectedExportateurs", "setSelectedExportateurs", "loading", "setLoading", "showNewImportateurForm", "setShowNewImportateurForm", "showMatching", "setShowMatching", "showConfirmation", "setShowConfirmation", "formData", "setFormData", "importateur", "objet", "pays", "date_debut", "date_fin", "secteurs", "newImportateur", "setNewImportateur", "societe", "nom_responsable", "prenom_responsable", "telephone_whatsapp", "email", "region", "Promise", "all", "fetch", "then", "r", "json", "importateursData", "exportateursData", "console", "log", "data", "catch", "err", "error", "isFormValid", "length", "handleMatching", "response", "method", "headers", "body", "JSON", "stringify", "newImp", "id", "matched", "filter", "exp", "_exp$attributes", "expSecteur", "attributes", "secteur_activite", "includes", "handleSendOpportunity", "getImportateurName", "_imp$attributes", "imp", "find", "i", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "max<PERSON><PERSON><PERSON>", "marginBottom", "display", "fontWeight", "value", "onChange", "e", "target", "width", "required", "map", "_imp$attributes2", "_imp$data", "_imp$data$attributes", "documentId", "border", "borderRadius", "backgroundColor", "gridTemplateColumns", "gap", "placeholder", "type", "gridColumn", "minHeight", "p", "secteur", "alignItems", "checked", "s", "marginRight", "color", "onClick", "disabled", "fontSize", "cursor", "borderCollapse", "textAlign", "_exp$attributes2", "_exp$attributes3", "_exp$attributes4", "_exp$attributes5", "_exp$attributes5$vill", "_exp$attributes5$vill2", "_exp$attributes5$vill3", "raison_sociale", "ville", "nom", "join", "marginLeft", "expId", "_exp$attributes6", "_exp$attributes7", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/dfghj/site/frontend/src/components/OpportuniteForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\n\r\nfunction OpportuniteForm() {\r\n  // États pour les données\r\n  const [importateurs, setImportateurs] = useState([]);\r\n  const [exportateurs, setExportateurs] = useState([]);\r\n  const [matchedExportateurs, setMatchedExportateurs] = useState([]);\r\n  const [selectedExportateurs, setSelectedExportateurs] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [showNewImportateurForm, setShowNewImportateurForm] = useState(false);\r\n  const [showMatching, setShowMatching] = useState(false);\r\n  const [showConfirmation, setShowConfirmation] = useState(false);\r\n\r\n  // État du formulaire principal\r\n  const [formData, setFormData] = useState({\r\n    importateur: \"\",\r\n    objet: \"\",\r\n    pays: \"\",\r\n    date_debut: \"\",\r\n    date_fin: \"\",\r\n    secteurs: []\r\n  });\r\n\r\n  // État du formulaire nouvel importateur\r\n  const [newImportateur, setNewImportateur] = useState({\r\n    societe: \"\",\r\n    pays: \"\",\r\n    nom_responsable: \"\",\r\n    prenom_responsable: \"\",\r\n    telephone_whatsapp: \"\",\r\n    email: \"\",\r\n    region: \"\"\r\n  });\r\n\r\n  // Listes des options\r\n  const secteurs = [\r\n    \"Agro-alimentaire\",\r\n    \"Textile\",\r\n    \"IME\",\r\n    \"Service\",\r\n    \"Artisanat\",\r\n    \"Divers\"\r\n  ];\r\n\r\n  const pays = [\r\n    \"France\", \"Allemagne\", \"Italie\", \"Espagne\", \"Belgique\", \"Pays-Bas\",\r\n    \"Royaume-Uni\", \"Suisse\", \"Canada\", \"États-Unis\", \"Maroc\", \"Algérie\",\r\n    \"Libye\", \"Égypte\", \"Arabie Saoudite\", \"Émirats Arabes Unis\", \"Qatar\",\r\n    \"Koweït\", \"Turquie\", \"Chine\", \"Japon\", \"Corée du Sud\", \"Inde\", \"Autre\"\r\n  ];\r\n\r\n  // Charger les importateurs au démarrage\r\n  useEffect(() => {\r\n    Promise.all([\r\n      fetch(\"http://localhost:1337/api/importateurs?populate=*\").then(r => r.json()),\r\n      fetch(\"http://localhost:1337/api/exportateurs?populate=*\").then(r => r.json())\r\n    ])\r\n    .then(([importateursData, exportateursData]) => {\r\n      console.log(\"Importateurs:\", importateursData);\r\n      console.log(\"Exportateurs:\", exportateursData);\r\n\r\n      if (importateursData?.data) setImportateurs(importateursData.data);\r\n      if (exportateursData?.data) setExportateurs(exportateursData.data);\r\n\r\n      setLoading(false);\r\n    })\r\n    .catch(err => {\r\n      console.error(\"Erreur lors du chargement:\", err);\r\n      setLoading(false);\r\n    });\r\n  }, []);\r\n\r\n  // Fonctions utilitaires\r\n  const isFormValid = () => {\r\n    return formData.importateur &&\r\n           formData.objet &&\r\n           formData.pays &&\r\n           formData.date_debut &&\r\n           formData.date_fin &&\r\n           formData.secteurs.length > 0;\r\n  };\r\n\r\n  const handleMatching = async () => {\r\n    if (!isFormValid()) return;\r\n\r\n    // Si \"autre\" est sélectionné, créer d'abord le nouvel importateur\r\n    if (formData.importateur === \"autre\") {\r\n      try {\r\n        const response = await fetch(\"http://localhost:1337/api/importateurs\", {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({ data: newImportateur })\r\n        });\r\n        const newImp = await response.json();\r\n        setFormData({...formData, importateur: newImp.data.id});\r\n        setImportateurs([...importateurs, newImp.data]);\r\n        setShowNewImportateurForm(false);\r\n      } catch (error) {\r\n        console.error(\"Erreur lors de la création de l'importateur:\", error);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Filtrer les exportateurs par secteur\r\n    const matched = exportateurs.filter(exp => {\r\n      const expSecteur = exp.attributes?.secteur_activite;\r\n      return formData.secteurs.includes(expSecteur);\r\n    });\r\n\r\n    setMatchedExportateurs(matched);\r\n    setShowMatching(true);\r\n  };\r\n\r\n  const handleSendOpportunity = () => {\r\n    setShowConfirmation(true);\r\n  };\r\n\r\n  const getImportateurName = () => {\r\n    if (formData.importateur === \"autre\") {\r\n      return newImportateur.societe;\r\n    }\r\n    const imp = importateurs.find(i => i.id === formData.importateur);\r\n    return imp?.attributes?.societe || \"Importateur inconnu\";\r\n  };\r\n\r\n  if (loading) return <div>Chargement...</div>;\r\n\r\n  return (\r\n    <div style={{ padding: \"20px\", maxWidth: \"800px\" }}>\r\n      <h2>🔍 Formulaire Opportunité d'Importation</h2>\r\n\r\n      {!showConfirmation ? (\r\n        <div>\r\n          {/* Formulaire principal */}\r\n          <form style={{ marginBottom: \"30px\" }}>\r\n            {/* Sélection Importateur */}\r\n            <div style={{ marginBottom: \"20px\" }}>\r\n              <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                Importateur *\r\n              </label>\r\n              <select\r\n                value={formData.importateur}\r\n                onChange={(e) => {\r\n                  setFormData({...formData, importateur: e.target.value});\r\n                  setShowNewImportateurForm(e.target.value === \"autre\");\r\n                }}\r\n                style={{ width: \"100%\", padding: \"8px\", marginBottom: \"10px\" }}\r\n                required\r\n              >\r\n                <option value=\"\">-- Sélectionner un importateur --</option>\r\n                {importateurs.map(imp => {\r\n                  // Utiliser la même logique flexible que dans les autres composants\r\n                  const societe = imp?.attributes?.societe ||\r\n                                 imp?.societe ||\r\n                                 imp?.data?.attributes?.societe ||\r\n                                 'Nom non disponible';\r\n                  return (\r\n                    <option key={imp.id || imp.documentId} value={imp.id || imp.documentId}>\r\n                      {societe}\r\n                    </option>\r\n                  );\r\n                })}\r\n                <option value=\"autre\">➕ Autre (Ajouter un nouveau)</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Formulaire nouvel importateur (conditionnel) */}\r\n            {showNewImportateurForm && (\r\n              <div style={{\r\n                border: \"2px solid #007bff\",\r\n                padding: \"15px\",\r\n                marginBottom: \"20px\",\r\n                borderRadius: \"5px\",\r\n                backgroundColor: \"#f8f9fa\"\r\n              }}>\r\n                <h4>➕ Ajouter un nouvel importateur</h4>\r\n                <div style={{ display: \"grid\", gridTemplateColumns: \"1fr 1fr\", gap: \"10px\" }}>\r\n                  <input\r\n                    placeholder=\"Société *\"\r\n                    value={newImportateur.societe}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, societe: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Pays *\"\r\n                    value={newImportateur.pays}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, pays: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Nom responsable *\"\r\n                    value={newImportateur.nom_responsable}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, nom_responsable: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Prénom responsable *\"\r\n                    value={newImportateur.prenom_responsable}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, prenom_responsable: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Téléphone WhatsApp *\"\r\n                    value={newImportateur.telephone_whatsapp}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, telephone_whatsapp: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Email *\"\r\n                    type=\"email\"\r\n                    value={newImportateur.email}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, email: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Région *\"\r\n                    value={newImportateur.region}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, region: e.target.value})}\r\n                    style={{ padding: \"8px\", gridColumn: \"1 / -1\" }}\r\n                    required\r\n                  />\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Objet */}\r\n            <div style={{ marginBottom: \"20px\" }}>\r\n              <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                Objet (Produit recherché, spécifications...) *\r\n              </label>\r\n              <textarea\r\n                value={formData.objet}\r\n                onChange={(e) => setFormData({...formData, objet: e.target.value})}\r\n                style={{ width: \"100%\", padding: \"8px\", minHeight: \"100px\" }}\r\n                placeholder=\"Décrivez le produit recherché, les spécifications techniques, quantités, etc.\"\r\n                required\r\n              />\r\n            </div>\r\n\r\n            {/* Pays de destination */}\r\n            <div style={{ marginBottom: \"20px\" }}>\r\n              <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                Pays de destination *\r\n              </label>\r\n              <select\r\n                value={formData.pays}\r\n                onChange={(e) => setFormData({...formData, pays: e.target.value})}\r\n                style={{ width: \"100%\", padding: \"8px\" }}\r\n                required\r\n              >\r\n                <option value=\"\">-- Sélectionner un pays --</option>\r\n                {pays.map(p => (\r\n                  <option key={p} value={p}>{p}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n\r\n            {/* Dates */}\r\n            <div style={{ display: \"grid\", gridTemplateColumns: \"1fr 1fr\", gap: \"20px\", marginBottom: \"20px\" }}>\r\n              <div>\r\n                <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                  Date début *\r\n                </label>\r\n                <input\r\n                  type=\"date\"\r\n                  value={formData.date_debut}\r\n                  onChange={(e) => setFormData({...formData, date_debut: e.target.value})}\r\n                  style={{ width: \"100%\", padding: \"8px\" }}\r\n                  required\r\n                />\r\n              </div>\r\n              <div>\r\n                <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                  Date fin *\r\n                </label>\r\n                <input\r\n                  type=\"date\"\r\n                  value={formData.date_fin}\r\n                  onChange={(e) => setFormData({...formData, date_fin: e.target.value})}\r\n                  style={{ width: \"100%\", padding: \"8px\" }}\r\n                  required\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Secteurs (sélection multiple) */}\r\n            <div style={{ marginBottom: \"20px\" }}>\r\n              <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                Secteurs d'activité * (sélection multiple)\r\n              </label>\r\n              <div style={{\r\n                border: \"1px solid #ccc\",\r\n                padding: \"10px\",\r\n                borderRadius: \"4px\",\r\n                display: \"grid\",\r\n                gridTemplateColumns: \"repeat(2, 1fr)\",\r\n                gap: \"10px\"\r\n              }}>\r\n                {secteurs.map(secteur => (\r\n                  <label key={secteur} style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={formData.secteurs.includes(secteur)}\r\n                      onChange={(e) => {\r\n                        if (e.target.checked) {\r\n                          setFormData({\r\n                            ...formData,\r\n                            secteurs: [...formData.secteurs, secteur]\r\n                          });\r\n                        } else {\r\n                          setFormData({\r\n                            ...formData,\r\n                            secteurs: formData.secteurs.filter(s => s !== secteur)\r\n                          });\r\n                        }\r\n                      }}\r\n                      style={{ marginRight: \"8px\" }}\r\n                    />\r\n                    {secteur}\r\n                  </label>\r\n                ))}\r\n              </div>\r\n              {formData.secteurs.length === 0 && (\r\n                <small style={{ color: \"red\" }}>Veuillez sélectionner au moins un secteur</small>\r\n              )}\r\n            </div>\r\n\r\n            {/* Bouton MATCHING */}\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => handleMatching()}\r\n              disabled={!isFormValid()}\r\n              style={{\r\n                backgroundColor: isFormValid() ? \"#28a745\" : \"#6c757d\",\r\n                color: \"white\",\r\n                padding: \"12px 30px\",\r\n                border: \"none\",\r\n                borderRadius: \"5px\",\r\n                fontSize: \"16px\",\r\n                fontWeight: \"bold\",\r\n                cursor: isFormValid() ? \"pointer\" : \"not-allowed\",\r\n                marginRight: \"10px\"\r\n              }}\r\n            >\r\n              🔍 MATCHING\r\n            </button>\r\n          </form>\r\n\r\n          {/* Section Matching des Exportateurs */}\r\n          {showMatching && (\r\n            <div style={{\r\n              border: \"2px solid #28a745\",\r\n              padding: \"20px\",\r\n              borderRadius: \"5px\",\r\n              backgroundColor: \"#f8fff9\",\r\n              marginBottom: \"20px\"\r\n            }}>\r\n              <h3>🎯 Exportateurs correspondants ({matchedExportateurs.length})</h3>\r\n\r\n              {matchedExportateurs.length > 0 ? (\r\n                <div>\r\n                  <table style={{ width: \"100%\", borderCollapse: \"collapse\", marginBottom: \"20px\" }}>\r\n                    <thead>\r\n                      <tr style={{ backgroundColor: \"#e9ecef\" }}>\r\n                        <th style={{ padding: \"10px\", border: \"1px solid #ddd\", textAlign: \"left\" }}>\r\n                          <input\r\n                            type=\"checkbox\"\r\n                            onChange={(e) => {\r\n                              if (e.target.checked) {\r\n                                setSelectedExportateurs(matchedExportateurs.map(exp => exp.id));\r\n                              } else {\r\n                                setSelectedExportateurs([]);\r\n                              }\r\n                            }}\r\n                            style={{ marginRight: \"5px\" }}\r\n                          />\r\n                          Sélectionner\r\n                        </th>\r\n                        <th style={{ padding: \"10px\", border: \"1px solid #ddd\", textAlign: \"left\" }}>Raison Sociale</th>\r\n                        <th style={{ padding: \"10px\", border: \"1px solid #ddd\", textAlign: \"left\" }}>Email</th>\r\n                        <th style={{ padding: \"10px\", border: \"1px solid #ddd\", textAlign: \"left\" }}>Secteur</th>\r\n                        <th style={{ padding: \"10px\", border: \"1px solid #ddd\", textAlign: \"left\" }}>Ville</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      {matchedExportateurs.map(exp => (\r\n                        <tr key={exp.id}>\r\n                          <td style={{ padding: \"10px\", border: \"1px solid #ddd\" }}>\r\n                            <input\r\n                              type=\"checkbox\"\r\n                              checked={selectedExportateurs.includes(exp.id)}\r\n                              onChange={(e) => {\r\n                                if (e.target.checked) {\r\n                                  setSelectedExportateurs([...selectedExportateurs, exp.id]);\r\n                                } else {\r\n                                  setSelectedExportateurs(selectedExportateurs.filter(id => id !== exp.id));\r\n                                }\r\n                              }}\r\n                            />\r\n                          </td>\r\n                          <td style={{ padding: \"10px\", border: \"1px solid #ddd\" }}>\r\n                            {exp.attributes?.raison_sociale || 'N/A'}\r\n                          </td>\r\n                          <td style={{ padding: \"10px\", border: \"1px solid #ddd\" }}>\r\n                            {exp.attributes?.email || 'N/A'}\r\n                          </td>\r\n                          <td style={{ padding: \"10px\", border: \"1px solid #ddd\" }}>\r\n                            {exp.attributes?.secteur_activite || 'N/A'}\r\n                          </td>\r\n                          <td style={{ padding: \"10px\", border: \"1px solid #ddd\" }}>\r\n                            {exp.attributes?.ville?.data?.attributes?.nom || 'N/A'}\r\n                          </td>\r\n                        </tr>\r\n                      ))}\r\n                    </tbody>\r\n                  </table>\r\n\r\n                  <button\r\n                    onClick={handleSendOpportunity}\r\n                    disabled={selectedExportateurs.length === 0}\r\n                    style={{\r\n                      backgroundColor: selectedExportateurs.length > 0 ? \"#007bff\" : \"#6c757d\",\r\n                      color: \"white\",\r\n                      padding: \"12px 30px\",\r\n                      border: \"none\",\r\n                      borderRadius: \"5px\",\r\n                      fontSize: \"16px\",\r\n                      fontWeight: \"bold\",\r\n                      cursor: selectedExportateurs.length > 0 ? \"pointer\" : \"not-allowed\"\r\n                    }}\r\n                  >\r\n                    📧 ENVOYER ({selectedExportateurs.length} sélectionné{selectedExportateurs.length > 1 ? 's' : ''})\r\n                  </button>\r\n                </div>\r\n              ) : (\r\n                <p style={{ color: \"#856404\", backgroundColor: \"#fff3cd\", padding: \"10px\", borderRadius: \"4px\" }}>\r\n                  ⚠️ Aucun exportateur trouvé pour les secteurs sélectionnés : {formData.secteurs.join(\", \")}\r\n                </p>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      ) : (\r\n        <div style={{\r\n          backgroundColor: \"#d4edda\",\r\n          border: \"2px solid #28a745\",\r\n          borderRadius: \"10px\",\r\n          padding: \"30px\",\r\n          textAlign: \"center\"\r\n        }}>\r\n          <h2 style={{ color: \"#155724\", marginBottom: \"20px\" }}>\r\n            🎉 Félicitations ! Votre opportunité a bien été soumise.\r\n          </h2>\r\n\r\n          <div style={{\r\n            backgroundColor: \"white\",\r\n            padding: \"20px\",\r\n            borderRadius: \"8px\",\r\n            textAlign: \"left\",\r\n            marginBottom: \"20px\"\r\n          }}>\r\n            <h3 style={{ color: \"#155724\", marginBottom: \"15px\" }}>\r\n              📧 Un email sera envoyé aux exportateurs sélectionnés avec le contenu suivant :\r\n            </h3>\r\n\r\n            <div style={{ backgroundColor: \"#f8f9fa\", padding: \"15px\", borderRadius: \"5px\" }}>\r\n              <p><strong>Objet :</strong> Nouvelle Opportunité d'Importation</p>\r\n              <br />\r\n              <p><strong>Contenu :</strong></p>\r\n              <ul style={{ marginLeft: \"20px\" }}>\r\n                <li><strong>Importateur :</strong> {getImportateurName()}</li>\r\n                <li><strong>Objet :</strong> {formData.objet}</li>\r\n                <li><strong>Pays de destination :</strong> {formData.pays}</li>\r\n                <li><strong>Période :</strong> {formData.date_debut} → {formData.date_fin}</li>\r\n                <li><strong>Secteurs concernés :</strong> {formData.secteurs.join(\", \")}</li>\r\n              </ul>\r\n              <br />\r\n              <p><strong>Exportateurs contactés :</strong></p>\r\n              <ul style={{ marginLeft: \"20px\" }}>\r\n                {selectedExportateurs.map(expId => {\r\n                  const exp = matchedExportateurs.find(e => e.id === expId);\r\n                  return (\r\n                    <li key={expId}>\r\n                      {exp?.attributes?.email} ({exp?.attributes?.raison_sociale})\r\n                    </li>\r\n                  );\r\n                })}\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <button\r\n            onClick={() => {\r\n              setShowConfirmation(false);\r\n              setShowMatching(false);\r\n              setSelectedExportateurs([]);\r\n              setMatchedExportateurs([]);\r\n              setFormData({\r\n                importateur: \"\",\r\n                objet: \"\",\r\n                pays: \"\",\r\n                date_debut: \"\",\r\n                date_fin: \"\",\r\n                secteurs: []\r\n              });\r\n              setNewImportateur({\r\n                societe: \"\",\r\n                pays: \"\",\r\n                nom_responsable: \"\",\r\n                prenom_responsable: \"\",\r\n                telephone_whatsapp: \"\",\r\n                email: \"\",\r\n                region: \"\"\r\n              });\r\n              setShowNewImportateurForm(false);\r\n            }}\r\n            style={{\r\n              backgroundColor: \"#007bff\",\r\n              color: \"white\",\r\n              padding: \"12px 30px\",\r\n              border: \"none\",\r\n              borderRadius: \"5px\",\r\n              fontSize: \"16px\",\r\n              fontWeight: \"bold\",\r\n              cursor: \"pointer\"\r\n            }}\r\n          >\r\n            ✨ Nouvelle Opportunité\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default OpportuniteForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACU,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACY,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC;IACvCwB,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC;IACnDgC,OAAO,EAAE,EAAE;IACXN,IAAI,EAAE,EAAE;IACRO,eAAe,EAAE,EAAE;IACnBC,kBAAkB,EAAE,EAAE;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMR,QAAQ,GAAG,CACf,kBAAkB,EAClB,SAAS,EACT,KAAK,EACL,SAAS,EACT,WAAW,EACX,QAAQ,CACT;EAED,MAAMH,IAAI,GAAG,CACX,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAClE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EACnE,OAAO,EAAE,QAAQ,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,OAAO,EACpE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,CACvE;;EAED;EACAzB,SAAS,CAAC,MAAM;IACdqC,OAAO,CAACC,GAAG,CAAC,CACVC,KAAK,CAAC,mDAAmD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,EAC9EH,KAAK,CAAC,mDAAmD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAC/E,CAAC,CACDF,IAAI,CAAC,CAAC,CAACG,gBAAgB,EAAEC,gBAAgB,CAAC,KAAK;MAC9CC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEH,gBAAgB,CAAC;MAC9CE,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEF,gBAAgB,CAAC;MAE9C,IAAID,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEI,IAAI,EAAEzC,eAAe,CAACqC,gBAAgB,CAACI,IAAI,CAAC;MAClE,IAAIH,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEG,IAAI,EAAEvC,eAAe,CAACoC,gBAAgB,CAACG,IAAI,CAAC;MAElEjC,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDkC,KAAK,CAACC,GAAG,IAAI;MACZJ,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAED,GAAG,CAAC;MAChDnC,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqC,WAAW,GAAGA,CAAA,KAAM;IACxB,OAAO9B,QAAQ,CAACE,WAAW,IACpBF,QAAQ,CAACG,KAAK,IACdH,QAAQ,CAACI,IAAI,IACbJ,QAAQ,CAACK,UAAU,IACnBL,QAAQ,CAACM,QAAQ,IACjBN,QAAQ,CAACO,QAAQ,CAACwB,MAAM,GAAG,CAAC;EACrC,CAAC;EAED,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACF,WAAW,CAAC,CAAC,EAAE;;IAEpB;IACA,IAAI9B,QAAQ,CAACE,WAAW,KAAK,OAAO,EAAE;MACpC,IAAI;QACF,MAAM+B,QAAQ,GAAG,MAAMf,KAAK,CAAC,wCAAwC,EAAE;UACrEgB,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEZ,IAAI,EAAElB;UAAe,CAAC;QAC/C,CAAC,CAAC;QACF,MAAM+B,MAAM,GAAG,MAAMN,QAAQ,CAACZ,IAAI,CAAC,CAAC;QACpCpB,WAAW,CAAC;UAAC,GAAGD,QAAQ;UAAEE,WAAW,EAAEqC,MAAM,CAACb,IAAI,CAACc;QAAE,CAAC,CAAC;QACvDvD,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAEuD,MAAM,CAACb,IAAI,CAAC,CAAC;QAC/C/B,yBAAyB,CAAC,KAAK,CAAC;MAClC,CAAC,CAAC,OAAOkC,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;MACF;IACF;;IAEA;IACA,MAAMY,OAAO,GAAGvD,YAAY,CAACwD,MAAM,CAACC,GAAG,IAAI;MAAA,IAAAC,eAAA;MACzC,MAAMC,UAAU,IAAAD,eAAA,GAAGD,GAAG,CAACG,UAAU,cAAAF,eAAA,uBAAdA,eAAA,CAAgBG,gBAAgB;MACnD,OAAO/C,QAAQ,CAACO,QAAQ,CAACyC,QAAQ,CAACH,UAAU,CAAC;IAC/C,CAAC,CAAC;IAEFxD,sBAAsB,CAACoD,OAAO,CAAC;IAC/B5C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMoD,qBAAqB,GAAGA,CAAA,KAAM;IAClClD,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMmD,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,eAAA;IAC/B,IAAInD,QAAQ,CAACE,WAAW,KAAK,OAAO,EAAE;MACpC,OAAOM,cAAc,CAACE,OAAO;IAC/B;IACA,MAAM0C,GAAG,GAAGpE,YAAY,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACd,EAAE,KAAKxC,QAAQ,CAACE,WAAW,CAAC;IACjE,OAAO,CAAAkD,GAAG,aAAHA,GAAG,wBAAAD,eAAA,GAAHC,GAAG,CAAEN,UAAU,cAAAK,eAAA,uBAAfA,eAAA,CAAiBzC,OAAO,KAAI,qBAAqB;EAC1D,CAAC;EAED,IAAIlB,OAAO,EAAE,oBAAOX,OAAA;IAAA0E,QAAA,EAAK;EAAa;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAE5C,oBACE9E,OAAA;IAAK+E,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAQ,CAAE;IAAAP,QAAA,gBACjD1E,OAAA;MAAA0E,QAAA,EAAI;IAAuC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE/C,CAAC7D,gBAAgB,gBAChBjB,OAAA;MAAA0E,QAAA,gBAEE1E,OAAA;QAAM+E,KAAK,EAAE;UAAEG,YAAY,EAAE;QAAO,CAAE;QAAAR,QAAA,gBAEpC1E,OAAA;UAAK+E,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACnC1E,OAAA;YAAO+E,KAAK,EAAE;cAAEI,OAAO,EAAE,OAAO;cAAED,YAAY,EAAE,KAAK;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAE7E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9E,OAAA;YACEqF,KAAK,EAAElE,QAAQ,CAACE,WAAY;YAC5BiE,QAAQ,EAAGC,CAAC,IAAK;cACfnE,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEE,WAAW,EAAEkE,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAC;cACvDvE,yBAAyB,CAACyE,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,OAAO,CAAC;YACvD,CAAE;YACFN,KAAK,EAAE;cAAEU,KAAK,EAAE,MAAM;cAAET,OAAO,EAAE,KAAK;cAAEE,YAAY,EAAE;YAAO,CAAE;YAC/DQ,QAAQ;YAAAhB,QAAA,gBAER1E,OAAA;cAAQqF,KAAK,EAAC,EAAE;cAAAX,QAAA,EAAC;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC1D3E,YAAY,CAACwF,GAAG,CAACpB,GAAG,IAAI;cAAA,IAAAqB,gBAAA,EAAAC,SAAA,EAAAC,oBAAA;cACvB;cACA,MAAMjE,OAAO,GAAG,CAAA0C,GAAG,aAAHA,GAAG,wBAAAqB,gBAAA,GAAHrB,GAAG,CAAEN,UAAU,cAAA2B,gBAAA,uBAAfA,gBAAA,CAAiB/D,OAAO,MACzB0C,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE1C,OAAO,MACZ0C,GAAG,aAAHA,GAAG,wBAAAsB,SAAA,GAAHtB,GAAG,CAAE1B,IAAI,cAAAgD,SAAA,wBAAAC,oBAAA,GAATD,SAAA,CAAW5B,UAAU,cAAA6B,oBAAA,uBAArBA,oBAAA,CAAuBjE,OAAO,KAC9B,oBAAoB;cACnC,oBACE7B,OAAA;gBAAuCqF,KAAK,EAAEd,GAAG,CAACZ,EAAE,IAAIY,GAAG,CAACwB,UAAW;gBAAArB,QAAA,EACpE7C;cAAO,GADG0C,GAAG,CAACZ,EAAE,IAAIY,GAAG,CAACwB,UAAU;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE7B,CAAC;YAEb,CAAC,CAAC,eACF9E,OAAA;cAAQqF,KAAK,EAAC,OAAO;cAAAX,QAAA,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLjE,sBAAsB,iBACrBb,OAAA;UAAK+E,KAAK,EAAE;YACViB,MAAM,EAAE,mBAAmB;YAC3BhB,OAAO,EAAE,MAAM;YACfE,YAAY,EAAE,MAAM;YACpBe,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE;UACnB,CAAE;UAAAxB,QAAA,gBACA1E,OAAA;YAAA0E,QAAA,EAAI;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxC9E,OAAA;YAAK+E,KAAK,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEgB,mBAAmB,EAAE,SAAS;cAAEC,GAAG,EAAE;YAAO,CAAE;YAAA1B,QAAA,gBAC3E1E,OAAA;cACEqG,WAAW,EAAC,iBAAW;cACvBhB,KAAK,EAAE1D,cAAc,CAACE,OAAQ;cAC9ByD,QAAQ,EAAGC,CAAC,IAAK3D,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEE,OAAO,EAAE0D,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACjFN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF9E,OAAA;cACEqG,WAAW,EAAC,QAAQ;cACpBhB,KAAK,EAAE1D,cAAc,CAACJ,IAAK;cAC3B+D,QAAQ,EAAGC,CAAC,IAAK3D,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEJ,IAAI,EAAEgE,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC9EN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF9E,OAAA;cACEqG,WAAW,EAAC,mBAAmB;cAC/BhB,KAAK,EAAE1D,cAAc,CAACG,eAAgB;cACtCwD,QAAQ,EAAGC,CAAC,IAAK3D,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEG,eAAe,EAAEyD,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACzFN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF9E,OAAA;cACEqG,WAAW,EAAC,yBAAsB;cAClChB,KAAK,EAAE1D,cAAc,CAACI,kBAAmB;cACzCuD,QAAQ,EAAGC,CAAC,IAAK3D,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEI,kBAAkB,EAAEwD,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC5FN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF9E,OAAA;cACEqG,WAAW,EAAC,4BAAsB;cAClChB,KAAK,EAAE1D,cAAc,CAACK,kBAAmB;cACzCsD,QAAQ,EAAGC,CAAC,IAAK3D,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEK,kBAAkB,EAAEuD,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC5FN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF9E,OAAA;cACEqG,WAAW,EAAC,SAAS;cACrBC,IAAI,EAAC,OAAO;cACZjB,KAAK,EAAE1D,cAAc,CAACM,KAAM;cAC5BqD,QAAQ,EAAGC,CAAC,IAAK3D,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEM,KAAK,EAAEsD,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC/EN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACF9E,OAAA;cACEqG,WAAW,EAAC,aAAU;cACtBhB,KAAK,EAAE1D,cAAc,CAACO,MAAO;cAC7BoD,QAAQ,EAAGC,CAAC,IAAK3D,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEO,MAAM,EAAEqD,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAChFN,KAAK,EAAE;gBAAEC,OAAO,EAAE,KAAK;gBAAEuB,UAAU,EAAE;cAAS,CAAE;cAChDb,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD9E,OAAA;UAAK+E,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACnC1E,OAAA;YAAO+E,KAAK,EAAE;cAAEI,OAAO,EAAE,OAAO;cAAED,YAAY,EAAE,KAAK;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAE7E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9E,OAAA;YACEqF,KAAK,EAAElE,QAAQ,CAACG,KAAM;YACtBgE,QAAQ,EAAGC,CAAC,IAAKnE,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEG,KAAK,EAAEiE,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACnEN,KAAK,EAAE;cAAEU,KAAK,EAAE,MAAM;cAAET,OAAO,EAAE,KAAK;cAAEwB,SAAS,EAAE;YAAQ,CAAE;YAC7DH,WAAW,EAAC,2FAA+E;YAC3FX,QAAQ;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN9E,OAAA;UAAK+E,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACnC1E,OAAA;YAAO+E,KAAK,EAAE;cAAEI,OAAO,EAAE,OAAO;cAAED,YAAY,EAAE,KAAK;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAE7E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9E,OAAA;YACEqF,KAAK,EAAElE,QAAQ,CAACI,IAAK;YACrB+D,QAAQ,EAAGC,CAAC,IAAKnE,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEI,IAAI,EAAEgE,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YAClEN,KAAK,EAAE;cAAEU,KAAK,EAAE,MAAM;cAAET,OAAO,EAAE;YAAM,CAAE;YACzCU,QAAQ;YAAAhB,QAAA,gBAER1E,OAAA;cAAQqF,KAAK,EAAC,EAAE;cAAAX,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACnDvD,IAAI,CAACoE,GAAG,CAACc,CAAC,iBACTzG,OAAA;cAAgBqF,KAAK,EAAEoB,CAAE;cAAA/B,QAAA,EAAE+B;YAAC,GAAfA,CAAC;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuB,CACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN9E,OAAA;UAAK+E,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEgB,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAElB,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACjG1E,OAAA;YAAA0E,QAAA,gBACE1E,OAAA;cAAO+E,KAAK,EAAE;gBAAEI,OAAO,EAAE,OAAO;gBAAED,YAAY,EAAE,KAAK;gBAAEE,UAAU,EAAE;cAAO,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9E,OAAA;cACEsG,IAAI,EAAC,MAAM;cACXjB,KAAK,EAAElE,QAAQ,CAACK,UAAW;cAC3B8D,QAAQ,EAAGC,CAAC,IAAKnE,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEK,UAAU,EAAE+D,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACxEN,KAAK,EAAE;gBAAEU,KAAK,EAAE,MAAM;gBAAET,OAAO,EAAE;cAAM,CAAE;cACzCU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN9E,OAAA;YAAA0E,QAAA,gBACE1E,OAAA;cAAO+E,KAAK,EAAE;gBAAEI,OAAO,EAAE,OAAO;gBAAED,YAAY,EAAE,KAAK;gBAAEE,UAAU,EAAE;cAAO,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9E,OAAA;cACEsG,IAAI,EAAC,MAAM;cACXjB,KAAK,EAAElE,QAAQ,CAACM,QAAS;cACzB6D,QAAQ,EAAGC,CAAC,IAAKnE,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEM,QAAQ,EAAE8D,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACtEN,KAAK,EAAE;gBAAEU,KAAK,EAAE,MAAM;gBAAET,OAAO,EAAE;cAAM,CAAE;cACzCU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9E,OAAA;UAAK+E,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACnC1E,OAAA;YAAO+E,KAAK,EAAE;cAAEI,OAAO,EAAE,OAAO;cAAED,YAAY,EAAE,KAAK;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAE7E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACR9E,OAAA;YAAK+E,KAAK,EAAE;cACViB,MAAM,EAAE,gBAAgB;cACxBhB,OAAO,EAAE,MAAM;cACfiB,YAAY,EAAE,KAAK;cACnBd,OAAO,EAAE,MAAM;cACfgB,mBAAmB,EAAE,gBAAgB;cACrCC,GAAG,EAAE;YACP,CAAE;YAAA1B,QAAA,EACChD,QAAQ,CAACiE,GAAG,CAACe,OAAO,iBACnB1G,OAAA;cAAqB+E,KAAK,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEwB,UAAU,EAAE;cAAS,CAAE;cAAAjC,QAAA,gBACpE1E,OAAA;gBACEsG,IAAI,EAAC,UAAU;gBACfM,OAAO,EAAEzF,QAAQ,CAACO,QAAQ,CAACyC,QAAQ,CAACuC,OAAO,CAAE;gBAC7CpB,QAAQ,EAAGC,CAAC,IAAK;kBACf,IAAIA,CAAC,CAACC,MAAM,CAACoB,OAAO,EAAE;oBACpBxF,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXO,QAAQ,EAAE,CAAC,GAAGP,QAAQ,CAACO,QAAQ,EAAEgF,OAAO;oBAC1C,CAAC,CAAC;kBACJ,CAAC,MAAM;oBACLtF,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXO,QAAQ,EAAEP,QAAQ,CAACO,QAAQ,CAACmC,MAAM,CAACgD,CAAC,IAAIA,CAAC,KAAKH,OAAO;oBACvD,CAAC,CAAC;kBACJ;gBACF,CAAE;gBACF3B,KAAK,EAAE;kBAAE+B,WAAW,EAAE;gBAAM;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,EACD4B,OAAO;YAAA,GAnBEA,OAAO;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBZ,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACL3D,QAAQ,CAACO,QAAQ,CAACwB,MAAM,KAAK,CAAC,iBAC7BlD,OAAA;YAAO+E,KAAK,EAAE;cAAEgC,KAAK,EAAE;YAAM,CAAE;YAAArC,QAAA,EAAC;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACjF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN9E,OAAA;UACEsG,IAAI,EAAC,QAAQ;UACbU,OAAO,EAAEA,CAAA,KAAM7D,cAAc,CAAC,CAAE;UAChC8D,QAAQ,EAAE,CAAChE,WAAW,CAAC,CAAE;UACzB8B,KAAK,EAAE;YACLmB,eAAe,EAAEjD,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS;YACtD8D,KAAK,EAAE,OAAO;YACd/B,OAAO,EAAE,WAAW;YACpBgB,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBiB,QAAQ,EAAE,MAAM;YAChB9B,UAAU,EAAE,MAAM;YAClB+B,MAAM,EAAElE,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,aAAa;YACjD6D,WAAW,EAAE;UACf,CAAE;UAAApC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGN/D,YAAY,iBACXf,OAAA;QAAK+E,KAAK,EAAE;UACViB,MAAM,EAAE,mBAAmB;UAC3BhB,OAAO,EAAE,MAAM;UACfiB,YAAY,EAAE,KAAK;UACnBC,eAAe,EAAE,SAAS;UAC1BhB,YAAY,EAAE;QAChB,CAAE;QAAAR,QAAA,gBACA1E,OAAA;UAAA0E,QAAA,GAAI,4CAAgC,EAACnE,mBAAmB,CAAC2C,MAAM,EAAC,GAAC;QAAA;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAErEvE,mBAAmB,CAAC2C,MAAM,GAAG,CAAC,gBAC7BlD,OAAA;UAAA0E,QAAA,gBACE1E,OAAA;YAAO+E,KAAK,EAAE;cAAEU,KAAK,EAAE,MAAM;cAAE2B,cAAc,EAAE,UAAU;cAAElC,YAAY,EAAE;YAAO,CAAE;YAAAR,QAAA,gBAChF1E,OAAA;cAAA0E,QAAA,eACE1E,OAAA;gBAAI+E,KAAK,EAAE;kBAAEmB,eAAe,EAAE;gBAAU,CAAE;gBAAAxB,QAAA,gBACxC1E,OAAA;kBAAI+E,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEgB,MAAM,EAAE,gBAAgB;oBAAEqB,SAAS,EAAE;kBAAO,CAAE;kBAAA3C,QAAA,gBAC1E1E,OAAA;oBACEsG,IAAI,EAAC,UAAU;oBACfhB,QAAQ,EAAGC,CAAC,IAAK;sBACf,IAAIA,CAAC,CAACC,MAAM,CAACoB,OAAO,EAAE;wBACpBlG,uBAAuB,CAACH,mBAAmB,CAACoF,GAAG,CAAC7B,GAAG,IAAIA,GAAG,CAACH,EAAE,CAAC,CAAC;sBACjE,CAAC,MAAM;wBACLjD,uBAAuB,CAAC,EAAE,CAAC;sBAC7B;oBACF,CAAE;oBACFqE,KAAK,EAAE;sBAAE+B,WAAW,EAAE;oBAAM;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,mBAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9E,OAAA;kBAAI+E,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEgB,MAAM,EAAE,gBAAgB;oBAAEqB,SAAS,EAAE;kBAAO,CAAE;kBAAA3C,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChG9E,OAAA;kBAAI+E,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEgB,MAAM,EAAE,gBAAgB;oBAAEqB,SAAS,EAAE;kBAAO,CAAE;kBAAA3C,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvF9E,OAAA;kBAAI+E,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEgB,MAAM,EAAE,gBAAgB;oBAAEqB,SAAS,EAAE;kBAAO,CAAE;kBAAA3C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzF9E,OAAA;kBAAI+E,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEgB,MAAM,EAAE,gBAAgB;oBAAEqB,SAAS,EAAE;kBAAO,CAAE;kBAAA3C,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACR9E,OAAA;cAAA0E,QAAA,EACGnE,mBAAmB,CAACoF,GAAG,CAAC7B,GAAG;gBAAA,IAAAwD,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;gBAAA,oBAC1B5H,OAAA;kBAAA0E,QAAA,gBACE1E,OAAA;oBAAI+E,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEgB,MAAM,EAAE;oBAAiB,CAAE;oBAAAtB,QAAA,eACvD1E,OAAA;sBACEsG,IAAI,EAAC,UAAU;sBACfM,OAAO,EAAEnG,oBAAoB,CAAC0D,QAAQ,CAACL,GAAG,CAACH,EAAE,CAAE;sBAC/C2B,QAAQ,EAAGC,CAAC,IAAK;wBACf,IAAIA,CAAC,CAACC,MAAM,CAACoB,OAAO,EAAE;0BACpBlG,uBAAuB,CAAC,CAAC,GAAGD,oBAAoB,EAAEqD,GAAG,CAACH,EAAE,CAAC,CAAC;wBAC5D,CAAC,MAAM;0BACLjD,uBAAuB,CAACD,oBAAoB,CAACoD,MAAM,CAACF,EAAE,IAAIA,EAAE,KAAKG,GAAG,CAACH,EAAE,CAAC,CAAC;wBAC3E;sBACF;oBAAE;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACL9E,OAAA;oBAAI+E,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEgB,MAAM,EAAE;oBAAiB,CAAE;oBAAAtB,QAAA,EACtD,EAAA4C,gBAAA,GAAAxD,GAAG,CAACG,UAAU,cAAAqD,gBAAA,uBAAdA,gBAAA,CAAgBO,cAAc,KAAI;kBAAK;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACL9E,OAAA;oBAAI+E,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEgB,MAAM,EAAE;oBAAiB,CAAE;oBAAAtB,QAAA,EACtD,EAAA6C,gBAAA,GAAAzD,GAAG,CAACG,UAAU,cAAAsD,gBAAA,uBAAdA,gBAAA,CAAgBtF,KAAK,KAAI;kBAAK;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACL9E,OAAA;oBAAI+E,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEgB,MAAM,EAAE;oBAAiB,CAAE;oBAAAtB,QAAA,EACtD,EAAA8C,gBAAA,GAAA1D,GAAG,CAACG,UAAU,cAAAuD,gBAAA,uBAAdA,gBAAA,CAAgBtD,gBAAgB,KAAI;kBAAK;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC,CAAC,eACL9E,OAAA;oBAAI+E,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEgB,MAAM,EAAE;oBAAiB,CAAE;oBAAAtB,QAAA,EACtD,EAAA+C,gBAAA,GAAA3D,GAAG,CAACG,UAAU,cAAAwD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBK,KAAK,cAAAJ,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuB7E,IAAI,cAAA8E,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA6B1D,UAAU,cAAA2D,sBAAA,uBAAvCA,sBAAA,CAAyCG,GAAG,KAAI;kBAAK;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CAAC;gBAAA,GAzBEhB,GAAG,CAACH,EAAE;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0BX,CAAC;cAAA,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAER9E,OAAA;YACEgH,OAAO,EAAE5C,qBAAsB;YAC/B6C,QAAQ,EAAExG,oBAAoB,CAACyC,MAAM,KAAK,CAAE;YAC5C6B,KAAK,EAAE;cACLmB,eAAe,EAAEzF,oBAAoB,CAACyC,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;cACxE6D,KAAK,EAAE,OAAO;cACd/B,OAAO,EAAE,WAAW;cACpBgB,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBiB,QAAQ,EAAE,MAAM;cAChB9B,UAAU,EAAE,MAAM;cAClB+B,MAAM,EAAE1G,oBAAoB,CAACyC,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;YACxD,CAAE;YAAAwB,QAAA,GACH,wBACa,EAACjE,oBAAoB,CAACyC,MAAM,EAAC,oBAAY,EAACzC,oBAAoB,CAACyC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,GACnG;UAAA;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN9E,OAAA;UAAG+E,KAAK,EAAE;YAAEgC,KAAK,EAAE,SAAS;YAAEb,eAAe,EAAE,SAAS;YAAElB,OAAO,EAAE,MAAM;YAAEiB,YAAY,EAAE;UAAM,CAAE;UAAAvB,QAAA,GAAC,kFACnC,EAACvD,QAAQ,CAACO,QAAQ,CAACsG,IAAI,CAAC,IAAI,CAAC;QAAA;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,gBAEN9E,OAAA;MAAK+E,KAAK,EAAE;QACVmB,eAAe,EAAE,SAAS;QAC1BF,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBjB,OAAO,EAAE,MAAM;QACfqC,SAAS,EAAE;MACb,CAAE;MAAA3C,QAAA,gBACA1E,OAAA;QAAI+E,KAAK,EAAE;UAAEgC,KAAK,EAAE,SAAS;UAAE7B,YAAY,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAEL9E,OAAA;QAAK+E,KAAK,EAAE;UACVmB,eAAe,EAAE,OAAO;UACxBlB,OAAO,EAAE,MAAM;UACfiB,YAAY,EAAE,KAAK;UACnBoB,SAAS,EAAE,MAAM;UACjBnC,YAAY,EAAE;QAChB,CAAE;QAAAR,QAAA,gBACA1E,OAAA;UAAI+E,KAAK,EAAE;YAAEgC,KAAK,EAAE,SAAS;YAAE7B,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAEL9E,OAAA;UAAK+E,KAAK,EAAE;YAAEmB,eAAe,EAAE,SAAS;YAAElB,OAAO,EAAE,MAAM;YAAEiB,YAAY,EAAE;UAAM,CAAE;UAAAvB,QAAA,gBAC/E1E,OAAA;YAAA0E,QAAA,gBAAG1E,OAAA;cAAA0E,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,0CAAmC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClE9E,OAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN9E,OAAA;YAAA0E,QAAA,eAAG1E,OAAA;cAAA0E,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjC9E,OAAA;YAAI+E,KAAK,EAAE;cAAEkD,UAAU,EAAE;YAAO,CAAE;YAAAvD,QAAA,gBAChC1E,OAAA;cAAA0E,QAAA,gBAAI1E,OAAA;gBAAA0E,QAAA,EAAQ;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACT,kBAAkB,CAAC,CAAC;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9D9E,OAAA;cAAA0E,QAAA,gBAAI1E,OAAA;gBAAA0E,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3D,QAAQ,CAACG,KAAK;YAAA;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClD9E,OAAA;cAAA0E,QAAA,gBAAI1E,OAAA;gBAAA0E,QAAA,EAAQ;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3D,QAAQ,CAACI,IAAI;YAAA;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/D9E,OAAA;cAAA0E,QAAA,gBAAI1E,OAAA;gBAAA0E,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3D,QAAQ,CAACK,UAAU,EAAC,UAAG,EAACL,QAAQ,CAACM,QAAQ;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/E9E,OAAA;cAAA0E,QAAA,gBAAI1E,OAAA;gBAAA0E,QAAA,EAAQ;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC3D,QAAQ,CAACO,QAAQ,CAACsG,IAAI,CAAC,IAAI,CAAC;YAAA;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACL9E,OAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN9E,OAAA;YAAA0E,QAAA,eAAG1E,OAAA;cAAA0E,QAAA,EAAQ;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChD9E,OAAA;YAAI+E,KAAK,EAAE;cAAEkD,UAAU,EAAE;YAAO,CAAE;YAAAvD,QAAA,EAC/BjE,oBAAoB,CAACkF,GAAG,CAACuC,KAAK,IAAI;cAAA,IAAAC,gBAAA,EAAAC,gBAAA;cACjC,MAAMtE,GAAG,GAAGvD,mBAAmB,CAACiE,IAAI,CAACe,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAKuE,KAAK,CAAC;cACzD,oBACElI,OAAA;gBAAA0E,QAAA,GACGZ,GAAG,aAAHA,GAAG,wBAAAqE,gBAAA,GAAHrE,GAAG,CAAEG,UAAU,cAAAkE,gBAAA,uBAAfA,gBAAA,CAAiBlG,KAAK,EAAC,IAAE,EAAC6B,GAAG,aAAHA,GAAG,wBAAAsE,gBAAA,GAAHtE,GAAG,CAAEG,UAAU,cAAAmE,gBAAA,uBAAfA,gBAAA,CAAiBP,cAAc,EAAC,GAC7D;cAAA,GAFSK,KAAK;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CAAC;YAET,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9E,OAAA;QACEgH,OAAO,EAAEA,CAAA,KAAM;UACb9F,mBAAmB,CAAC,KAAK,CAAC;UAC1BF,eAAe,CAAC,KAAK,CAAC;UACtBN,uBAAuB,CAAC,EAAE,CAAC;UAC3BF,sBAAsB,CAAC,EAAE,CAAC;UAC1BY,WAAW,CAAC;YACVC,WAAW,EAAE,EAAE;YACfC,KAAK,EAAE,EAAE;YACTC,IAAI,EAAE,EAAE;YACRC,UAAU,EAAE,EAAE;YACdC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACFE,iBAAiB,CAAC;YAChBC,OAAO,EAAE,EAAE;YACXN,IAAI,EAAE,EAAE;YACRO,eAAe,EAAE,EAAE;YACnBC,kBAAkB,EAAE,EAAE;YACtBC,kBAAkB,EAAE,EAAE;YACtBC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE;UACV,CAAC,CAAC;UACFpB,yBAAyB,CAAC,KAAK,CAAC;QAClC,CAAE;QACFiE,KAAK,EAAE;UACLmB,eAAe,EAAE,SAAS;UAC1Ba,KAAK,EAAE,OAAO;UACd/B,OAAO,EAAE,WAAW;UACpBgB,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBiB,QAAQ,EAAE,MAAM;UAChB9B,UAAU,EAAE,MAAM;UAClB+B,MAAM,EAAE;QACV,CAAE;QAAAzC,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC5E,EAAA,CAzhBQD,eAAe;AAAAoI,EAAA,GAAfpI,eAAe;AA2hBxB,eAAeA,eAAe;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}