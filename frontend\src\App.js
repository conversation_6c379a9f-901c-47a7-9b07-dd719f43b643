import React from "react";
import { BrowserRouter as Router, Routes, Route, Link } from "react-router-dom";
import ImportateurForm from "./components/ImportateurForm";
import ExportateurForm from "./components/ExportateurForm";
import OpportuniteForm from "./components/OpportuniteForm";
import DebugTest from "./components/DebugTest";

function App() {
  return (
    <Router>
      <div style={{ padding: "20px" }}>
        <nav style={{ marginBottom: "20px" }}>
          <Link to="/debug" style={{ marginRight: "10px" }}>🔍 Debug</Link>
          <Link to="/importateur" style={{ marginRight: "10px" }}>➕ Importateur</Link>
          <Link to="/exportateur" style={{ marginRight: "10px" }}>➕ Exportateur</Link>
          <Link to="/opportunite">📄 Opportunité</Link>
        </nav>

        <Routes>
          <Route path="/debug" element={<DebugTest />} />
          <Route path="/importateur" element={<ImportateurForm />} />
          <Route path="/exportateur" element={<ExportateurForm />} />
          <Route path="/opportunite" element={<OpportuniteForm />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
