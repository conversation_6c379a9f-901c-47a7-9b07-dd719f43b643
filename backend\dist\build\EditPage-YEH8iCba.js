const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["MagicLinkEE-DZQ66Mn9.js","strapi-z7ApxZZq.js","strapi-D6821bIJ.css","SelectRoles-DgSmECKC.js","useAdminRoles-DhqKz3-J.js"])))=>i.map(i=>d[i]);
import{aL as v,j as e,P as c,a as C,aw as B,V as z,w as D,bi as F,_ as V,v as q,z as O,bj as N,bk as H,r as $,bl as E,C as K,aP as Y,B as P,bg as G,D as Q,E as J,$ as p,e as h,T as y,H as l,aU as W,a_ as X,b0 as Z,aR as T,aT as ee}from"./strapi-z7ApxZZq.js";import{s as se}from"./selectors-B6uMLQu7.js";import{g as ae}from"./users-DLc-PG84.js";import{M as te,S as re}from"./SelectRoles-DgSmECKC.js";import{C as ie}from"./validation-BpdfDyDS.js";import"./useAdminRoles-DhqKz3-J.js";const oe=Y().shape({...ie,isActive:Z(),roles:X().min(1,{id:T.required.id,defaultMessage:"This field is required"}).required({id:T.required.id,defaultMessage:"This field is required"})}),L=["email","firstname","lastname","username","isActive","roles"],ne=()=>{const{formatMessage:s}=C(),b=B("/settings/users/:id")?.params?.id??"",M=z(),{toggleNotification:r}=D(),x=F(te,async()=>(await V(async()=>{const{MagicLinkEE:a}=await import("./MagicLinkEE-DZQ66Mn9.js");return{MagicLinkEE:a}},__vite__mapDeps([0,1,2,3,4]))).MagicLinkEE),{_unstableFormatAPIError:m,_unstableFormatValidationErrors:_}=q(),A=v(se),{isLoading:I,allowedActions:{canUpdate:u}}=O({read:A.settings?.users.read??[],update:A.settings?.users.update??[]}),[k]=N(),{data:R,error:i,isLoading:U}=H({id:b},{refetchOnMountOrArgChange:!0}),[o]=R?.users??[];if($.useEffect(()=>{i&&(i.name==="UnauthorizedError"?(r({type:"info",message:s({id:"notification.permission.not-allowed-read",defaultMessage:"You are not allowed to see this document"})}),M("/")):r({type:"danger",message:m(i)}))},[i,m,s,M,r]),U||!x||I)return e.jsx(c.Loading,{});const w={...E(o,L),roles:o.roles.map(({id:a})=>a),password:"",confirmPassword:""},S=async(a,n)=>{const{confirmPassword:j,...g}=a,t=await k({id:b,...g});"error"in t&&ee(t.error)?(t.error.name==="ValidationError"&&n.setErrors(_(t.error)),r({type:"danger",message:m(t.error)})):(r({type:"success",message:s({id:"notification.success.saved",defaultMessage:"Saved"})}),n.setValues({...E(a,L),password:"",confirmPassword:""}))};return e.jsxs(c.Main,{children:[e.jsx(c.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Users"})}),e.jsx(K,{method:"PUT",onSubmit:S,initialValues:w,validationSchema:oe,children:({isSubmitting:a,modified:n})=>e.jsxs(e.Fragment,{children:[e.jsx(P.Header,{primaryAction:e.jsx(Q,{disabled:a||!u||!n,startIcon:e.jsx(J,{}),loading:a,type:"submit",children:s({id:"global.save",defaultMessage:"Save"})}),title:s({id:"app.containers.Users.EditPage.header.label",defaultMessage:"Edit {name}"},{name:ae(w)}),navigationAction:e.jsx(G,{fallback:"../users"})}),e.jsxs(P.Content,{children:[o?.registrationToken&&e.jsx(p,{paddingBottom:6,children:e.jsx(x,{registrationToken:o.registrationToken})}),e.jsxs(h,{direction:"column",alignItems:"stretch",gap:7,children:[e.jsx(p,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:e.jsxs(h,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(y,{variant:"delta",tag:"h2",children:s({id:"app.components.Users.ModalCreateBody.block-title.details",defaultMessage:"Details"})}),e.jsx(l.Root,{gap:5,children:de.map(j=>j.map(({size:g,label:t,...d})=>e.jsx(l.Item,{col:g,direction:"column",alignItems:"stretch",children:e.jsx(W,{...d,disabled:!u,label:s(t),placeholder:"placeholder"in d?s(d.placeholder):void 0})},d.name)))})]})}),e.jsx(p,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:e.jsxs(h,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(y,{variant:"delta",tag:"h2",children:s({id:"global.roles",defaultMessage:"User's role"})}),e.jsx(l.Root,{gap:5,children:e.jsx(l.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(re,{disabled:!u})})})]})})]})]})]})})]})},de=[[{label:{id:"Auth.form.firstname.label",defaultMessage:"First name"},name:"firstname",placeholder:{id:"Auth.form.firstname.placeholder",defaultMessage:"e.g. Kai"},type:"string",size:6,required:!0},{label:{id:"Auth.form.lastname.label",defaultMessage:"Last name"},name:"lastname",placeholder:{id:"Auth.form.lastname.placeholder",defaultMessage:"e.g. Doe"},type:"string",size:6}],[{label:{id:"Auth.form.email.label",defaultMessage:"Email"},name:"email",placeholder:{id:"Auth.form.email.placeholder",defaultMessage:"e.g. <EMAIL>"},type:"email",size:6,required:!0},{label:{id:"Auth.form.username.label",defaultMessage:"Username"},name:"username",placeholder:{id:"Auth.form.username.placeholder",defaultMessage:"e.g. Kai_Doe"},type:"string",size:6}],[{autoComplete:"new-password",label:{id:"global.password",defaultMessage:"Password"},name:"password",type:"password",size:6},{autoComplete:"new-password",label:{id:"Auth.form.confirmPassword.label",defaultMessage:"Password confirmation"},name:"confirmPassword",type:"password",size:6}],[{label:{id:"Auth.form.active.label",defaultMessage:"Active"},name:"isActive",type:"boolean",size:6}]],fe=()=>{const s=v(f=>f.admin_app.permissions.settings?.users.read);return e.jsx(c.Protect,{permissions:s,children:e.jsx(ne,{})})};export{ne as EditPage,fe as ProtectedEditPage};
