{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/Users/<USER>/MagicLinkEE.tsx"], "sourcesContent": ["import { useIntl } from 'react-intl';\n\nimport { getBasename } from '../../../../../../../../admin/src/core/utils/basename';\nimport { MagicLinkWrapper } from '../../../../../../../../admin/src/pages/Settings/pages/Users/<USER>/MagicLinkWrapper';\n\nimport type { MagicLinkCEProps } from '../../../../../../../../admin/src/pages/Settings/pages/Users/<USER>/MagicLinkCE';\n\n// FIXME replace with parts compo when ready\nexport const MagicLinkEE = ({ registrationToken }: MagicLinkCEProps) => {\n  const { formatMessage } = useIntl();\n\n  if (registrationToken) {\n    return (\n      <MagicLinkWrapper\n        target={`${\n          window.location.origin\n        }${getBasename()}/auth/register?registrationToken=${registrationToken}`}\n      >\n        {formatMessage({\n          id: 'app.components.Users.MagicLink.connect',\n          defaultMessage: 'Copy and share this link to give access to this user',\n        })}\n      </MagicLinkWrapper>\n    );\n  }\n\n  return (\n    <MagicLinkWrapper target={`${window.location.origin}${getBasename()}/auth/login`}>\n      {formatMessage({\n        id: 'app.components.Users.MagicLink.connect.sso',\n        defaultMessage:\n          'Send this link to the user, the first login can be made via a SSO provider.',\n      })}\n    </MagicLinkWrapper>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAQaA,IAAAA,cAAc,CAAC,EAAEC,kBAAiB,MAAoB;AACjE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,MAAIF,mBAAmB;AACrB,eACEG,wBAACC,kBAAAA;MACCC,QAAQ,GACNC,OAAOC,SAASC,MAAM,GACrBC,YAAc,CAAA,oCAAmCT,iBAAAA;gBAEnDC,cAAc;QACbS,IAAI;QACJC,gBAAgB;MAClB,CAAA;;EAGN;AAEA,aACER,wBAACC,kBAAAA;IAAiBC,QAAQ,GAAGC,OAAOC,SAASC,MAAM,GAAGC,YAAc,CAAA;cACjER,cAAc;MACbS,IAAI;MACJC,gBACE;IACJ,CAAA;;AAGN;", "names": ["MagicLinkEE", "registrationToken", "formatMessage", "useIntl", "_jsx", "MagicLinkWrapper", "target", "window", "location", "origin", "getBasename", "id", "defaultMessage"]}