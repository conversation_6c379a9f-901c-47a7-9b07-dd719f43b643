import{a as w,j as e,$ as u,e as p,T as P,D as I,H as j,M as l,aJ as N,bh as U,aL as V,P as b,w as _,aw as H,r as Q,b as q,v as G,ba as $,bb as z,bY as J,bd as W,aB as Y,aE as K,be as X,aP as Z,B as S,bg as ee,E as se,aQ as L,aR as re,aT as v}from"./strapi-z7ApxZZq.js";import{u as ie}from"./useAdminRoles-DhqKz3-J.js";import{P as te}from"./Permissions-B6GrFVsT.js";import"./groupBy--H6Arigv.js";import"./_baseEach-ZnnftuGj.js";const ae=({disabled:t,role:s,values:h,errors:r,onChange:n,onBlur:m})=>{const{formatMessage:i}=w();return e.jsx(u,{background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:e.jsxs(p,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsxs(p,{justifyContent:"space-between",children:[e.jsxs(u,{children:[e.jsx(u,{children:e.jsx(P,{fontWeight:"bold",children:s?s.name:i({id:"global.details",defaultMessage:"Details"})})}),e.jsx(u,{children:e.jsx(P,{textColor:"neutral500",variant:"pi",children:s?s.description:i({id:"Settings.roles.form.description",defaultMessage:"Name and description of the role"})})})]}),e.jsx(I,{disabled:!0,variant:"secondary",children:i({id:"Settings.roles.form.button.users-with-role",defaultMessage:"{number, plural, =0 {# users} one {# user} other {# users}} with this role"},{number:s.usersCount})})]}),e.jsxs(j.Root,{gap:4,children:[e.jsx(j.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(l.Root,{name:"name",error:r.name&&i({id:r.name}),required:!0,children:[e.jsx(l.Label,{children:i({id:"global.name",defaultMessage:"Name"})}),e.jsx(N,{disabled:t,onChange:n,onBlur:m,value:h.name||""}),e.jsx(l.Error,{})]})}),e.jsx(j.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(l.Root,{name:"description",error:r.description&&i({id:r.description}),children:[e.jsx(l.Label,{children:i({id:"global.description",defaultMessage:"Description"})}),e.jsx(U,{disabled:t,onChange:n,onBlur:m,value:h.description}),e.jsx(l.Error,{})]})})]})]})})},oe=Z().shape({name:L().required(re.required.id),description:L().optional()}),ne=()=>{const{toggleNotification:t}=_(),{formatMessage:s}=w(),r=H("/settings/roles/:id")?.params.id,n=Q.useRef(null),{trackUsage:m}=q(),{_unstableFormatAPIError:i,_unstableFormatValidationErrors:R}=G(),{isLoading:C,data:E}=$({role:r??""}),{roles:T,isLoading:y,refetch:A}=ie({id:r},{refetchOnMountOrArgChange:!0}),d=T[0]??{},{data:F,isLoading:B}=z({id:r},{skip:!r,refetchOnMountOrArgChange:!0}),[D]=J(),[k]=W();if(!r)return e.jsx(Y,{to:"/settings/roles"});const O=async(f,g)=>{try{const{permissionsToSend:c,didUpdateConditions:x}=n.current?.getPermissions()??{},a=await D({id:r,...f});if("error"in a){v(a.error)&&a.error.name==="ValidationError"?g.setErrors(R(a.error)):t({type:"danger",message:i(a.error)});return}if(d.code!=="strapi-super-admin"&&c){const o=await k({id:a.data.id,permissions:c});if("error"in o){v(o.error)&&o.error.name==="ValidationError"?g.setErrors(R(o.error)):t({type:"danger",message:i(o.error)});return}x&&m("didUpdateConditions")}n.current?.setFormAfterSubmit(),await A(),t({type:"success",message:s({id:"notification.success.saved"})})}catch{t({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occurred"})})}},M=!y&&d.code==="strapi-super-admin";return C||y||B||!E?e.jsx(b.Loading,{}):e.jsxs(K,{children:[e.jsx(b.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Roles"})}),e.jsx(X,{enableReinitialize:!0,initialValues:{name:d.name??"",description:d.description??""},onSubmit:O,validationSchema:oe,validateOnChange:!1,children:({handleSubmit:f,values:g,errors:c,handleChange:x,handleBlur:a,isSubmitting:o})=>e.jsxs("form",{onSubmit:f,children:[e.jsx(S.Header,{primaryAction:e.jsx(p,{gap:2,children:e.jsx(I,{type:"submit",startIcon:e.jsx(se,{}),disabled:d.code==="strapi-super-admin",loading:o,children:s({id:"global.save",defaultMessage:"Save"})})}),title:s({id:"Settings.roles.edit.title",defaultMessage:"Edit a role"}),subtitle:s({id:"Settings.roles.create.description",defaultMessage:"Define the rights given to the role"}),navigationAction:e.jsx(ee,{fallback:"../roles"})}),e.jsx(S.Content,{children:e.jsxs(p,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsx(ae,{disabled:M,errors:c,values:g,onChange:x,onBlur:a,role:d}),e.jsx(u,{shadow:"filterShadow",hasRadius:!0,children:e.jsx(te,{isFormDisabled:M,permissions:F,ref:n,layout:E})})]})})]})})]})},ge=()=>{const t=V(s=>s.admin_app.permissions.settings?.roles.update);return e.jsx(b.Protect,{permissions:t,children:e.jsx(ne,{})})};export{ne as EditPage,ge as ProtectedEditPage};
