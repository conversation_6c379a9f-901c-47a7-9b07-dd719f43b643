import{d as qe,t as ie,gm as Ve,gn as h,go as Ye,r as D,j as o,gp as Xe,bv as Se,a as w,b as Q,eb as Qe,dU as Je,dV as Ke,e as m,I as Ze,h as et,$ as I,D as Te,k as x,T as O,at as De,au as xe,ej as oe,c$ as be,bq as Ee,b8 as Oe,gq as tt,bw as H,a6 as Ie,V as nt,a4 as P,cf as N,a5 as rt,bC as we,gr as Ce,eg as st,aA as it,b3 as ot,b7 as at,cn as ct,a7 as J,bV as lt,P as je,ei as ut,aL as dt,gs as gt,gt as ft,cg as ht,g1 as pt,gu as mt,gv as vt,gw as yt,gx as St,co as Tt}from"./strapi-z7ApxZZq.js";import{e as ae,f as Dt}from"./lt-BjNpJj8i.js";import{L as xt,V as F,S as j,N as ce,s as bt,i as Et}from"./Ornaments-WHFaeDcJ.js";import{g as Ot,a as It,h as wt}from"./users-DLc-PG84.js";import{f as Ct}from"./index-C3HeomYJ.js";import{P as jt}from"./PrivateRoute-_HMyEha1.js";import"./index-BRVyLNfZ.js";function Lt(t,e){qe(2,arguments);var n=ie(t),s=ie(e);return n.getTime()<s.getTime()}function Nt(){return Ve(Date.now())}function Pt(t,e,n){return e.split(".").reduce((s,r)=>s&&s[r]?s[r]:n||null,t)}function kt(t,e){return t.filter(n=>n!==e)}function Le(t){return typeof t=="object"}function Rt(t,e){const n=new Map,s=i=>{n.set(i,n.has(i)?n.get(i)+1:1)};t.forEach(s),e.forEach(s);const r=[];return n.forEach((i,a)=>{i===1&&r.push(a)}),r}function At(t,e){return t.filter(n=>e.indexOf(n)>-1)}const K="dnd-core/INIT_COORDS",_="dnd-core/BEGIN_DRAG",Z="dnd-core/PUBLISH_DRAG_SOURCE",B="dnd-core/HOVER",U="dnd-core/DROP",$="dnd-core/END_DRAG";function le(t,e){return{type:K,payload:{sourceClientOffset:e||null,clientOffset:t||null}}}const Mt={type:K,payload:{clientOffset:null,sourceClientOffset:null}};function _t(t){return function(n=[],s={publishSource:!0}){const{publishSource:r=!0,clientOffset:i,getSourceClientOffset:a}=s,c=t.getMonitor(),d=t.getRegistry();t.dispatch(le(i)),Bt(n,c,d);const l=Gt(n,c);if(l==null){t.dispatch(Mt);return}let g=null;if(i){if(!a)throw new Error("getSourceClientOffset must be defined");Ut(a),g=a(l)}t.dispatch(le(i,g));const p=d.getSource(l).beginDrag(c,l);if(p==null)return;$t(p),d.pinSource(l);const u=d.getSourceType(l);return{type:_,payload:{itemType:u,item:p,sourceId:l,clientOffset:i||null,sourceClientOffset:g||null,isSourcePublic:!!r}}}}function Bt(t,e,n){h(!e.isDragging(),"Cannot call beginDrag while dragging."),t.forEach(function(s){h(n.getSource(s),"Expected sourceIds to be registered.")})}function Ut(t){h(typeof t=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function $t(t){h(Le(t),"Item must be an object.")}function Gt(t,e){let n=null;for(let s=t.length-1;s>=0;s--)if(e.canDragSource(t[s])){n=t[s];break}return n}function Ht(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Ft(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){Ht(t,r,n[r])})}return t}function Wt(t){return function(n={}){const s=t.getMonitor(),r=t.getRegistry();zt(s),Yt(s).forEach((a,c)=>{const d=qt(a,c,r,s),l={type:U,payload:{dropResult:Ft({},n,d)}};t.dispatch(l)})}}function zt(t){h(t.isDragging(),"Cannot call drop while not dragging."),h(!t.didDrop(),"Cannot call drop twice during one drag operation.")}function qt(t,e,n,s){const r=n.getTarget(t);let i=r?r.drop(s,t):void 0;return Vt(i),typeof i>"u"&&(i=e===0?{}:s.getDropResult()),i}function Vt(t){h(typeof t>"u"||Le(t),"Drop result must either be an object or undefined.")}function Yt(t){const e=t.getTargetIds().filter(t.canDropOnTarget,t);return e.reverse(),e}function Xt(t){return function(){const n=t.getMonitor(),s=t.getRegistry();Qt(n);const r=n.getSourceId();return r!=null&&(s.getSource(r,!0).endDrag(n,r),s.unpinSource()),{type:$}}}function Qt(t){h(t.isDragging(),"Cannot call endDrag while not dragging.")}function V(t,e){return e===null?t===null:Array.isArray(t)?t.some(n=>n===e):t===e}function Jt(t){return function(n,{clientOffset:s}={}){Kt(n);const r=n.slice(0),i=t.getMonitor(),a=t.getRegistry(),c=i.getItemType();return en(r,a,c),Zt(r,i,a),tn(r,i,a),{type:B,payload:{targetIds:r,clientOffset:s||null}}}}function Kt(t){h(Array.isArray(t),"Expected targetIds to be an array.")}function Zt(t,e,n){h(e.isDragging(),"Cannot call hover while not dragging."),h(!e.didDrop(),"Cannot call hover after drop.");for(let s=0;s<t.length;s++){const r=t[s];h(t.lastIndexOf(r)===s,"Expected targetIds to be unique in the passed array.");const i=n.getTarget(r);h(i,"Expected targetIds to be registered.")}}function en(t,e,n){for(let s=t.length-1;s>=0;s--){const r=t[s],i=e.getTargetType(r);V(i,n)||t.splice(s,1)}}function tn(t,e,n){t.forEach(function(s){n.getTarget(s).hover(e,s)})}function nn(t){return function(){if(t.getMonitor().isDragging())return{type:Z}}}function rn(t){return{beginDrag:_t(t),publishDragSource:nn(t),hover:Jt(t),drop:Wt(t),endDrag:Xt(t)}}class sn{receiveBackend(e){this.backend=e}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const e=this,{dispatch:n}=this.store;function s(i){return(...a)=>{const c=i.apply(e,a);typeof c<"u"&&n(c)}}const r=rn(this);return Object.keys(r).reduce((i,a)=>{const c=r[a];return i[a]=s(c),i},{})}dispatch(e){this.store.dispatch(e)}constructor(e,n){this.isSetUp=!1,this.handleRefCountChange=()=>{const s=this.store.getState().refCount>0;this.backend&&(s&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!s&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=e,this.monitor=n,e.subscribe(this.handleRefCountChange)}}function on(t,e){return{x:t.x+e.x,y:t.y+e.y}}function Ne(t,e){return{x:t.x-e.x,y:t.y-e.y}}function an(t){const{clientOffset:e,initialClientOffset:n,initialSourceClientOffset:s}=t;return!e||!n||!s?null:Ne(on(e,s),n)}function cn(t){const{clientOffset:e,initialClientOffset:n}=t;return!e||!n?null:Ne(e,n)}const k=[],ee=[];k.__IS_NONE__=!0;ee.__IS_ALL__=!0;function ln(t,e){return t===k?!1:t===ee||typeof e>"u"?!0:At(e,t).length>0}class un{subscribeToStateChange(e,n={}){const{handlerIds:s}=n;h(typeof e=="function","listener must be a function."),h(typeof s>"u"||Array.isArray(s),"handlerIds, when specified, must be an array of strings.");let r=this.store.getState().stateId;const i=()=>{const a=this.store.getState(),c=a.stateId;try{c===r||c===r+1&&!ln(a.dirtyHandlerIds,s)||e()}finally{r=c}};return this.store.subscribe(i)}subscribeToOffsetChange(e){h(typeof e=="function","listener must be a function.");let n=this.store.getState().dragOffset;const s=()=>{const r=this.store.getState().dragOffset;r!==n&&(n=r,e())};return this.store.subscribe(s)}canDragSource(e){if(!e)return!1;const n=this.registry.getSource(e);return h(n,`Expected to find a valid source. sourceId=${e}`),this.isDragging()?!1:n.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;const n=this.registry.getTarget(e);if(h(n,`Expected to find a valid target. targetId=${e}`),!this.isDragging()||this.didDrop())return!1;const s=this.registry.getTargetType(e),r=this.getItemType();return V(s,r)&&n.canDrop(this,e)}isDragging(){return!!this.getItemType()}isDraggingSource(e){if(!e)return!1;const n=this.registry.getSource(e,!0);if(h(n,`Expected to find a valid source. sourceId=${e}`),!this.isDragging()||!this.isSourcePublic())return!1;const s=this.registry.getSourceType(e),r=this.getItemType();return s!==r?!1:n.isDragging(this,e)}isOverTarget(e,n={shallow:!1}){if(!e)return!1;const{shallow:s}=n;if(!this.isDragging())return!1;const r=this.registry.getTargetType(e),i=this.getItemType();if(i&&!V(r,i))return!1;const a=this.getTargetIds();if(!a.length)return!1;const c=a.indexOf(e);return s?c===a.length-1:c>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return an(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return cn(this.store.getState().dragOffset)}constructor(e,n){this.store=e,this.registry=n}}const ue=typeof global<"u"?global:self,Pe=ue.MutationObserver||ue.WebKitMutationObserver;function ke(t){return function(){const n=setTimeout(r,0),s=setInterval(r,50);function r(){clearTimeout(n),clearInterval(s),t()}}}function dn(t){let e=1;const n=new Pe(t),s=document.createTextNode("");return n.observe(s,{characterData:!0}),function(){e=-e,s.data=e}}const gn=typeof Pe=="function"?dn:ke;class fn{enqueueTask(e){const{queue:n,requestFlush:s}=this;n.length||(s(),this.flushing=!0),n[n.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:e}=this;for(;this.index<e.length;){const n=this.index;if(this.index++,e[n].call(),this.index>this.capacity){for(let s=0,r=e.length-this.index;s<r;s++)e[s]=e[s+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=gn(this.flush),this.requestErrorThrow=ke(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class hn{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,n){this.onError=e,this.release=n,this.task=null}}class pn{create(e){const n=this.freeTasks,s=n.length?n.pop():new hn(this.onError,r=>n[n.length]=r);return s.task=e,s}constructor(e){this.onError=e,this.freeTasks=[]}}const Re=new fn,mn=new pn(Re.registerPendingError);function vn(t){Re.enqueueTask(mn.create(t))}const te="dnd-core/ADD_SOURCE",ne="dnd-core/ADD_TARGET",re="dnd-core/REMOVE_SOURCE",G="dnd-core/REMOVE_TARGET";function yn(t){return{type:te,payload:{sourceId:t}}}function Sn(t){return{type:ne,payload:{targetId:t}}}function Tn(t){return{type:re,payload:{sourceId:t}}}function Dn(t){return{type:G,payload:{targetId:t}}}function xn(t){h(typeof t.canDrag=="function","Expected canDrag to be a function."),h(typeof t.beginDrag=="function","Expected beginDrag to be a function."),h(typeof t.endDrag=="function","Expected endDrag to be a function.")}function bn(t){h(typeof t.canDrop=="function","Expected canDrop to be a function."),h(typeof t.hover=="function","Expected hover to be a function."),h(typeof t.drop=="function","Expected beginDrag to be a function.")}function Y(t,e){if(e&&Array.isArray(t)){t.forEach(n=>Y(n,!1));return}h(typeof t=="string"||typeof t=="symbol",e?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var b;(function(t){t.SOURCE="SOURCE",t.TARGET="TARGET"})(b||(b={}));let En=0;function On(){return En++}function In(t){const e=On().toString();switch(t){case b.SOURCE:return`S${e}`;case b.TARGET:return`T${e}`;default:throw new Error(`Unknown Handler Role: ${t}`)}}function de(t){switch(t[0]){case"S":return b.SOURCE;case"T":return b.TARGET;default:throw new Error(`Cannot parse handler ID: ${t}`)}}function ge(t,e){const n=t.entries();let s=!1;do{const{done:r,value:[,i]}=n.next();if(i===e)return!0;s=!!r}while(!s);return!1}class wn{addSource(e,n){Y(e),xn(n);const s=this.addHandler(b.SOURCE,e,n);return this.store.dispatch(yn(s)),s}addTarget(e,n){Y(e,!0),bn(n);const s=this.addHandler(b.TARGET,e,n);return this.store.dispatch(Sn(s)),s}containsHandler(e){return ge(this.dragSources,e)||ge(this.dropTargets,e)}getSource(e,n=!1){return h(this.isSourceId(e),"Expected a valid source ID."),n&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return h(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return h(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return h(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return de(e)===b.SOURCE}isTargetId(e){return de(e)===b.TARGET}removeSource(e){h(this.getSource(e),"Expected an existing source."),this.store.dispatch(Tn(e)),vn(()=>{this.dragSources.delete(e),this.types.delete(e)})}removeTarget(e){h(this.getTarget(e),"Expected an existing target."),this.store.dispatch(Dn(e)),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){const n=this.getSource(e);h(n,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=n}unpinSource(){h(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,n,s){const r=In(e);return this.types.set(r,n),e===b.SOURCE?this.dragSources.set(r,s):e===b.TARGET&&this.dropTargets.set(r,s),r}constructor(e){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=e}}const Cn=(t,e)=>t===e;function jn(t,e){return!t&&!e?!0:!t||!e?!1:t.x===e.x&&t.y===e.y}function Ln(t,e,n=Cn){if(t.length!==e.length)return!1;for(let s=0;s<t.length;++s)if(!n(t[s],e[s]))return!1;return!0}function Nn(t=k,e){switch(e.type){case B:break;case te:case ne:case G:case re:return k;case _:case Z:case $:case U:default:return ee}const{targetIds:n=[],prevTargetIds:s=[]}=e.payload,r=Rt(n,s);if(!(r.length>0||!Ln(n,s)))return k;const a=s[s.length-1],c=n[n.length-1];return a!==c&&(a&&r.push(a),c&&r.push(c)),r}function Pn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function kn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){Pn(t,r,n[r])})}return t}const fe={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function Rn(t=fe,e){const{payload:n}=e;switch(e.type){case K:case _:return{initialSourceClientOffset:n.sourceClientOffset,initialClientOffset:n.clientOffset,clientOffset:n.clientOffset};case B:return jn(t.clientOffset,n.clientOffset)?t:kn({},t,{clientOffset:n.clientOffset});case $:case U:return fe;default:return t}}function An(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function L(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){An(t,r,n[r])})}return t}const Mn={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function _n(t=Mn,e){const{payload:n}=e;switch(e.type){case _:return L({},t,{itemType:n.itemType,item:n.item,sourceId:n.sourceId,isSourcePublic:n.isSourcePublic,dropResult:null,didDrop:!1});case Z:return L({},t,{isSourcePublic:!0});case B:return L({},t,{targetIds:n.targetIds});case G:return t.targetIds.indexOf(n.targetId)===-1?t:L({},t,{targetIds:kt(t.targetIds,n.targetId)});case U:return L({},t,{dropResult:n.dropResult,didDrop:!0,targetIds:[]});case $:return L({},t,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return t}}function Bn(t=0,e){switch(e.type){case te:case ne:return t+1;case re:case G:return t-1;default:return t}}function Un(t=0){return t+1}function $n(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Gn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){$n(t,r,n[r])})}return t}function Hn(t={},e){return{dirtyHandlerIds:Nn(t.dirtyHandlerIds,{type:e.type,payload:Gn({},e.payload,{prevTargetIds:Pt(t,"dragOperation.targetIds",[])})}),dragOffset:Rn(t.dragOffset,e),refCount:Bn(t.refCount,e),dragOperation:_n(t.dragOperation,e),stateId:Un(t.stateId)}}function Fn(t,e=void 0,n={},s=!1){const r=Wn(s),i=new un(r,new wn(r)),a=new sn(r,i),c=t(a,e,n);return a.receiveBackend(c),a}function Wn(t){const e=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return Ye(Hn,t&&e&&e({name:"dnd-core",instanceId:"dnd-core"}))}function zn(t,e){if(t==null)return{};var n=qn(t,e),s,r;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)s=i[r],!(e.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(t,s)&&(n[s]=t[s])}return n}function qn(t,e){if(t==null)return{};var n={},s=Object.keys(t),r,i;for(i=0;i<s.length;i++)r=s[i],!(e.indexOf(r)>=0)&&(n[r]=t[r]);return n}let he=0;const M=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var Vn=D.memo(function(e){var{children:n}=e,s=zn(e,["children"]);const[r,i]=Yn(s);return D.useEffect(()=>{if(i){const a=Ae();return++he,()=>{--he===0&&(a[M]=null)}}},[]),o.jsx(Xe.Provider,{value:r,children:n})});function Yn(t){if("manager"in t)return[{dragDropManager:t.manager},!1];const e=Xn(t.backend,t.context,t.options,t.debugMode),n=!t.context;return[e,n]}function Xn(t,e=Ae(),n,s){const r=e;return r[M]||(r[M]={dragDropManager:Fn(t,e,n,s)}),r[M]}function Ae(){return typeof global<"u"?global:window}function Me(t){let e=null;return()=>(e==null&&(e=t()),e)}function Qn(t,e){return t.filter(n=>n!==e)}function Jn(t,e){const n=new Set,s=i=>n.add(i);t.forEach(s),e.forEach(s);const r=[];return n.forEach(i=>r.push(i)),r}class Kn{enter(e){const n=this.entered.length,s=r=>this.isNodeInDocument(r)&&(!r.contains||r.contains(e));return this.entered=Jn(this.entered.filter(s),[e]),n===0&&this.entered.length>0}leave(e){const n=this.entered.length;return this.entered=Qn(this.entered.filter(this.isNodeInDocument),e),n>0&&this.entered.length===0}reset(){this.entered=[]}constructor(e){this.entered=[],this.isNodeInDocument=e}}class Zn{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach(e=>{Object.defineProperty(this.item,e,{configurable:!0,enumerable:!0,get(){return console.warn(`Browser doesn't allow reading "${e}" until the drop event.`),null}})})}loadDataTransfer(e){if(e){const n={};Object.keys(this.config.exposeProperties).forEach(s=>{const r=this.config.exposeProperties[s];r!=null&&(n[s]={value:r(e,this.config.matchesTypes),configurable:!0,enumerable:!0})}),Object.defineProperties(this.item,n)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(e,n){return n===e.getSourceId()}endDrag(){}constructor(e){this.config=e,this.item={},this.initializeExposedProperties()}}const _e="__NATIVE_FILE__",Be="__NATIVE_URL__",Ue="__NATIVE_TEXT__",$e="__NATIVE_HTML__",pe=Object.freeze(Object.defineProperty({__proto__:null,FILE:_e,HTML:$e,TEXT:Ue,URL:Be},Symbol.toStringTag,{value:"Module"}));function W(t,e,n){const s=e.reduce((r,i)=>r||t.getData(i),"");return s??n}const X={[_e]:{exposeProperties:{files:t=>Array.prototype.slice.call(t.files),items:t=>t.items,dataTransfer:t=>t},matchesTypes:["Files"]},[$e]:{exposeProperties:{html:(t,e)=>W(t,e,""),dataTransfer:t=>t},matchesTypes:["Html","text/html"]},[Be]:{exposeProperties:{urls:(t,e)=>W(t,e,"").split(`
`),dataTransfer:t=>t},matchesTypes:["Url","text/uri-list"]},[Ue]:{exposeProperties:{text:(t,e)=>W(t,e,""),dataTransfer:t=>t},matchesTypes:["Text","text/plain"]}};function er(t,e){const n=X[t];if(!n)throw new Error(`native type ${t} has no configuration`);const s=new Zn(n);return s.loadDataTransfer(e),s}function z(t){if(!t)return null;const e=Array.prototype.slice.call(t.types||[]);return Object.keys(X).filter(n=>{const s=X[n];return s?.matchesTypes?s.matchesTypes.some(r=>e.indexOf(r)>-1):!1})[0]||null}const tr=Me(()=>/firefox/i.test(navigator.userAgent)),Ge=Me(()=>!!window.safari);class me{interpolate(e){const{xs:n,ys:s,c1s:r,c2s:i,c3s:a}=this;let c=n.length-1;if(e===n[c])return s[c];let d=0,l=a.length-1,g;for(;d<=l;){g=Math.floor(.5*(d+l));const u=n[g];if(u<e)d=g+1;else if(u>e)l=g-1;else return s[g]}c=Math.max(0,l);const v=e-n[c],p=v*v;return s[c]+r[c]*v+i[c]*p+a[c]*v*p}constructor(e,n){const{length:s}=e,r=[];for(let u=0;u<s;u++)r.push(u);r.sort((u,y)=>e[u]<e[y]?-1:1);const i=[],a=[];let c,d;for(let u=0;u<s-1;u++)c=e[u+1]-e[u],d=n[u+1]-n[u],i.push(c),a.push(d/c);const l=[a[0]];for(let u=0;u<i.length-1;u++){const y=a[u],S=a[u+1];if(y*S<=0)l.push(0);else{c=i[u];const T=i[u+1],f=c+T;l.push(3*f/((f+T)/y+(f+c)/S))}}l.push(a[a.length-1]);const g=[],v=[];let p;for(let u=0;u<l.length-1;u++){p=a[u];const y=l[u],S=1/i[u],T=y+l[u+1]-p-p;g.push((p-y-T)*S),v.push(T*S*S)}this.xs=e,this.ys=n,this.c1s=l,this.c2s=g,this.c3s=v}}const nr=1;function He(t){const e=t.nodeType===nr?t:t.parentElement;if(!e)return null;const{top:n,left:s}=e.getBoundingClientRect();return{x:s,y:n}}function A(t){return{x:t.clientX,y:t.clientY}}function rr(t){var e;return t.nodeName==="IMG"&&(tr()||!(!((e=document.documentElement)===null||e===void 0)&&e.contains(t)))}function sr(t,e,n,s){let r=t?e.width:n,i=t?e.height:s;return Ge()&&t&&(i/=window.devicePixelRatio,r/=window.devicePixelRatio),{dragPreviewWidth:r,dragPreviewHeight:i}}function ir(t,e,n,s,r){const i=rr(e),c=He(i?t:e),d={x:n.x-c.x,y:n.y-c.y},{offsetWidth:l,offsetHeight:g}=t,{anchorX:v,anchorY:p}=s,{dragPreviewWidth:u,dragPreviewHeight:y}=sr(i,e,l,g),S=()=>{let se=new me([0,.5,1],[d.y,d.y/g*y,d.y+y-g]).interpolate(p);return Ge()&&i&&(se+=(window.devicePixelRatio-1)*y),se},T=()=>new me([0,.5,1],[d.x,d.x/l*u,d.x+u-l]).interpolate(v),{offsetX:f,offsetY:E}=r,C=f===0||f,We=E===0||E;return{x:C?f:T(),y:We?E:S()}}class or{get window(){if(this.globalContext)return this.globalContext;if(typeof window<"u")return window}get document(){var e;return!((e=this.globalContext)===null||e===void 0)&&e.document?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var e;return((e=this.optionsArgs)===null||e===void 0?void 0:e.rootElement)||this.window}constructor(e,n){this.ownerDocument=null,this.globalContext=e,this.optionsArgs=n}}function ar(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ve(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){ar(t,r,n[r])})}return t}class cr{profile(){var e,n;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:((e=this.dragStartSourceIds)===null||e===void 0?void 0:e.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:((n=this.dragOverTargetIds)===null||n===void 0?void 0:n.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const e=this.rootElement;if(e!==void 0){if(e.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");e.__isReactDndBackendSetUp=!0,this.addEventListeners(e)}}teardown(){const e=this.rootElement;if(e!==void 0&&(e.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var n;(n=this.window)===null||n===void 0||n.cancelAnimationFrame(this.asyncEndDragFrameId)}}connectDragPreview(e,n,s){return this.sourcePreviewNodeOptions.set(e,s),this.sourcePreviewNodes.set(e,n),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDragSource(e,n,s){this.sourceNodes.set(e,n),this.sourceNodeOptions.set(e,s);const r=a=>this.handleDragStart(a,e),i=a=>this.handleSelectStart(a);return n.setAttribute("draggable","true"),n.addEventListener("dragstart",r),n.addEventListener("selectstart",i),()=>{this.sourceNodes.delete(e),this.sourceNodeOptions.delete(e),n.removeEventListener("dragstart",r),n.removeEventListener("selectstart",i),n.setAttribute("draggable","false")}}connectDropTarget(e,n){const s=a=>this.handleDragEnter(a,e),r=a=>this.handleDragOver(a,e),i=a=>this.handleDrop(a,e);return n.addEventListener("dragenter",s),n.addEventListener("dragover",r),n.addEventListener("drop",i),()=>{n.removeEventListener("dragenter",s),n.removeEventListener("dragover",r),n.removeEventListener("drop",i)}}addEventListeners(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const e=this.monitor.getSourceId(),n=this.sourceNodeOptions.get(e);return ve({dropEffect:this.altKeyPressed?"copy":"move"},n||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const e=this.monitor.getSourceId(),n=this.sourcePreviewNodeOptions.get(e);return ve({anchorX:.5,anchorY:.5,captureDraggingState:!1},n||{})}isDraggingNativeItem(){const e=this.monitor.getItemType();return Object.keys(pe).some(n=>pe[n]===e)}beginDragNativeItem(e,n){this.clearCurrentDragSourceNode(),this.currentNativeSource=er(e,n),this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e;const n=1e3;this.mouseMoveTimeoutTimer=setTimeout(()=>{var s;return(s=this.rootElement)===null||s===void 0?void 0:s.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)},n)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var e;(e=this.window)===null||e===void 0||e.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(e,n){e.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(n))}handleDragEnter(e,n){this.dragEnterTargetIds.unshift(n)}handleDragOver(e,n){this.dragOverTargetIds===null&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(n)}handleDrop(e,n){this.dropTargetIds.unshift(n)}constructor(e,n,s){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=r=>{const i=this.sourceNodes.get(r);return i&&He(i)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=r=>!!(r&&this.document&&this.document.body&&this.document.body.contains(r)),this.endDragIfSourceWasRemovedFromDOM=()=>{const r=this.currentDragSourceNode;r==null||this.isNodeInDocument(r)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=r=>{this.hoverRafId===null&&typeof requestAnimationFrame<"u"&&(this.hoverRafId=requestAnimationFrame(()=>{this.monitor.isDragging()&&this.actions.hover(r||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null}))},this.cancelHover=()=>{this.hoverRafId!==null&&typeof cancelAnimationFrame<"u"&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=r=>{if(r.defaultPrevented)return;const{dragStartSourceIds:i}=this;this.dragStartSourceIds=null;const a=A(r);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(i||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:a});const{dataTransfer:c}=r,d=z(c);if(this.monitor.isDragging()){if(c&&typeof c.setDragImage=="function"){const g=this.monitor.getSourceId(),v=this.sourceNodes.get(g),p=this.sourcePreviewNodes.get(g)||v;if(p){const{anchorX:u,anchorY:y,offsetX:S,offsetY:T}=this.getCurrentSourcePreviewNodeOptions(),C=ir(v,p,a,{anchorX:u,anchorY:y},{offsetX:S,offsetY:T});c.setDragImage(p,C.x,C.y)}}try{c?.setData("application/json",{})}catch{}this.setCurrentDragSourceNode(r.target);const{captureDraggingState:l}=this.getCurrentSourcePreviewNodeOptions();l?this.actions.publishDragSource():setTimeout(()=>this.actions.publishDragSource(),0)}else if(d)this.beginDragNativeItem(d);else{if(c&&!c.types&&(r.target&&!r.target.hasAttribute||!r.target.hasAttribute("draggable")))return;r.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=r=>{if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()){var i;(i=this.currentNativeSource)===null||i===void 0||i.loadDataTransfer(r.dataTransfer)}if(!this.enterLeaveCounter.enter(r.target)||this.monitor.isDragging())return;const{dataTransfer:c}=r,d=z(c);d&&this.beginDragNativeItem(d,c)},this.handleTopDragEnter=r=>{const{dragEnterTargetIds:i}=this;if(this.dragEnterTargetIds=[],!this.monitor.isDragging())return;this.altKeyPressed=r.altKey,i.length>0&&this.actions.hover(i,{clientOffset:A(r)}),i.some(c=>this.monitor.canDropOnTarget(c))&&(r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=r=>{if(this.dragOverTargetIds=[],this.isDraggingNativeItem()){var i;(i=this.currentNativeSource)===null||i===void 0||i.loadDataTransfer(r.dataTransfer)}},this.handleTopDragOver=r=>{const{dragOverTargetIds:i}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging()){r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect="none");return}this.altKeyPressed=r.altKey,this.lastClientOffset=A(r),this.scheduleHover(i),(i||[]).some(c=>this.monitor.canDropOnTarget(c))?(r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?r.preventDefault():(r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=r=>{this.isDraggingNativeItem()&&r.preventDefault(),this.enterLeaveCounter.leave(r.target)&&(this.isDraggingNativeItem()&&setTimeout(()=>this.endDragNativeItem(),0),this.cancelHover())},this.handleTopDropCapture=r=>{if(this.dropTargetIds=[],this.isDraggingNativeItem()){var i;r.preventDefault(),(i=this.currentNativeSource)===null||i===void 0||i.loadDataTransfer(r.dataTransfer)}else z(r.dataTransfer)&&r.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=r=>{const{dropTargetIds:i}=this;this.dropTargetIds=[],this.actions.hover(i,{clientOffset:A(r)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=r=>{const i=r.target;typeof i.dragDrop=="function"&&(i.tagName==="INPUT"||i.tagName==="SELECT"||i.tagName==="TEXTAREA"||i.isContentEditable||(r.preventDefault(),i.dragDrop()))},this.options=new or(n,s),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enterLeaveCounter=new Kn(this.isNodeInDocument)}}const lr=function(e,n,s){return new cr(e,n,s)},ur="5.18.0",dr={version:ur},gr=()=>{const t=Se("GuidedTourModal",C=>C),{currentStep:e,guidedTourState:n,setCurrentStep:s,setStepState:r,isGuidedTourVisible:i,setSkipped:a}=t,{formatMessage:c}=w(),{trackUsage:d}=Q();if(!e||!i)return null;const l=Qe(xt,e),g=Object.keys(n),[v,p]=e.split("."),u=g.indexOf(v),y=Object.keys(n[v]).indexOf(p),S=u<g.length-1,T=y<Object.keys(n[v]).length-1,f=()=>{r(e,!0),l&&d(l.trackingEvent),s(null)},E=()=>{a(!0),s(null),d("didSkipGuidedtour")};return o.jsx(Je,{children:o.jsx(fr,{onClick:f,padding:8,justifyContent:"center",children:o.jsx(Ke,{onEscape:f,children:o.jsxs(m,{direction:"column",alignItems:"stretch",background:"neutral0",width:"66rem",shadow:"popupShadow",hasRadius:!0,padding:4,gap:8,role:"dialog","aria-modal":!0,onClick:C=>C.stopPropagation(),children:[o.jsx(m,{justifyContent:"flex-end",children:o.jsx(Ze,{onClick:f,withTooltip:!1,label:c({id:"app.utils.close-label",defaultMessage:"Close"}),children:o.jsx(et,{})})}),o.jsx(I,{paddingLeft:7,paddingRight:7,paddingBottom:!T&&!S?8:0,children:o.jsx(hr,{title:l&&"title"in l?l.title:void 0,cta:l&&"cta"in l?l.cta:void 0,onCtaClick:f,sectionIndex:u,stepIndex:y,hasSectionAfter:S,children:l&&"content"in l&&o.jsx(pr,{...l.content})})}),!(!T&&!S)&&o.jsx(m,{justifyContent:"flex-end",children:o.jsx(Te,{variant:"tertiary",onClick:E,children:c({id:"app.components.GuidedTour.skip",defaultMessage:"Skip the tour"})})})]})})})})},fr=x(m)`
  position: fixed;
  z-index: 4;
  inset: 0;
  /* this is theme.colors.neutral800 with opacity */
  background: ${({theme:t})=>`${t.colors.neutral800}1F`};
`,hr=({title:t,children:e,cta:n,onCtaClick:s,sectionIndex:r,stepIndex:i,hasSectionAfter:a})=>{const{formatMessage:c}=w(),d=r>0,l=i>0,g=r+1;return o.jsxs(o.Fragment,{children:[o.jsxs(m,{alignItems:"stretch",children:[o.jsx(m,{marginRight:8,justifyContent:"center",minWidth:"3rem",children:d&&o.jsx(F,{state:j.IS_DONE,minHeight:"2.4rem"})}),o.jsx(O,{variant:"sigma",textColor:"primary600",children:c({id:"app.components.GuidedTour.title",defaultMessage:"3 steps to get started"})})]}),o.jsxs(m,{children:[o.jsx(m,{marginRight:8,minWidth:"3rem",children:o.jsx(ce,{state:l?j.IS_DONE:j.IS_ACTIVE,paddingTop:3,paddingBottom:3,children:r+1})}),t&&o.jsx(O,{variant:"alpha",fontWeight:"bold",textColor:"neutral800",tag:"h3",id:"title",children:c(t)})]}),o.jsxs(m,{alignItems:"stretch",children:[o.jsx(m,{marginRight:8,direction:"column",justifyContent:"center",minWidth:"3rem",children:a&&o.jsxs(o.Fragment,{children:[o.jsx(F,{state:j.IS_DONE}),l&&o.jsx(ce,{state:j.IS_ACTIVE,paddingTop:3,children:g+1})]})}),o.jsxs(I,{children:[e,n&&(n.target?o.jsx(De,{tag:xe,endIcon:o.jsx(oe,{}),onClick:s,to:n.target,children:c(n.title)}):o.jsx(Te,{endIcon:o.jsx(oe,{}),onClick:s,children:c(n.title)}))]})]}),l&&a&&o.jsx(I,{paddingTop:3,children:o.jsx(m,{marginRight:8,justifyContent:"center",width:"3rem",children:o.jsx(F,{state:j.IS_DONE,minHeight:"2.4rem"})})})]})},pr=({id:t,defaultMessage:e})=>{const{formatMessage:n}=w();return o.jsx(m,{direction:"column",alignItems:"stretch",gap:4,paddingBottom:6,children:n({id:t,defaultMessage:e},{documentationLink:mr,b:vr,p:yr,light:Sr,ul:Tr,li:xr})})},mr=t=>o.jsx(O,{tag:"a",textColor:"primary600",target:"_blank",rel:"noopener noreferrer",href:"https://docs.strapi.io/developer-docs/latest/developer-resources/database-apis-reference/rest-api.html#api-parameters",children:t}),vr=t=>o.jsx(O,{fontWeight:"semiBold",children:t}),yr=t=>o.jsx(O,{children:t}),Sr=t=>o.jsx(O,{textColor:"neutral600",children:t}),Tr=t=>o.jsx(I,{paddingLeft:6,children:o.jsx("ul",{children:t})}),Dr=x.li`
  list-style: disc;
  &::marker {
    color: ${({theme:t})=>t.colors.neutral800};
  }
`,xr=t=>o.jsx(Dr,{children:t}),br=x(m)`
  border-right: 1px solid ${({theme:t})=>t.colors.neutral150};
`,Er=t=>o.jsx(br,{alignItems:"normal",tag:"nav",background:"neutral0",direction:"column",height:"100vh",position:"sticky",top:0,zIndex:2,width:10,...t}),Or=x(m)`
  svg,
  img {
    border-radius: ${({theme:t})=>t.borderRadius};
    object-fit: contain;
    height: 2.4rem;
    width: 2.4rem;
  }
`,Ir=()=>{const{formatMessage:t}=w(),{logos:{menu:e}}=be("LeftMenu");return o.jsx(I,{padding:3,children:o.jsxs(Or,{direction:"column",justifyContent:"center",width:"3.2rem",height:"3.2rem",children:[o.jsx("img",{src:e.custom?.url||e.default,alt:t({id:"app.components.LeftMenu.logo.alt",defaultMessage:"Application logo"}),width:"100%",height:"100%"}),o.jsxs(Ee,{children:[o.jsx("span",{children:t({id:"app.components.LeftMenu.navbrand.title",defaultMessage:"Strapi Dashboard"})}),o.jsx("span",{children:t({id:"app.components.LeftMenu.navbrand.workplace",defaultMessage:"Workplace"})})]})]})})},wr=x(xe)`
  text-decoration: none;
  display: flex;
  border-radius: ${({theme:t})=>t.borderRadius};
  background: ${({theme:t})=>t.colors.neutral0};
  color: ${({theme:t})=>t.colors.neutral500};
  position: relative;
  width: fit-content;
  padding-block: 0.6rem;
  padding-inline: 0.6rem;

  &:hover {
    svg path {
      fill: ${({theme:t})=>t.colors.neutral600};
    }
    background: ${({theme:t})=>t.colors.neutral100};
  }

  &.active {
    svg path {
      fill: ${({theme:t})=>t.colors.primary600};
    }
    background: ${({theme:t})=>t.colors.primary100};
  }
`,Cr=t=>{switch(t.toString().replace(/\//g,"")){case"content-manager":return H.contentTypeBuilder.Finish;case"":return H.apiTokens.Finish;case"settings":return H.contentManager.Finish;default:return D.Fragment}},jr=({children:t,...e})=>{const n=Cr(e.to);return o.jsx(n,{children:o.jsx(wr,{...e,children:t})})},Lr=({children:t,label:e,position:n="right"})=>o.jsx(Oe,{side:n,label:e,delayDuration:0,children:o.jsx("span",{children:t})}),Nr=({label:t,children:e})=>e?o.jsx(tt,{label:t,children:e}):null,Pr=x(Ie)`
  /* override default badge styles to change the border radius of the Base element in the Design System */
  border-radius: ${({theme:t})=>t.spaces[10]};
  height: 2rem;
`,kr=({children:t,label:e,...n})=>t?o.jsx(Pr,{position:"absolute",top:"-0.8rem",left:"1.7rem","aria-label":e,active:!1,...n,children:t}):null,R={Link:jr,Tooltip:Lr,Icon:Nr,Badge:kr},Rr=x(N.Trigger)`
  height: ${({theme:t})=>t.spaces[7]};
  width: ${({theme:t})=>t.spaces[7]};
  border: none;
  border-radius: 50%;
  padding: 0;
  overflow: hidden;
`,Ar=x(N.Content)`
  max-height: fit-content;
  width: 200px;
`,Mr=x(m)`
  && {
    padding: ${({theme:t})=>t.spaces[3]};
  }
  align-items: flex-start;
`,_r=x(m)`
  display: flex;
  flex-wrap: wrap;
  gap: ${({theme:t})=>t.spaces[1]};

  width: 100%;
`,Br=x(O)`
  word-break: break-word;
  margin-bottom: ${({theme:t})=>t.spaces[3]};
`,Ur=({children:t,initials:e,...n})=>{const{formatMessage:s}=w(),r=nt(),i=P("User",l=>l.user),a=P("Logout",l=>l.logout),c=()=>{r("/me")},d=()=>{a(),r("/auth/login")};return o.jsx(m,{justifyContent:"center",padding:3,borderStyle:"solid",borderWidth:"1px 0 0 0",borderColor:"neutral150",...n,children:o.jsxs(N.Root,{children:[o.jsxs(Rr,{endIcon:null,fullWidth:!0,justifyContent:"center",children:[o.jsx(rt.Item,{delayMs:0,fallback:e}),o.jsx(Ee,{tag:"span",children:t})]}),o.jsxs(Ar,{popoverPlacement:"top-start",zIndex:3,children:[o.jsxs(Mr,{direction:"column",gap:0,alignItems:"flex-start",children:[o.jsx(O,{variant:"omega",fontWeight:"bold",textTransform:"none",children:t}),o.jsx(Br,{variant:"pi",textColor:"neutral600",children:i?.email}),o.jsx(_r,{children:i?.roles?.map(l=>o.jsx(Ie,{children:l.name},l.id))})]}),o.jsx(N.Separator,{}),o.jsx(N.Item,{onSelect:c,children:s({id:"global.profile.settings",defaultMessage:"Profile settings"})}),o.jsx(N.Item,{variant:"danger",onSelect:d,color:"danger600",children:s({id:"app.components.LeftMenu.logout",defaultMessage:"Log out"})})]})]})})},$r=({percentage:t})=>{const e=st(),n=45,s=2*Math.PI*n,r=s*(1-t/100);return o.jsxs("svg",{width:"32",height:"32",viewBox:"0 0 100 100",children:[o.jsx("defs",{children:o.jsxs("linearGradient",{id:"progressGradient",x1:"0%",y1:"0%",x2:"100%",y2:"0%",children:[o.jsx("stop",{offset:"0%",stopColor:e.colors.primary600}),o.jsx("stop",{offset:"100%",stopColor:e.colors.alternative600})]})}),o.jsx("circle",{cx:"50",cy:"50",r:n,stroke:"#ccc",strokeWidth:"10",fill:"none"}),o.jsx("circle",{cx:"50",cy:"50",r:n,stroke:"url(#progressGradient)",strokeWidth:"10",fill:"none",strokeDasharray:s,strokeDashoffset:r,transform:"rotate(-90 50 50)",strokeLinecap:"round"}),o.jsx("svg",{x:"35",y:"25",width:"50",height:"50",viewBox:"0 0 32 32",children:o.jsx("path",{fill:"url(#progressGradient)",d:"m21.731 14.683-14 15a1 1 0 0 1-1.711-.875l1.832-9.167L.65 16.936a1 1 0 0 1-.375-1.625l14-15a1 1 0 0 1 1.71.875l-1.837 9.177 7.204 2.7a1 1 0 0 1 .375 1.62z"})})]})},Gr=()=>{const{formatMessage:t}=w(),{license:e,isError:n,isLoading:s}=we(),r=Ce(void 0,{skip:!e?.isTrial});if(n||s||!e?.isTrial||r.isLoading||r.isError||!r.data||!r.data.trialEndsAt)return null;const i=new Date(r.data.trialEndsAt),a=new Date,c=Lt(i,Nt()),d=1e3*60*60*24,l=i.getTime()-a.getTime(),g=Math.ceil(l/d)<=0?0:Math.ceil(l/d);return o.jsx(m,{justifyContent:"center",padding:3,children:o.jsx(Oe,{label:t(c?{id:"app.components.LeftMenu.trialCountdown.endedAt",defaultMessage:"Your trial ended on {date}"}:{id:"app.components.LeftMenu.trialCountdown.endsAt",defaultMessage:"Your trial ends on {date}"},{date:Ct(new Date(r.data.trialEndsAt),"PPP")}),side:"right",children:o.jsx("div",{"data-testid":"trial-countdown",children:o.jsx($r,{percentage:(30-g)*100/30})})})})},Hr=t=>t.sort((e,n)=>{const s=e.position??6,r=n.position??6;return s<r?-1:1}),Fr=x(R.Badge)`
  span {
    color: ${({theme:t})=>t.colors.neutral0};
  }
`,Wr=x(R.Badge)`
  background-color: transparent;
`,zr=x(m)`
  overflow-y: auto;
`,qr=({generalSectionLinks:t,pluginsSectionLinks:e})=>{const n=P("AuthenticatedApp",u=>u.user),{trackUsage:s}=Q(),{pathname:r}=it(),i=Ot(n),{formatMessage:a,locale:c}=w(),d=ot(c,{sensitivity:"base"}),l=It(n),g=u=>{s("willNavigate",{from:r,to:u})},v=[...e,...t].sort((u,y)=>d.compare(a(u.intlLabel),a(y.intlLabel))),p=Hr(v);return o.jsxs(Er,{children:[o.jsx(Ir,{}),o.jsx(at,{}),o.jsx(zr,{tag:"ul",gap:3,direction:"column",flex:1,paddingTop:3,paddingBottom:3,children:p.length>0?p.map(u=>{const y=u.icon,S=u?.licenseOnly?o.jsx(ct,{fill:"primary600"}):void 0,T=u.notificationsCount&&u.notificationsCount>0?u.notificationsCount.toString():void 0,f=a(u.intlLabel);return o.jsx(m,{tag:"li",children:o.jsx(R.Tooltip,{label:f,children:o.jsxs(R.Link,{to:u.to,onClick:()=>g(u.to),"aria-label":f,children:[o.jsx(R.Icon,{label:f,children:o.jsx(y,{width:"20",height:"20",fill:"neutral500"})}),S?o.jsx(Wr,{label:"locked",textColor:"neutral500",paddingLeft:0,paddingRight:0,children:S}):T?o.jsx(Fr,{label:T,backgroundColor:"primary600",width:"2.3rem",color:"neutral0",children:T}):null]})})},u.to)}):null}),o.jsx(Gr,{}),o.jsx(Ur,{initials:l,children:i})]})},Vr=({children:t})=>{const e=J("PluginsInitializer",a=>a.plugins),[{plugins:n},s]=D.useReducer(Yr,Fe,()=>Xr(e)),r=D.useRef(a=>{s({type:"SET_PLUGIN_READY",pluginId:a})});if(Object.keys(n).some(a=>n[a].isReady===!1)){const a=Object.keys(n).reduce((c,d)=>{const l=n[d].initializer;if(l){const g=n[d].pluginId;c.push(o.jsx(l,{setPlugin:r.current},g))}return c},[]);return o.jsxs(o.Fragment,{children:[a,o.jsx(je.Loading,{})]})}return t},Fe={plugins:{}},Yr=(t=Fe,e)=>lt(t,n=>{switch(e.type){case"SET_PLUGIN_READY":{n.plugins[e.pluginId].isReady=!0;break}default:return n}}),Xr=t=>({plugins:t}),Qr=x(m)`
  background: linear-gradient(
    90deg,
    ${({theme:t})=>t.colors.primary600} 0%,
    ${({theme:t})=>t.colors.alternative600} 121.48%
  );
`,Jr=({isTrialEndedRecently:t})=>{const{formatMessage:e}=w();return o.jsx(Qr,{width:"100%",justifyContent:"center",children:o.jsxs(m,{justifyContent:"center",alignItems:"center",width:"100%",paddingTop:2,paddingBottom:2,paddingLeft:10,paddingRight:10,gap:2,children:[o.jsxs(I,{children:[o.jsx(O,{variant:"delta",fontWeight:"bold",textColor:"neutral0",textAlign:"center",fontSize:2,children:e(t?{id:"app.components.UpsellBanner.intro.ended",defaultMessage:"Your trial has ended: "}:{id:"app.components.UpsellBanner.intro",defaultMessage:"Access to Growth plan features: "})}),o.jsx(O,{variant:"delta",textColor:"neutral0",textAlign:"center",paddingRight:4,fontSize:2,children:e(t?{id:"app.components.UpsellBanner.text.ended",defaultMessage:"Keep access to Growth features by upgrading now."}:{id:"app.components.UpsellBanner.text",defaultMessage:"As part of your trial, you can explore premium tools such as Content History, Releases, and Single Sign-On (SSO)."})})]}),o.jsx(I,{children:o.jsx(De,{width:"max-content",variant:"tertiary",href:"https://strapi.chargebeeportal.com",target:"_blank",children:e(t?{id:"app.components.UpsellBanner.button.ended",defaultMessage:"Keep Growth plan"}:{id:"app.components.UpsellBanner.button",defaultMessage:"Upgrade now"})})})]})})},Kr=()=>{const{license:t}=we(),[e,n]=ut("STRAPI_FREE_TRIAL_ENDS_AT",void 0),s=bt(new Date,7),r=Ce(void 0,{skip:!t?.isTrial});D.useEffect(()=>{r.data?.trialEndsAt&&n(r.data.trialEndsAt)},[r.data?.trialEndsAt]);const i=!!(!t?.isTrial&&!window.strapi.isEE&&e&&Et(new Date(e),s));return r.data?.trialEndsAt||i?o.jsx(Jr,{isTrialEndedRecently:i}):null},Zr=t=>{const e=P("useMenu",c=>c.checkUserHasPermissions),n=J("useMenu",c=>c.menu),s=dt(c=>c.admin_app.permissions),[r,i]=D.useState({generalSectionLinks:[{icon:gt,intlLabel:{id:"global.home",defaultMessage:"Home"},to:"/",permissions:[],position:0},{icon:ft,intlLabel:{id:"global.marketplace",defaultMessage:"Marketplace"},to:"/marketplace",permissions:s.marketplace?.main??[],position:7},{icon:ht,intlLabel:{id:"global.settings",defaultMessage:"Settings"},to:"/settings",permissions:[],notificationsCount:0,position:9}],pluginsSectionLinks:[],isLoading:!0}),a=D.useRef(r.generalSectionLinks);return D.useEffect(()=>{async function c(){const d=await ts(n,e),l=await es(a.current,t,e);i(g=>({...g,generalSectionLinks:l,pluginsSectionLinks:d,isLoading:!1}))}c()},[i,a,n,s,t,e]),r},es=async(t,e=!1,n)=>{const s=await Promise.all(t.map(({permissions:c})=>n(c))),r=t.filter((c,d)=>s[d].length>0),i=r.findIndex(c=>c.to==="/settings");if(i===-1)return[];const a=pt(r);return a[i].notificationsCount=e?1:0,a},ts=async(t,e)=>{const n=await Promise.all(t.map(({permissions:r})=>e(r)));return t.filter((r,i)=>n[i].length>0)},{version:q}=dr,ns=()=>{const t=Se("AdminLayout",f=>f.setGuidedTourVisibility),{formatMessage:e}=w(),n=P("AuthenticatedApp",f=>f.user),[s,r]=D.useState(),{showReleaseNotification:i}=be("AuthenticatedApp"),{data:a,isLoading:c}=mt(),[d,l]=D.useState(q);D.useEffect(()=>{i&&fetch("https://api.github.com/repos/strapi/strapi/releases/latest").then(async f=>{if(!f.ok)return;const E=await f.json();if(!E.tag_name)throw new Error;l(E.tag_name)}).catch(()=>{})},[i]);const g=P("AuthenticatedApp",f=>f.user?.roles);D.useEffect(()=>{g&&g.find(({code:E})=>E==="strapi-super-admin")&&a?.autoReload&&t(!0)},[g,a?.autoReload,t]),D.useEffect(()=>{wt(n).then(f=>{f&&r(f)})},[n]);const{trackUsage:v}=Q(),{isLoading:p,generalSectionLinks:u,pluginsSectionLinks:y}=Zr(ye(q,d)),S=J("TrackingProvider",f=>f.widgets.getAll),T=a?.projectId;return D.useEffect(()=>{T&&v("didAccessAuthenticatedAdministration",{registeredWidgets:S().map(f=>f.uid),projectId:T})},[T,S,v]),p||c?o.jsx(je.Loading,{}):o.jsxs(vt,{...a,userId:s,latestStrapiReleaseTag:d,shouldUpdateStrapi:ye(q,d),children:[o.jsx(yt,{}),o.jsx(Vr,{children:o.jsx(Vn,{backend:lr,children:o.jsxs(I,{background:"neutral100",children:[o.jsx(St,{children:e({id:"skipToContent",defaultMessage:"Skip to content"})}),o.jsxs(m,{alignItems:"flex-start",children:[o.jsx(qr,{generalSectionLinks:u,pluginsSectionLinks:y}),o.jsxs(I,{flex:1,children:[o.jsx(Kr,{}),o.jsx(Tt,{}),o.jsx(gr,{})]})]})]})})})]})},us=()=>o.jsx(jt,{children:o.jsx(ns,{})}),ye=(t,e="")=>!ae(t)||!ae(e)?!1:Dt(t,e);export{ns as AdminLayout,us as PrivateAdminLayout};
