import{r as v,a as h,cA as w,cB as E,j as e,u as B,V as M,b as R,b3 as I,bE as a,T as g,e as A,g as L,bt as b,$,I as N,bm as P,bu as U,au as F,k as O,at as H}from"./strapi-z7ApxZZq.js";const K=s=>v.useEffect(s,W),W=[],q=["years","months","days","hours","minutes","seconds"],k=v.forwardRef(({timestamp:s,customIntervals:n=[],...l},i)=>{const{formatRelativeTime:d,formatDate:r,formatTime:c}=h(),u=w({start:s,end:Date.now()}),x=q.find(o=>u[o]>0&&Object.keys(u).includes(o)),j=E(s)?-u[x]:u[x],m=n.find(o=>u[o.unit]<o.threshold),p=m?m.text:d(j,x,{numeric:"auto"});return e.jsx("time",{ref:i,dateTime:s.toISOString(),role:"time",title:`${r(s)} ${c(s)}`,...l,children:p})}),X=({permissions:s,headers:n=[],isLoading:l=!1,tokens:i=[],onConfirmDelete:d,tokenType:r})=>{const[{query:c}]=B(),{formatMessage:u,locale:x}=h(),[,j]=c&&c.sort?c.sort.split(":"):[void 0,"ASC"],m=M(),{trackUsage:p}=R(),o=I(x),f=[...i].sort((t,y)=>j==="DESC"?o.compare(y.name,t.name):o.compare(t.name,y.name)),{canDelete:C,canUpdate:T,canRead:D}=s,S=t=>()=>{D&&(p("willEditTokenFromList",{tokenType:r}),m(t.toString()))};return e.jsx(a.Root,{headers:n,rows:f,isLoading:l,children:e.jsxs(a.Content,{children:[e.jsx(a.Head,{children:n.map(t=>e.jsx(a.HeaderCell,{...t},t.name))}),e.jsx(a.Empty,{}),e.jsx(a.Loading,{}),e.jsx(a.Body,{children:f.map(t=>e.jsxs(a.Row,{onClick:S(t.id),children:[e.jsx(a.Cell,{maxWidth:"25rem",children:e.jsx(g,{textColor:"neutral800",fontWeight:"bold",ellipsis:!0,children:t.name})}),e.jsx(a.Cell,{maxWidth:"25rem",children:e.jsx(g,{textColor:"neutral800",ellipsis:!0,children:t.description})}),e.jsx(a.Cell,{children:e.jsx(g,{textColor:"neutral800",children:e.jsx(k,{timestamp:new Date(t.createdAt)})})}),e.jsx(a.Cell,{children:t.lastUsedAt&&e.jsx(g,{textColor:"neutral800",children:e.jsx(k,{timestamp:new Date(t.lastUsedAt),customIntervals:[{unit:"hours",threshold:1,text:u({id:"Settings.apiTokens.lastHour",defaultMessage:"last hour"})}]})})}),T||D||C?e.jsx(a.Cell,{children:e.jsxs(A,{justifyContent:"end",children:[T&&e.jsx(_,{tokenName:t.name,tokenId:t.id}),C&&e.jsx(V,{tokenName:t.name,onClickDelete:()=>d?.(t.id),tokenType:r})]})}):null]},t.id))})]})})},z={edit:{id:"app.component.table.edit",defaultMessage:"Edit {target}"},read:{id:"app.component.table.read",defaultMessage:"Read {target}"}},G=({tokenName:s,tokenId:n,buttonType:l="edit",children:i})=>{const{formatMessage:d}=h();return e.jsx(Q,{tag:F,to:n.toString(),onClick:r=>r.stopPropagation(),title:d(z[l],{target:s}),variant:"ghost",size:"S",children:i})},Q=O(H)`
  padding: 0.7rem;

  & > span {
    display: flex;
  }
`,V=({tokenName:s,onClickDelete:n,tokenType:l})=>{const{formatMessage:i}=h(),{trackUsage:d}=R(),r=()=>{d("willDeleteToken",{tokenType:l}),n()};return e.jsx(b.Root,{children:e.jsxs($,{paddingLeft:1,onClick:c=>c.stopPropagation(),children:[e.jsx(b.Trigger,{children:e.jsx(N,{label:i({id:"global.delete-target",defaultMessage:"Delete {target}"},{target:`${s}`}),name:"delete",variant:"ghost",children:e.jsx(P,{})})}),e.jsx(U,{onConfirm:r})]})})},_=({tokenName:s,tokenId:n})=>e.jsx(G,{tokenName:s,tokenId:n,children:e.jsx(L,{})});export{X as T,K as u};
