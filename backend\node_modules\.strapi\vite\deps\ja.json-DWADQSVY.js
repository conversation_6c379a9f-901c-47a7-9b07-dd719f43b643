import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-type-builder/dist/admin/translations/ja.json.mjs
var from = "from";
var ja = {
  "attribute.boolean": "Boolean",
  "attribute.date": "Date",
  "attribute.email": "Email",
  "attribute.enumeration": "Enumeration",
  "attribute.json": "JSON",
  "attribute.media": "Media",
  "attribute.password": "Password",
  "attribute.relation": "Relation",
  "attribute.text": "Text",
  "form.attribute.item.customColumnName": "カスタム列名",
  "form.attribute.item.customColumnName.description": "これは、データベースのカラム名をAPIのレスポンスのより包括的なフォーマットに変更する場合に便利です",
  "form.attribute.item.defineRelation.fieldName": "フィールド名",
  "form.attribute.item.enumeration.graphql": "GraphQLの名前の上書き",
  "form.attribute.item.enumeration.graphql.description": "GraphQLの既定の生成名をオーバーライドできます",
  "form.attribute.item.enumeration.rules": "Values (one line per value)",
  "form.attribute.item.maximum": "最大値",
  "form.attribute.item.maximumLength": "最大長",
  "form.attribute.item.minimum": "最小値",
  "form.attribute.item.minimumLength": "最小長",
  "form.attribute.item.number.type": "数値形式",
  "form.attribute.item.number.type.decimal": "decimal (ex: 2.22)",
  "form.attribute.item.number.type.float": "float (ex: 3.33333333)",
  "form.attribute.item.number.type.integer": "integer (ex: 10)",
  "form.attribute.item.requiredField": "必須フィールド",
  "form.attribute.item.requiredField.description": "このフィールドが空の場合、エントリを作成することはできません",
  "form.attribute.item.uniqueField": "一意のフィールド",
  "form.attribute.item.uniqueField.description": "同じ内容の既存のエントリがある場合、エントリを作成することはできません",
  "form.attribute.settings.default": "デフォルト値",
  "form.button.cancel": "キャンセル",
  from,
  "modelPage.attribute.relationWith": "関係",
  "plugin.description.long": "APIのデータ構造をモデル化します。数分で新しいフィールドと関係を作成します。ファイルはプロジェクトで自動的に作成され、更新されます。",
  "plugin.description.short": "APIのデータ構造をモデル化します。",
  "popUpForm.navContainer.advanced": "高度な設定",
  "popUpForm.navContainer.base": "基本設定",
  "popUpWarning.bodyMessage.contentType.delete": "このコンテンツタイプを削除してもよろしいですか？",
  "relation.attributeName.placeholder": "例：author、category、tag",
  "relation.manyToMany": "has and belongs to many",
  "relation.manyToOne": "has many",
  "relation.oneToMany": "belongs to many",
  "relation.oneToOne": "has and belongs to one",
  "relation.oneWay": "has one"
};
export {
  ja as default,
  from
};
//# sourceMappingURL=ja.json-DWADQSVY.js.map
