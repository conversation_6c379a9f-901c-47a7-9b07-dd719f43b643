import{a as F,am as ee,ce as te,dB as xe,dC as P,a7 as ne,dD as je,dE as ve,an as Ce,j as e,dF as Ie,e as h,M as q,aU as $,dG as Me,dH as Te,dI as Fe,dJ as we,dK as Ae,r as R,k as se,dL as Ve,K as ie,C as O,$ as v,dk as W,dM as Re,b8 as Le,T as p,au as _,a0 as ae,bH as Q,B as re,b7 as Be,cj as De,dN as ke,dO as Pe,dP as Se,H as S,bQ as ze,V as Ee,b as He,w as Ne,u as H,b9 as G,z as oe,dQ as de,bt as Z,D as K,dR as $e,G as Ue,bu as qe,q as Oe,aC as N,a3 as le,dS as We,a1 as _e,dT as Qe,dU as Ge,dV as Ze,P as V,dW as Ke,dX as Ye,dY as Xe,dz as Je,dZ as et,aB as Y,aE as tt}from"./strapi-z7ApxZZq.js";import{u as ce}from"./hooks-DCxDPOZs.js";const z=se(Ve).attrs({closeLabel:"Close",onClose:()=>{},shadow:"none"})`
  button {
    display: none;
  }
`,nt=se(Q)`
  display: block;

  & > span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
`,st=n=>{const{formatMessage:s}=F(),i=ie(n.name);let a;if(i&&(a=Array.isArray(i.value)?{results:i.value,meta:{missingCount:0}}:i.value),!a||a.results.length===0&&a.meta.missingCount===0)return e.jsxs(e.Fragment,{children:[e.jsx(q.Label,{action:n.labelAction,children:n.label}),e.jsx(v,{marginTop:1,children:e.jsx(z,{variant:"default",children:s({id:"content-manager.history.content.no-relations",defaultMessage:"No relations."})})})]});const{results:t,meta:r}=a;return e.jsxs(v,{children:[e.jsx(q.Label,{children:n.label}),t.length>0&&e.jsx(h,{direction:"column",gap:2,marginTop:1,alignItems:"stretch",children:t.map(o=>{const{targetModel:d}=n.attribute,y=`../${W}/${d}/${o.documentId}`,f=Re(o,n.mainField),C=d==="admin::user";return e.jsxs(h,{paddingTop:2,paddingBottom:2,paddingLeft:4,paddingRight:4,hasRadius:!0,borderColor:"neutral200",background:"neutral150",justifyContent:"space-between",children:[e.jsx(v,{minWidth:0,paddingTop:1,paddingBottom:1,paddingRight:4,children:e.jsx(Le,{label:f,children:C?e.jsx(p,{children:f}):e.jsx(nt,{tag:_,to:y,children:f})})}),e.jsx(ae,{status:o.status})]},o.documentId??o.id)})}),r.missingCount>0&&e.jsx(z,{marginTop:1,variant:"warning",title:s({id:"content-manager.history.content.missing-relations.title",defaultMessage:"{number, plural, =1 {Missing relation} other {{number} missing relations}}"},{number:r.missingCount}),children:s({id:"content-manager.history.content.missing-relations.message",defaultMessage:"{number, plural, =1 {It has} other {They have}} been deleted and can't be restored."},{number:r.missingCount})})]})},it=(n,s)=>{const i=n.split("."),a={};let t=a;return i.forEach((r,o)=>{r==="__proto__"||r==="constructor"||(o===i.length-1?t[r]=s:t[r]=t[r]||{},t=t[r])}),a},at=n=>{const{value:s}=ie(n.name),i=s?.results??[],a=s?.meta??{missingCount:0},{formatMessage:t}=F(),o=ne("CustomMediaInput",d=>d.fields).media;return e.jsxs(h,{direction:"column",gap:2,alignItems:"stretch",children:[e.jsx(O,{method:"PUT",disabled:!0,initialValues:it(n.name,i),children:e.jsx(o,{...n,disabled:!0,multiple:i.length>1})}),a.missingCount>0&&e.jsx(z,{variant:"warning",closeLabel:"Close",onClose:()=>{},title:t({id:"content-manager.history.content.missing-assets.title",defaultMessage:"{number, plural, =1 {Missing asset} other {{number} missing assets}}"},{number:a.missingCount}),children:t({id:"content-manager.history.content.missing-assets.message",defaultMessage:"{number, plural, =1 {It has} other {They have}} been deleted in the Media Library and can't be restored."},{number:a.missingCount})})]})},rt=n=>{if(!R.isValidElement(n))return n;const s=n.props.title.id;return s==="i18n.Field.localized"?R.cloneElement(n,{...n.props,title:{id:"history.content.localized",defaultMessage:"This value is specific to this locale. If you restore this version, the content will not be replaced for other locales."}}):s==="i18n.Field.not-localized"?R.cloneElement(n,{...n.props,title:{id:"history.content.not-localized",defaultMessage:"This value is common to all locales. If you restore this version and save the changes, the content will be replaced for all locales."}}):n},E=({visible:n,hint:s,shouldIgnoreRBAC:i=!1,labelAction:a,...t})=>{const r=rt(a),{formatMessage:o}=F(),d=I("VersionContent",l=>l.selectedVersion),y=I("VersionContent",l=>l.configuration),f=ce(l=>l["content-manager"].app.fieldSizes),{id:C,components:T}=ee(),c=te("InputRenderer",l=>l.disabled),g=xe("isInDynamicZone",l=>l.isInDynamicZone),m=P("InputRenderer",l=>l.canCreateFields),w=P("InputRenderer",l=>l.canReadFields),M=P("InputRenderer",l=>l.canUpdateFields),A=P("InputRenderer",l=>l.canUserAction),b=C?M:m,L=C?w:m,B=A(t.name,L,t.type),u=A(t.name,b,t.type),k=ne("InputRenderer",l=>l.fields),{lazyComponentStore:ge}=je(U(t.attribute)?[t.attribute.customField]:void 0),x=ve(s,t.attribute),{edit:{components:he}}=Ce();if(!n)return null;if(!i&&!B&&!g)return e.jsx(Ie,{hint:x,...t});const j=!u&&!g||t.disabled||c,ye=d.meta.unknownAttributes.added;if(Object.keys(ye).includes(t.name))return e.jsxs(h,{direction:"column",alignItems:"flex-start",gap:1,children:[e.jsx(q.Label,{children:t.label}),e.jsx(z,{width:"100%",closeLabel:"Close",onClose:()=>{},variant:"warning",title:o({id:"content-manager.history.content.new-field.title",defaultMessage:"New field"}),children:o({id:"content-manager.history.content.new-field.message",defaultMessage:"This field didn't exist when this version was saved. If you restore this version, it will be empty."})})]});if(U(t.attribute)){const l=ge[t.attribute.customField];return l?e.jsx(l,{...t,hint:x,labelAction:r,disabled:j}):e.jsx($,{...t,hint:x,labelAction:r,type:t.attribute.customField,disabled:j})}if(t.type==="media")return e.jsx(at,{...t,labelAction:r,disabled:j});const fe=Object.keys(k);if(!U(t.attribute)&&fe.includes(t.type)){const l=k[t.type];return e.jsx(l,{...t,hint:x,labelAction:r,disabled:j})}switch(t.type){case"blocks":return e.jsx(Ae,{...t,hint:x,type:t.type,disabled:j});case"component":const{layout:l}=he[t.attribute.component],[be]=me({layout:[l],metadatas:y.components[t.attribute.component].metadatas,fieldSizes:f,schemaAttributes:T[t.attribute.component].attributes});return e.jsx(we,{...t,layout:[...l,...be||[]],hint:x,labelAction:r,disabled:j,children:D=>e.jsx(E,{...D,shouldIgnoreRBAC:!0})});case"dynamiczone":return e.jsx(Fe,{...t,hint:x,labelAction:r,disabled:j,children:D=>e.jsx(E,{...D,shouldIgnoreRBAC:!0})});case"relation":return e.jsx(st,{...t,hint:x,labelAction:r,disabled:j});case"richtext":return e.jsx(Te,{...t,hint:x,type:t.type,labelAction:r,disabled:j});case"uid":return e.jsx(Me,{...t,hint:x,type:t.type,labelAction:r,disabled:j});case"enumeration":return e.jsx($,{...t,hint:x,labelAction:r,options:t.attribute.enum.map(D=>({value:D})),type:t.customField?"custom-field":t.type,disabled:j});default:const{unique:pt,mainField:xt,...pe}=t;return e.jsx($,{...pe,hint:x,labelAction:r,type:t.customField?"custom-field":t.type,disabled:j})}},U=n=>"customField"in n&&typeof n.customField=="string",ue=n=>n.reduce((s,i)=>i.type==="dynamiczone"?(s.push([i]),s):(s[s.length-1]||s.push([]),s[s.length-1].push(i),s),[]).map(s=>[s]);function me({layout:n,metadatas:s,schemaAttributes:i,fieldSizes:a}){const t=n.flatMap(o=>o.flatMap(d=>d.flatMap(y=>y.name))),r=Object.entries(s).reduce((o,[d,y])=>{if(!t.includes(d)&&y.edit.visible===!0){const f=i[d];o.push({attribute:f,type:f.type,visible:!0,disabled:!0,label:y.edit.label||d,name:d,size:a[f.type].default??12})}return o},[]);return ue(r)}const X=({panel:n})=>{const s=te("Fields",a=>a.values),i=Se();if(n.some(a=>a.some(t=>t.type==="dynamiczone"))){const[a]=n,[t]=a,r=t.attribute?.conditions?.visible;return r&&!i.evaluate(r,s)?null:e.jsx(S.Root,{gap:4,children:e.jsx(S.Item,{col:12,s:12,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(E,{...t})})},t.name)}return e.jsx(v,{hasRadius:!0,background:"neutral0",shadow:"tableShadow",paddingLeft:6,paddingRight:6,paddingTop:6,paddingBottom:6,borderColor:"neutral150",children:e.jsx(h,{direction:"column",alignItems:"stretch",gap:6,children:n.map((a,t)=>{const r=a.filter(o=>{const d=o.attribute?.conditions?.visible;return d?i.evaluate(d,s):!0});return r.length===0?null:e.jsx(S.Root,{gap:4,children:r.map(({size:o,...d})=>e.jsx(S.Item,{col:o,s:12,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(E,{...d})},d.name))},t)})})})},ot=()=>{const{formatMessage:n}=F(),{fieldSizes:s}=ce(c=>c["content-manager"].app),i=I("VersionContent",c=>c.selectedVersion),a=I("VersionContent",c=>c.layout),t=I("VersionContent",c=>c.configuration),r=I("VersionContent",c=>c.schema),o=i.meta.unknownAttributes.removed,d=Object.entries(o).map(([c,g])=>({attribute:g,shouldIgnoreRBAC:!0,type:g.type,visible:!0,disabled:!0,label:c,name:c,size:s[g.type].default??12})),y=ue(d),f=me({metadatas:t.contentType.metadatas,layout:a,schemaAttributes:r.attributes,fieldSizes:s}),{components:C}=ee(),T=R.useMemo(()=>((g,m={})=>w=>{const M={attributes:g};return De(ke(M),Pe(M,m))(w)})(i.schema,C)(i.data),[C,i.data,i.schema]);return e.jsxs(re.Content,{children:[e.jsx(v,{paddingBottom:8,children:e.jsx(O,{disabled:!0,method:"PUT",initialValues:T,children:e.jsx(h,{direction:"column",alignItems:"stretch",gap:6,position:"relative",children:[...a,...f].map((c,g)=>e.jsx(X,{panel:c},g))})})}),d.length>0&&e.jsxs(e.Fragment,{children:[e.jsx(Be,{}),e.jsxs(v,{paddingTop:8,children:[e.jsxs(h,{direction:"column",alignItems:"flex-start",paddingBottom:6,gap:1,children:[e.jsx(p,{variant:"delta",children:n({id:"content-manager.history.content.unknown-fields.title",defaultMessage:"Unknown fields"})}),e.jsx(p,{variant:"pi",children:n({id:"content-manager.history.content.unknown-fields.message",defaultMessage:"These fields have been deleted or renamed in the Content-Type Builder. <b>These fields will not be restored.</b>"},{b:c=>e.jsx(p,{variant:"pi",fontWeight:"bold",children:c})})})]}),e.jsx(O,{disabled:!0,method:"PUT",initialValues:i.data,children:e.jsx(h,{direction:"column",alignItems:"stretch",gap:6,position:"relative",children:y.map((c,g)=>e.jsx(X,{panel:c},g))})})]})]})]})},dt=ze.injectEndpoints({endpoints:n=>({getHistoryVersions:n.query({query(s){return{url:"/content-manager/history-versions",method:"GET",config:{params:s}}},providesTags:["HistoryVersion"]}),restoreVersion:n.mutation({query({params:s,body:i}){return{url:`/content-manager/history-versions/${s.versionId}/restore`,method:"PUT",data:i}},invalidatesTags:(s,i,{documentId:a,collectionType:t,params:r})=>["HistoryVersion",{type:"Document",id:t===W?`${r.contentType}_${a}`:r.contentType}]})})}),{useGetHistoryVersionsQuery:lt,useRestoreVersionMutation:ct}=dt,ut=({headerId:n})=>{const[s,i]=R.useState(!1),a=Ee(),{formatMessage:t,formatDate:r}=F(),{trackUsage:o}=He(),{toggleNotification:d}=Ne(),[{query:y}]=H(),{collectionType:f,slug:C}=G(),[T,{isLoading:c}]=ct(),{allowedActions:g}=oe(de.map(u=>({action:u,subject:C}))),m=I("VersionHeader",u=>u.selectedVersion),w=I("VersionHeader",u=>u.mainField),M=I("VersionHeader",u=>u.schema),A=I("VersionHeader",u=>u.page===1&&u.versions.data[0].id===u.selectedVersion.id),b=m.data[w],L=()=>({pathname:"..",search:N.stringify({plugins:y.plugins},{encode:!1})}),B=async()=>{try{const u=await T({documentId:m.relatedDocumentId,collectionType:f,params:{versionId:m.id,contentType:m.contentType},body:{contentType:m.contentType}});"data"in u&&(a(L(),{relative:"path"}),d({type:"success",title:t({id:"content-manager.restore.success.title",defaultMessage:"Version restored."}),message:t({id:"content-manager.restore.success.message",defaultMessage:"A past version of the content was restored."})}),o("didRestoreHistoryVersion")),"error"in u&&d({type:"danger",message:t({id:"content-manager.history.restore.error.message",defaultMessage:"Could not restore version."})})}catch{d({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred"})})}};return e.jsxs(Z.Root,{open:s,onOpenChange:i,children:[e.jsx(re.BaseHeader,{id:n,title:r(new Date(m.createdAt),{year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric"}),secondaryAction:e.jsx(Ue,{label:t({id:"components.premiumFeature.title",defaultMessage:"Premium feature"})}),subtitle:e.jsx(p,{variant:"epsilon",textColor:"neutral600",children:t({id:"content-manager.history.version.subtitle",defaultMessage:"{hasLocale, select, true {{subtitle}, in {locale}} other {{subtitle}}}"},{hasLocale:!!m.locale,subtitle:`${b||""} (${M.info.singularName})`.trim(),locale:m.locale?.name})}),navigationAction:e.jsx(Q,{startIcon:e.jsx($e,{}),tag:_,to:L(),relative:"path",isExternal:!1,children:t({id:"global.back",defaultMessage:"Back"})}),sticky:!1,primaryAction:e.jsx(Z.Trigger,{children:e.jsx(K,{disabled:!g.canUpdate||A,onClick:()=>{i(!0)},children:t({id:"content-manager.history.restore.confirm.button",defaultMessage:"Restore"})})})}),e.jsx(qe,{onConfirm:B,endAction:e.jsx(K,{variant:"secondary",onClick:B,loading:c,children:t({id:"content-manager.history.restore.confirm.button",defaultMessage:"Restore"})}),children:e.jsxs(h,{direction:"column",alignItems:"center",justifyContent:"center",gap:2,textAlign:"center",children:[e.jsx(h,{justifyContent:"center",children:e.jsx(Oe,{width:"24px",height:"24px",fill:"danger600"})}),e.jsx(p,{children:t({id:"content-manager.history.restore.confirm.title",defaultMessage:"Are you sure you want to restore this version?"})}),e.jsx(p,{children:t({id:"content-manager.history.restore.confirm.message",defaultMessage:"{isDraft, select, true {The restored content will override your draft.} other {The restored content won't be published, it will override the draft and be saved as pending changes. You'll be able to publish the changes at anytime.}}"},{isDraft:m.status==="draft"})})]})})]})},mt=n=>e.jsx(p,{textColor:"primary600",variant:"pi",children:n}),gt=({version:n,isCurrent:s})=>{const{formatDate:i,formatMessage:a}=F(),[{query:t}]=H(),r=t.id===n.id.toString(),o=n.createdBy&&We(n.createdBy);return e.jsxs(h,{direction:"column",alignItems:"flex-start",gap:3,hasRadius:!0,borderWidth:"1px",borderStyle:"solid",borderColor:r?"primary600":"neutral200",color:"neutral800",padding:5,tag:le,to:`?${N.stringify({...t,id:n.id})}`,style:{textDecoration:"none"},children:[e.jsxs(h,{direction:"column",gap:1,alignItems:"flex-start",children:[e.jsx(p,{tag:"h3",fontWeight:"semiBold",children:i(n.createdAt,{day:"numeric",month:"numeric",year:"numeric",hour:"2-digit",minute:"2-digit"})}),e.jsx(p,{tag:"p",variant:"pi",textColor:"neutral600",children:a({id:"content-manager.history.sidebar.versionDescription",defaultMessage:"{distanceToNow}{isAnonymous, select, true {} other { by {author}}}{isCurrent, select, true { <b>(current)</b>} other {}}"},{distanceToNow:e.jsx(_e,{timestamp:new Date(n.createdAt)}),author:o,isAnonymous:!n.createdBy,isCurrent:s,b:mt})})]}),n.status&&e.jsx(ae,{status:n.status,size:"XS"})]})},J=({page:n,children:s})=>{const[{query:i}]=H(),{id:a,...t}=i;return e.jsx(le,{to:{search:N.stringify({...t,page:n})},style:{textDecoration:"none"},children:e.jsx(p,{variant:"omega",textColor:"primary600",children:s})})},ht=()=>{const{formatMessage:n}=F(),{versions:s,page:i}=I("VersionsList",a=>({versions:a.versions,page:a.page}));return e.jsxs(h,{shrink:0,direction:"column",alignItems:"stretch",width:"320px",height:"100vh",background:"neutral0",borderColor:"neutral200",borderWidth:"0 0 0 1px",borderStyle:"solid",tag:"aside",children:[e.jsxs(h,{direction:"row",justifyContent:"space-between",padding:4,borderColor:"neutral200",borderWidth:"0 0 1px",borderStyle:"solid",tag:"header",children:[e.jsx(p,{tag:"h2",variant:"omega",fontWeight:"semiBold",children:n({id:"content-manager.history.sidebar.title",defaultMessage:"Versions"})}),e.jsx(v,{background:"neutral150",hasRadius:!0,padding:1,children:e.jsx(p,{variant:"sigma",textColor:"neutral600",children:s.meta.pagination.total})})]}),e.jsxs(v,{flex:1,overflow:"auto",children:[s.meta.pagination.page>1&&e.jsx(v,{paddingTop:4,textAlign:"center",children:e.jsx(J,{page:i-1,children:n({id:"content-manager.history.sidebar.show-newer",defaultMessage:"Show newer versions"})})}),e.jsx(h,{direction:"column",gap:3,padding:4,tag:"ul",alignItems:"stretch",children:s.data.map((a,t)=>e.jsx("li",{"aria-label":n({id:"content-manager.history.sidebar.title.version-card.aria-label",defaultMessage:"Version card"}),children:e.jsx(gt,{version:a,isCurrent:i===1&&t===0})},a.id))}),s.meta.pagination.page<s.meta.pagination.pageCount&&e.jsx(v,{paddingBottom:4,textAlign:"center",children:e.jsx(J,{page:i+1,children:n({id:"content-manager.history.sidebar.show-older",defaultMessage:"Show older versions"})})})]})]})},[yt,I]=Qe("HistoryPage"),ft=()=>{const n=R.useId(),{formatMessage:s}=F(),{slug:i,id:a,collectionType:t}=G(),{isLoading:r,schema:o}=Ye({collectionType:t,model:i}),{isLoading:d,edit:{layout:y,settings:{displayName:f,mainField:C}}}=Xe(i),{data:T,isLoading:c}=Je(i),[{query:g}]=H(),{id:m,...w}=g,M=et(w),A=M.page?Number(M.page):1,b=lt({contentType:i,...a?{documentId:a}:{},...M},{refetchOnMountOrArgChange:!0}),L=R.useRef(b.requestId),B=b.requestId===L.current;if(!i||t===W&&!a)return e.jsx(Y,{to:"/content-manager"});if(r||d||b.isFetching||B||c)return e.jsx(V.Loading,{});if(!b.isError&&!b.data?.data?.length)return e.jsx(e.Fragment,{children:e.jsx(V.NoData,{action:e.jsx(Q,{tag:_,to:`/content-manager/${t}/${i}${a?`/${a}`:""}`,children:s({id:"global.back",defaultMessage:"Back"})})})});if(b.data?.data?.length&&!m)return e.jsx(Y,{to:{search:N.stringify({...g,id:b.data.data[0].id})},replace:!0});const u=b.data?.data?.find(k=>k.id.toString()===m);return b.isError||!y||!o||!u||!T||b.data.error?e.jsx(V.Error,{}):e.jsxs(e.Fragment,{children:[e.jsx(V.Title,{children:s({id:"content-manager.history.page-title",defaultMessage:"{contentType} history"},{contentType:f})}),e.jsx(yt,{contentType:i,id:a,schema:o,layout:y,configuration:T,selectedVersion:u,versions:b.data,page:A,mainField:C,children:e.jsxs(h,{direction:"row",alignItems:"flex-start",children:[e.jsxs(tt,{grow:1,height:"100vh",background:"neutral100",paddingBottom:6,overflow:"auto",labelledBy:n,children:[e.jsx(ut,{headerId:n}),e.jsx(ot,{})]}),e.jsx(ht,{})]})})]})},bt=()=>{const{slug:n}=G(),{permissions:s=[],isLoading:i,error:a}=oe(de.map(t=>({action:t,subject:n})));return i?e.jsx(V.Loading,{}):a||!n?e.jsx(v,{height:"100vh",width:"100vw",position:"fixed",top:0,left:0,zIndex:2,background:"neutral0",children:e.jsx(V.Error,{})}):e.jsx(v,{height:"100vh",width:"100vw",position:"fixed",top:0,left:0,zIndex:2,background:"neutral0",children:e.jsx(V.Protect,{permissions:s,children:({permissions:t})=>e.jsx(Ke,{permissions:t,children:e.jsx(ft,{})})})})},Ct=()=>e.jsx(Ge,{children:e.jsx(Ze,{children:e.jsx(bt,{})})});export{yt as HistoryProvider,Ct as ProtectedHistoryPage,I as useHistoryContext};
