import{aL as B,j as e,P as m,r as k,a as F,v as Y,w as Z,V as K,z as Q,aM as ee,B as p,at as D,au as W,av as L,T as l,D as se,bm as $,W as te,bn as ae,Y as E,bo as x,bp as I,bq as oe,X as ne,Z as f,e as R,br as ie,I as N,g as re,bs as le,as as de,s as ce,bt as O,bu as z}from"./strapi-z7ApxZZq.js";import{u as he}from"./useWebhooks-CzWtIcqU.js";const ge=()=>{const[d,i]=k.useState(!1),[r,o]=k.useState([]),c=B(s=>s.admin_app.permissions.settings?.webhooks),{formatMessage:t}=F(),{_unstableFormatAPIError:h}=Y(),{toggleNotification:n}=Z(),v=K(),{isLoading:H,allowedActions:{canCreate:w,canUpdate:C,canDelete:T}}=Q(c),{notifyStatus:A}=ee(),{isLoading:U,webhooks:g,error:M,updateWebhook:_,deleteManyWebhooks:P}=he();k.useEffect(()=>{if(M){n({type:"danger",message:h(M)});return}g&&A(t({id:"Settings.webhooks.list.loading.success",defaultMessage:"Webhooks have been loaded"}))},[g,M,n,t,A,h]);const V=async s=>{try{const a=await _(s);"error"in a&&n({type:"danger",message:h(a.error)})}catch{n({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred"})})}},q=async s=>{try{const a=await P({ids:[s]});if("error"in a){n({type:"danger",message:h(a.error)});return}o(b=>b.filter(y=>y!==s))}catch{n({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred"})})}},G=async()=>{try{const s=await P({ids:r});if("error"in s){n({type:"danger",message:h(s.error)});return}o([])}catch{n({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred"})})}finally{i(!1)}},J=s=>o(s?g?.map(a=>a.id)??[]:[]),X=(s,a)=>o(s?b=>[...b,a]:b=>b.filter(y=>y!==a)),S=H||U,j=g?.length??0,u=r.length;return S?e.jsx(m.Loading,{}):e.jsxs(p.Root,{children:[e.jsx(m.Title,{children:t({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Webhooks"})}),e.jsxs(m.Main,{"aria-busy":S,children:[e.jsx(p.Header,{title:t({id:"Settings.webhooks.title",defaultMessage:"Webhooks"}),subtitle:t({id:"Settings.webhooks.list.description",defaultMessage:"Get POST changes notifications"}),primaryAction:w&&!S&&e.jsx(D,{tag:W,startIcon:e.jsx(L,{}),variant:"default",to:"create",size:"S",children:t({id:"Settings.webhooks.list.button.add",defaultMessage:"Create new webhook"})})}),u>0&&T&&e.jsx(p.Action,{startActions:e.jsxs(e.Fragment,{children:[e.jsx(l,{variant:"epsilon",textColor:"neutral600",children:t({id:"Settings.webhooks.to.delete",defaultMessage:"{webhooksToDeleteLength, plural, one {# webhook} other {# webhooks}} selected"},{webhooksToDeleteLength:u})}),e.jsx(se,{onClick:()=>i(!0),startIcon:e.jsx($,{}),size:"L",variant:"danger-light",children:t({id:"global.delete",defaultMessage:"Delete"})})]})}),e.jsx(p.Content,{children:j>0?e.jsxs(te,{colCount:5,rowCount:j+1,footer:e.jsx(le,{onClick:()=>{w&&v("create")},icon:e.jsx(L,{}),children:t({id:"Settings.webhooks.list.button.add",defaultMessage:"Create new webhook"})}),children:[e.jsx(ae,{children:e.jsxs(E,{children:[e.jsx(x,{children:e.jsx(I,{"aria-label":t({id:"global.select-all-entries",defaultMessage:"Select all entries"}),checked:u>0&&u<j?"indeterminate":u===j,onCheckedChange:J})}),e.jsx(x,{width:"20%",children:e.jsx(l,{variant:"sigma",textColor:"neutral600",children:t({id:"global.name",defaultMessage:"Name"})})}),e.jsx(x,{width:"60%",children:e.jsx(l,{variant:"sigma",textColor:"neutral600",children:t({id:"Settings.webhooks.form.url",defaultMessage:"URL"})})}),e.jsx(x,{width:"20%",children:e.jsx(l,{variant:"sigma",textColor:"neutral600",children:t({id:"Settings.webhooks.list.th.status",defaultMessage:"Status"})})}),e.jsx(x,{children:e.jsx(oe,{children:t({id:"Settings.webhooks.list.th.actions",defaultMessage:"Actions"})})})]})}),e.jsx(ne,{children:g?.map(s=>e.jsxs(E,{onClick:()=>{C&&v(s.id)},style:{cursor:C?"pointer":"default"},children:[e.jsx(f,{onClick:a=>a.stopPropagation(),children:e.jsx(I,{"aria-label":`${t({id:"global.select",defaultMessage:"Select"})} ${s.name}`,checked:r?.includes(s.id),onCheckedChange:a=>X(!!a,s.id),name:"select"})}),e.jsx(f,{children:e.jsx(l,{fontWeight:"semiBold",textColor:"neutral800",children:s.name})}),e.jsx(f,{children:e.jsx(l,{textColor:"neutral800",children:s.url})}),e.jsx(f,{onClick:a=>a.stopPropagation(),children:e.jsx(R,{children:e.jsx(ie,{onLabel:t({id:"global.enabled",defaultMessage:"Enabled"}),offLabel:t({id:"global.disabled",defaultMessage:"Disabled"}),"aria-label":`${s.name} ${t({id:"Settings.webhooks.list.th.status",defaultMessage:"Status"})}`,checked:s.isEnabled,onCheckedChange:a=>{V({...s,isEnabled:a})},visibleLabels:!0})})}),e.jsx(f,{children:e.jsxs(R,{gap:1,children:[C&&e.jsx(N,{label:t({id:"Settings.webhooks.events.update",defaultMessage:"Update"}),variant:"ghost",children:e.jsx(re,{})}),T&&e.jsx(ue,{onDelete:()=>{q(s.id)}})]})})]},s.id))})]}):e.jsx(de,{icon:e.jsx(ce,{width:"160px"}),content:t({id:"Settings.webhooks.list.empty.description",defaultMessage:"No webhooks found"}),action:w?e.jsx(D,{variant:"secondary",startIcon:e.jsx(L,{}),tag:W,to:"create",children:t({id:"Settings.webhooks.list.button.add",defaultMessage:"Create new webhook"})}):null})})]}),e.jsx(O.Root,{open:d,onOpenChange:i,children:e.jsx(z,{onConfirm:G})})]})},ue=({onDelete:d})=>{const[i,r]=k.useState(!1),{formatMessage:o}=F();return e.jsxs(e.Fragment,{children:[e.jsx(N,{onClick:c=>{c.stopPropagation(),r(!0)},label:o({id:"Settings.webhooks.events.delete",defaultMessage:"Delete webhook"}),variant:"ghost",children:e.jsx($,{})}),e.jsx(O.Root,{open:i,onOpenChange:r,children:e.jsx(z,{onConfirm:c=>{c?.stopPropagation(),d()}})})]})},fe=()=>{const d=B(i=>i.admin_app.permissions.settings?.webhooks.main);return e.jsx(m.Protect,{permissions:d,children:e.jsx(ge,{})})};export{ge as ListPage,fe as ProtectedListPage};
