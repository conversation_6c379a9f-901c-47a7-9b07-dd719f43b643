{"kind": "collectionType", "collectionName": "exportateurs", "info": {"singularName": "exportateur", "pluralName": "exportateurs", "displayName": "Exportateur"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"raison_sociale": {"type": "string", "required": true}, "nom_contact": {"type": "string", "required": true}, "prenom_contact": {"type": "string", "required": true}, "matricule_fiscal": {"type": "string", "required": true}, "effectif": {"type": "integer"}, "forme_juridique": {"type": "enumeration", "required": true, "enum": ["S.A", "S.A.R.L", "S.U.A.R.L", "<PERSON><PERSON>"]}, "forme_juridique_autre": {"type": "string"}, "statut": {"type": "enumeration", "required": true, "enum": ["Résidente", "Non résidente"]}, "totalement_exportatrice": {"type": "boolean", "required": true}, "partiellement_exportatrice": {"type": "boolean", "required": true}, "adresse": {"type": "string", "required": true}, "gouvernorat": {"type": "relation", "relation": "oneToOne", "target": "api::gouvernorat.gouvernorat"}, "ville": {"type": "relation", "relation": "oneToOne", "target": "api::ville.ville"}, "code_postal": {"type": "relation", "relation": "oneToOne", "target": "api::code-postal.code-postal"}, "telephone_siege": {"type": "string", "required": true}, "mobile": {"type": "string"}, "email": {"type": "email", "required": true}, "site_web": {"type": "string"}, "secteur_activite": {"type": "enumeration", "required": true, "enum": ["Agro-alimentaire", "Textile", "IME", "Service", "Artisanat", "Divers"]}, "opportunites": {"type": "relation", "relation": "manyToMany", "target": "api::opportunite.opportunite", "mappedBy": "exportateurs"}}}