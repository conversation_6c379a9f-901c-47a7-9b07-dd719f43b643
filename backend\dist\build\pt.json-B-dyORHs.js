var e="Link",i={link:e,"Settings.email.plugin.button.test-email":"Enviar e-mail de teste","Settings.email.plugin.label.defaultFrom":"E-mail do remetente padrão","Settings.email.plugin.label.defaultReplyTo":"E-mail de resposta padrão","Settings.email.plugin.label.provider":"Provedor de E-mail","Settings.email.plugin.label.testAddress":"E-mail do destinatário","Settings.email.plugin.notification.config.error":"Falha ao recuperar a configuração de e-mail","Settings.email.plugin.notification.data.loaded":"Os dados de configurações de e-mail foram carregados","Settings.email.plugin.notification.test.error":"Falha ao enviar um e-mail de teste para {to}","Settings.email.plugin.notification.test.success":"Teste de e-mail bem-sucedido, verifique a caixa de correio {to}","Settings.email.plugin.placeholder.defaultFrom":"ex: Strapi No-Reply <<EMAIL>>","Settings.email.plugin.placeholder.defaultReplyTo":"ex: Strapi <<EMAIL>>","Settings.email.plugin.placeholder.testAddress":"ex: <EMAIL>","Settings.email.plugin.subTitle":"Teste as configurações do plug-in de e-mail","Settings.email.plugin.text.configuration":"O plugin é configurado através do ficheiro {file}, acede a este {link} para a documentação.","Settings.email.plugin.title":"Configuração","Settings.email.plugin.title.config":"Configuração","Settings.email.plugin.title.test":"Testar entrega de e-mail","SettingsNav.link.settings":"Definições","SettingsNav.section-label":"Plug-in de e-mail","components.Input.error.validation.email":"Este é um e-mail inválido"};export{i as default,e as link};
