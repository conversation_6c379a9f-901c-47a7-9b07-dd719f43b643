import {
  DocumentStatus,
  RelativeTime
} from "./chunk-UIEUQC33.js";
import {
  contentManagerApi
} from "./chunk-JVGUDHAQ.js";
import "./chunk-MLDZODJM.js";
import "./chunk-GXEE7KZL.js";
import {
  Widget
} from "./chunk-6EC7MKBK.js";
import "./chunk-55Q6LON3.js";
import "./chunk-QVDAYSOU.js";
import "./chunk-A34HN3WE.js";
import "./chunk-APY4KZ5L.js";
import "./chunk-OOAHAGSN.js";
import "./chunk-BEZCWAXF.js";
import "./chunk-MFYBRT3Z.js";
import "./chunk-73ABBQBL.js";
import "./chunk-WNXMQTNQ.js";
import "./chunk-VJ3LKUI5.js";
import "./chunk-3OKC5SXR.js";
import "./chunk-ITIYAEI7.js";
import "./chunk-K4LYEYIN.js";
import "./chunk-XGEFBNZK.js";
import "./chunk-YW3XCEFV.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-UCU7ROGU.js";
import "./chunk-K65KIEAL.js";
import "./chunk-F5JI4FJS.js";
import "./chunk-TFPYBHFW.js";
import "./chunk-MMKYPT4K.js";
import "./chunk-U63NXCTP.js";
import "./chunk-SGQXSZWC.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-7US6K6XC.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-DZIOEGNZ.js";
import "./chunk-L3WQFZOD.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-67LA7IEH.js";
import "./chunk-UHKKWDMK.js";
import "./chunk-GM54BMM2.js";
import "./chunk-2DNMQP4H.js";
import "./chunk-L32VSWBJ.js";
import "./chunk-XVW7MCOW.js";
import "./chunk-2U7FOQNK.js";
import "./chunk-WIFIVZU3.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import "./chunk-T3UNFN7Y.js";
import "./chunk-DETWX3NC.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import "./chunk-FTSHQ5RF.js";
import "./chunk-RZWN45NY.js";
import "./chunk-FOUXGIF2.js";
import "./chunk-TXOJWASW.js";
import "./chunk-BUMITFEB.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-PW6GS6S3.js";
import {
  Box,
  IconButton,
  Table,
  Tbody,
  Td,
  Tr,
  Typography,
  useIntl
} from "./chunk-Y6YT4U2T.js";
import "./chunk-5ZC4PE57.js";
import {
  Link,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1v
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/components/Widgets.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/content-manager/dist/admin/services/homepage.mjs
var homepageService = contentManagerApi.enhanceEndpoints({
  addTagTypes: [
    "RecentDocumentList"
  ]
}).injectEndpoints({
  /**
   * TODO: Remove overrideExisting when we remove the future flag
   * and delete the old homepage service in the admin
   */
  overrideExisting: true,
  endpoints: (builder) => ({
    getRecentDocuments: builder.query({
      query: (params) => `/content-manager/homepage/recent-documents?action=${params.action}`,
      transformResponse: (response) => response.data,
      providesTags: (res, _err, { action }) => [
        {
          type: "RecentDocumentList",
          id: action
        }
      ]
    })
  })
});
var { useGetRecentDocumentsQuery } = homepageService;

// node_modules/@strapi/content-manager/dist/admin/components/Widgets.mjs
var CellTypography = dt(Typography).attrs({
  maxWidth: "14.4rem",
  display: "block"
})`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;
var RecentDocumentsTable = ({ documents }) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const navigate = useNavigate();
  const getEditViewLink = (document) => {
    const isSingleType = document.kind === "singleType";
    const kindPath = isSingleType ? "single-types" : "collection-types";
    const queryParams = document.locale ? `?plugins[i18n][locale]=${document.locale}` : "";
    return `/content-manager/${kindPath}/${document.contentTypeUid}${isSingleType ? "" : "/" + document.documentId}${queryParams}`;
  };
  const handleRowClick = (document) => () => {
    trackUsage("willEditEntryFromHome");
    const link = getEditViewLink(document);
    navigate(link);
  };
  return (0, import_jsx_runtime.jsx)(Table, {
    colCount: 5,
    rowCount: (documents == null ? void 0 : documents.length) ?? 0,
    children: (0, import_jsx_runtime.jsx)(Tbody, {
      children: documents == null ? void 0 : documents.map((document) => (0, import_jsx_runtime.jsxs)(Tr, {
        onClick: handleRowClick(document),
        cursor: "pointer",
        children: [
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(CellTypography, {
              title: document.title,
              variant: "omega",
              textColor: "neutral800",
              children: document.title
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(CellTypography, {
              variant: "omega",
              textColor: "neutral600",
              children: document.kind === "singleType" ? formatMessage({
                id: "content-manager.widget.last-edited.single-type",
                defaultMessage: "Single-Type"
              }) : formatMessage({
                id: document.contentTypeDisplayName,
                defaultMessage: document.contentTypeDisplayName
              })
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(Box, {
              display: "inline-block",
              children: document.status ? (0, import_jsx_runtime.jsx)(DocumentStatus, {
                status: document.status
              }) : (0, import_jsx_runtime.jsx)(Typography, {
                textColor: "neutral600",
                "aria-hidden": true,
                children: "-"
              })
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(Typography, {
              textColor: "neutral600",
              children: (0, import_jsx_runtime.jsx)(RelativeTime, {
                timestamp: new Date(document.updatedAt)
              })
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            onClick: (e) => e.stopPropagation(),
            children: (0, import_jsx_runtime.jsx)(Box, {
              display: "inline-block",
              children: (0, import_jsx_runtime.jsx)(IconButton, {
                tag: Link,
                to: getEditViewLink(document),
                onClick: () => trackUsage("willEditEntryFromHome"),
                label: formatMessage({
                  id: "content-manager.actions.edit.label",
                  defaultMessage: "Edit"
                }),
                variant: "ghost",
                children: (0, import_jsx_runtime.jsx)(ForwardRef$1v, {})
              })
            })
          })
        ]
      }, document.documentId))
    })
  });
};
var LastEditedWidget = () => {
  const { formatMessage } = useIntl();
  const { data, isLoading, error } = useGetRecentDocumentsQuery({
    action: "update"
  });
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Widget.Loading, {});
  }
  if (error || !data) {
    return (0, import_jsx_runtime.jsx)(Widget.Error, {});
  }
  if (data.length === 0) {
    return (0, import_jsx_runtime.jsx)(Widget.NoData, {
      children: formatMessage({
        id: "content-manager.widget.last-edited.no-data",
        defaultMessage: "No edited entries"
      })
    });
  }
  return (0, import_jsx_runtime.jsx)(RecentDocumentsTable, {
    documents: data
  });
};
var LastPublishedWidget = () => {
  const { formatMessage } = useIntl();
  const { data, isLoading, error } = useGetRecentDocumentsQuery({
    action: "publish"
  });
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Widget.Loading, {});
  }
  if (error || !data) {
    return (0, import_jsx_runtime.jsx)(Widget.Error, {});
  }
  if (data.length === 0) {
    return (0, import_jsx_runtime.jsx)(Widget.NoData, {
      children: formatMessage({
        id: "content-manager.widget.last-published.no-data",
        defaultMessage: "No published entries"
      })
    });
  }
  return (0, import_jsx_runtime.jsx)(RecentDocumentsTable, {
    documents: data
  });
};
export {
  LastEditedWidget,
  LastPublishedWidget
};
//# sourceMappingURL=Widgets-IZM7HYL4.js.map
