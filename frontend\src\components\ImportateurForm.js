import React, { useState, useEffect } from "react";

export default function ImportateurForm() {
  const [importateurs, setImportateurs] = useState([]);
  const [formData, setFormData] = useState({
    societe: "",
    pays: "",
    nom_responsable: "",
    prenom_responsable: "",
    telephone_whatsapp: "",
    email: "",
    region: "",
  });

  // Fetch all importateurs
  useEffect(() => {
    fetch("http://localhost:1337/api/importateurs?populate=*")
      .then((res) => res.json())
      .then((data) => {
        console.log("Importateurs API response:", data);
        console.log("First item structure:", data.data && data.data[0]);
        if (data && data.data && Array.isArray(data.data)) {
          // Filtrer les éléments null/undefined avant de les stocker
          const validImportateurs = data.data.filter(imp => imp !== null && imp !== undefined);
          setImportateurs(validImportateurs);
        } else {
          console.warn("Unexpected API response structure:", data);
          setImportateurs([]);
        }
      })
      .catch((err) => {
        console.error("Error fetching importateurs:", err);
        setImportateurs([]);
      });
  }, []);

  // Handle form input change
  function handleChange(e) {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  }

  // Submit new importateur
  function handleSubmit(e) {
    e.preventDefault();

    fetch("http://localhost:1337/api/importateurs", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ data: formData }),
    })
      .then((res) => res.json())
      .then((data) => {
        console.log("Nouvel importateur créé:", data);
        // Vérifier que data.data n'est pas null avant de l'ajouter
        if (data && data.data) {
          setImportateurs([...importateurs, data.data]);
        } else {
          console.warn("Réponse inattendue lors de la création:", data);
        }
        setFormData({
          societe: "",
          pays: "",
          nom_responsable: "",
          prenom_responsable: "",
          telephone_whatsapp: "",
          email: "",
          region: "",
        });
      })
      .catch((err) => console.error(err));
  }

  return (
    <div>
      <h2>Liste des Importateurs</h2>
      <ul>
        {importateurs && importateurs.length > 0 ? (
          importateurs
            .filter(imp => imp !== null && imp !== undefined) // Filtrer les éléments null/undefined
            .map((imp) => {
              console.log("Processing importateur:", imp);
              // Try different possible data structures
              const societe = imp?.attributes?.societe ||
                             imp?.societe ||
                             imp?.data?.attributes?.societe ||
                             'Structure de données inconnue';
              return (
                <li key={imp.id || imp.documentId || Math.random()}>
                  {societe}
                </li>
              );
            })
        ) : (
          <li>Aucun importateur trouvé</li>
        )}
      </ul>

      <h3>Ajouter un Importateur</h3>
      <form onSubmit={handleSubmit}>
        <input
          name="societe"
          placeholder="Société"
          value={formData.societe}
          onChange={handleChange}
          required
        />
        <input
          name="pays"
          placeholder="Pays"
          value={formData.pays}
          onChange={handleChange}
          required
        />
        <input
          name="nom_responsable"
          placeholder="Nom du responsable"
          value={formData.nom_responsable}
          onChange={handleChange}
          required
        />
        <input
          name="prenom_responsable"
          placeholder="Prénom du responsable"
          value={formData.prenom_responsable}
          onChange={handleChange}
          required
        />
        <input
          name="telephone_whatsapp"
          placeholder="Téléphone WhatsApp"
          value={formData.telephone_whatsapp}
          onChange={handleChange}
          required
        />
        <input
          name="email"
          placeholder="Email"
          type="email"
          value={formData.email}
          onChange={handleChange}
          required
        />
        <input
          name="region"
          placeholder="Région"
          value={formData.region}
          onChange={handleChange}
          required
        />
        <button type="submit">Ajouter</button>
      </form>
    </div>
  );
}
