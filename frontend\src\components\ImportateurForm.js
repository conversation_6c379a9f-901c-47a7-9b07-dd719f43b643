import React, { useState, useEffect } from "react";

export default function ImportateurForm() {
  const [importateurs, setImportateurs] = useState([]);
  const [formData, setFormData] = useState({
    societe: "",
    pays: "",
    responsableNom: "",
    responsablePrenom: "",
    telephone: "",
    email: "",
    region: "",
  });

  // Fetch all importateurs
  useEffect(() => {
    fetch("http://localhost:1337/api/importateurs?populate=*")
      .then((res) => res.json())
      .then((data) => setImportateurs(data.data))
      .catch((err) => console.error(err));
  }, []);

  // Handle form input change
  function handleChange(e) {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  }

  // Submit new importateur
  function handleSubmit(e) {
    e.preventDefault();

    fetch("http://localhost:1337/api/importateurs", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ data: formData }),
    })
      .then((res) => res.json())
      .then((data) => {
        setImportateurs([...importateurs, data.data]);
        setFormData({
          societe: "",
          pays: "",
          responsableNom: "",
          responsablePrenom: "",
          telephone: "",
          email: "",
          region: "",
        });
      })
      .catch((err) => console.error(err));
  }

  return (
    <div>
      <h2>Liste des Importateurs</h2>
      <ul>
        {importateurs.map((imp) => (
          <li key={imp.id}>{imp.attributes.societe}</li>
        ))}
      </ul>

      <h3>Ajouter un Importateur</h3>
      <form onSubmit={handleSubmit}>
        <input
          name="societe"
          placeholder="Société"
          value={formData.societe}
          onChange={handleChange}
          required
        />
        <input
          name="pays"
          placeholder="Pays"
          value={formData.pays}
          onChange={handleChange}
          required
        />
        <input
          name="responsableNom"
          placeholder="Nom du responsable"
          value={formData.responsableNom}
          onChange={handleChange}
          required
        />
        <input
          name="responsablePrenom"
          placeholder="Prénom du responsable"
          value={formData.responsablePrenom}
          onChange={handleChange}
          required
        />
        <input
          name="telephone"
          placeholder="Téléphone"
          value={formData.telephone}
          onChange={handleChange}
          required
        />
        <input
          name="email"
          placeholder="Email"
          type="email"
          value={formData.email}
          onChange={handleChange}
          required
        />
        <input
          name="region"
          placeholder="Région"
          value={formData.region}
          onChange={handleChange}
          required
        />
        <button type="submit">Ajouter</button>
      </form>
    </div>
  );
}
