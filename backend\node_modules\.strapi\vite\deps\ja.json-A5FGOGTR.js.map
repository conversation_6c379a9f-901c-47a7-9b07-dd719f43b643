{"version": 3, "sources": ["../../../@strapi/upload/dist/admin/translations/ja.json.mjs"], "sourcesContent": ["var ja = {\n    \"button.next\": \"次へ\",\n    \"checkControl.crop-duplicate\": \"複製したアセットをトリミングする\",\n    \"checkControl.crop-original\": \"元のアセットをトリミングする\",\n    \"control-card.add\": \"追加\",\n    \"control-card.cancel\": \"キャンセル\",\n    \"control-card.copy-link\": \"リンクをコピー\",\n    \"control-card.crop\": \"クロップ\",\n    \"control-card.download\": \"ダウンロード\",\n    \"control-card.edit\": \"編集\",\n    \"control-card.replace-media\": \"メディアの置き換え\",\n    \"control-card.save\": \"保存\",\n    \"filter.add\": \"フィルタを追加\",\n    \"form.button.replace-media\": \"メディアの置き換え\",\n    \"form.input.description.file-alt\": \"このテキストはアセットを表示できない場合に表示されます。\",\n    \"form.input.label.file-alt\": \"代替テキスト\",\n    \"form.input.label.file-caption\": \"キャプション\",\n    \"form.input.label.file-name\": \"ファイル名\",\n    \"form.upload-url.error.url.invalid\": \"1つのURLが無効です\",\n    \"form.upload-url.error.url.invalids\": \"{number}つのURLは無効です\",\n    \"header.actions.upload-assets\": \"アップロード\",\n    \"header.content.assets\": \"{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 アセット} other {# アセット}}\",\n    \"input.button.label\": \"ファイルを選択\",\n    \"input.label-bold\": \"ドラッグ＆ドロップ\",\n    \"input.label-normal\": \"でアップロードするか\",\n    \"input.placeholder\": \"クリックしてアセットを選択するか、このエリアにファイルをドラッグ＆ドロップします。\",\n    \"input.url.description\": \"URLのリンクは改行で区切ってください。\",\n    \"input.url.label\": \"URL\",\n    \"list.assets-empty.subtitle\": \"最初の1つを追加します。\",\n    \"list.assets-empty.title\": \"アセットはまだありません\",\n    \"list.assets-empty.title-withSearch\": \"適用されたフィルターを持つアセットはありません\",\n    \"list.assets.type-not-allowed\": \"このファイルの拡張子は許可されていません。\",\n    \"modal.file-details.date\": \"日付\",\n    \"modal.file-details.dimensions\": \"寸法\",\n    \"modal.file-details.extension\": \"拡張\",\n    \"modal.file-details.size\": \"データサイズ\",\n    \"modal.header.browse\": \"アセットのアップロード\",\n    \"modal.header.file-detail\": \"詳細\",\n    \"modal.header.pending-assets\": \"アップロード前のアセット\",\n    \"modal.header.select-files\": \"選択されたファイル\",\n    \"modal.nav.browse\": \"ブラウズ\",\n    \"modal.nav.computer\": \"PCから\",\n    \"modal.nav.selected\": \"選りすぐり\",\n    \"modal.nav.url\": \"URLから\",\n    \"modal.selected-list.sub-header-subtitle\": \"ドラッグ＆ドロップでフィールド内のアセットを並べ替える\",\n    \"modal.upload-list.sub-header-subtitle\": \"メディアライブラリに追加する前にアセットを管理する\",\n    \"modal.upload-list.sub-header.button\": \"別のアセットを追加する\",\n    \"plugin.description.long\": \"メディアファイルの管理\",\n    \"plugin.description.short\": \"メディアファイルの管理\",\n    \"plugin.name\": \"メディアライブラリ\",\n    \"search.placeholder\": \"アセットの検索...\",\n    \"settings.form.autoOrientation.description\": \"EXIF情報に従って画像を自動的に回転させます。\",\n    \"settings.form.autoOrientation.label\": \"画像の自動方向補正機能を有効にする\",\n    \"settings.form.responsiveDimensions.description\": \"アップロードされたアセットから複数のサイズ（大、中、小）を自動的に生成します。\",\n    \"settings.form.responsiveDimensions.label\": \"レスポンシブに最適化するアップロードを有効にする\",\n    \"settings.form.sizeOptimization.label\": \"サイズの最適化を有効にする (品質は落とさない)\",\n    \"settings.form.videoPreview.description\": \"動画（GIF）の6秒プレビューを生成します。\",\n    \"settings.form.videoPreview.label\": \"プレビュー\",\n    \"settings.header.label\": \"メディアライブラリ - 設定\",\n    \"settings.section.image.label\": \"イメージ\",\n    \"settings.section.video.label\": \"ビデオ\",\n    \"settings.sub-header.label\": \"メディアライブラリの設定を行う\",\n    \"sort.created_at_asc\": \"アップロードが古い順\",\n    \"sort.created_at_desc\": \"アップロードが新しい順\",\n    \"sort.label\": \"並び替え\",\n    \"sort.name_asc\": \"アルファベット順（A～Z\",\n    \"sort.name_desc\": \"アルファベット逆順（Z〜A\",\n    \"sort.updated_at_asc\": \"アップデートが古い順\",\n    \"sort.updated_at_desc\": \"アップデートが新しい順\",\n    \"window.confirm.close-modal.file\": \"保存されていない変更がありますが、いいですか？。\",\n    \"window.confirm.close-modal.files\": \"まだアップロードされていないファイルがありますが、いいですか？\"\n};\n\nexport { ja as default };\n//# sourceMappingURL=ja.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,eAAe;AAAA,EACf,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,6CAA6C;AAAA,EAC7C,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,mCAAmC;AAAA,EACnC,oCAAoC;AACxC;", "names": []}