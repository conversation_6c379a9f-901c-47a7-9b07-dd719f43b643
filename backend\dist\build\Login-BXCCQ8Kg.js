import{a as l,b5 as p,j as s,b6 as r,$ as a,e as t,T as x,k as u,b7 as c}from"./strapi-z7ApxZZq.js";import{S as g}from"./SSOProviders-CURKLlPV.js";const d=u(c)`
  flex: 1;
`,h=i=>{const{formatMessage:n}=l(),{isLoading:o,data:e=[]}=p(void 0,{skip:!window.strapi.features.isEnabled(window.strapi.features.SSO)});return!window.strapi.features.isEnabled(window.strapi.features.SSO)||!o&&e.length===0?s.jsx(r,{...i}):s.jsx(r,{...i,children:s.jsx(a,{paddingTop:7,children:s.jsxs(t,{direction:"column",alignItems:"stretch",gap:7,children:[s.jsxs(t,{children:[s.jsx(d,{}),s.jsx(a,{paddingLeft:3,paddingRight:3,children:s.jsx(x,{variant:"sigma",textColor:"neutral600",children:n({id:"Auth.login.sso.divider"})})}),s.jsx(d,{})]}),s.jsx(g,{providers:e,displayAllProviders:!1})]})})})};export{h as LoginEE};
