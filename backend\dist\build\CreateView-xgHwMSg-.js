import{aL as o,j as t,P as e}from"./strapi-z7ApxZZq.js";import{EditView as s}from"./EditViewPage-C2UeAsNT.js";import"./apiTokens-BDLby-vQ.js";import"./constants-Q2dfXdfa.js";import"./TokenTypeSelect-CcKLfmXH.js";import"./transferTokens-DDbhemls.js";import"./index-C3HeomYJ.js";import"./index-BRVyLNfZ.js";import"./tail-ClM4A4bE.js";import"./_baseMap-BaaWdrf-.js";import"./_baseEach-ZnnftuGj.js";const g=()=>{const r=o(i=>i.admin_app.permissions.settings?.["api-tokens"].create);return t.jsx(e.Protect,{permissions:r,children:t.jsx(s,{})})};export{g as ProtectedCreateView};
