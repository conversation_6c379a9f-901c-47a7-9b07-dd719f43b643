{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/pages/NoContentTypePage.tsx"], "sourcesContent": ["import { Page, Layouts } from '@strapi/admin/strapi-admin';\nimport { EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\n\nimport { getTranslation } from '../utils/translations';\n\nconst NoContentType = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Page.Main>\n      <Layouts.Header\n        title={formatMessage({\n          id: getTranslation('header.name'),\n          defaultMessage: 'Content',\n        })}\n      />\n      <Layouts.Content>\n        <EmptyStateLayout\n          action={\n            <LinkButton\n              tag={NavLink}\n              variant=\"secondary\"\n              startIcon={<Plus />}\n              to=\"/plugins/content-type-builder/content-types/create-content-type\"\n            >\n              {formatMessage({\n                id: 'app.components.HomePage.create',\n                defaultMessage: 'Create your first Content-type',\n              })}\n            </LinkButton>\n          }\n          content={formatMessage({\n            id: 'content-manager.pages.NoContentType.text',\n            defaultMessage:\n              \"You don't have any content yet, we recommend you to create your first Content-Type.\",\n          })}\n          hasRadius\n          icon={<EmptyDocuments width=\"16rem\" />}\n          shadow=\"tableShadow\"\n        />\n      </Layouts.Content>\n    </Page.Main>\n  );\n};\n\nexport { NoContentType };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAMA,gBAAgB,MAAA;AACpB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,aACEC,yBAACC,KAAKC,MAAI;;UACRC,wBAACC,QAAQC,QAAM;QACbC,OAAOR,cAAc;UACnBS,IAAIC,eAAe,aAAA;UACnBC,gBAAgB;QAClB,CAAA;;UAEFN,wBAACC,QAAQM,SAAO;QACd,cAAAP,wBAACQ,kBAAAA;UACCC,YACET,wBAACU,YAAAA;YACCC,KAAKC;YACLC,SAAQ;YACRC,eAAWd,wBAACe,eAAAA,CAAAA,CAAAA;YACZC,IAAG;sBAEFrB,cAAc;cACbS,IAAI;cACJE,gBAAgB;YAClB,CAAA;;UAGJW,SAAStB,cAAc;YACrBS,IAAI;YACJE,gBACE;UACJ,CAAA;UACAY,WAAS;UACTC,UAAMnB,wBAACoB,cAAAA;YAAeC,OAAM;;UAC5BC,QAAO;;;;;AAKjB;", "names": ["NoContentType", "formatMessage", "useIntl", "_jsxs", "Page", "Main", "_jsx", "Layouts", "Header", "title", "id", "getTranslation", "defaultMessage", "Content", "EmptyStateLayout", "action", "LinkButton", "tag", "NavLink", "variant", "startIcon", "Plus", "to", "content", "hasRadius", "icon", "EmptyDocuments", "width", "shadow"]}