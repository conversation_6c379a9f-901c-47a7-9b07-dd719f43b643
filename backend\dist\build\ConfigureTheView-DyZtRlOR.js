import{a as y,j as e,$ as A,H as c,M as d,c7 as o,aH as b,e8 as H,aI as x,e9 as $,ea as u,bV as O,eb as T,c4 as B,b as F,w as z,ec as W,r as j,B as g,P as _,D as G,c6 as P,E as U,bH as V,au as q,dR as Y,bt as J,bu as K}from"./strapi-z7ApxZZq.js";const Q=({sort:i="",pageSize:s=10,onChange:t})=>{const{formatMessage:n}=y();return e.jsx(A,{background:"neutral0",hasRadius:!0,shadow:"tableShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:e.jsxs(c.Root,{gap:4,children:[e.jsx(c.Item,{s:12,col:6,direction:"column",alignItems:"stretch",children:e.jsxs(d<PERSON><PERSON>,{hint:n({id:o("config.entries.note"),defaultMessage:"Number of assets displayed by default in the Media Library"}),name:"pageSize",children:[e.jsx(d.Label,{children:n({id:o("config.entries.title"),defaultMessage:"Entries per page"})}),e.jsx(b,{onChange:a=>t({target:{name:"pageSize",value:a}}),value:s,children:H.map(a=>e.jsx(x,{value:a,children:a},a))}),e.jsx(d.Hint,{})]})}),e.jsx(c.Item,{s:12,col:6,direction:"column",alignItems:"stretch",children:e.jsxs(d.Root,{hint:n({id:o("config.note"),defaultMessage:"Note: You can override this value in the media library."}),name:"sort",children:[e.jsx(d.Label,{children:n({id:o("config.sort.title"),defaultMessage:"Default sort order"})}),e.jsx(b,{onChange:a=>t({target:{name:"sort",value:a}}),value:i,"test-sort":i,"data-testid":"sort-select",children:$.map(a=>e.jsx(x,{"data-testid":`sort-option-${a.value}`,value:a.value,children:n({id:o(a.key),defaultMessage:`${a.value}`})},a.key))}),e.jsx(d.Hint,{})]})})]})})},D=`${u}/ON_CHANGE`,v=`${u}/SET_LOADED`,X=({name:i,value:s})=>({type:D,keys:i,value:s}),Z=()=>({type:v}),f={initialData:{},modifiedData:{}},S=i=>({...f,initialData:i,modifiedData:i}),ee=(i=f,s={type:""})=>O(i,t=>{switch(s.type){case D:{"keys"in s&&"value"in s&&s.keys&&B(t,["modifiedData",...s.keys.split(".")],s.value);break}case v:{const n=S(T(t,["modifiedData"],{}));t.initialData=n.initialData,t.modifiedData=n.modifiedData;break}default:return t}}),te=({config:i})=>{const{trackUsage:s}=F(),{formatMessage:t}=y(),{toggleNotification:n}=z(),{mutateConfig:a}=W(),{isLoading:M}=a,[C,h]=j.useState(!1),m=()=>h(r=>!r),[k,w]=j.useReducer(ee,f,()=>S(i)),p=w,{initialData:L,modifiedData:l}=k,E=r=>{r.preventDefault(),m()},R=async()=>{s("willEditMediaLibraryConfig"),await a.mutateAsync(l),h(!1),p(Z()),n({type:"success",message:t({id:"notification.form.success.fields",defaultMessage:"Changes saved"})})},I=({target:{name:r,value:N}})=>{p(X({name:r,value:N}))};return e.jsx(g.Root,{children:e.jsx(_.Main,{"aria-busy":M,children:e.jsxs("form",{onSubmit:E,children:[e.jsx(g.Header,{navigationAction:e.jsx(V,{tag:q,startIcon:e.jsx(Y,{}),to:`/plugins/${u}`,id:"go-back",children:t({id:o("config.back"),defaultMessage:"Back"})}),primaryAction:e.jsx(G,{size:"S",startIcon:e.jsx(U,{}),disabled:P(l,L),type:"submit",children:t({id:"global.save",defaultMessage:"Save"})}),subtitle:t({id:o("config.subtitle"),defaultMessage:"Define the view settings of the media library."}),title:t({id:o("config.title"),defaultMessage:"Configure the view - Media Library"})}),e.jsx(g.Content,{children:e.jsx(Q,{"data-testid":"settings",pageSize:l.pageSize||"",sort:l.sort||"",onChange:I})}),"x",e.jsx(J.Root,{open:C,onOpenChange:m,children:e.jsx(K,{onConfirm:R,variant:"default",children:t({id:o("config.popUpWarning.warning.updateAllSettings"),defaultMessage:"This will modify all your settings"})})})]})})})};export{te as ConfigureTheView};
