{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/sk.json.mjs"], "sourcesContent": ["var Analytics = \"Analytika\";\nvar Documentation = \"Dokumentácia\";\nvar Email = \"E-mailová adresa\";\nvar Password = \"<PERSON><PERSON><PERSON>\";\nvar Provider = \"Poskytovateľ\";\nvar ResetPasswordToken = \"Token pre obnovu hesla\";\nvar Role = \"Rola\";\nvar light = \"Svetlý\";\nvar dark = \"Tmavý\";\nvar Username = \"Používateľské meno\";\nvar Users = \"Používatelia\";\nvar anErrorOccurred = \"Hups! Niečo sa pokazilo. Prosím, skúste znovu.\";\nvar clearLabel = \"Vyčistiť\";\nvar or = \"ALEBO\";\nvar skipToContent = \"Prejsť na obsah\";\nvar submit = \"Odoslať\";\nvar sk = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"V<PERSON>š <PERSON>et bol pozastavený\",\n    \"Auth.components.Oops.text.admin\": \"Ak je toto chyba, kontaktuje prosím administrátora.\",\n    \"Auth.components.Oops.title\": \"Hups...\",\n    \"Auth.form.active.label\": \"Aktívny\",\n    \"Auth.form.button.forgot-password\": \"Poslať e-mail\",\n    \"Auth.form.button.go-home\": \"NASPÄŤ DOMOV\",\n    \"Auth.form.button.login\": \"Prihlásiť sa\",\n    \"Auth.form.button.login.providers.error\": \"Cez vybraného poskytovateľa sa nám nepodarilo vás prepojiť.\",\n    \"Auth.form.button.login.strapi\": \"Prihlásiť sa cez Strapi\",\n    \"Auth.form.button.password-recovery\": \"Obnovenie hesla\",\n    \"Auth.form.button.register\": \"Registrovať sa\",\n    \"Auth.form.confirmPassword.label\": \"Potvrdenie hesla\",\n    \"Auth.form.currentPassword.label\": \"Aktuálne heslo\",\n    \"Auth.form.email.label\": \"E-mailová adresa\",\n    \"Auth.form.email.placeholder\": \"napr. <EMAIL>\",\n    \"Auth.form.error.blocked\": \"Váš účet bol zablokovaný administrátorom.\",\n    \"Auth.form.error.code.provide\": \"Zadaný kód je neplatný.\",\n    \"Auth.form.error.confirmed\": \"Táto e-mailová adresa nie je overená.\",\n    \"Auth.form.error.email.invalid\": \"Táto e-mailová adresa je neplatná.\",\n    \"Auth.form.error.email.provide\": \"Prosím zadajte vaše používateľské meno alebo e-mailovú adresu.\",\n    \"Auth.form.error.email.taken\": \"E-mailová adresa je už registrovaná.\",\n    \"Auth.form.error.invalid\": \"Používateľské meno alebo heslo je nesprávne.\",\n    \"Auth.form.error.params.provide\": \"Zadané hodnoty sú nesprávne.\",\n    \"Auth.form.error.password.format\": \"Vaše heslo obsahuje znak `$` viac ako trikrát.\",\n    \"Auth.form.error.password.local\": \"Tento používateľ si nikdy nenastavil heslo, prosím prihláste sa cez poskytovateľa použitého pri registrácii.\",\n    \"Auth.form.error.password.matching\": \"Heslá sa nezhodujú.\",\n    \"Auth.form.error.password.provide\": \"Prosím zadajte vaše heslo.\",\n    \"Auth.form.error.ratelimit\": \"Príliš veľa pokusov, voľbu opakujte neskôr.\",\n    \"Auth.form.error.user.not-exist\": \"E-mailová adresa neexistuje.\",\n    \"Auth.form.error.username.taken\": \"Zvolené používateľské meno už je registrované\",\n    \"Auth.form.firstname.label\": \"Krstné meno\",\n    \"Auth.form.firstname.placeholder\": \"Janko\",\n    \"Auth.form.forgot-password.email.label\": \"Zadajte e-mailovú adresu\",\n    \"Auth.form.forgot-password.email.label.success\": \"Email bol odoslaný na zadanú adresu\",\n    \"Auth.form.lastname.label\": \"Priezvisko\",\n    \"Auth.form.lastname.placeholder\": \"Hraško\",\n    \"Auth.form.password.hide-password\": \"Schovať heslo\",\n    \"Auth.form.password.hint\": \"Musí obsahovať aspoň 8 znakov, veľké a malé písmeno a číslo\",\n    \"Auth.form.password.show-password\": \"Zobraziť heslo\",\n    \"Auth.form.register.news.label\": \"Informujte ma o nových funkciách a pripravovaných vylepšeniach (týmto akceptujete {terms} a {policy}).\",\n    \"Auth.form.rememberMe.label\": \"Zapamätať si\",\n    \"Auth.form.username.label\": \"Používateľské meno\",\n    \"Auth.form.username.placeholder\": \"Janko Hraško\",\n    \"Auth.form.welcome.subtitle\": \"Prihláste sa do Vášho Strapi účtu\",\n    \"Auth.form.welcome.title\": \"Vitajte v Strapi!\",\n    \"Auth.link.forgot-password\": \"Zabudli ste heslo?\",\n    \"Auth.link.ready\": \"Chcete sa prihlásiť?\",\n    \"Auth.link.signin\": \"Prihlásiť sa\",\n    \"Auth.link.signin.account\": \"Máte už vytvorený účet?\",\n    \"Auth.login.sso.divider\": \"Alebo sa prihláste pomocou\",\n    \"Auth.login.sso.loading\": \"Načítavam poskytovateľov...\",\n    \"Auth.login.sso.subtitle\": \"Prihláste sa pomocou SSO\",\n    \"Auth.privacy-policy-agreement.policy\": \"zásady ochrany osobných údajov\",\n    \"Auth.privacy-policy-agreement.terms\": \"podmienky používania\",\n    \"Auth.reset-password.title\": \"Obnoviť heslo\",\n    \"Content Manager\": \"Správca obsahu\",\n    \"Content Type Builder\": \"Tvorca obsahových typov\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Nahrať súbory\",\n    \"HomePage.head.title\": \"Úvodná stránka\",\n    \"HomePage.roadmap\": \"Pozrite sa na smerovanie projektu\",\n    \"HomePage.welcome.congrats\": \"Hurá!\",\n    \"HomePage.welcome.congrats.content\": \"Ste prihlásený ako prvý administrátor. Na zoznámenie sa s úžasnými funkciami Strapi\",\n    \"HomePage.welcome.congrats.content.bold\": \"Vám odporúčame pokračovať vytvorením prvej kolekcie\",\n    \"Media Library\": \"Knižnica súborov\",\n    \"New entry\": \"Nový záznam\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Roly a oprávnenia\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Niektoré roly nebolo možné odstrániť, pretože sú spojené s používateľmi\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"Rola nemôže byť zmazaná, pretože je spojená s používateľmi.\",\n    \"Roles.components.List.empty.withSearch\": \"Neexistuje žiadna rola, ktorá zodpovedá vyhľadávaniu ({search})...\",\n    \"Settings.PageTitle\": \"Nastavenia - {name}\",\n    \"Settings.application.title\": \"Prehľad\",\n    \"Settings.application.customization\": \"Prispôsobenie\",\n    \"Settings.application.customization.carousel.title\": \"Logo\",\n    \"Settings.application.customization.carousel.change-action\": \"Zmeniť logo\",\n    \"Settings.application.customization.carousel.reset-action\": \"Obnoviť logo\",\n    \"Settings.application.customization.carousel-hint\": \"Zmeniť logo admin panelu (max. rozmery: {dimension}x{dimension}, max. veľkosť: {size}KB)\",\n    \"Settings.application.customization.modal.cancel\": \"Zrušiť\",\n    \"Settings.application.customization.modal.upload\": \"Nahrať logo\",\n    \"Settings.application.customization.modal.tab.label\": \"Ako si prajete nahrať vaše súbory?\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"Z počítača\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"max. rozmery: {dimension}x{dimension}, max. veľkosť: {size}KB\",\n    \"Settings.application.customization.modal.upload.error-format\": \"Zlý formát (podorované formáty: jpeg, jpg, png, svg).\",\n    \"Settings.application.customization.modal.upload.error-size\": \"Nahrávaný súbor je príliš veľký (max. rozmery: {dimension}x{dimension}, max. veľkosť: {size}KB)\",\n    \"Settings.application.customization.modal.upload.error-network\": \"Chyba pripojenia\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"Prehľadávať súbory\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"Drag & Drop alebo\",\n    \"Settings.application.customization.modal.upload.from-url\": \"Z url\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n    \"Settings.application.customization.modal.upload.next\": \"Ďalej\",\n    \"Settings.application.customization.modal.pending\": \"Čakajúce logo\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"Vybrať iné logo\",\n    \"Settings.application.customization.modal.pending.title\": \"Logo je pripravené na nahratie\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"Spravovať vybrané logo pred nahratím\",\n    \"Settings.application.customization.modal.pending.upload\": \"Nahrať logo\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"obrázok\",\n    \"Settings.error\": \"Chyba\",\n    \"Settings.global\": \"Globálne nastavenia\",\n    \"Settings.permissions\": \"Oprávnenia\",\n    \"Settings.permissions.category\": \"Nastavenie oprávnení pre {category}\",\n    \"Settings.permissions.category.plugins\": \"Nastavenie oprávnení pre {category} plugin\",\n    \"Settings.permissions.conditions.anytime\": \"Kedykoľvek\",\n    \"Settings.permissions.conditions.apply\": \"Použiť\",\n    \"Settings.permissions.conditions.can\": \"Môcť\",\n    \"Settings.permissions.conditions.conditions\": \"Zadajte podmienky\",\n    \"Settings.permissions.conditions.links\": \"Odkazy\",\n    \"Settings.permissions.conditions.no-actions\": \"Akcia neexistuje\",\n    \"Settings.permissions.conditions.none-selected\": \"Kedykoľvek\",\n    \"Settings.permissions.conditions.or\": \"ALEBO\",\n    \"Settings.permissions.conditions.when\": \"Kedy\",\n    \"Settings.permissions.select-all-by-permission\": \"Vybrať všetky {label} oprávnenia\",\n    \"Settings.permissions.select-by-permission\": \"Vybrať {label} oprávnenie\",\n    \"Settings.permissions.users.create\": \"Vytvoriť používateľa\",\n    \"Settings.permissions.users.email\": \"Email\",\n    \"Settings.permissions.users.firstname\": \"Krstné meno\",\n    \"Settings.permissions.users.lastname\": \"Priezvisko\",\n    \"Settings.permissions.users.user-status\": \"Status\",\n    \"Settings.permissions.users.roles\": \"Roly\",\n    \"Settings.permissions.users.username\": \"Používateľské meno\",\n    \"Settings.permissions.users.active\": \"Aktívny\",\n    \"Settings.permissions.users.inactive\": \"Neaktívny\",\n    \"Settings.permissions.users.form.sso\": \"Prepojiť s SSO\",\n    \"Settings.permissions.users.form.sso.description\": \"Ak je dostupný (ON), používatelia sa môžu prihlásiť pomocou SSO\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Všetci používatelia, ktorí majú prístup do Strapi admin panelu\",\n    \"Settings.permissions.users.tabs.label\": \"Tabs Oprávnenia\",\n    \"Settings.permissions.users.strapi-super-admin\": \"Super Admin\",\n    \"Settings.permissions.users.strapi-editor\": \"Editor\",\n    \"Settings.permissions.users.strapi-author\": \"Autor\",\n    \"Settings.profile.form.notify.data.loaded\": \"Váš profil bol načítaný\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Odstrániť vybraný jazyk rozhrania\",\n    \"Settings.profile.form.section.experience.here\": \"tu\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Jazyk rozhrania\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Jazyk bude zmenený iba pre Vaše rozhranie.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Tieto nastavenia sa vzťahujú iba na Vás. Viac informácií nájdete {here}.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Mód rozhrania\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Mód rozhrania bude zmenený iba pre Vás.\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name} mód\",\n    light: light,\n    dark: dark,\n    \"Settings.profile.form.section.experience.title\": \"Používateľské rozhranie\",\n    \"Settings.profile.form.section.head.title\": \"Profil používateľa\",\n    \"Settings.profile.form.section.profile.page.title\": \"Profil\",\n    \"Settings.roles.create.description\": \"Definujte práva priradené role\",\n    \"Settings.roles.create.title\": \"Votvoriť rolu\",\n    \"Settings.roles.created\": \"Rola bola vytvorená\",\n    \"Settings.roles.edit.title\": \"Upraviť rolu\",\n    \"Settings.roles.form.button.users-with-role\": \"Používatelia s touto rolou\",\n    \"Settings.roles.form.created\": \"Vytvorené\",\n    \"Settings.roles.form.description\": \"Názov a popis roly\",\n    \"Settings.roles.form.permission.property-label\": \"{label} oprávnenia\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Oprávnenia polí\",\n    \"Settings.roles.form.permissions.create\": \"Vytvoriť\",\n    \"Settings.roles.form.permissions.delete\": \"Vymazať\",\n    \"Settings.roles.form.permissions.publish\": \"Publikovať\",\n    \"Settings.roles.form.permissions.read\": \"Čítať\",\n    \"Settings.roles.form.permissions.update\": \"Zmeniť\",\n    \"Settings.roles.list.button.add\": \"Pridať novú rolu\",\n    \"Settings.roles.list.description\": \"Zoznam rolí\",\n    \"Settings.roles.title.singular\": \"Rola\",\n    \"Settings.sso.description\": \"Upravte nastavenia pre Single Sign-On.\",\n    \"Settings.sso.form.defaultRole.description\": \"Prepojí nového autentifikovaného používateľa s vybranou rolou\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"Pre čítanie admin rolí potrebujete mať potrebné oprávnenia\",\n    \"Settings.sso.form.defaultRole.label\": \"Predvolená rola\",\n    \"Settings.sso.form.registration.description\": \"Vytvoriť nového používateľa pri SSO prihlásení, ak taký účet neexistuje\",\n    \"Settings.sso.form.registration.label\": \"Auto-registrácia\",\n    \"Settings.sso.title\": \"Single Sign-On\",\n    \"Settings.webhooks.create\": \"Vytvoriť Webhook\",\n    \"Settings.webhooks.create.header\": \"Vytvoriť novú hlavičku\",\n    \"Settings.webhooks.created\": \"Webhook bol vytvorený\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Táto udalosť existuje iba pre obsah s povoleným Draft/Publish systémom\",\n    \"Settings.webhooks.events.create\": \"Vytvoriť\",\n    \"Settings.webhooks.events.update\": \"Upraviť\",\n    \"Settings.webhooks.form.events\": \"Eventy\",\n    \"Settings.webhooks.form.headers\": \"Hlavičky\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"Odstrániť riadok hlavičky č. {number}\",\n    \"Settings.webhooks.key\": \"Klúč\",\n    \"Settings.webhooks.list.button.add\": \"Pridať nový Webhook\",\n    \"Settings.webhooks.list.description\": \"Získajte upozornenia na POST request zmeny.\",\n    \"Settings.webhooks.list.empty.description\": \"Pridajte Váš prvý webhook do zoznamu.\",\n    \"Settings.webhooks.list.empty.link\": \"Pozrieť si našu dokumentáciu\",\n    \"Settings.webhooks.list.empty.title\": \"Zatiaľ neexistujú žiadne Webhooky\",\n    \"Settings.webhooks.list.th.actions\": \"akcie\",\n    \"Settings.webhooks.list.th.status\": \"stav\",\n    \"Settings.webhooks.singular\": \"Webhook\",\n    \"Settings.webhooks.title\": \"Webhooky\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# vybraný záznam} few {# vybrané záznamy} other {# vybraných záznamov}}\",\n    \"Settings.webhooks.trigger\": \"Spustiť\",\n    \"Settings.webhooks.trigger.cancel\": \"Zrušiť\",\n    \"Settings.webhooks.trigger.pending\": \"Čakám…\",\n    \"Settings.webhooks.trigger.save\": \"Pre spustenie najskôr uložte\",\n    \"Settings.webhooks.trigger.success\": \"Podarilo sa!\",\n    \"Settings.webhooks.trigger.success.label\": \"Trigger bol úspešný\",\n    \"Settings.webhooks.trigger.test\": \"Testovacie spustenier\",\n    \"Settings.webhooks.trigger.title\": \"Pred spustením najskôr uložte\",\n    \"Settings.webhooks.value\": \"Hodnota\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Používatelia a oprávnenia\",\n    \"Users.components.List.empty\": \"Neexistujú žiadny používatelia...\",\n    \"Users.components.List.empty.withFilters\": \"Neexistujú žiadny používatelia so zvolenými filtrami...\",\n    \"Users.components.List.empty.withSearch\": \"Neexistujú žiadny používatelia zodpovedajúci vyhľadávaniu ({search})...\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Skopírovať do schránky\",\n    \"app.component.search.label\": \"Vyhľadať {target}\",\n    \"app.component.table.duplicate\": \"Duplikovať {target}\",\n    \"app.component.table.edit\": \"Upraviť {target}\",\n    \"app.component.table.select.one-entry\": \"Vybrať {target}\",\n    \"app.components.Button.cancel\": \"Zrušiť\",\n    \"app.components.Button.confirm\": \"Potvrdiť\",\n    \"app.components.Button.reset\": \"Obnoviť\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Už čoskoro\",\n    \"app.components.ConfirmDialog.title\": \"Potvrdenie\",\n    \"app.components.DownloadInfo.download\": \"Prebieha sťahovanie...\",\n    \"app.components.DownloadInfo.text\": \"Toto bude chvíľu trvať, prosíme o trpezlivosť.\",\n    \"app.components.EmptyAttributes.title\": \"Zatiaľ tu nie sú žiadne políčka\",\n    \"app.components.EmptyStateLayout.content-document\": \"Žiadny nájdený obsah\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"Nemáte oprávnenia pre prístup k tomuto obsahu\",\n    \"app.components.HomePage.button.blog\": \"Čítať na blogu\",\n    \"app.components.HomePage.community\": \"Nájdite komunitu na webe\",\n    \"app.components.HomePage.community.content\": \"Komunikujte s členmi tímu a vývojármi, zdieľajte vaše problémy a nápady.\",\n    \"app.components.HomePage.create\": \"Vytvorte váš prvý obsahový typ\",\n    \"app.components.HomePage.roadmap\": \"Pozrite si našu roadmapu\",\n    \"app.components.HomePage.welcome\": \"Vitajte na palube 👋\",\n    \"app.components.HomePage.welcome.again\": \"Vitajte 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"Sme radi, že ste súčasťou komunity. Vždy sa tešíme na spätnú väzbu, preto nás neváhajte kontaktovať správou\",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Dúfame, že sa vám darí s vaším projektom. Pozrite si čo je nové v Strapi. Vždy sa snažíme vylepšovať produkt na základe spätnej väzby.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"problém.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" alebo nahláste \",\n    \"app.components.ImgPreview.hint\": \"Pretiahnite súbory alebo {browse}\",\n    \"app.components.ImgPreview.hint.browse\": \"vyberte\",\n    \"app.components.InputFile.newFile\": \"Pridať súbor\",\n    \"app.components.InputFileDetails.open\": \"Otvoriť v novom okne\",\n    \"app.components.InputFileDetails.originalName\": \"Pôvodný názov:\",\n    \"app.components.InputFileDetails.remove\": \"Odstrániť tento súbor\",\n    \"app.components.InputFileDetails.size\": \"Veľkosť:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Môže to chvíľu trvať, kým sa plugin stiahne a nainštaluje.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Sťahovanie...\",\n    \"app.components.InstallPluginPage.description\": \"Rozšírte možnosti aplikácie bez námahy.\",\n    \"app.components.LeftMenu.collapse\": \"Zbaliť navigačný panel\",\n    \"app.components.LeftMenu.expand\": \"Rozbaliť navigačný panel\",\n    \"app.components.LeftMenu.general\": \"Všeoecné\",\n    \"app.components.LeftMenu.logout\": \"Odhlásiť sa\",\n    \"app.components.LeftMenu.logo.alt\": \"Logo aplikácie\",\n    \"app.components.LeftMenu.plugins\": \"Pluginy\",\n    \"app.components.LeftMenu.trialCountdown\": \"Váš prečerávanie skončí {date}.\",\n    \"app.components.LeftMenu.navbrand.title\": \"Strapi Dashboard\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"Workplace\",\n    \"app.components.LeftMenuFooter.help\": \"Pomoc\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Poháňané \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Kolekcie\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Konfigurácia\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Všeobecné\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Žiadne pluginy nie sú zatiaľ nainštalované\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Pluginy\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Jednoduché typy\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"Odinštalovanie pluginu môže trvať niekoľko sekúnd.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Odstraňujem plugin\",\n    \"app.components.ListPluginsPage.description\": \"Zoznam nainštalovaných pluginov v tomto projekte.\",\n    \"app.components.ListPluginsPage.head.title\": \"Zoznam pluginov\",\n    \"app.components.Logout.logout\": \"Odhlásiť sa\",\n    \"app.components.Logout.profile\": \"Profil\",\n    \"app.components.NotFoundPage.back\": \"Naspať na úvodnú stránku\",\n    \"app.components.NotFoundPage.description\": \"Nenájdené\",\n    \"app.components.Official\": \"Oficiálne\",\n    \"app.components.Onboarding.help.button\": \"Tlačidlo pomoci\",\n    \"app.components.Onboarding.label.completed\": \"% dokončené\",\n    \"app.components.Onboarding.title\": \"Začíname\",\n    \"app.components.PluginCard.Button.label.download\": \"Stiahnuť\",\n    \"app.components.PluginCard.Button.label.install\": \"Už nainštalované\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Funkcia autoReload musí byť vypnutá. Prosím zapnite aplikíciu cez `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Rozumiem!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Kvôli bezpečnosti môže byť plugin nainštalovaný iba v development prostredí.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Nie je možné stiahnuť\",\n    \"app.components.PluginCard.compatible\": \"Kompatibilné s vašou aplikáciou\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Kompatibilné s komunitou\",\n    \"app.components.PluginCard.more-details\": \"Viac detailov\",\n    \"app.components.ToggleCheckbox.off-label\": \"False\",\n    \"app.components.ToggleCheckbox.on-label\": \"True\",\n    \"app.components.Users.MagicLink.connect\": \"Skopírujte a zazdieľajte tento link používateľovi pre pihlásenie.\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Odošlite tento link používateľovi. Prvé prihlásenie môže byť vykonané cez SSO poskytovateľa\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Detaily\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Používateľové roly\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Používateľ môže mať jednu alebo viacer rolí\",\n    \"app.components.Users.SortPicker.button-label\": \"Zoradiť podľa\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"Email (A do Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"Email (Z do A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Krstné meno (A do Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Krstné meno (Z do A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Priezvisko (A do Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Priezvisko (Z do A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Používateľské meno (A do Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Používateľské meno (Z do A)\",\n    \"app.components.listPlugins.button\": \"Pridať nový plugin\",\n    \"app.components.listPlugins.title.none\": \"Nie sú nainštalované žiadne pluginy\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Nastala chyba pri odinštalovávaní pluginu\",\n    \"app.containers.App.notification.error.init\": \"Nastala chyba pri komunikácii s API serverom\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Ak ste neobdržali tento link, tak prosím kontaktujte administrátora.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Obdržanie odkazu na obnovenie hesla môže trvať pár minút.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email bol odoslaný\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Aktívny\",\n    \"app.containers.Users.EditPage.header.label\": \"Upraviť {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Upraviť používateľa\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Priradené roly\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Vytvoriť používateľa\",\n    \"app.links.configure-view\": \"Upraviť zobrazenie\",\n    \"app.page.not.found\": \"Hups! Vyzerá to tak, že stránku, ktorú hľadáte, nevieme nájsť...\",\n    \"app.static.links.cheatsheet\": \"CheatSheet\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Pridať filter\",\n    \"app.utils.close-label\": \"Zavrieť\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"Duplikovať\",\n    \"app.utils.edit\": \"Upraviť\",\n    \"app.utils.delete\": \"Vymazať\",\n    \"app.utils.errors.file-too-big.message\": \"Súbor je príliš veľký\",\n    \"app.utils.filter-value\": \"Hodnota filtra\",\n    \"app.utils.filters\": \"Filtre\",\n    \"app.utils.notify.data-loaded\": \"{target} sa načítal\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Publikovať\",\n    \"app.utils.select-all\": \"Zvoliť všetky\",\n    \"app.utils.select-field\": \"Zvoliť políčko\",\n    \"app.utils.select-filter\": \"Zvoliť filter\",\n    \"app.utils.unpublish\": \"Odpublikovať\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Tento obsah sa práve pripravuje a bude dostupný už o niekoľko týždňov!\",\n    \"component.Input.error.validation.integer\": \"Táto hodnota musí byť číslo\",\n    \"components.AutoReloadBlocker.description\": \"Spustite Strapi s jedným z nasledujúcich príkazov:\",\n    \"components.AutoReloadBlocker.header\": \"Pre tento plugin je požadované opätovné načítanie stránky.\",\n    \"components.ErrorBoundary.title\": \"Niečo sa pokazilo...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"obsahuje\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"obsahuje (nerozlišujú sa malé a veľké písmená)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"končí na\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"končí na (nerozlišujú sa malé a veľké písmená)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"je\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"je (nerozlišujú sa malé a veľké písmená)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"je väčší ako\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"je väčší alebo rovný ako\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"je menší ako\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"je menčí alebo rovný ako\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"sa nerovná\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"sa nerovná (nerozlišujú sa malé a veľké písmená)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"neobsahuje\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"neobsahuje (nerozlišujú sa malé a veľké písmená)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"nie je null\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"je null\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"začína na\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"začína na (nerozlišujú sa malé a veľké písmená)\",\n    \"components.Input.error.attribute.key.taken\": \"Táto hodnota už existuje\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Nemôže sa rovnať\",\n    \"components.Input.error.attribute.taken\": \"Plíčko s týmto názvom už existuje\",\n    \"components.Input.error.contain.lowercase\": \"Heslo musí obsahovať aspoň jedno malé písmeno\",\n    \"components.Input.error.contain.number\": \"Heslo musí obsahovať aspoň jedno číslo\",\n    \"components.Input.error.contain.uppercase\": \"Heslo musí obsahovať aspoň jedo veľké písmeno\",\n    \"components.Input.error.contentTypeName.taken\": \"Obsahový typ s týmto názvom už existuje\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Heslá sa nezhodujú\",\n    \"components.Input.error.validation.email\": \"Neplatná e-mailová adresa\",\n    \"components.Input.error.validation.json\": \"Táto hodnota nespĺňa JSON formát\",\n    \"components.Input.error.validation.lowercase\": \"Táto hodnota môže obrahovať iba malé písmená\",\n    \"components.Input.error.validation.max\": \"Táto hodnota je príliš vysoká {max}.\",\n    \"components.Input.error.validation.maxLength\": \"Táto hodnota je príliš dlhá {max}.\",\n    \"components.Input.error.validation.min\": \"Táto hodnota je príliš nízka {min}.\",\n    \"components.Input.error.validation.minLength\": \"Táto hodnota je príliš krátka {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Nemôže byť nadriadený\",\n    \"components.Input.error.validation.regex\": \"Táto hodnota nespĺňa požadovaný vzor (regex).\",\n    \"components.Input.error.validation.required\": \"Táto hodnota je povinná.\",\n    \"components.Input.error.validation.unique\": \"Táto hodnota sa už používa.\",\n    \"components.InputSelect.option.placeholder\": \"Vyberte\",\n    \"components.ListRow.empty\": \"Žiadne dáta na zobrazenie.\",\n    \"components.NotAllowedInput.text\": \"Nedostatočé oprávenia na zobrazenie tohto poľa\",\n    \"components.OverlayBlocker.description\": \"Používate funkciu, ktorá vyžaduje reštart servera. Počkajte prosím, kým bude server pripravený.\",\n    \"components.OverlayBlocker.description.serverError\": \"Server by sa mal teraz reštartovať. Skontrolujte logy v terminály.\",\n    \"components.OverlayBlocker.title\": \"Čaká sa na reštart...\",\n    \"components.OverlayBlocker.title.serverError\": \"Reštart servera trvá dlhšie ako sa očakávalo\",\n    \"components.PageFooter.select\": \"výsledkov na stránku\",\n    \"components.ProductionBlocker.description\": \"Z bezpečnostných dôvodov je tento plugin zablokovaný v iných prostrediach.\",\n    \"components.ProductionBlocker.header\": \"Tento plugin je dostupný iba v development prostredí.\",\n    \"components.Search.placeholder\": \"Hľadať...\",\n    \"components.TableHeader.sort\": \"Zoradiť podľa {label}\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown mód\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Mód náhľadu\",\n    \"components.Wysiwyg.collapse\": \"Zbaliť\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Nadpis H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Nadpis H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Nadpis H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Nadpis H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Nadpis H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Nadpis H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Pridať názov\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"znaky\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Rozbaliť\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Pretiahnite súbory, vložte zo schránky alebo {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"vyberte\",\n    \"components.pagination.go-to\": \"Ísť na stranu {page}\",\n    \"components.pagination.go-to-next\": \"Ísť na nasledujúcu stranu\",\n    \"components.pagination.go-to-previous\": \"Ísť na predošlú stranu\",\n    \"components.pagination.remaining-links\": \"Pridať {number} ďalších odkazov\",\n    \"components.popUpWarning.button.cancel\": \"Nie, zrušiť\",\n    \"components.popUpWarning.button.confirm\": \"Áno, potvrdiť\",\n    \"components.popUpWarning.message\": \"Ste si istý, že to chcete odstrániť?\",\n    \"components.popUpWarning.title\": \"Prosím potvrďte\",\n    \"form.button.continue\": \"Pokračovať\",\n    \"form.button.done\": \"Hotovo\",\n    \"global.search\": \"Hľadať\",\n    \"global.actions\": \"Akcie\",\n    \"global.back\": \"Späť\",\n    \"global.cancel\": \"Zrušiť\",\n    \"global.change-password\": \"Zmeniť heslo\",\n    \"global.content-manager\": \"Správca obsahu\",\n    \"global.continue\": \"Pokračovať\",\n    \"global.delete\": \"Vymazať\",\n    \"global.delete-target\": \"Vymazať {target}\",\n    \"global.description\": \"Popis\",\n    \"global.details\": \"Detaily\",\n    \"global.disabled\": \"Disabled\",\n    \"global.documentation\": \"Dokumentácia\",\n    \"global.enabled\": \"Enabled\",\n    \"global.finish\": \"Dokončiť\",\n    \"global.marketplace\": \"Marketplace\",\n    \"global.name\": \"Meno\",\n    \"global.none\": \"Žiadne\",\n    \"global.password\": \"Heslo\",\n    \"global.plugins\": \"Pluginy\",\n    \"global.profile\": \"Profil\",\n    \"global.prompt.unsaved\": \"Ak opustíte túto stránku všetky zmeny budú zahodené. Chcete pokračovať?\",\n    \"global.reset-password\": \"Obnoviť heslo\",\n    \"global.roles\": \"Roly\",\n    \"global.save\": \"Uložiť\",\n    \"global.see-more\": \"Zobraziť viac\",\n    \"global.select\": \"Vybrať\",\n    \"global.select-all-entries\": \"Vybrať všetky záznamy\",\n    \"global.settings\": \"Nastavenia\",\n    \"global.type\": \"Typ\",\n    \"global.users\": \"Používatelia\",\n    \"notification.contentType.relations.conflict\": \"Niektoré prepojenia v obsahovom type sú konfliktné\",\n    \"notification.default.title\": \"Informácie:\",\n    \"notification.error\": \"Nastala chyba\",\n    \"notification.error.layout\": \"Nepodarilo sa načítať rozloženie\",\n    \"notification.form.error.fields\": \"Formulár obsahuje chyby\",\n    \"notification.form.success.fields\": \"Zmeny boli uložené\",\n    \"notification.link-copied\": \"Odkaz bol skopírovaný\",\n    \"notification.permission.not-allowed-read\": \"Nemáte povolený prístup k tomuto dokumentu\",\n    \"notification.success.delete\": \"Položka bola odstránená\",\n    \"notification.success.saved\": \"Uložené\",\n    \"notification.success.title\": \"Podarilo sa:\",\n    \"notification.success.apitokencreated\": \"API Token úspešne vytvorený\",\n    \"notification.success.apitokenedited\": \"API Token úspešne upravený\",\n    \"notification.error.tokennamenotunique\": \"Názov už je priradený inému tokenu\",\n    \"notification.version.update.message\": \"Nová verzia Strapi je dostupná!\",\n    \"notification.warning.title\": \"Upozornenie:\",\n    \"notification.warning.404\": \"404 - nenájdené\",\n    or: or,\n    \"request.error.model.unknown\": \"Tento model neexistuje\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, sk as default, light, or, skipToContent, submit };\n//# sourceMappingURL=sk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,8BAA8B;AAAA,EAC9B,sCAAsC;AAAA,EACtC,qDAAqD;AAAA,EACrD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,8DAA8D;AAAA,EAC9D,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,mEAAmE;AAAA,EACnE,0DAA0D;AAAA,EAC1D,6DAA6D;AAAA,EAC7D,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}