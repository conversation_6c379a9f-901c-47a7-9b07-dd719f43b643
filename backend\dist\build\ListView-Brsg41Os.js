import{aL as b,j as e,P as o,a as N,w as R,z as F,V,b as v,v as I,r as g,aC as D,B as T,at as p,a3 as h,av as y,as as L,s as x}from"./strapi-z7ApxZZq.js";import{u as _,T as B}from"./Table-CPnHPqLc.js";import{c as U,d as H}from"./transferTokens-DDbhemls.js";import{T as i}from"./constants-Q2dfXdfa.js";const z=[{name:"name",label:{id:"Settings.tokens.ListView.headers.name",defaultMessage:"Name"},sortable:!0},{name:"description",label:{id:"Settings.tokens.ListView.headers.description",defaultMessage:"Description"},sortable:!1},{name:"createdAt",label:{id:"Settings.tokens.ListView.headers.createdAt",defaultMessage:"Created at"},sortable:!1},{name:"lastUsedAt",label:{id:"Settings.tokens.ListView.headers.lastUsedAt",defaultMessage:"Last used"},sortable:!1}],O=()=>{const{formatMessage:s}=N(),{toggleNotification:a}=R(),j=b(n=>n.admin_app.permissions.settings?.["transfer-tokens"]),{isLoading:A,allowedActions:{canCreate:d,canDelete:w,canUpdate:S,canRead:l}}=F(j),u=V(),{trackUsage:r}=v(),{_unstableFormatAPIError:c}=I();g.useEffect(()=>{u({search:D.stringify({sort:"name:ASC"},{encode:!1})})},[u]),_(()=>{r("willAccessTokenList",{tokenType:i})});const M=z.map(n=>({...n,label:s(n.label)})),{data:t=[],isLoading:E,error:f}=U(void 0,{skip:!l});g.useEffect(()=>{t&&r("didAccessTokenList",{number:t.length,tokenType:i})},[r,t]),g.useEffect(()=>{f&&a({type:"danger",message:c(f)})},[f,c,a]);const[C]=H(),P=async n=>{try{const k=await C(n);"error"in k&&a({type:"danger",message:c(k.error)})}catch{a({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occured"})})}},m=E||A;return e.jsxs(e.Fragment,{children:[e.jsx(o.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Transfer Tokens"})}),e.jsx(T.Header,{title:s({id:"Settings.transferTokens.title",defaultMessage:"Transfer Tokens"}),subtitle:s({id:"Settings.transferTokens.description",defaultMessage:'"List of generated transfer tokens"'}),primaryAction:d?e.jsx(p,{role:"button",tag:h,"data-testid":"create-transfer-token-button",startIcon:e.jsx(y,{}),size:"S",onClick:()=>r("willAddTokenFromList",{tokenType:i}),to:"/settings/transfer-tokens/create",children:s({id:"Settings.transferTokens.create",defaultMessage:"Create new Transfer Token"})}):void 0}),l?e.jsx(o.Main,{"aria-busy":m,children:e.jsxs(T.Content,{children:[t.length>0&&e.jsx(B,{permissions:{canRead:l,canDelete:w,canUpdate:S},headers:M,isLoading:m,onConfirmDelete:P,tokens:t,tokenType:i}),d&&t.length===0?e.jsx(L,{action:e.jsx(p,{tag:h,variant:"secondary",startIcon:e.jsx(y,{}),to:"/settings/transfer-tokens/create",children:s({id:"Settings.transferTokens.addNewToken",defaultMessage:"Add new Transfer Token"})}),icon:e.jsx(x,{width:"16rem"}),content:s({id:"Settings.transferTokens.addFirstToken",defaultMessage:"Add your first Transfer Token"})}):null,!d&&t.length===0?e.jsx(L,{icon:e.jsx(x,{width:"16rem"}),content:s({id:"Settings.transferTokens.emptyStateLayout",defaultMessage:"You don’t have any content yet..."})}):null]})}):e.jsx(o.NoPermissions,{})]})},K=()=>{const s=b(a=>a.admin_app.permissions.settings?.["transfer-tokens"].main);return e.jsx(o.Protect,{permissions:s,children:e.jsx(O,{})})};export{O as ListView,K as ProtectedListView};
