import{a as v,b as _,a4 as je,u as H,df as ye,r as C,ew as le,bk as oe,dz as be,b3 as ce,ex as Ce,dS as Q,j as e,ct as N,ey as Se,K as Le,N as Me,O as Te,ez as Ae,eA as Ie,cf as A,a6 as Re,T as S,b8 as K,eB as Z,a5 as de,e as O,k as J,dM as ue,am as Y,aM as Fe,eC as Ee,ar as P,L as we,eD as z,z as pe,eE as q,I as ke,cg as ve,at as Pe,aC as V,au as Oe,eF as Be,dY as me,dA as $e,aK as De,bp as He,V as Ne,w as Ue,v as Ve,eG as ge,c6 as We,dZ as _e,eH as ze,dC as qe,a7 as Ge,di as Qe,P as k,bw as Ke,$ as X,bg as ee,B as D,eI as te,as as Je,s as Ye,bE as b,a0 as Ze,eJ as Xe,cu as G,b9 as et,dQ as tt,dW as st,D as nt,a3 as at,av as it,eK as rt,eL as lt,eM as ot}from"./strapi-z7ApxZZq.js";import{S as se}from"./SearchInput-Cmj1ynyp.js";import{u as ct}from"./hooks-DCxDPOZs.js";const ne=["json","component","media","richtext","dynamiczone","password","blocks"],dt=["createdAt","updatedAt"],ut=[...le,"strapi_assignee"],ae=({disabled:t,schema:n})=>{const{attributes:s,uid:a,options:l}=n,{formatMessage:o,locale:j}=v(),{trackUsage:d}=_(),i=je("FiltersImpl",h=>h.permissions),[{query:g}]=H(),{schemas:u}=ye(),L=C.useMemo(()=>i.filter(h=>h.action==="admin::users.read"&&h.subject===null).length>0,[i]),M=(g?.filters?.$and??[]).reduce((h,F)=>{const[f,x]=Object.entries(F)[0];if(typeof x.id!="object")return h;const r=x.id.$eq||x.id.$ne;return r&&ut.includes(f)&&!h.includes(r)&&h.push(r),h},[]),{data:y,isLoading:p}=oe({filters:{id:{$in:M}}},{skip:M.length===0||!L}),{users:m=[]}=y??{},{metadata:I}=be(a,{selectFromResult:({data:h})=>({metadata:h?.contentType.metadatas??{}})}),B=ce(j,{sensitivity:"base"}),R=C.useMemo(()=>{const[{properties:{fields:h=[]}={fields:[]}}]=i.filter(f=>f.action==="plugin::content-manager.explorer.read"&&f.subject===a);return["id","documentId",...h.filter(f=>{const x=s[f]??{};return x.type&&!ne.includes(x.type)}),...dt,...L?le:[]].map(f=>{const x=s[f];if(ne.includes(x.type))return null;const{mainField:r="",label:E}=I[f].list;let c={name:f,label:E??"",mainField:Ce(x,r,{schemas:u,components:{}}),type:x.type};return x.type==="relation"&&"target"in x&&x.target==="admin::user"&&(c={...c,input:pt,options:m.map(w=>({label:Q(w),value:w.id.toString()})),operators:[{label:o({id:"components.FilterOptions.FILTER_TYPES.$eq",defaultMessage:"is"}),value:"$eq"},{label:o({id:"components.FilterOptions.FILTER_TYPES.$ne",defaultMessage:"is not"}),value:"$ne"}],mainField:{name:"id",type:"integer"}}),x.type==="enumeration"&&(c={...c,options:x.enum.map(w=>({label:w,value:w}))}),c}).filter(Boolean).toSorted((f,x)=>B.compare(f.label,x.label))},[i,L,a,s,I,u,m,o,B]),T=h=>{h&&d("willFilterEntries")},$=h=>{const F=s[h.name];F&&d("didFilterEntries",{useRelation:F.type==="relation"})};return e.jsxs(N.Root,{disabled:t,options:R,onOpenChange:T,onChange:$,children:[e.jsx(N.Trigger,{}),e.jsx(N.Popover,{}),e.jsx(N.List,{})]})},pt=({name:t})=>{const[n,s]=C.useState(10),[a,l]=C.useState(""),{formatMessage:o}=v(),j=Se(a,300),{data:d,isLoading:i}=oe({pageSize:n,_q:j}),g=Le(t),u=m=>{m||s(10)},{users:L=[],pagination:M}=d??{},{pageCount:y=1,page:p=1}=M??{};return e.jsx(Me,{value:g.value,"aria-label":o({id:"content-manager.components.Filters.usersSelect.label",defaultMessage:"Search and select a user to filter"}),onOpenChange:u,onChange:m=>g.onChange(t,m),loading:i,onLoadMore:()=>s(n+10),hasMoreItems:p<y,onInputChange:m=>{l(m.currentTarget.value)},children:L.map(m=>e.jsx(Te,{value:m.id.toString(),children:Q(m)},m.id))})},W=({type:t,value:n})=>{const{formatDate:s,formatTime:a,formatNumber:l}=v();let o=n;if(t==="date"&&(o=s(Ae(n),{dateStyle:"full"})),t==="datetime"&&(o=s(n,{dateStyle:"full",timeStyle:"short"})),t==="time"){const[j,d,i]=n.split(":"),g=new Date;g.setHours(j),g.setMinutes(d),g.setSeconds(i),o=a(g,{timeStyle:"short"})}return["float","decimal"].includes(t)&&(o=l(n,{maximumFractionDigits:20})),["integer","biginteger"].includes(t)&&(o=l(n,{maximumFractionDigits:0})),Ie(o)},mt=({content:t,mainField:n})=>n?e.jsx(K,{label:t[n.name],children:e.jsx(S,{maxWidth:"25rem",textColor:"neutral800",ellipsis:!0,children:e.jsx(W,{type:n.type,value:t[n.name]})})}):null,gt=({content:t,mainField:n})=>{const{formatMessage:s}=v();return n?e.jsxs(A.Root,{children:[e.jsxs(A.Trigger,{onClick:a=>a.stopPropagation(),children:[e.jsx(Re,{children:t.length}),s({id:"content-manager.containers.list.items",defaultMessage:"{number, plural, =0 {items} one {item} other {items}}"},{number:t.length})]}),e.jsx(A.Content,{children:t.map(a=>e.jsx(A.Item,{disabled:!0,children:e.jsx(S,{maxWidth:"50rem",ellipsis:!0,children:e.jsx(W,{type:n.type,value:a[n.name]})})},a.id))})]}):null},ht=t=>t&&t[0]==="."?t.substring(1):t,he=({url:t,mime:n,alternativeText:s,name:a,ext:l,formats:o})=>{const j=Z(t);if(n.includes("image")){const g=o?.thumbnail?.url,u=Z(g)||j;return e.jsx(de.Item,{src:u,alt:s||a,fallback:s||a,preview:!0})}const d=ht(l),i=a.length>100?`${a.substring(0,100)}...`:a;return e.jsx(K,{description:i,children:e.jsx(fe,{children:d})})},fe=({children:t})=>e.jsx(O,{tag:"span",position:"relative",borderRadius:"50%",width:"26px",height:"26px",borderColor:"neutral200",background:"neutral150",paddingLeft:"1px",justifyContent:"center",alignItems:"center",children:e.jsx(ft,{variant:"sigma",textColor:"neutral600",children:t})}),ft=J(S)`
  font-size: 0.9rem;
  line-height: 0.9rem;
`,xt=({content:t})=>e.jsx(de.Group,{children:t.map((n,s)=>{const a=`${n.id}${s}`;if(s===3){const l=`+${t.length-3}`;return e.jsx(fe,{children:l},a)}return s>3?null:e.jsx(he,{...n},a)})}),jt=({mainField:t,content:n})=>e.jsx(S,{maxWidth:"50rem",textColor:"neutral800",ellipsis:!0,children:ue(n,t)}),yt=({mainField:t,content:n,rowId:s,name:a})=>{const{model:l}=Y(),{formatMessage:o}=v(),{notifyStatus:j}=Fe(),[d,i]=C.useState(!1),[g]=a.split("."),{data:u,isLoading:L}=Ee({model:l,id:s,targetField:g},{skip:!d,refetchOnMountOrArgChange:!0}),M=Array.isArray(n)?n.length:n.count;return C.useEffect(()=>{u&&j(o({id:P("DynamicTable.relation-loaded"),defaultMessage:"Relations have been loaded"}))},[u,o,j]),e.jsxs(A.Root,{onOpenChange:y=>i(y),children:[e.jsx(A.Trigger,{onClick:y=>y.stopPropagation(),children:e.jsx(S,{style:{cursor:"pointer"},textColor:"neutral800",fontWeight:"regular",children:M>0?o({id:"content-manager.containers.list.items",defaultMessage:"{number} {number, plural, =0 {items} one {item} other {items}}"},{number:M}):"-"})}),e.jsxs(A.Content,{children:[L&&e.jsx(A.Item,{disabled:!0,children:e.jsx(we,{small:!0,children:o({id:P("ListViewTable.relation-loading"),defaultMessage:"Relations are loading"})})}),u?.results&&e.jsxs(e.Fragment,{children:[u.results.map(y=>e.jsx(A.Item,{children:e.jsx(S,{maxWidth:"50rem",ellipsis:!0,children:ue(y,t)})},y.documentId)),u?.pagination&&u?.pagination.total>10&&e.jsx(A.Item,{"aria-disabled":!0,"aria-label":o({id:P("ListViewTable.relation-more"),defaultMessage:"This relation contains more entities than displayed"}),children:e.jsx(S,{children:"…"})})]})]})]})},bt=({content:t,mainField:n,attribute:s,rowId:a,name:l})=>{if(!Ct(t,n,s))return e.jsx(S,{textColor:"neutral800",paddingLeft:s.type==="relation"?"1.6rem":0,paddingRight:s.type==="relation"?"1.6rem":0,children:"-"});switch(s.type){case"media":return s.multiple?e.jsx(xt,{content:t}):e.jsx(he,{...t});case"relation":return xe(s.relation)?e.jsx(jt,{mainField:n,content:t}):e.jsx(yt,{rowId:a,mainField:n,content:t,name:l});case"component":return s.repeatable?e.jsx(gt,{mainField:n,content:t}):e.jsx(mt,{mainField:n,content:t});case"string":return e.jsx(K,{description:t,children:e.jsx(S,{maxWidth:"30rem",ellipsis:!0,textColor:"neutral800",children:e.jsx(W,{type:s.type,value:t})})});default:return e.jsx(S,{maxWidth:"30rem",ellipsis:!0,textColor:"neutral800",children:e.jsx(W,{type:s.type,value:t})})}},Ct=(t,n,s)=>{if(s.type==="component"){if(s.repeatable||!n)return t?.length>0;const a=t?.[n.name];return n.name==="id"&&![void 0,null].includes(a)?!0:!z(a)}return s.type==="relation"?xe(s.relation)?!z(t):Array.isArray(t)?t.length>0:t?.count>0:["integer","decimal","float","number"].includes(s.type)?typeof t=="number":s.type==="boolean"?t!==null:!z(t)},xe=t=>["oneToOne","manyToOne","oneToOneMorph"].includes(t),ie=t=>{const n=ct(o=>o.admin_app.permissions.contentManager?.collectionTypesConfigurations??[]),[{query:s}]=H(),{formatMessage:a}=v(),{allowedActions:{canConfigureView:l}}=pe(n);return e.jsxs(q.Root,{children:[e.jsx(q.Trigger,{children:e.jsx(ke,{label:a({id:"components.ViewSettings.tooltip",defaultMessage:"View Settings"}),children:e.jsx(ve,{})})}),e.jsx(q.Content,{side:"bottom",align:"end",sideOffset:4,children:e.jsxs(O,{alignItems:"stretch",direction:"column",padding:3,gap:3,children:[l?e.jsx(Pe,{size:"S",startIcon:e.jsx(Be,{}),variant:"secondary",tag:Oe,to:{pathname:"configurations/list",search:s.plugins?V.stringify({plugins:s.plugins},{encode:!1}):""},children:a({id:"app.links.configure-view",defaultMessage:"Configure the view"})}):null,e.jsx(St,{...t})]})})]})},St=({headers:t=[],resetHeaders:n,setHeaders:s})=>{const{trackUsage:a}=_(),{formatMessage:l,locale:o}=v(),{schema:j,model:d}=Y(),{list:i}=me(d),g=ce(o,{sensitivity:"base"}),u=j?.attributes??{},L=Object.keys(u).filter(p=>$e(u[p])).map(p=>({name:p,label:i.metadatas[p]?.label??""})).sort((p,m)=>g.compare(p.label,m.label)),M=p=>{a("didChangeDisplayedFields");const m=t.includes(p)?t.filter(I=>I!==p):[...t,p];s(m)},y=()=>{n()};return e.jsxs(O,{tag:"fieldset",direction:"column",alignItems:"stretch",gap:3,borderWidth:0,maxHeight:"240px",overflow:"scroll",children:[e.jsxs(O,{justifyContent:"space-between",children:[e.jsx(S,{tag:"legend",variant:"pi",fontWeight:"bold",children:l({id:"containers.list.displayedFields",defaultMessage:"Displayed fields"})}),e.jsx(De,{onClick:y,children:l({id:"app.components.Button.reset",defaultMessage:"Reset"})})]}),e.jsx(O,{direction:"column",alignItems:"stretch",children:L.map(p=>{const m=t.includes(p.name);return e.jsx(O,{wrap:"wrap",gap:2,background:m?"primary100":"transparent",hasRadius:!0,padding:2,children:e.jsx(He,{onCheckedChange:()=>M(p.name),checked:m,name:p.name,children:e.jsx(S,{fontSize:1,children:p.label})})},p.name)})})]})},{INJECT_COLUMN_IN_TABLE:Lt}=Qe,re=J(D.Header)`
  overflow-wrap: anywhere;
`,Mt=()=>{const{trackUsage:t}=_(),n=Ne(),{formatMessage:s}=v(),{toggleNotification:a}=Ue(),{_unstableFormatAPIError:l}=Ve(P),{collectionType:o,model:j,schema:d}=Y(),{list:i}=me(j),[g,u]=C.useState([]),L=ge(i.layout);C.useEffect(()=>{We(L,i.layout)||u(i.layout)},[i.layout,L]);const M=r=>{u(rt(r,d.attributes,i.metadatas))},[{query:y}]=H({page:"1",pageSize:i.settings.pageSize.toString(),sort:i.settings.defaultSortBy?`${i.settings.defaultSortBy}:${i.settings.defaultSortOrder}`:""}),p=C.useMemo(()=>_e(y),[y]),{data:m,error:I,isFetching:B}=ze({model:j,params:p});C.useEffect(()=>{I&&a({type:"danger",message:l(I)})},[I,l,a]);const{results:R=[],pagination:T}=m??{};C.useEffect(()=>{T&&T.pageCount>0&&T.page>T.pageCount&&n({search:V.stringify({...y,page:T.pageCount})},{replace:!0})},[T,s,y,n]);const{canCreate:$}=qe("ListViewPage",({canCreate:r})=>({canCreate:r})),h=Ge("ListViewPage",({runHookWaterfall:r})=>r),F=C.useMemo(()=>{const E=h(Lt,{displayedHeaders:g,layout:i}).displayedHeaders.map(c=>{const w=typeof c.label=="string"?{id:`content-manager.content-types.${j}.${c.name}`,defaultMessage:c.label}:c.label;return{...c,label:s(w),name:`${c.name}${c.mainField?.name?`.${c.mainField.name}`:""}`}});return d?.options?.draftAndPublish&&E.push({attribute:{type:"custom"},name:"status",label:s({id:P("containers.list.table-headers.status"),defaultMessage:"status"}),searchable:!1,sortable:!1}),E},[g,s,i,h,d?.options?.draftAndPublish,j]);if(B)return e.jsx(k.Loading,{});if(I)return e.jsx(k.Error,{});const f=d?.info.displayName?s({id:d.info.displayName,defaultMessage:d.info.displayName}):s({id:"content-manager.containers.untitled",defaultMessage:"Untitled"}),x=r=>()=>{t("willEditEntryFromList"),n({pathname:r.toString(),search:V.stringify({plugins:y.plugins})})};return!B&&R.length===0?e.jsxs(e.Fragment,{children:[e.jsx(Ke.contentManager.Introduction,{children:e.jsx(X,{paddingTop:5})}),e.jsxs(k.Main,{children:[e.jsx(k.Title,{children:`${f}`}),e.jsx(re,{primaryAction:$?e.jsx(U,{}):null,subtitle:s({id:P("pages.ListView.header-subtitle"),defaultMessage:"{number, plural, =0 {# entries} one {# entry} other {# entries}} found"},{number:T?.total}),title:f,navigationAction:e.jsx(ee,{})}),e.jsx(D.Action,{endActions:e.jsxs(e.Fragment,{children:[e.jsx(te,{area:"listView.actions"}),e.jsx(ie,{setHeaders:M,resetHeaders:()=>u(i.layout),headers:g.map(r=>r.name)})]}),startActions:e.jsxs(e.Fragment,{children:[i.settings.searchable&&e.jsx(se,{disabled:R.length===0,label:s({id:"app.component.search.label",defaultMessage:"Search for {target}"},{target:f}),placeholder:s({id:"global.search",defaultMessage:"Search"}),trackedEvent:"didSearch"}),i.settings.filterable&&d?e.jsx(ae,{disabled:R.length===0,schema:d}):null]})}),e.jsx(D.Content,{children:e.jsx(X,{background:"neutral0",shadow:"filterShadow",hasRadius:!0,children:e.jsx(Je,{action:$?e.jsx(U,{variant:"secondary"}):null,content:s({id:"app.components.EmptyStateLayout.content-document",defaultMessage:"No content found"}),hasRadius:!0,icon:e.jsx(Ye,{width:"16rem"})})})})]})]}):e.jsxs(k.Main,{children:[e.jsx(k.Title,{children:`${f}`}),e.jsx(re,{primaryAction:$?e.jsx(U,{}):null,subtitle:s({id:P("pages.ListView.header-subtitle"),defaultMessage:"{number, plural, =0 {# entries} one {# entry} other {# entries}} found"},{number:T?.total}),title:f,navigationAction:e.jsx(ee,{})}),e.jsx(D.Action,{endActions:e.jsxs(e.Fragment,{children:[e.jsx(te,{area:"listView.actions"}),e.jsx(ie,{setHeaders:M,resetHeaders:()=>u(i.layout),headers:g.map(r=>r.name)})]}),startActions:e.jsxs(e.Fragment,{children:[i.settings.searchable&&e.jsx(se,{disabled:R.length===0,label:s({id:"app.component.search.label",defaultMessage:"Search for {target}"},{target:f}),placeholder:s({id:"global.search",defaultMessage:"Search"}),trackedEvent:"didSearch"}),i.settings.filterable&&d?e.jsx(ae,{disabled:R.length===0,schema:d}):null]})}),e.jsx(D.Content,{children:e.jsxs(O,{gap:4,direction:"column",alignItems:"stretch",children:[e.jsxs(b.Root,{rows:R,headers:F,isLoading:B,children:[e.jsx(At,{}),e.jsxs(b.Content,{children:[e.jsxs(b.Head,{children:[e.jsx(b.HeaderCheckboxCell,{}),F.map(r=>e.jsx(b.HeaderCell,{...r},r.name))]}),e.jsx(b.Loading,{}),e.jsx(b.Empty,{action:$?e.jsx(U,{variant:"secondary"}):null}),e.jsx(b.Body,{children:R.map(r=>e.jsxs(b.Row,{cursor:"pointer",onClick:x(r.documentId),children:[e.jsx(b.CheckboxCell,{id:r.id}),F.map(({cellFormatter:E,...c})=>{if(c.name==="status"){const{status:w}=r;return e.jsx(b.Cell,{children:e.jsx(Ze,{status:w,maxWidth:"min-content"})},c.name)}return["createdBy","updatedBy"].includes(c.name.split(".")[0])?e.jsx(b.Cell,{children:e.jsx(S,{textColor:"neutral800",children:r[c.name.split(".")[0]]?Q(r[c.name.split(".")[0]]):"-"})},c.name):typeof E=="function"?e.jsx(b.Cell,{children:E(r,c,{collectionType:o,model:j})},c.name):e.jsx(b.Cell,{children:e.jsx(bt,{content:r[c.name.split(".")[0]],rowId:r.documentId,...c})},c.name)}),e.jsx(Tt,{onClick:E=>E.stopPropagation(),children:e.jsx(Xe,{document:r})})]},r.id))})]})]}),e.jsxs(G.Root,{...T,onPageSizeChange:()=>t("willChangeNumberOfEntriesPerPage"),children:[e.jsx(G.PageSize,{}),e.jsx(G.Links,{})]})]})})]})},Tt=J(b.Cell)`
  display: flex;
  justify-content: flex-end;
`,At=()=>{const t=lt("TableActionsBar",l=>l.selectRow),[{query:n}]=H(),s=n?.plugins?.i18n?.locale,a=ge(s);return C.useEffect(()=>{a!==s&&t([])},[t,a,s]),e.jsx(b.ActionBar,{children:e.jsx(ot,{})})},U=({variant:t})=>{const{formatMessage:n}=v(),{trackUsage:s}=_(),[{query:a}]=H();return e.jsx(nt,{variant:t,tag:at,onClick:()=>{s("willCreateEntry",{status:"draft"})},startIcon:e.jsx(it,{}),style:{textDecoration:"none"},to:{pathname:"create",search:V.stringify({plugins:a.plugins})},minWidth:"max-content",marginLeft:2,children:n({id:P("HeaderLayout.button.label-add-entry"),defaultMessage:"Create new entry"})})},Et=()=>{const{slug:t=""}=et(),{permissions:n=[],isLoading:s,error:a}=pe(tt.map(l=>({action:l,subject:t})));return s?e.jsx(k.Loading,{}):a||!t?e.jsx(k.Error,{}):e.jsx(k.Protect,{permissions:n,children:({permissions:l})=>e.jsx(st,{permissions:l,children:e.jsx(Mt,{})})})};export{Mt as ListViewPage,Et as ProtectedListViewPage};
