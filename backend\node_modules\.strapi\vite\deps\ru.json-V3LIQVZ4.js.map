{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/ru.json.mjs"], "sourcesContent": ["var Analytics = \"Аналитика\";\nvar Documentation = \"Документация\";\nvar Email = \"Email\";\nvar Password = \"Пароль\";\nvar Provider = \"Провайдер\";\nvar ResetPasswordToken = \"Сброс токена пароля\";\nvar Role = \"Роль\";\nvar Username = \"Имя пользователя\";\nvar Users = \"Пользователи\";\nvar anErrorOccurred = \"Упс! Что-то пошло не так. Пожалуйста, попробуйте ещё раз.\";\nvar clearLabel = \"Очистить\";\nvar dark = \"Тёмная\";\nvar light = \"Светлая\";\nvar or = \"ИЛИ\";\nvar selectButtonTitle = \"Выбрать\";\nvar skipToContent = \"Перейти к содержимому\";\nvar submit = \"Отправить\";\nvar ru = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Ваш аккаунт был заморожен.\",\n    \"Auth.components.Oops.text.admin\": \"Если это ошибка, пожалуйста, обратитесь к администратору.\",\n    \"Auth.components.Oops.title\": \"Упс...\",\n    \"Auth.form.active.label\": \"Активно\",\n    \"Auth.form.button.forgot-password\": \"Отправить письмо\",\n    \"Auth.form.button.go-home\": \"ВЕРНУТЬСЯ НА ГЛАВНУЮ\",\n    \"Auth.form.button.login\": \"Войти\",\n    \"Auth.form.button.login.providers.error\": \"Мы не можем подключить вас через выбранного провайдера.\",\n    \"Auth.form.button.login.strapi\": \"Войти через Strapi\",\n    \"Auth.form.button.password-recovery\": \"Восстановление пароля\",\n    \"Auth.form.button.register\": \"Давайте начнём\",\n    \"Auth.form.confirmPassword.label\": \"Подтверждение пароля\",\n    \"Auth.form.currentPassword.label\": \"Текущий пароль\",\n    \"Auth.form.email.label\": \"Email\",\n    \"Auth.form.email.placeholder\": \"<EMAIL>\",\n    \"Auth.form.error.blocked\": \"Ваш аккаунт был заблокирован администратором.\",\n    \"Auth.form.error.code.provide\": \"Указан неверный код.\",\n    \"Auth.form.error.confirmed\": \"Ваш email не подтверждён.\",\n    \"Auth.form.error.email.invalid\": \"Неправильный email.\",\n    \"Auth.form.error.email.provide\": \"Укажите своё имя пользователя или email.\",\n    \"Auth.form.error.email.taken\": \"Данный email уже используется.\",\n    \"Auth.form.error.invalid\": \"Неверный логин или пароль.\",\n    \"Auth.form.error.params.provide\": \"Предоставлены неверные параметры.\",\n    \"Auth.form.error.password.format\": \"Пароль не может содержать символ `$` больше трёх раз.\",\n    \"Auth.form.error.password.local\": \"Этот пользователь никогда не задавал пароль, пожалуйста, войдите в систему через провайдера, используемого при создании учётной записи.\",\n    \"Auth.form.error.password.matching\": \"Пароли не совпадают.\",\n    \"Auth.form.error.password.provide\": \"Укажите свой пароль.\",\n    \"Auth.form.error.ratelimit\": \"Слишком много попыток, повторите через минуту.\",\n    \"Auth.form.error.user.not-exist\": \"Этот email не существует.\",\n    \"Auth.form.error.username.taken\": \"Имя пользователя уже используется.\",\n    \"Auth.form.firstname.label\": \"Имя\",\n    \"Auth.form.firstname.placeholder\": \"Иван\",\n    \"Auth.form.forgot-password.email.label\": \"Введите ваш email\",\n    \"Auth.form.forgot-password.email.label.success\": \"Письмо успешно отправлено на email\",\n    \"Auth.form.lastname.label\": \"Фамилия\",\n    \"Auth.form.lastname.placeholder\": \"Иванов\",\n    \"Auth.form.password.hide-password\": \"Скрыть пароль\",\n    \"Auth.form.password.hint\": \"Должно быть не менее 8 символов, минимум 1 прописная буква, 1 строчная буква и 1 цифра\",\n    \"Auth.form.password.show-password\": \"Показать пароль\",\n    \"Auth.form.register.news.label\": \"Хочу быть в курсе новых функций и предстоящих улучшений (делая это, вы принимаете {terms} и {policy}).\",\n    \"Auth.form.register.subtitle\": \"Учётные данные используются только для аутентификации в Strapi. Все сохраненные данные будут храниться в вашей базе данных.\",\n    \"Auth.form.rememberMe.label\": \"Запомнить меня\",\n    \"Auth.form.username.label\": \"Имя пользователя\",\n    \"Auth.form.username.placeholder\": \"Ivan_Ivanov\",\n    \"Auth.form.welcome.subtitle\": \"Войдите в свою учётную запись Strapi\",\n    \"Auth.form.welcome.title\": \"Добро пожаловать в Strapi!\",\n    \"Auth.link.forgot-password\": \"Забыли пароль?\",\n    \"Auth.link.ready\": \"Готовы войти?\",\n    \"Auth.link.signin\": \"Войти\",\n    \"Auth.link.signin.account\": \"Уже есть аккаунт?\",\n    \"Auth.login.sso.divider\": \"Или войдите в систему с помощью\",\n    \"Auth.login.sso.loading\": \"Загрузка провайдеров...\",\n    \"Auth.login.sso.subtitle\": \"Войдите в свою учётную запись через SSO\",\n    \"Auth.privacy-policy-agreement.policy\": \"Политику конфиденциальности\",\n    \"Auth.privacy-policy-agreement.terms\": \"Условия использования\",\n    \"Auth.reset-password.title\": \"Сброс пароля\",\n    \"Content Manager\": \"Редактор контента\",\n    \"Content Type Builder\": \"Конструктор типов содержимого\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Загрузка файлов\",\n    \"HomePage.head.title\": \"Домашняя страница\",\n    \"HomePage.roadmap\": \"Смотрите нашу дорожную карту\",\n    \"HomePage.welcome.congrats\": \"Поздравляем!\",\n    \"HomePage.welcome.congrats.content\": \"Вы вошли как первый администратор. Чтобы открыть для себя мощные функции, предоставляемые Strapi,\",\n    \"HomePage.welcome.congrats.content.bold\": \"мы рекомендуем вам создать свой первый тип коллекции.\",\n    \"Media Library\": \"Библиотека медиа\",\n    \"New entry\": \"Новая запись\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Роли и Разрешения\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Некоторые роли нельзя было удалить, так как они связаны с пользователями\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"Невозможно удалить роль, если она связана с пользователями\",\n    \"Roles.RoleRow.select-all\": \"Выберите {name} для групповых действий\",\n    \"Roles.RoleRow.user-count\": \"{number, plural, =0{# пользователей} one{# пользователь} few{# пользователя} many {# пользователей}}\",\n    \"Roles.components.List.empty.withSearch\": \"Нет роли, соответствующей поиску ({search})...\",\n    \"Settings.PageTitle\": \"Настройки — {name}\",\n    \"Settings.apiTokens.ListView.headers.createdAt\": \"Cоздан в\",\n    \"Settings.apiTokens.ListView.headers.description\": \"Описание\",\n    \"Settings.apiTokens.ListView.headers.lastUsedAt\": \"Последнее использование\",\n    \"Settings.apiTokens.ListView.headers.name\": \"Название\",\n    \"Settings.apiTokens.ListView.headers.type\": \"Тип токена\",\n    \"Settings.apiTokens.addFirstToken\": \"Добавьте свой первый API-токен\",\n    \"Settings.apiTokens.addNewToken\": \"Добавить новый API-токен\",\n    \"Settings.apiTokens.create\": \"Создайте новый API-токен\",\n    \"Settings.apiTokens.createPage.BoundRoute.title\": \"Связанный маршрут к\",\n    \"Settings.apiTokens.createPage.permissions.description\": \"Ниже перечислены только действия связанные с маршрутом.\",\n    \"Settings.apiTokens.createPage.permissions.header.hint\": \"Выберите действия для приложения или плагина и нажмите на значок шестеренки, чтобы отобразить связанный маршрут\",\n    \"Settings.apiTokens.createPage.permissions.header.title\": \"Дополнительные настройки\",\n    \"Settings.apiTokens.createPage.permissions.title\": \"Разрешения\",\n    \"Settings.apiTokens.createPage.title\": \"Создать API-токен\",\n    \"Settings.apiTokens.description\": \"Список сгенерированных токенов для использования API\",\n    \"Settings.apiTokens.emptyStateLayout\": \"У вас ещё нет контента...\",\n    \"Settings.apiTokens.lastHour\": \"последний час\",\n    \"Settings.apiTokens.regenerate\": \"Перегенерировать\",\n    \"Settings.apiTokens.title\": \"API-токены\",\n    \"Settings.application.customization\": \"Персонализация\",\n    \"Settings.application.customization.auth-logo.carousel-hint\": \"Заменить логотип на страницах аутентификации\",\n    \"Settings.application.customization.carousel-hint\": \"Изменить логотип панели администратора (максимальный размер: {dimension}x{dimension}, максимальный размер файла: {size}KB)\",\n    \"Settings.application.customization.carousel-slide.label\": \"Слайд с логотипом\",\n    \"Settings.application.customization.carousel.auth-logo.title\": \"Логотип на странице авторизации\",\n    \"Settings.application.customization.carousel.change-action\": \"Изменить логотип\",\n    \"Settings.application.customization.carousel.menu-logo.title\": \"Логотип в меню\",\n    \"Settings.application.customization.carousel.reset-action\": \"Сброс логотипа\",\n    \"Settings.application.customization.carousel.title\": \"Логотип\",\n    \"Settings.application.customization.menu-logo.carousel-hint\": \"Замените логотип в главном меню\",\n    \"Settings.application.customization.modal.cancel\": \"Отмена\",\n    \"Settings.application.customization.modal.pending\": \"Ожидание логотипа\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"изображение\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"Выберите другой логотип\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"Управление выбранным логотипом перед его загрузкой\",\n    \"Settings.application.customization.modal.pending.title\": \"Логотип готов к загрузке\",\n    \"Settings.application.customization.modal.pending.upload\": \"Загрузить логотип\",\n    \"Settings.application.customization.modal.tab.label\": \"Как вы хотите загрузить свои ассеты?\",\n    \"Settings.application.customization.modal.upload\": \"Загрузить логотип\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"Просмотр файлов\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"Перетащите сюда или\",\n    \"Settings.application.customization.modal.upload.error-format\": \"Загружен неправильный формат (разрешены только форматы: jpeg, jpg, png, svg).\",\n    \"Settings.application.customization.modal.upload.error-network\": \"Ошибка сети\",\n    \"Settings.application.customization.modal.upload.error-size\": \"Загруженный файл слишком большой (максимальный размер: {dimension}x{dimension}, максимальный размер файла: {size}KB)\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"Максимальный размер: {dimension}x{dimension}, максимальный размер файла: {size}KB\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"С компьютера\",\n    \"Settings.application.customization.modal.upload.from-url\": \"По ссылке\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n    \"Settings.application.customization.modal.upload.next\": \"Далее\",\n    \"Settings.application.customization.size-details\": \"Максимальное разрешение: {dimension}×{dimension}, максимальный размер файла: {size}KB\",\n    \"Settings.application.description\": \"Глобальная информация панели администратора\",\n    \"Settings.application.edition-title\": \"Текущий план\",\n    \"Settings.application.ee-or-ce\": \"{communityEdition, select, true {Community Edition} other {Enterprise Edition}}\",\n    \"Settings.application.ee.admin-seats.add-seats\": \"Управление места\",\n    \"Settings.application.ee.admin-seats.support\": \"Контактные продажи\",\n    \"Settings.application.ee.admin-seats.at-limit-tooltip\": \"При исчерпании лимита: добавьте места, чтобы пригласить больше пользователей\",\n    \"Settings.application.ee.admin-seats.count\": \"<text>{enforcementUserCount}</text>/{permittedSeats}\",\n    \"Settings.application.get-help\": \"Получить помощь\",\n    \"Settings.application.link-pricing\": \"Посмотреть все тарифы\",\n    \"Settings.application.link-upgrade\": \"Обновить ваше приложение\",\n    \"Settings.application.node-version\": \"Версия Node\",\n    \"Settings.application.strapi-version\": \"Версия Strapi\",\n    \"Settings.application.strapiVersion\": \"Версия Strapi\",\n    \"Settings.application.title\": \"Обзор\",\n    \"Settings.error\": \"Ошибка\",\n    \"Settings.global\": \"Глобальные Настройки\",\n    \"Settings.permissions\": \"Панель администратора\",\n    \"Settings.permissions.auditLogs.action\": \"Действие\",\n    \"Settings.permissions.auditLogs.admin.auth.success\": \"Вход администратора\",\n    \"Settings.permissions.auditLogs.admin.logout\": \"Выход администратора\",\n    \"Settings.permissions.auditLogs.component.create\": \"Создать компонент\",\n    \"Settings.permissions.auditLogs.component.delete\": \"Удалить компонент\",\n    \"Settings.permissions.auditLogs.component.update\": \"Обновить компонент\",\n    \"Settings.permissions.auditLogs.content-type.create\": \"Создать тип контента\",\n    \"Settings.permissions.auditLogs.content-type.delete\": \"Удалить тип контента\",\n    \"Settings.permissions.auditLogs.content-type.update\": \"Обновить тип контента\",\n    \"Settings.permissions.auditLogs.date\": \"Дата\",\n    \"Settings.permissions.auditLogs.details\": \"Детали лога\",\n    \"Settings.permissions.auditLogs.entry.create\": \"Создать {model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.delete\": \"Удалить {model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.publish\": \"Опубликовать {model, select, undefined {} other {({model})}}\",\n    \"Settings.permissions.auditLogs.entry.unpublish\": \"Сделать непубличным {model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.entry.update\": \"Обновить {model, select, undefined {} other { ({model})}}\",\n    \"Settings.permissions.auditLogs.filters.combobox.aria-label\": \"Выполните поиск и выберите вариант для фильтрации\",\n    \"Settings.permissions.auditLogs.listview.header.subtitle\": \"Журналы всех действий, которые произошли в вашей среде\",\n    \"Settings.permissions.auditLogs.media.create\": \"Создать медиафайл\",\n    \"Settings.permissions.auditLogs.media.delete\": \"Удалить медиафайл\",\n    \"Settings.permissions.auditLogs.media.update\": \"Обновить медиафайл\",\n    \"Settings.permissions.auditLogs.payload\": \"Полезная нагрузка\",\n    \"Settings.permissions.auditLogs.permission.create\": \"Создать разрешение\",\n    \"Settings.permissions.auditLogs.permission.delete\": \"Удалить разрешение\",\n    \"Settings.permissions.auditLogs.permission.update\": \"Обновить разрешение\",\n    \"Settings.permissions.auditLogs.role.create\": \"Создать роль\",\n    \"Settings.permissions.auditLogs.role.delete\": \"Удалить роль\",\n    \"Settings.permissions.auditLogs.role.update\": \"Обновить роль\",\n    \"Settings.permissions.auditLogs.user\": \"Пользователь\",\n    \"Settings.permissions.auditLogs.user.create\": \"Создать пользователя\",\n    \"Settings.permissions.auditLogs.user.delete\": \"Удалить пользователя\",\n    \"Settings.permissions.auditLogs.user.fullname\": \"{firstname} {lastname}\",\n    \"Settings.permissions.auditLogs.user.update\": \"Обновить пользователя\",\n    \"Settings.permissions.auditLogs.userId\": \"ID пользователя\",\n    \"Settings.permissions.category\": \"Настройки разрешений для {category}\",\n    \"Settings.permissions.category.plugins\": \"Настройки разрешений для плагина {category}\",\n    \"Settings.permissions.conditions.anytime\": \"Всегда\",\n    \"Settings.permissions.conditions.apply\": \"Применить\",\n    \"Settings.permissions.conditions.can\": \"Можно\",\n    \"Settings.permissions.conditions.conditions\": \"Условия\",\n    \"Settings.permissions.conditions.define-conditions\": \"Определить условия\",\n    \"Settings.permissions.conditions.links\": \"Ссылки\",\n    \"Settings.permissions.conditions.no-actions\": \"Сначала вам нужно выбрать действия (создать, прочитать, обновить, ...), прежде чем определять условия для них.\",\n    \"Settings.permissions.conditions.none-selected\": \"Любое время\",\n    \"Settings.permissions.conditions.or\": \"ИЛИ\",\n    \"Settings.permissions.conditions.when\": \"Когда\",\n    \"Settings.permissions.select-all-by-permission\": \"Выбрать все разрешения {label}\",\n    \"Settings.permissions.select-by-permission\": \"Выберите разрешения {label}\",\n    \"Settings.permissions.users.active\": \"Активен\",\n    \"Settings.permissions.users.create\": \"Создать нового пользователя\",\n    \"Settings.permissions.users.email\": \"Email\",\n    \"Settings.permissions.users.firstname\": \"Имя\",\n    \"Settings.permissions.users.form.sso\": \"Соединить с SSO\",\n    \"Settings.permissions.users.form.sso.description\": \"Когда включено, пользователи смогут входить через SSO\",\n    \"Settings.permissions.users.inactive\": \"Неактивен\",\n    \"Settings.permissions.users.lastname\": \"Фамилия\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Все пользователи, имеющие доступ к панели администратора Strapi\",\n    \"Settings.permissions.users.roles\": \"Роли\",\n    \"Settings.permissions.users.sso.provider.error\": \"Произошла ошибка при запросе настроек единого входа (SSO)\",\n    \"Settings.permissions.users.strapi-author\": \"Автор\",\n    \"Settings.permissions.users.strapi-editor\": \"Редактор\",\n    \"Settings.permissions.users.strapi-super-admin\": \"Супер-администратор\",\n    \"Settings.permissions.users.tabs.label\": \"Вкладки Разрешения\",\n    \"Settings.permissions.users.user-status\": \"Статус пользователя\",\n    \"Settings.permissions.users.username\": \"Имя пользователя\",\n    \"Settings.profile.form.notify.data.loaded\": \"Данные вашего профиля загружены\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Очистить выбранный язык интерфейса\",\n    \"Settings.profile.form.section.experience.here\": \"здесь\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Язык интерфейса\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Только ваш интерфейс будет отображаться на выбранном языке.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Изменения предпочтений будут касаться только вас. Дополнительная информация доступна {here}.\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Отображает ваш интерфейс в выбранной теме.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Тема интерфейса\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name} тема\",\n    \"Settings.profile.form.section.experience.title\": \"Опыт\",\n    \"Settings.profile.form.section.head.title\": \"Профиль пользователя\",\n    \"Settings.profile.form.section.profile.page.title\": \"Страница профиля\",\n    \"Settings.roles.create.description\": \"Определите права, предоставленные ролью\",\n    \"Settings.roles.create.title\": \"Создать роль\",\n    \"Settings.roles.created\": \"Роль создана\",\n    \"Settings.roles.edit.title\": \"Изменить роль\",\n    \"Settings.roles.form.button.users-with-role\": \"Пользователи с этой ролью\",\n    \"Settings.roles.form.created\": \"Создано\",\n    \"Settings.roles.form.description\": \"Название и описание роли\",\n    \"Settings.roles.form.permission.property-label\": \"{label} разрешения\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Разрешения полей\",\n    \"Settings.roles.form.permissions.create\": \"Создать\",\n    \"Settings.roles.form.permissions.delete\": \"Удалить\",\n    \"Settings.roles.form.permissions.publish\": \"Опубликовать\",\n    \"Settings.roles.form.permissions.read\": \"Прочесть\",\n    \"Settings.roles.form.permissions.update\": \"Обновить\",\n    \"Settings.roles.list.button.add\": \"Добавить новую роль\",\n    \"Settings.roles.list.description\": \"Список ролей\",\n    \"Settings.roles.title.singular\": \"роль\",\n    \"Settings.sso.description\": \"Настройте параметры для функции единого входа.\",\n    \"Settings.sso.form.defaultRole.description\": \"Присоединит нового аутентифицированного пользователя к выбранной роли\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"У вас должно быть разрешение на чтение ролей администратора\",\n    \"Settings.sso.form.defaultRole.label\": \"Роль по умолчанию\",\n    \"Settings.sso.form.localAuthenticationLock.description\": \"Выберите роли, для которых вы хотите отключить локальную проверку аутентификацию\",\n    \"Settings.sso.form.localAuthenticationLock.label\": \"Блокировать локальную аутентификацию\",\n    \"Settings.sso.form.registration.description\": \"Создать нового пользователя при входе через SSO, если учётной записи нет\",\n    \"Settings.sso.form.registration.label\": \"Авто-регистрация\",\n    \"Settings.sso.title\": \"Функция единого входа\",\n    \"Settings.tokens.Button.cancel\": \"Отмена\",\n    \"Settings.tokens.Button.regenerate\": \"Восстановить\",\n    \"Settings.tokens.ListView.headers.createdAt\": \"Создан\",\n    \"Settings.tokens.ListView.headers.description\": \"Описание\",\n    \"Settings.tokens.ListView.headers.lastUsedAt\": \"Последний раз использовался\",\n    \"Settings.tokens.ListView.headers.name\": \"Название\",\n    \"Settings.tokens.RegenerateDialog.title\": \"Восстановить токен\",\n    \"Settings.tokens.copy.editMessage\": \"Из соображений безопасности вы можете увидеть свой токен только один раз.\",\n    \"Settings.tokens.copy.editTitle\": \"Этот токен больше не доступен.\",\n    \"Settings.tokens.copy.lastWarning\": \"Обязательно скопируйте этот токен, вы больше не сможете его увидеть!\",\n    \"Settings.tokens.duration.30-days\": \"30 дней\",\n    \"Settings.tokens.duration.7-days\": \"7 дней\",\n    \"Settings.tokens.duration.90-days\": \"90 дней\",\n    \"Settings.tokens.duration.expiration-date\": \"Срок действия\",\n    \"Settings.tokens.duration.unlimited\": \"Неограниченный\",\n    \"Settings.tokens.form.description\": \"Описание\",\n    \"Settings.tokens.form.duration\": \"Срок действия токена\",\n    \"Settings.tokens.form.name\": \"Название\",\n    \"Settings.tokens.form.type\": \"Тип токена\",\n    \"Settings.tokens.notification.copied\": \"Токен скопирован в буфер обмена.\",\n    \"Settings.tokens.popUpWarning.message\": \"Вы уверены, что хотите восстановить этот токен?\",\n    \"Settings.tokens.regenerate\": \"Перегенерировать\",\n    \"Settings.tokens.types.custom\": \"Пользовательский тип\",\n    \"Settings.tokens.types.full-access\": \"Полный доступ\",\n    \"Settings.tokens.types.read-only\": \"Только для чтения\",\n    \"Settings.transferTokens.ListView.headers.type\": \"Тип токена\",\n    \"Settings.transferTokens.addFirstToken\": \"Добавьте свой первый токен для передачи\",\n    \"Settings.transferTokens.addNewToken\": \"Добавить новый токен для передачи\",\n    \"Settings.transferTokens.create\": \"Создать новый токен для передачи\",\n    \"Settings.transferTokens.createPage.title\": \"Создать передачу токена\",\n    \"Settings.transferTokens.description\": \"Список сгенерированных токенов для передачи\",\n    \"Settings.transferTokens.emptyStateLayout\": \"У вас ещё нет никакого контента...\",\n    \"Settings.transferTokens.title\": \"Передача токенов\",\n    \"Settings.webhooks.create\": \"Создание webhook`а\",\n    \"Settings.webhooks.create.header\": \"Создание нового заголовка\",\n    \"Settings.webhooks.created\": \"Webhook создан\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Это событие существует только для содержимого с включенной системой Черновиков/Публикаций\",\n    \"Settings.webhooks.event.select\": \"Выберите событие\",\n    \"Settings.webhooks.events.create\": \"Создание\",\n    \"Settings.webhooks.events.delete\": \"Удалить вебхук\",\n    \"Settings.webhooks.events.isLoading\": \"Загрузка событий\",\n    \"Settings.webhooks.events.update\": \"Обновление\",\n    \"Settings.webhooks.form.events\": \"События\",\n    \"Settings.webhooks.form.headers\": \"Заголовки\",\n    \"Settings.webhooks.form.url\": \"URL\",\n    \"Settings.webhooks.headers.remove\": \"Удалить строку заголовка {number}\",\n    \"Settings.webhooks.key\": \"Ключ\",\n    \"Settings.webhooks.list.button.add\": \"Добавить новый webhook\",\n    \"Settings.webhooks.list.description\": \"Уведомления с помощью POST событий\",\n    \"Settings.webhooks.list.empty.description\": \"Добавить первый в этот список\",\n    \"Settings.webhooks.list.empty.link\": \"Просмотреть документацию\",\n    \"Settings.webhooks.list.empty.title\": \"Пока ещё нет ни одного webhook'а\",\n    \"Settings.webhooks.list.loading.success\": \"Вебхуки были загружены\",\n    \"Settings.webhooks.list.th.actions\": \"действия\",\n    \"Settings.webhooks.list.th.status\": \"статус\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhook'и\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, =0{# ассетов} one{# ассет} few{# ассета} many {# ассетов}} выбраны\",\n    \"Settings.webhooks.trigger\": \"Триггер\",\n    \"Settings.webhooks.trigger.cancel\": \"Отмена триггера\",\n    \"Settings.webhooks.trigger.pending\": \"Ожидание…\",\n    \"Settings.webhooks.trigger.save\": \"Пожалуйста сохраните триггер\",\n    \"Settings.webhooks.trigger.success\": \"Успех!\",\n    \"Settings.webhooks.trigger.success.label\": \"Триггер выполнен\",\n    \"Settings.webhooks.trigger.test\": \"Тест триггер\",\n    \"Settings.webhooks.trigger.title\": \"Сохранить перед триггером\",\n    \"Settings.webhooks.validation.key\": \"Ключ обязателен\",\n    \"Settings.webhooks.validation.name.regex\": \"Имя должно начинаться с буквы и содержать только буквы, цифры, пробелы и подчеркивания\",\n    \"Settings.webhooks.validation.name.required\": \"Имя обязательно\",\n    \"Settings.webhooks.validation.url.regex\": \"Значение должно быть допустимым URL-адресом\",\n    \"Settings.webhooks.validation.url.required\": \"URL-адрес обязателен\",\n    \"Settings.webhooks.validation.value\": \"Значение обязательно\",\n    \"Settings.webhooks.value\": \"Значение\",\n    \"Usecase.back-end\": \"Back-end разработчик\",\n    \"Usecase.button.skip\": \"Пропустить этот вопрос\",\n    \"Usecase.content-creator\": \"Создатель контента\",\n    \"Usecase.front-end\": \"Front-end разработчик\",\n    \"Usecase.full-stack\": \"Full-stack разработчик\",\n    \"Usecase.input.work-type\": \"Какой тип работы вы выполняете?\",\n    \"Usecase.notification.success.project-created\": \"Проект успешно создан\",\n    \"Usecase.other\": \"Другое\",\n    \"Usecase.title\": \"Расскажите нам немного больше о себе\",\n    Username: Username,\n    \"Users & Permissions\": \"Пользователи и Разрешения\",\n    Users: Users,\n    \"Users.components.List.empty\": \"Нет пользователей...\",\n    \"Users.components.List.empty.withFilters\": \"Нет пользователей с применёнными фильтрами...\",\n    \"Users.components.List.empty.withSearch\": \"Нет пользователей, соответствующих запросу ({search})...\",\n    \"admin.pages.MarketPlacePage.filters.categories\": \"Категории\",\n    \"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"Выбрано {count, plural, =0{# категорий} one{# категория} few{# категории} many {# категорий}}\",\n    \"admin.pages.MarketPlacePage.filters.collections\": \"Коллекции\",\n    \"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"Выбрано {count, plural, =0{# Коллекций} one{# Коллекция} few{# Коллекции} many {# Коллекций}}\",\n    \"admin.pages.MarketPlacePage.head\": \"Маркет плагинов — Плагины\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"Расскажите нам, какой плагин вы ищете, и мы сообщим об этом разработчикам плагинов нашего сообщества на случай, если они ищут вдохновение!\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"Вам не хватает плагина?\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"Для доступа к Маркету Strapi необходимо подключение к Интернету.\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"Вы не в сети\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"Скопируйте команду установки\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"Команда установки готова к вставке в терминал\",\n    \"admin.pages.MarketPlacePage.plugin.downloads\": \"Этот плагин был загружен {downloadsCount} раз(-а) за неделю\",\n    \"admin.pages.MarketPlacePage.plugin.githubStars\": \"Этот плагин имеет {starsCount} звёзд на GitHub\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"Узнать больше\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"Узнать больше про {pluginName}\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"Узнать больше\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"Установлено\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Сделано Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Плагин проверен командой Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.version\": \"Обновите вашу версию Strapi: \\\"{strapiAppVersion}\\\" до: \\\"{versionRange}\\\"\",\n    \"admin.pages.MarketPlacePage.plugin.version.null\": \"Невозможно проверить совместимость с вашей версией Strapi: \\\"{strapiAppVersion}\\\"\",\n    \"admin.pages.MarketPlacePage.plugins\": \"Плагины\",\n    \"admin.pages.MarketPlacePage.provider.downloads\": \"Этот провайдер был загружен {downloadsCount} раз(-а) за неделю\",\n    \"admin.pages.MarketPlacePage.provider.githubStars\": \"Этот провайдер имеет {starsCount} звёзд на GitHub\",\n    \"admin.pages.MarketPlacePage.providers\": \"Провайдеры\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"Очистить поиск\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"Нет результатов для \\\"{target}\\\"\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"Найти плагин\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical\": \"По алфавиту\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Сортировать по алфавиту\",\n    \"admin.pages.MarketPlacePage.sort.githubStars\": \"По звёздам на GitHub\",\n    \"admin.pages.MarketPlacePage.sort.githubStars.selected\": \"Сортировать по звёздам на GitHub\",\n    \"admin.pages.MarketPlacePage.sort.label\": \"Сортировка\",\n    \"admin.pages.MarketPlacePage.sort.newest\": \"Новые\",\n    \"admin.pages.MarketPlacePage.sort.newest.selected\": \"Сортировать по новейшим\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads\": \"Количество загрузок\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads.selected\": \"Сортировать по npm загрузкам\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Отправить плагин\",\n    \"admin.pages.MarketPlacePage.submit.provider.link\": \"Отправить провайдера\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Получите больше от Strapi\",\n    \"admin.pages.MarketPlacePage.tab-group.label\": \"Плагины и провайдеры для Strapi\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Копировать в буфер обмена\",\n    \"app.component.search.label\": \"Искать {target}\",\n    \"app.component.table.duplicate\": \"Дубликат {target}\",\n    \"app.component.table.edit\": \"Редактировать {target}\",\n    \"app.component.table.read\": \"Читать {target}\",\n    \"app.component.table.select.one-entry\": \"Выбрать {target}\",\n    \"app.component.table.view\": \"{target} детали\",\n    \"app.components.BlockLink.blog\": \"Блог\",\n    \"app.components.BlockLink.blog.content\": \"Читайте последние новости о Strapi и экосистеме.\",\n    \"app.components.BlockLink.cloud\": \"Strapi Cloud\",\n    \"app.components.BlockLink.cloud.content\": \"Полностью настраиваемая платформа для совместной работы, повышающая скорость работы вашей команды.\",\n    \"app.components.BlockLink.code\": \"Примеры кода\",\n    \"app.components.BlockLink.code.content\": \"Учитесь, тестируя реальные проекты, разработанные сообществом.\",\n    \"app.components.BlockLink.documentation.content\": \"Откройте для себя основные понятия, руководства и инструкции.\",\n    \"app.components.BlockLink.tutorial\": \"Учебные материалы\",\n    \"app.components.BlockLink.tutorial.content\": \"Следуйте пошаговым инструкциям по использованию и настройке Strapi.\",\n    \"app.components.Button.cancel\": \"Отменить\",\n    \"app.components.Button.confirm\": \"Подтвердить\",\n    \"app.components.Button.reset\": \"Сброс\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Скоро\",\n    \"app.components.ConfirmDialog.title\": \"Подтверждение\",\n    \"app.components.DownloadInfo.download\": \"Выполняется загрузка...\",\n    \"app.components.DownloadInfo.text\": \"Это может занять около минуты. Спасибо за ваше терпение.\",\n    \"app.components.EmptyAttributes.title\": \"Пока нет полей\",\n    \"app.components.EmptyStateLayout.content-document\": \"Данные не найдены\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"У вас нет прав доступа к этому содержимому\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>Создавайте и управляйте всем содержимым здесь, в Редакторе контента.</p><p>Например, если взять пример с сайтом-блогом, можно написать статью, сохранить и опубликовать её по своему усмотрению.</p><p>💡 Краткий совет — не забудьте нажать опубликовать для публикации созданного вами контента.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ Создавайте контент\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>Потрясающе, остался последний шаг!</p><b>🚀 Посмотрите как в итоге выглядит контент</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"Протестируйте API\",\n    \"app.components.GuidedTour.CM.success.title\": \"Шаг 2: Завершено ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>Типы \\\"Коллекция\\\" помогают управлять несколькими записями, типы \\\"Одиночная запись\\\" подходят для управления только одной записью.</p> <p>Например: Для сайта-блога статьи будут типом \\\"Коллекция\\\", а домашняя страница — типом \\\"Одиночная запись\\\".</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"Создайте тип записей Коллекция\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 Создайте первый тип записей Коллекция\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>Так держать!</p><b>⚡️ Чем бы вы хотели поделиться с миром?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"Шаг 1: Завершено ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>Сгенерируйте здесь токен аутентификации и получите только что созданный контент.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"Сгенерируйте API-токен\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Посмотрите на контент в действии\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>Посмотрите содержимое в действии, сделав HTTP-запрос:</p><ul><li><p>По этому URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>С заголовком: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>О дополнительных способах взаимодействия с контентом смотрите в <documentationLink>документации</documentationLink>.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"Вернуться на главную страницу\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"Шаг 3: Завершено ✅\",\n    \"app.components.GuidedTour.create-content\": \"Создавайте контент\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ Чем бы вы хотели поделиться с миром?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"Перейти к Редактору типов контента\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 Постройте структуру контента\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"Протестируйте API\",\n    \"app.components.GuidedTour.skip\": \"Пропустить тур\",\n    \"app.components.GuidedTour.title\": \"3 шага для начала работы\",\n    \"app.components.HomePage.button.blog\": \"Смотрите больше в нашем блоге\",\n    \"app.components.HomePage.community\": \"Присоединиться к нашему сообществу\",\n    \"app.components.HomePage.community.content\": \"Участвуйте в обсуждениях с членами команды и разработчиками.\",\n    \"app.components.HomePage.community.links.github\": \"GitHub\",\n    \"app.components.HomePage.community.links.discord\": \"Discord\",\n    \"app.components.HomePage.community.links.reddit\": \"Reddit\",\n    \"app.components.HomePage.community.links.twitter\": \"X\",\n    \"app.components.HomePage.community.links.forum\": \"Форум\",\n    \"app.components.HomePage.community.links.blog\": \"Блог\",\n    \"app.components.HomePage.community.links.career\": \"Мы принимаем на работу!\",\n    \"app.components.HomePage.create\": \"Создайте свой первый тип контента\",\n    \"app.components.HomePage.roadmap\": \"Ознакомьтесь с нашей дорожной картой\",\n    \"app.components.HomePage.welcome\": \"Добро пожаловать на борт 👋\",\n    \"app.components.HomePage.welcome.again\": \"Добро пожаловать 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"Поздравляем! Вы вошли в систему как первый администратор. Чтобы открыть для себя мощные функции, предоставляемые Strapi, мы рекомендуем вам создать свой первый тип контента!\",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Надеемся, что вы делаете успехи в вашем проекте... Следите за последними новостями о Strapi. Мы стараемся изо всех сил, чтобы улучшить продукт, основываясь на ваших пожеланиях.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"проблемах.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" или сообщать о \",\n    \"app.components.ImgPreview.hint\": \"Перетащите файл в эту область или {browse} файл для загрузки\",\n    \"app.components.ImgPreview.hint.browse\": \"выберите\",\n    \"app.components.InputFile.newFile\": \"Добавить новый файл\",\n    \"app.components.InputFileDetails.open\": \"Открыть в новой вкладке\",\n    \"app.components.InputFileDetails.originalName\": \"Оригинальное название:\",\n    \"app.components.InputFileDetails.remove\": \"Удалить этот файл\",\n    \"app.components.InputFileDetails.size\": \"Размер:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Для загрузки и установки плагина может потребоваться несколько секунд.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Загрузка...\",\n    \"app.components.InstallPluginPage.description\": \"Расширяйте ваше приложение без особых усилий.\",\n    \"app.components.LeftMenu.collapse\": \"Свернуть панель навигации\",\n    \"app.components.LeftMenu.expand\": \"Развернуть панель навигации\",\n    \"app.components.LeftMenu.general\": \"Общее\",\n    \"app.components.LeftMenu.logo.alt\": \"Логотип приложения\",\n    \"app.components.LeftMenu.logout\": \"Выйти\",\n    \"app.components.LeftMenu.navbrand.title\": \"Панель управления Strapi\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"Рабочая область\",\n    \"app.components.LeftMenu.plugins\": \"Плагины\",\n    \"app.components.LeftMenu.trialCountdown\": \"Ваш пробный период заканчивается {date}.\",\n    \"app.components.LeftMenuFooter.help\": \"Помощь\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Работает на \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Типы Коллекций\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Настройки\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Общие\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Нет установленных плагинов\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Плагины\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Страницы\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"Удаление плагина может занять несколько секунд.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Удаление\",\n    \"app.components.ListPluginsPage.description\": \"Список установленных плагинов в проекте.\",\n    \"app.components.ListPluginsPage.head.title\": \"Список плагинов\",\n    \"app.components.Logout.logout\": \"Выйти\",\n    \"app.components.Logout.profile\": \"Профиль\",\n    \"app.components.MarketplaceBanner\": \"На Маркете Strapi вы найдете плагины, созданные сообществом, и множество других удивительных вещей для запуска вашего проекта.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"A Strapi rocket logo\",\n    \"app.components.MarketplaceBanner.link\": \"Посмотреть\",\n    \"app.components.NotFoundPage.back\": \"Вернуться на главную\",\n    \"app.components.NotFoundPage.description\": \"Не найдено\",\n    \"app.components.NpsSurvey.banner-title\": \"Насколько велика вероятность того, что вы порекомендуете Strapi другу или коллеге?\",\n    \"app.components.NpsSurvey.dismiss-survey-label\": \"Отказаться от опроса\",\n    \"app.components.NpsSurvey.feedback-question\": \"Есть ли у вас какие-либо предложения по улучшению?\",\n    \"app.components.NpsSurvey.feedback-response\": \"Большое вам спасибо за ваш отзыв!\",\n    \"app.components.NpsSurvey.happy-to-recommend\": \"Чрезвычайно вероятно\",\n    \"app.components.NpsSurvey.no-recommendation\": \"Совсем маловероятно\",\n    \"app.components.NpsSurvey.submit-feedback\": \"Отправить отзыв\",\n    \"app.components.Official\": \"Официальный\",\n    \"app.components.Onboarding.help.button\": \"Открыть меню помощи\",\n    \"app.components.Onboarding.help.button-close\": \"Закрыть меню помощи\",\n    \"app.components.Onboarding.label.completed\": \"% завершено\",\n    \"app.components.Onboarding.link.build-content\": \"Создайте архитектуру контента\",\n    \"app.components.Onboarding.link.manage-content\": \"Добавляйте и управляйте контентом\",\n    \"app.components.Onboarding.link.manage-media\": \"Управляйте медиа\",\n    \"app.components.Onboarding.link.more-videos\": \"Смотреть больше видео\",\n    \"app.components.Onboarding.title\": \"Смотреть видео о начале работы\",\n    \"app.components.PluginCard.Button.label.download\": \"Скачать\",\n    \"app.components.PluginCard.Button.label.install\": \"Уже установлено\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Функция autoReload (автоматической перезагрузки) должна быть включена. Пожалуйста, запустите ваше приложение с помощью `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Я понимаю!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"В целях безопасности плагин может быть загружен только в среде разработки.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Загрузка невозможна\",\n    \"app.components.PluginCard.compatible\": \"Совместимо с вашим приложением\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Совместимо с сообществом\",\n    \"app.components.PluginCard.more-details\": \"Больше деталей\",\n    \"app.components.ToggleCheckbox.off-label\": \"Нет\",\n    \"app.components.ToggleCheckbox.on-label\": \"Да\",\n    \"app.components.Users.MagicLink.connect\": \"Отправьте эту ссылку пользователю, чтобы предоставить ему доступ\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Отправьте эту ссылку пользователю, первый вход может быть осуществлен через провайдера SSO\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Детали\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Роли пользователя\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Пользователь может иметь одну или несколько ролей\",\n    \"app.components.Users.SortPicker.button-label\": \"Сортировать по\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"Электронная почта (от А до Я)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"Электронная почта (от Я до А)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Имя (от А до Я)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Имя (от Я до А)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Фамилия (от А до Я)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Фамилия (от Я до А)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Имя пользователя (от А до Я)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Имя пользователя (от Я до А)\",\n    \"app.components.listPlugins.button\": \"Добавить новый плагин\",\n    \"app.components.listPlugins.title.none\": \"Нет установленных плагинов\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Произошла ошибка при удалении плагина\",\n    \"app.containers.App.notification.error.init\": \"Произошла ошибка при запросе к API\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Если вы не получили эту ссылку, обратитесь к администратору.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Получение ссылки для восстановления пароля может занять несколько минут...\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Письмо отправлено\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Активный\",\n    \"app.containers.Users.EditPage.header.label\": \"Редактировать {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Редактировать пользователя\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Атрибуты ролей\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Создать пользователя\",\n    \"app.error\": \"Что-то пошло не так\",\n    \"app.error.message\": \"Похоже, что в вашем экземпляре обнаружена ошибка, но мы с вами справимся. Пожалуйста, сообщите об этом своей технической команде, чтобы они могли выяснить источник проблемы и сообщить нам о проблеме, открыв отчет об ошибке по ссылке: {link}.\",\n    \"app.error.copy\": \"Скопировать в буфер обмена\",\n    \"app.links.configure-view\": \"Настройка представления\",\n    \"app.page.not.found\": \"Упс! Мы не можем найти страницу, которую вы ищете...\",\n    \"app.static.links.cheatsheet\": \"Шпаргалка\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Добавить фильтр\",\n    \"app.utils.close-label\": \"Закрыть\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.delete\": \"Удалить\",\n    \"app.utils.duplicate\": \"Дублировать\",\n    \"app.utils.edit\": \"Редактировать\",\n    \"app.utils.errors.file-too-big.message\": \"Файл слишком большой\",\n    \"app.utils.filter-value\": \"Значение фильтра\",\n    \"app.utils.filters\": \"Фильтры\",\n    \"app.utils.notify.data-loaded\": \"{target} загружена\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Опубликовать\",\n    \"app.utils.published\": \"Опубликовано\",\n    \"app.utils.ready-to-publish\": \"Готово к публикации\",\n    \"app.utils.refresh\": \"Обновить\",\n    \"app.utils.select-all\": \"Выбрать все\",\n    \"app.utils.select-field\": \"Выберите поле\",\n    \"app.utils.select-filter\": \"Выберите фильтр\",\n    \"app.utils.unpublish\": \"Отменить публикацию\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Содержимое находится в стадии разработки и через несколько недель оно будет возвращено!\",\n    \"component.Input.error.validation.integer\": \"Значение должно быть целочисленным\",\n    \"components.AutoReloadBlocker.description\": \"Запустите Strapi с помощью одной из следующих команд:\",\n    \"components.AutoReloadBlocker.header\": \"Для этого плагина требуется функция перезагрузки.\",\n    \"components.ErrorBoundary.title\": \"Что-то пошло не так...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"содержит\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"содержит (без учета регистра)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"заканчивается на\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"заканчивается на (без учета регистра)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"равно\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"равно (без учета регистра)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"больше, чем\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"больше или равно\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"меньше, чем\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"меньше или равно\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"не\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"не (без учета регистра)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"не содержит\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"не содержит (без учета регистра)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"задано\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"не задано\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"начинается с\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"начинается с (без учета регистра)\",\n    \"components.Input.error.attribute.key.taken\": \"Это значение уже существует\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Не может быть одинаковым\",\n    \"components.Input.error.attribute.taken\": \"Поле с таким названием уже существует\",\n    \"components.Input.error.contain.lowercase\": \"Пароль должен содержать хотя бы одну прописную букву\",\n    \"components.Input.error.contain.number\": \"Пароль должен содержать хотя бы одну цифру\",\n    \"components.Input.error.contain.uppercase\": \"Пароль должен содержать хотя бы одну заглавную букву\",\n    \"components.Input.error.contentTypeName.taken\": \"Это название уже существует\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Пароли не совпадают\",\n    \"components.Input.error.validation.email\": \"Неправильный или написанный с ошибкой email\",\n    \"components.Input.error.validation.email.withField\": \"Поле {field} — содержит неверный адрес электронной почты\",\n    \"components.Input.error.validation.json\": \"Не соответствует JSON формату\",\n    \"components.Input.error.validation.json.withField\": \"Поле {field} — не соответствует формату JSON\",\n    \"components.Input.error.validation.lowercase\": \"Значение должно быть строкой в нижнем регистре\",\n    \"components.Input.error.validation.lowercase.withField\": \"Поле {field} — должно быть строкой в нижнем регистре (без заглавных букв)\",\n    \"components.Input.error.validation.max\": \"Значение слишком большое.\",\n    \"components.Input.error.validation.max.withField\": \"Поле {field} — слишком велико.\",\n    \"components.Input.error.validation.maxLength\": \"Значение слишком длинное.\",\n    \"components.Input.error.validation.maxLength.withField\": \"Поле {field} — слишком длинное.\",\n    \"components.Input.error.validation.min\": \"Значение слишком маленькое.\",\n    \"components.Input.error.validation.min.withField\": \"Поле {field} — слишком мало.\",\n    \"components.Input.error.validation.minLength\": \"Значение слишком короткое.\",\n    \"components.Input.error.validation.minLength.withField\": \"Поле {field} — слишком короткое.\",\n    \"components.Input.error.validation.minSupMax\": \"Не может быть больше\",\n    \"components.Input.error.validation.minSupMax.withField\": \"Поле {field} — не может быть выше\",\n    \"components.Input.error.validation.regex\": \"Значение не соответствует регулярному выражению.\",\n    \"components.Input.error.validation.regex.withField\": \"Поле {field} — не соответствует формату регулярных выражений.\",\n    \"components.Input.error.validation.required\": \"Обязательное значение.\",\n    \"components.Input.error.validation.required.withField\": \"Поле {field} — обязательное.\",\n    \"components.Input.error.validation.unique\": \"Это значение уже используется.\",\n    \"components.Input.error.validation.unique.withField\": \"Поле {field} — уже используется.\",\n    \"components.InputSelect.option.placeholder\": \"Выберите здесь\",\n    \"components.ListRow.empty\": \"Нет данных для отображения.\",\n    \"components.NotAllowedInput.text\": \"Нет прав на просмотр этого поля\",\n    \"components.OverlayBlocker.description\": \"Вы используете функцию, для которой требуется перезагрузка сервера. Пожалуйста, подождите, пока сервер не перезагрузится.\",\n    \"components.OverlayBlocker.description.serverError\": \"Сервер должен был перезагрузиться, пожалуйста, проверьте ваши логи в терминале.\",\n    \"components.OverlayBlocker.title\": \"Ожидание перезагрузки...\",\n    \"components.OverlayBlocker.title.serverError\": \"Перезагрузка занимает больше времени, чем ожидалось\",\n    \"components.PageFooter.select\": \"записей на странице\",\n    \"components.ProductionBlocker.description\": \"В целях безопасности мы должны отключить этот плагин в других средах.\",\n    \"components.ProductionBlocker.header\": \"Этот плагин доступен только на стадии разработки.\",\n    \"components.Search.placeholder\": \"Поиск...\",\n    \"components.TableHeader.sort\": \"Сортировать по {label}\",\n    \"components.ViewSettings.tooltip\": \"Настройки просмотра\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Режим разметки\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Режим предпросмотра\",\n    \"components.Wysiwyg.collapse\": \"Свернуть\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Заголовок H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Заголовок H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Заголовок H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Заголовок H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Заголовок H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Заголовок H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Добавить заголовок\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"символов\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Развернуть\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Перетащите файлы в эту область, вставьте из буфера обмена или {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"выберите их\",\n    \"components.pagination.go-to\": \"Перейти на страницу {page}\",\n    \"components.pagination.go-to-next\": \"Перейти на следующую страницу\",\n    \"components.pagination.go-to-previous\": \"Перейти на предыдущую страницу\",\n    \"components.pagination.remaining-links\": \"И {number} других ссылок\",\n    \"components.popUpWarning.button.cancel\": \"Нет, отменить\",\n    \"components.popUpWarning.button.confirm\": \"Да, подтвердить\",\n    \"components.popUpWarning.message\": \"Вы уверены, что хотите удалить это?\",\n    \"components.popUpWarning.title\": \"Пожалуйста, подтвердите\",\n    \"containers.list.displayedFields\": \"Отображаемые поля\",\n    dark: dark,\n    \"form.button.continue\": \"Продолжить\",\n    \"form.button.done\": \"Выполнено\",\n    \"global.actions\": \"Действия\",\n    \"global.auditLogs\": \"Аудит логов\",\n    \"global.back\": \"Назад\",\n    \"global.cancel\": \"Отмена\",\n    \"global.change-password\": \"Сменить пароль\",\n    \"global.close\": \"Закрыть\",\n    \"global.content-manager\": \"Редактор контента\",\n    \"global.continue\": \"Продолжить\",\n    \"global.delete\": \"Удалить\",\n    \"global.delete-target\": \"Удалить {target}\",\n    \"global.description\": \"Описание\",\n    \"global.details\": \"Подробности\",\n    \"global.disabled\": \"Отключено\",\n    \"global.documentation\": \"Документация\",\n    \"global.enabled\": \"Включено\",\n    \"global.finish\": \"Готово\",\n    \"global.fullname\": \"{firstname} {lastname}\",\n    \"global.localeToggle.label\": \"Выбор языка интерфейса\",\n    \"global.marketplace\": \"Маркет\",\n    \"global.name\": \"Имя\",\n    \"global.none\": \"Нет\",\n    \"global.password\": \"Пароль\",\n    \"global.plugins\": \"Плагины\",\n    \"global.plugins.content-manager\": \"Редактор контента\",\n    \"global.plugins.content-manager.description\": \"Быстрый способ просмотра, редактирования и удаления данных в вашей базе данных.\",\n    \"global.plugins.content-type-builder\": \"Конструктор типов содержимого\",\n    \"global.plugins.content-type-builder.description\": \"Моделируйте структуру данных вашего API. Создавайте новые поля и отношения всего за минуту. Файлы автоматически создаются и обновляются в вашем проекте.\",\n    \"global.plugins.documentation\": \"Документация\",\n    \"global.plugins.documentation.description\": \"Создайте документ OpenAPI и визуализируйте свой API с помощью пользовательского интерфейса SWAGGER.\",\n    \"global.plugins.email\": \"Email\",\n    \"global.plugins.email.description\": \"Настройте своё приложение для отправки электронной почты.\",\n    \"global.plugins.graphql\": \"GraphQL\",\n    \"global.plugins.graphql.description\": \"Добавить конечную точку GraphQL с методами API по умолчанию.\",\n    \"global.plugins.i18n\": \"Интернационализация\",\n    \"global.plugins.i18n.description\": \"Этот плагин позволяет создавать, читать и обновлять контент на разных языках, как из панели администратора, так и с помощью API.\",\n    \"global.plugins.sentry\": \"Sentry\",\n    \"global.plugins.sentry.description\": \"Отправка событий об ошибках Strapi в Sentry.\",\n    \"global.plugins.upload\": \"Библиотека медиа\",\n    \"global.plugins.upload.description\": \"Управление медиафайлами.\",\n    \"global.plugins.users-permissions\": \"Роли и Разрешения\",\n    \"global.plugins.users-permissions.description\": \"Защитите свой API с помощью полного процесса аутентификации на основе JWT. Этот плагин также поставляется со стратегией ACL, которая позволяет вам управлять разрешениями между группами пользователей.\",\n    \"global.profile\": \"Профиль\",\n    \"global.prompt.unsaved\": \"Вы уверены, что хотите покинуть эту страницу? Все ваши модификации будут потеряны\",\n    \"global.reset-password\": \"Сброс пароля\",\n    \"global.roles\": \"Роли\",\n    \"global.save\": \"Сохранить\",\n    \"global.search\": \"Поиск\",\n    \"global.see-more\": \"Подробнее\",\n    \"global.select\": \"Выбрать\",\n    \"global.select-all-entries\": \"Выбрать все записи\",\n    \"global.settings\": \"Настройки\",\n    \"global.type\": \"Тип\",\n    \"global.users\": \"Пользователи\",\n    light: light,\n    \"notification.contentType.relations.conflict\": \"Тип контента имеет конфликтующие отношения\",\n    \"notification.default.title\": \"Информация:\",\n    \"notification.ee.warning.at-seat-limit.title\": \"{licenseLimitStatus, select, OVER_LIMIT {БОЛЬШЕ ЧЕМ} AT_LIMIT {РОВНО}} seat limit ({currentUserCount}/{permittedSeats})\",\n    \"notification.ee.warning.over-.message\": \"Добавить места чтобы {licenseLimitStatus, select, OVER_LIMIT {пригласить} AT_LIMIT {повторно подкючить}} пользователей. Если вы уже сделали это, но это ещё не отображается в Strapi, обязательно перезапустите своё приложение.\",\n    \"notification.error\": \"Произошла ошибка\",\n    \"notification.error.invalid.configuration\": \"У вас неправильная конфигурация настроек, проверьте журнал вашего сервера для получения дополнительной информации.\",\n    \"notification.error.layout\": \"Не удалось получить макет\",\n    \"notification.error.tokennamenotunique\": \"Имя уже присвоено другому токену\",\n    \"notification.form.error.fields\": \"Форма содержит некоторые ошибки\",\n    \"notification.form.success.fields\": \"Изменения сохранены\",\n    \"notification.link-copied\": \"Ссылка скопирована в буфер обмена\",\n    \"notification.permission.not-allowed-read\": \"Вам не разрешено просматривать этот документ\",\n    \"notification.success.apitokencreated\": \"API-токен успешно создан\",\n    \"notification.success.apitokenedited\": \"API-токен успешно отредактирован\",\n    \"notification.success.delete\": \"Элемент удален\",\n    \"notification.success.saved\": \"Сохранено\",\n    \"notification.success.title\": \"Успех:\",\n    \"notification.success.transfertokencreated\": \"Токен для передачи успешно создан\",\n    \"notification.success.transfertokenedited\": \"Токен для передачи успешно изменён\",\n    \"notification.version.update.message\": \"Доступна новая версия Strapi!\",\n    \"notification.warning.404\": \"404 — Не найдено\",\n    \"notification.warning.title\": \"Внимание:\",\n    or: or,\n    \"request.error.model.unknown\": \"Модель данных не существует\",\n    selectButtonTitle: selectButtonTitle,\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, ru as default, light, or, selectButtonTitle, skipToContent, submit };\n//# sourceMappingURL=ru.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,yDAAyD;AAAA,EACzD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,8DAA8D;AAAA,EAC9D,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,6DAA6D;AAAA,EAC7D,+DAA+D;AAAA,EAC/D,4DAA4D;AAAA,EAC5D,qDAAqD;AAAA,EACrD,8DAA8D;AAAA,EAC9D,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sDAAsD;AAAA,EACtD,mDAAmD;AAAA,EACnD,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,iEAAiE;AAAA,EACjE,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,wDAAwD;AAAA,EACxD,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,8DAA8D;AAAA,EAC9D,2DAA2D;AAAA,EAC3D,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,0CAA0C;AAAA,EAC1C,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,0CAA0C;AAAA,EAC1C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA,uBAAuB;AAAA,EACvB;AAAA,EACA,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,oCAAoC;AAAA,EACpC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,6CAA6C;AAAA,EAC7C,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,gDAAgD;AAAA,EAChD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qDAAqD;AAAA,EACrD,0CAA0C;AAAA,EAC1C,oDAAoD;AAAA,EACpD,+CAA+C;AAAA,EAC/C,yDAAyD;AAAA,EACzD,yCAAyC;AAAA,EACzC,mDAAmD;AAAA,EACnD,+CAA+C;AAAA,EAC/C,yDAAyD;AAAA,EACzD,yCAAyC;AAAA,EACzC,mDAAmD;AAAA,EACnD,+CAA+C;AAAA,EAC/C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,yDAAyD;AAAA,EACzD,2CAA2C;AAAA,EAC3C,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,wDAAwD;AAAA,EACxD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC;AAAA,EACA,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,gBAAgB;AAAA,EAChB,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB;AAAA,EACA,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,sBAAsB;AAAA,EACtB,4CAA4C;AAAA,EAC5C,6BAA6B;AAAA,EAC7B,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,6CAA6C;AAAA,EAC7C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AACJ;", "names": []}