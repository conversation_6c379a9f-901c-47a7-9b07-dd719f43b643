var e={"CMEditViewCopyLocale.copy-failure":"Yerel ayarlar kopyalanamadı","CMEditViewCopyLocale.copy-success":"Yerel ayarlar kopyalandı","CMEditViewCopyLocale.copy-text":"Başka bir yerel ayardan doldur","CMEditViewCopyLocale.submit-text":"Evet, doldur","CMListView.popover.display-locales.label":"Tercüme edilmiş yerel ayarları göster","CheckboxConfirmation.Modal.body":"Devredışı bırakmak istiyor musun?","CheckboxConfirmation.Modal.button-confirm":"Eve<PERSON>, devredışı bırak","CheckboxConfirmation.Modal.content":"Yerelleştirmeyi devredışı bırakmak varsayılan yerel ayarınız ile ilişkili olmayan tüm diğer içeriklerin silinmesine neden olur.","Field.localized":"<PERSON>u değer seçilmiş olan yerel ayara özgüdür","Field.not-localized":"Bu değer tüm yerel ayarlarda ortaktır","Settings.list.actions.add":"Yeni bir yerel ayar ekle","Settings.list.actions.delete":"Bir yerel ayarı sil","Settings.list.actions.deleteAdditionalInfos":"Bu aktif yerel ayar versiyonlarını silecektir <em>(Uluslararasılaştırma'dan)</em>","Settings.list.actions.edit":"Bir yerel ayarı düzenle","Settings.list.description":"Uluslararasılaştırma eklentisinin ayarlarını düzenle","Settings.list.empty.description":"Bu beklendik bir davranış değil. Veritabanına elle müdahale olduğu anlamına geliyor. Strapinin düzgün çalışabilmesi için veritabanınızda en az bir adet yerel ayarın olduğundan emin olun.","Settings.list.empty.title":"Hiçbir yerel ayar yok.","Settings.locales.default":"Varsayılan","Settings.locales.list.sort.default":"Varsayılan yerel ayara göre diz","Settings.locales.list.sort.displayName":"Görüntülenme adına göre diz","Settings.locales.list.sort.id":"Kimlik numarasına göre diz","Settings.locales.modal.advanced":"Gelişmiş ayarlar","Settings.locales.modal.advanced.setAsDefault":"Varsayılan yerel ayar olarak belirle","Settings.locales.modal.advanced.setAsDefault.hint":"Bir varsayılan yerel ayar gereklidir. Farklı bir yerel ayar seçerek değiştirin","Settings.locales.modal.advanced.settings":"Ayarlar","Settings.locales.modal.base":"Temel Ayarlar","Settings.locales.modal.create.alreadyExist":"Bu yerel ayar zaten mevcut","Settings.locales.modal.create.defaultLocales.loading":"Mevcut yerel ayarlar yükleniyor...","Settings.locales.modal.create.success":"Yerel ayar başarıyla eklendi","Settings.locales.modal.create.tab.label":"Temel uluslararasılaştırma ayarları ile gelişmiş ayarlar arasında geçiş yapılıyor","Settings.locales.modal.delete.confirm":"Evet, sil","Settings.locales.modal.delete.message":"Bu yerel ayarı silmek ilişkili tüm içeriği de siler. Eğer içerikleri korumak istiyorsanız, öncelikle farklı bir yerel ayar ile ilişkilendirin.","Settings.locales.modal.delete.secondMessage":"Bu yerel ayarı silmek istiyor musun?","Settings.locales.modal.delete.success":"Yerel ayar başarıyla silindi","Settings.locales.modal.edit.confirmation":"Tamamla","Settings.locales.modal.create.code.label":"Yerel Ayarlar","Settings.locales.modal.edit.success":"Yerel ayar başarıyla düzenlendi","Settings.locales.modal.edit.tab.label":"Temel uluslararasılaştırma ayarları ile gelişmiş ayarlar arasında geçiş yapılıyor","Settings.locales.modal.create.name.label":"Yerel ayar görüntülenme adı","Settings.locales.modal.create.name.label.description":"Yerel ayar yönetim panelinde bu isimde görüntülenecek","Settings.locales.modal.create.name.label.error":"Yerel ayar görüntülenme adı 50 karakterden küçük olmalıdır","Settings.locales.modal.locales.label":"Yerel ayarlar","Settings.locales.modal.locales.loaded":"Yerel ayarlar başarıyla yüklendi.","Settings.locales.modal.title":"Kurulumlar","Settings.locales.row.default-locale":"Varsayılan yerel ayar","Settings.locales.row.displayName":"Görüntülenme adı","Settings.locales.row.id":"Kimlik Numarası","Settings.permissions.loading":"İzinler yükleniyor","Settings.permissions.read.denied.description":"Bunu okuyabilmek için sistem yöneticinizle iletişime geçin.","Settings.permissions.read.denied.title":"Bu içeriye ulaşmak için yetkiniz bulunmuyor.","actions.select-locale":"Bir yerel ayar seçin","components.Select.locales.not-available":"İçerik mevcut değil","plugin.description.long":"Bu eklenti, hem Yönetim paneli hem de API üzerinden, farklı dillerdeki içeriği oluşturma, okuma ve güncelleme imkanı sağlar.","plugin.description.short":"Bu eklenti, hem Yönetim paneli hem de API üzerinden, farklı dillerdeki içeriği oluşturma, okuma ve güncelleme imkanı sağlar.","plugin.name":"Uluslararasılaştırma","plugin.schema.i18n.localized.description-content-type":"İçerikleri yerelleştirebilmenize imkan tanır","plugin.schema.i18n.localized.description-field":"Bu alan farklı yerel ayarlarda farklı değer alabilir","plugin.schema.i18n.localized.label-content-type":"Bu İçerik-Tipi için yerelleştirmeyi etkinleştir","plugin.schema.i18n.localized.label-field":"Bu Alan için yerelleştirmeyi etkinleştir"};export{e as default};
