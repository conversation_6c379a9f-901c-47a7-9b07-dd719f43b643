import{a as n,j as t,bK as s}from"./strapi-z7ApxZZq.js";import{a}from"./SelectRoles-DgSmECKC.js";import"./useAdminRoles-DhqKz3-J.js";const g=({registrationToken:e})=>{const{formatMessage:i}=n();return e?t.jsx(a,{target:`${window.location.origin}${s()}/auth/register?registrationToken=${e}`,children:i({id:"app.components.Users.MagicLink.connect",defaultMessage:"Copy and share this link to give access to this user"})}):t.jsx(a,{target:`${window.location.origin}${s()}/auth/login`,children:i({id:"app.components.Users.MagicLink.connect.sso",defaultMessage:"Send this link to the user, the first login can be made via a SSO provider."})})};export{g as MagicLinkEE};
