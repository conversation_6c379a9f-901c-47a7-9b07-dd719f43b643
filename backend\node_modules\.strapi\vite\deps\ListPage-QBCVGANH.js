import {
  useWebhooks
} from "./chunk-OKUR64FA.js";
import {
  ConfirmDialog
} from "./chunk-K4LYEYIN.js";
import {
  useRBAC
} from "./chunk-WIFIVZU3.js";
import {
  Layouts
} from "./chunk-DETWX3NC.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  ForwardRef$J,
  Page,
  useAPIErrorHandler
} from "./chunk-FTSHQ5RF.js";
import "./chunk-RZWN45NY.js";
import {
  useTypedSelector
} from "./chunk-FOUXGIF2.js";
import "./chunk-TXOJWASW.js";
import "./chunk-BUMITFEB.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-PW6GS6S3.js";
import {
  Button,
  CheckboxImpl,
  Dialog,
  EmptyStateLayout,
  Flex,
  IconButton,
  LinkButton,
  SwitchImpl,
  TFooter,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Typography,
  VisuallyHidden,
  useIntl,
  useNotifyAT
} from "./chunk-Y6YT4U2T.js";
import {
  NavLink,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h,
  ForwardRef$1v,
  ForwardRef$j
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/Webhooks/ListPage.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var ListPage = () => {
  const [showModal, setShowModal] = React.useState(false);
  const [webhooksToDelete, setWebhooksToDelete] = React.useState([]);
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a.webhooks;
  });
  const { formatMessage } = useIntl();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { toggleNotification } = useNotification();
  const navigate = useNavigate();
  const { isLoading: isRBACLoading, allowedActions: { canCreate, canUpdate, canDelete } } = useRBAC(permissions);
  const { notifyStatus } = useNotifyAT();
  const { isLoading: isWebhooksLoading, webhooks, error: webhooksError, updateWebhook, deleteManyWebhooks } = useWebhooks();
  React.useEffect(() => {
    if (webhooksError) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(webhooksError)
      });
      return;
    }
    if (webhooks) {
      notifyStatus(formatMessage({
        id: "Settings.webhooks.list.loading.success",
        defaultMessage: "Webhooks have been loaded"
      }));
    }
  }, [
    webhooks,
    webhooksError,
    toggleNotification,
    formatMessage,
    notifyStatus,
    formatAPIError
  ]);
  const enableWebhook = async (body) => {
    try {
      const res = await updateWebhook(body);
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
      }
    } catch {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    }
  };
  const deleteWebhook = async (id) => {
    try {
      const res = await deleteManyWebhooks({
        ids: [
          id
        ]
      });
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
        return;
      }
      setWebhooksToDelete((prev) => prev.filter((webhookId) => webhookId !== id));
    } catch {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    }
  };
  const confirmBulkDelete = async () => {
    try {
      const res = await deleteManyWebhooks({
        ids: webhooksToDelete
      });
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
        return;
      }
      setWebhooksToDelete([]);
    } catch {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    } finally {
      setShowModal(false);
    }
  };
  const selectAllCheckbox = (selected) => selected ? setWebhooksToDelete((webhooks == null ? void 0 : webhooks.map((webhook) => webhook.id)) ?? []) : setWebhooksToDelete([]);
  const selectOneCheckbox = (selected, id) => selected ? setWebhooksToDelete((prev) => [
    ...prev,
    id
  ]) : setWebhooksToDelete((prev) => prev.filter((webhookId) => webhookId !== id));
  const isLoading = isRBACLoading || isWebhooksLoading;
  const numberOfWebhooks = (webhooks == null ? void 0 : webhooks.length) ?? 0;
  const webhooksToDeleteLength = webhooksToDelete.length;
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Layouts.Root, {
    children: [
      (0, import_jsx_runtime.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "Webhooks"
        })
      }),
      (0, import_jsx_runtime.jsxs)(Page.Main, {
        "aria-busy": isLoading,
        children: [
          (0, import_jsx_runtime.jsx)(Layouts.Header, {
            title: formatMessage({
              id: "Settings.webhooks.title",
              defaultMessage: "Webhooks"
            }),
            subtitle: formatMessage({
              id: "Settings.webhooks.list.description",
              defaultMessage: "Get POST changes notifications"
            }),
            primaryAction: canCreate && !isLoading && (0, import_jsx_runtime.jsx)(LinkButton, {
              tag: NavLink,
              startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
              variant: "default",
              to: "create",
              size: "S",
              children: formatMessage({
                id: "Settings.webhooks.list.button.add",
                defaultMessage: "Create new webhook"
              })
            })
          }),
          webhooksToDeleteLength > 0 && canDelete && (0, import_jsx_runtime.jsx)(Layouts.Action, {
            startActions: (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
              children: [
                (0, import_jsx_runtime.jsx)(Typography, {
                  variant: "epsilon",
                  textColor: "neutral600",
                  children: formatMessage({
                    id: "Settings.webhooks.to.delete",
                    defaultMessage: "{webhooksToDeleteLength, plural, one {# webhook} other {# webhooks}} selected"
                  }, {
                    webhooksToDeleteLength
                  })
                }),
                (0, import_jsx_runtime.jsx)(Button, {
                  onClick: () => setShowModal(true),
                  startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$j, {}),
                  size: "L",
                  variant: "danger-light",
                  children: formatMessage({
                    id: "global.delete",
                    defaultMessage: "Delete"
                  })
                })
              ]
            })
          }),
          (0, import_jsx_runtime.jsx)(Layouts.Content, {
            children: numberOfWebhooks > 0 ? (0, import_jsx_runtime.jsxs)(Table, {
              colCount: 5,
              rowCount: numberOfWebhooks + 1,
              footer: (0, import_jsx_runtime.jsx)(TFooter, {
                onClick: () => {
                  if (canCreate) {
                    navigate("create");
                  }
                },
                icon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
                children: formatMessage({
                  id: "Settings.webhooks.list.button.add",
                  defaultMessage: "Create new webhook"
                })
              }),
              children: [
                (0, import_jsx_runtime.jsx)(Thead, {
                  children: (0, import_jsx_runtime.jsxs)(Tr, {
                    children: [
                      (0, import_jsx_runtime.jsx)(Th, {
                        children: (0, import_jsx_runtime.jsx)(CheckboxImpl, {
                          "aria-label": formatMessage({
                            id: "global.select-all-entries",
                            defaultMessage: "Select all entries"
                          }),
                          checked: webhooksToDeleteLength > 0 && webhooksToDeleteLength < numberOfWebhooks ? "indeterminate" : webhooksToDeleteLength === numberOfWebhooks,
                          onCheckedChange: selectAllCheckbox
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Th, {
                        width: "20%",
                        children: (0, import_jsx_runtime.jsx)(Typography, {
                          variant: "sigma",
                          textColor: "neutral600",
                          children: formatMessage({
                            id: "global.name",
                            defaultMessage: "Name"
                          })
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Th, {
                        width: "60%",
                        children: (0, import_jsx_runtime.jsx)(Typography, {
                          variant: "sigma",
                          textColor: "neutral600",
                          children: formatMessage({
                            id: "Settings.webhooks.form.url",
                            defaultMessage: "URL"
                          })
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Th, {
                        width: "20%",
                        children: (0, import_jsx_runtime.jsx)(Typography, {
                          variant: "sigma",
                          textColor: "neutral600",
                          children: formatMessage({
                            id: "Settings.webhooks.list.th.status",
                            defaultMessage: "Status"
                          })
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Th, {
                        children: (0, import_jsx_runtime.jsx)(VisuallyHidden, {
                          children: formatMessage({
                            id: "Settings.webhooks.list.th.actions",
                            defaultMessage: "Actions"
                          })
                        })
                      })
                    ]
                  })
                }),
                (0, import_jsx_runtime.jsx)(Tbody, {
                  children: webhooks == null ? void 0 : webhooks.map((webhook) => (0, import_jsx_runtime.jsxs)(Tr, {
                    onClick: () => {
                      if (canUpdate) {
                        navigate(webhook.id);
                      }
                    },
                    style: {
                      cursor: canUpdate ? "pointer" : "default"
                    },
                    children: [
                      (0, import_jsx_runtime.jsx)(Td, {
                        onClick: (e) => e.stopPropagation(),
                        children: (0, import_jsx_runtime.jsx)(CheckboxImpl, {
                          "aria-label": `${formatMessage({
                            id: "global.select",
                            defaultMessage: "Select"
                          })} ${webhook.name}`,
                          checked: webhooksToDelete == null ? void 0 : webhooksToDelete.includes(webhook.id),
                          onCheckedChange: (selected) => selectOneCheckbox(!!selected, webhook.id),
                          name: "select"
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Td, {
                        children: (0, import_jsx_runtime.jsx)(Typography, {
                          fontWeight: "semiBold",
                          textColor: "neutral800",
                          children: webhook.name
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Td, {
                        children: (0, import_jsx_runtime.jsx)(Typography, {
                          textColor: "neutral800",
                          children: webhook.url
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Td, {
                        onClick: (e) => e.stopPropagation(),
                        children: (0, import_jsx_runtime.jsx)(Flex, {
                          children: (0, import_jsx_runtime.jsx)(SwitchImpl, {
                            onLabel: formatMessage({
                              id: "global.enabled",
                              defaultMessage: "Enabled"
                            }),
                            offLabel: formatMessage({
                              id: "global.disabled",
                              defaultMessage: "Disabled"
                            }),
                            "aria-label": `${webhook.name} ${formatMessage({
                              id: "Settings.webhooks.list.th.status",
                              defaultMessage: "Status"
                            })}`,
                            checked: webhook.isEnabled,
                            onCheckedChange: (enabled) => {
                              enableWebhook({
                                ...webhook,
                                isEnabled: enabled
                              });
                            },
                            visibleLabels: true
                          })
                        })
                      }),
                      (0, import_jsx_runtime.jsx)(Td, {
                        children: (0, import_jsx_runtime.jsxs)(Flex, {
                          gap: 1,
                          children: [
                            canUpdate && (0, import_jsx_runtime.jsx)(IconButton, {
                              label: formatMessage({
                                id: "Settings.webhooks.events.update",
                                defaultMessage: "Update"
                              }),
                              variant: "ghost",
                              children: (0, import_jsx_runtime.jsx)(ForwardRef$1v, {})
                            }),
                            canDelete && (0, import_jsx_runtime.jsx)(DeleteActionButton, {
                              onDelete: () => {
                                deleteWebhook(webhook.id);
                              }
                            })
                          ]
                        })
                      })
                    ]
                  }, webhook.id))
                })
              ]
            }) : (0, import_jsx_runtime.jsx)(EmptyStateLayout, {
              icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, {
                width: "160px"
              }),
              content: formatMessage({
                id: "Settings.webhooks.list.empty.description",
                defaultMessage: "No webhooks found"
              }),
              action: canCreate ? (0, import_jsx_runtime.jsx)(LinkButton, {
                variant: "secondary",
                startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
                tag: NavLink,
                to: "create",
                children: formatMessage({
                  id: "Settings.webhooks.list.button.add",
                  defaultMessage: "Create new webhook"
                })
              }) : null
            })
          })
        ]
      }),
      (0, import_jsx_runtime.jsx)(Dialog.Root, {
        open: showModal,
        onOpenChange: setShowModal,
        children: (0, import_jsx_runtime.jsx)(ConfirmDialog, {
          onConfirm: confirmBulkDelete
        })
      })
    ]
  });
};
var DeleteActionButton = ({ onDelete }) => {
  const [showModal, setShowModal] = React.useState(false);
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
    children: [
      (0, import_jsx_runtime.jsx)(IconButton, {
        onClick: (e) => {
          e.stopPropagation();
          setShowModal(true);
        },
        label: formatMessage({
          id: "Settings.webhooks.events.delete",
          defaultMessage: "Delete webhook"
        }),
        variant: "ghost",
        children: (0, import_jsx_runtime.jsx)(ForwardRef$j, {})
      }),
      (0, import_jsx_runtime.jsx)(Dialog.Root, {
        open: showModal,
        onOpenChange: setShowModal,
        children: (0, import_jsx_runtime.jsx)(ConfirmDialog, {
          onConfirm: (e) => {
            e == null ? void 0 : e.stopPropagation();
            onDelete();
          }
        })
      })
    ]
  });
};
var ProtectedListPage = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a.webhooks.main;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(ListPage, {})
  });
};
export {
  ListPage,
  ProtectedListPage
};
//# sourceMappingURL=ListPage-QBCVGANH.js.map
