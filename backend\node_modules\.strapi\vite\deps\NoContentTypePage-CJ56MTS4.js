import {
  getTranslation
} from "./chunk-HIZVCZYI.js";
import "./chunk-MLDZODJM.js";
import "./chunk-GXEE7KZL.js";
import "./chunk-6EC7MKBK.js";
import "./chunk-55Q6LON3.js";
import "./chunk-QVDAYSOU.js";
import "./chunk-A34HN3WE.js";
import "./chunk-APY4KZ5L.js";
import "./chunk-OOAHAGSN.js";
import "./chunk-BEZCWAXF.js";
import "./chunk-MFYBRT3Z.js";
import "./chunk-73ABBQBL.js";
import "./chunk-WNXMQTNQ.js";
import "./chunk-VJ3LKUI5.js";
import "./chunk-3OKC5SXR.js";
import "./chunk-ITIYAEI7.js";
import "./chunk-K4LYEYIN.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-UCU7ROGU.js";
import "./chunk-K65KIEAL.js";
import "./chunk-F5JI4FJS.js";
import "./chunk-TFPYBHFW.js";
import "./chunk-MMKYPT4K.js";
import "./chunk-U63NXCTP.js";
import "./chunk-SGQXSZWC.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-7US6K6XC.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-DZIOEGNZ.js";
import "./chunk-L3WQFZOD.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-67LA7IEH.js";
import "./chunk-UHKKWDMK.js";
import "./chunk-GM54BMM2.js";
import "./chunk-2DNMQP4H.js";
import "./chunk-L32VSWBJ.js";
import "./chunk-XVW7MCOW.js";
import "./chunk-2U7FOQNK.js";
import "./chunk-WIFIVZU3.js";
import "./chunk-T3UNFN7Y.js";
import {
  Layouts
} from "./chunk-DETWX3NC.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  ForwardRef$J,
  Page
} from "./chunk-FTSHQ5RF.js";
import "./chunk-RZWN45NY.js";
import "./chunk-FOUXGIF2.js";
import "./chunk-TXOJWASW.js";
import "./chunk-BUMITFEB.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-PW6GS6S3.js";
import {
  EmptyStateLayout,
  LinkButton,
  useIntl
} from "./chunk-Y6YT4U2T.js";
import "./chunk-5ZC4PE57.js";
import {
  NavLink
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-3CQBCJ3G.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/pages/NoContentTypePage.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var NoContentType = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Page.Main, {
    children: [
      (0, import_jsx_runtime.jsx)(Layouts.Header, {
        title: formatMessage({
          id: getTranslation("header.name"),
          defaultMessage: "Content"
        })
      }),
      (0, import_jsx_runtime.jsx)(Layouts.Content, {
        children: (0, import_jsx_runtime.jsx)(EmptyStateLayout, {
          action: (0, import_jsx_runtime.jsx)(LinkButton, {
            tag: NavLink,
            variant: "secondary",
            startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
            to: "/plugins/content-type-builder/content-types/create-content-type",
            children: formatMessage({
              id: "app.components.HomePage.create",
              defaultMessage: "Create your first Content-type"
            })
          }),
          content: formatMessage({
            id: "content-manager.pages.NoContentType.text",
            defaultMessage: "You don't have any content yet, we recommend you to create your first Content-Type."
          }),
          hasRadius: true,
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, {
            width: "16rem"
          }),
          shadow: "tableShadow"
        })
      })
    ]
  });
};
export {
  NoContentType
};
//# sourceMappingURL=NoContentTypePage-CJ56MTS4.js.map
