{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/pt-BR.json.mjs"], "sourcesContent": ["var configurations = \"Configurações\";\nvar from = \"de\";\nvar ptBR = {\n    \"attribute.boolean\": \"Booleano\",\n    \"attribute.boolean.description\": \"Sim ou não, 1 ou 0, verdadeiro ou falso\",\n    \"attribute.component\": \"Componente\",\n    \"attribute.component.description\": \"Grupo de campos que você pode repetir ou reutilizar\",\n    \"attribute.date\": \"Data\",\n    \"attribute.date.description\": \"Seleciona datas com horas, minutos e segundos\",\n    \"attribute.datetime\": \"Data e hora\",\n    \"attribute.dynamiczone\": \"Zona dinâmica\",\n    \"attribute.dynamiczone.description\": \"Escolha um componente dinamicamente quando estiver editando um conteúdo\",\n    \"attribute.email\": \"E-mail\",\n    \"attribute.email.description\": \"Campo de email com validação de formato\",\n    \"attribute.enumeration\": \"Enumeração\",\n    \"attribute.enumeration.description\": \"Lista de valores, escolha um\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"Dados no formato de JSON\",\n    \"attribute.media\": \"Mídia\",\n    \"attribute.media.description\": \"Arquivos como imagens, vídeos, etc\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"Número\",\n    \"attribute.number.description\": \"Números (inteiro, flutuante, decimal)\",\n    \"attribute.password\": \"Senha\",\n    \"attribute.password.description\": \"Campo de senha com criptografia\",\n    \"attribute.relation\": \"Relação\",\n    \"attribute.relation.description\": \"Refere-se a um Tipo de Coleção\",\n    \"attribute.richtext\": \"Texto avançado\",\n    \"attribute.richtext.description\": \"Um editor de texto avançado com opções de formatação\",\n    \"attribute.text\": \"Texto\",\n    \"attribute.text.description\": \"Texto curto ou longo como título ou descrição\",\n    \"attribute.time\": \"Time\",\n    \"attribute.timestamp\": \"Timestamp\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Identificador único\",\n    \"button.attributes.add.another\": \"Adicionar outro campo\",\n    \"button.component.add\": \"Adicionar um componente\",\n    \"button.component.create\": \"Criar novo componente\",\n    \"button.model.create\": \"Criar novo tipo de coleção\",\n    \"button.single-types.create\": \"Criar novo tipo único\",\n    \"component.repeatable\": \"(repetível)\",\n    \"components.SelectComponents.displayed-value\": \"{number, plural, =0 {# componentes} one {# componente} other {# componentes}} selecionados\",\n    \"components.componentSelect.no-component-available\": \"Você já adicionou todos os seus componentes\",\n    \"components.componentSelect.no-component-available.with-search\": \"Não há nenhum componente que corresponda à sua pesquisa\",\n    \"components.componentSelect.value-component\": \"{number} componente selecionado (digite para pesquisar por um componente)\",\n    \"components.componentSelect.value-components\": \"{number} componentes selecionados\",\n    configurations: configurations,\n    \"contentType.apiId-plural.description\": \"ID de API pluralizado\",\n    \"contentType.apiId-plural.label\": \"ID da API (plural)\",\n    \"contentType.apiId-singular.description\": \"O UID é usado para gerar as rotas de API e tabelas/coleções de bancos de dados\",\n    \"contentType.apiId-singular.label\": \"ID da API (Singular)\",\n    \"contentType.collectionName.description\": \"Útil quando o nome do seu Tipo de Conteúdo e o nome da sua tabela diferem\",\n    \"contentType.collectionName.label\": \"Nome da coleção\",\n    \"contentType.displayName.label\": \"Mostrar nome\",\n    \"contentType.kind.change.warning\": \"Você acabou de alterar o tipo de um tipo de conteúdo: a API será redefinida (rotas, controladores e serviços serão substituídos).\",\n    \"error.attributeName.reserved-name\": \"Este nome não pode ser usado em seu tipo de conteúdo, pois pode quebrar outras funcionalidades\",\n    \"error.contentType.pluralName-used\": \"Este valor não pode ser igual ao singular\",\n    \"error.contentType.singularName-used\": \"Este valor não pode ser igual ao plural\",\n    \"error.contentTypeName.reserved-name\": \"Este nome não pode ser usado em seu projeto, pois pode quebrar outras funcionalidades\",\n    \"error.validation.enum-duplicate\": \"Valores duplicados não são permitidos (somente os caracteres alfanuméricos são considerados).\",\n    \"error.validation.enum-empty-string\": \"Strings vazias não são permitidas\",\n    \"error.validation.enum-regex\": \"Pelo menos um valor é inválido. Os valores devem ter pelo menos um caractere alfabético antes da primeira ocorrência de um número.\",\n    \"error.validation.minSupMax\": \"Não pode ser superior\",\n    \"error.validation.positive\": \"Valor deve ser positivo\",\n    \"error.validation.regex\": \"O padrão Regex é inválido\",\n    \"error.validation.relation.targetAttribute-taken\": \"Este atributo já está sendo usado\",\n    \"form.attribute.component.option.add\": \"Adicionar componente\",\n    \"form.attribute.component.option.create\": \"Criar novo componente\",\n    \"form.attribute.component.option.create.description\": \"Um componente é compartilhado entre tipos e componentes, ele estará disponível e acessível em qualquer lugar.\",\n    \"form.attribute.component.option.repeatable\": \"Componente repetível\",\n    \"form.attribute.component.option.repeatable.description\": \"Melhor para várias instâncias (array) de ingredientes, meta tags, etc.\",\n    \"form.attribute.component.option.reuse-existing\": \"Reutilizar componente existente\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Reutilize um componente já criado para manter seus dados consistentes em todos os tipos de conteúdo.\",\n    \"form.attribute.component.option.single\": \"Componente único\",\n    \"form.attribute.component.option.single.description\": \"Melhor para agrupar campos como endereço completo, informações principais, etc...\",\n    \"form.attribute.item.customColumnName\": \"Nomes de coluna personalizados\",\n    \"form.attribute.item.customColumnName.description\": \"Isso é útil para renomear os nomes das colunas do banco de dados em um formato mais abrangente para as respostas da API\",\n    \"form.attribute.item.date.type.date\": \"data (ex: 01/01/{currentYear})\",\n    \"form.attribute.item.date.type.datetime\": \"data e hora (ex: 01/01/{currentYear} 00:00 AM)\",\n    \"form.attribute.item.date.type.time\": \"hora (ex: 00:00 AM)\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Nome do campo\",\n    \"form.attribute.item.enumeration.graphql\": \"Substituição de nome para GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Permite que você substitua o nome padrão gerado para GraphQL\",\n    \"form.attribute.item.enumeration.placeholder\": \"Ex:\\nmanhã\\ntarde\\nnoite\",\n    \"form.attribute.item.enumeration.rules\": \"Valores (uma linha por valor)\",\n    \"form.attribute.item.maximum\": \"Valor máximo\",\n    \"form.attribute.item.maximumLength\": \"Tamanho máximo\",\n    \"form.attribute.item.minimum\": \"Valor mínimo\",\n    \"form.attribute.item.minimumLength\": \"Tamanho mínimo\",\n    \"form.attribute.item.number.type\": \"Formato de número\",\n    \"form.attribute.item.number.type.biginteger\": \"inteiro grande (ex: 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"decimal (ex: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"float (ex: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"inteiro (ex: 10)\",\n    \"form.attribute.item.privateField\": \"Campo privado\",\n    \"form.attribute.item.privateField.description\": \"Este campo não aparecerá na resposta da API\",\n    \"form.attribute.item.requiredField\": \"Campo obrigatório\",\n    \"form.attribute.item.requiredField.description\": \"Você não poderá criar uma entrada se este campo estiver vazio\",\n    \"form.attribute.item.text.regex\": \"Padrão Regex\",\n    \"form.attribute.item.text.regex.description\": \"O texto da expressão regular\",\n    \"form.attribute.item.uniqueField\": \"Campo único\",\n    \"form.attribute.item.uniqueField.description\": \"Você não poderá criar uma entrada se houver uma entrada existente com conteúdo idêntico\",\n    \"form.attribute.media.allowed-types\": \"Selecione os tipos de mídia permitidos\",\n    \"form.attribute.media.allowed-types.option-files\": \"Arquivos\",\n    \"form.attribute.media.allowed-types.option-images\": \"Imagens\",\n    \"form.attribute.media.allowed-types.option-videos\": \"Vídeos\",\n    \"form.attribute.media.option.multiple\": \"Múltiplos\",\n    \"form.attribute.media.option.multiple.description\": \"Melhor para sliders, carrosséis ou download de vários arquivos\",\n    \"form.attribute.media.option.single\": \"Único\",\n    \"form.attribute.media.option.single.description\": \"Melhor para avatar, foto de perfil ou capa\",\n    \"form.attribute.settings.default\": \"Valor Padrão\",\n    \"form.attribute.text.option.long-text\": \"Texto longo\",\n    \"form.attribute.text.option.long-text.description\": \"Melhor para descrições, biografia. A pesquisa exata está desativada.\",\n    \"form.attribute.text.option.short-text\": \"Texto curto\",\n    \"form.attribute.text.option.short-text.description\": \"Melhor para títulos, nomes, links (URL). Também permite a pesquisa exata no campo.\",\n    \"form.button.add-components-to-dynamiczone\": \"Adicionar componentes à zona\",\n    \"form.button.add-field\": \"Adicionar campo\",\n    \"form.button.add-first-field-to-created-component\": \"Adicionar primeiro campo ao componente criado\",\n    \"form.button.add.field.to.collectionType\": \"Adicionar outro campo a este tipo de coleção\",\n    \"form.button.add.field.to.component\": \"Adicionar outro campo a este componente\",\n    \"form.button.add.field.to.contentType\": \"Adicionar outro campo a este tipo de conteúdo\",\n    \"form.button.add.field.to.singleType\": \"Adicionar outro campo a este único tipo\",\n    \"form.button.cancel\": \"Cancelar\",\n    \"form.button.collection-type.description\": \"Melhor para várias instâncias, como artigos, produtos, comentários etc.\",\n    \"form.button.collection-type.name\": \"Tipo de Coleção\",\n    \"form.button.configure-component\": \"Configurar componente\",\n    \"form.button.configure-view\": \"Configurar visualização\",\n    \"form.button.select-component\": \"Selecionar componente\",\n    \"form.button.single-type.description\": \"Melhor para instância única, como sobre nós, página inicial etc.\",\n    \"form.button.single-type.name\": \"Tipo Único\",\n    from: from,\n    \"menu.section.components.name\": \"Componentes\",\n    \"menu.section.models.name\": \"Tipos de Coleção\",\n    \"menu.section.single-types.name\": \"Tipos Únicos\",\n    \"modalForm.attribute.form.base.name.description\": \"Nenhum espaço é permitido para o nome do atributo\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"por exemplo. slug, urlDeSeo, urlCanônica\",\n    \"modalForm.attribute.target-field\": \"Campo anexado\",\n    \"modalForm.attributes.select-component\": \"Selecione um componente\",\n    \"modalForm.attributes.select-components\": \"Selecione os componentes\",\n    \"modalForm.collectionType.header-create\": \"Criar modelo\",\n    \"modalForm.component.header-create\": \"Criar componente\",\n    \"modalForm.components.create-component.category.label\": \"Selecione uma categoria ou insira um nome para criar uma nova\",\n    \"modalForm.components.icon.label\": \"Ícone\",\n    \"modalForm.editCategory.base.name.description\": \"Não é permitido espaço para o nome da categoria\",\n    \"modalForm.header-edit\": \"Editar {name}\",\n    \"modalForm.header.categories\": \"Categorias\",\n    \"modalForm.header.back\": \"Voltar\",\n    \"modalForm.singleType.header-create\": \"Criar tipo único\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Adicionar novo componente à zona dinâmica\",\n    \"modalForm.sub-header.attribute.create\": \"Adicionar novo campo {type}\",\n    \"modalForm.sub-header.attribute.create.step\": \"Adicionar novo componente ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Editar {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Selecione um campo para seu tipo de coleção\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Selecione um campo para seu componente\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Selecione um campo para seu tipo único\",\n    \"modelPage.attribute.relation-polymorphic\": \"Relação (polimórfica)\",\n    \"modelPage.attribute.relationWith\": \"Relação com\",\n    \"notification.error.dynamiczone-min.validation\": \"Pelo menos um componente é necessário em uma zona dinâmica para poder salvar um tipo de conteúdo\",\n    \"notification.info.autoreaload-disable\": \"O recurso autoReload é necessário para usar este plugin. Inicie seu servidor com `strapi develop`\",\n    \"notification.info.creating.notSaved\": \"Por favor, salve seu trabalho antes de criar um novo tipo de coleção ou componente\",\n    \"plugin.description.long\": \"Modele a estrutura de dados da sua API. Crie novos campos e relações em apenas um minuto. Os arquivos são criados e atualizados automaticamente em seu projeto.\",\n    \"plugin.description.short\": \"Modele a estrutura de dados da sua API.\",\n    \"plugin.name\": \"Criador de tipo de conteúdo\",\n    \"popUpForm.navContainer.advanced\": \"Configurações avançadas\",\n    \"popUpForm.navContainer.base\": \"Configurações básicas\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"Tem certeza de que deseja cancelar suas modificações?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Tem certeza de que deseja cancelar suas modificações? Alguns componentes foram criados ou modificados...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"Tem certeza de que deseja excluir esta categoria? Todos os componentes também serão excluídos.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"Tem certeza de que deseja excluir este componente?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Tem certeza de que deseja excluir este tipo de coleção?\",\n    \"popUpWarning.draft-publish.button.confirm\": \"Sim, desabilitar \",\n    \"popUpWarning.draft-publish.message\": \"Se você desativar o sistema Rascunho/Publicar, seus rascunhos serão excluídos.\",\n    \"popUpWarning.draft-publish.second-message\": \"Tem certeza de que deseja desativá-lo?\",\n    \"prompt.unsaved\": \"Você tem certeza de que quer sair? Todas as suas modificações serão perdidas.\",\n    \"relation.attributeName.placeholder\": \"Ex: autor, categoria, tag\",\n    \"relation.manyToMany\": \"tem e pertence a muitos\",\n    \"relation.manyToOne\": \"tem e pertence a um\",\n    \"relation.manyWay\": \"tem e pertence a muitos\",\n    \"relation.oneToMany\": \"pertence a muitos\",\n    \"relation.oneToOne\": \"tem e pertence a um\",\n    \"relation.oneWay\": \"pertence a um\",\n    \"table.button.no-fields\": \"Adicionar novo campo\",\n    \"table.content.create-first-content-type\": \"Crie seu primeiro tipo de coleção\",\n    \"table.content.no-fields.collection-type\": \"Adicione seu primeiro campo a este Tipo de Coleção\",\n    \"table.content.no-fields.component\": \"Adicione seu primeiro campo a este Componente\"\n};\n\nexport { configurations, ptBR as default, from };\n//# sourceMappingURL=pt-BR.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,OAAO;AAAA,EACP,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC;AAAA,EACA,gCAAgC;AAAA,EAChC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qCAAqC;AACzC;", "names": []}