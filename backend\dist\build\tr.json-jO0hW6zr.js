var e="Analizler",i="Doküman<PERSON>yon",n="E-posta",a="<PERSON><PERSON><PERSON>",t="Sağlayıcı",o="Şifre sıfırlama anahtarı",l="Rol",r="<PERSON>llanı<PERSON>ı Adı",s="<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",p="Haydaa! Bir şeyler ters gitti. Lütfen tekrar dene.",m="Temizle",u="Koyu",c="Açık",d="YA DA",g="İçeriğe atla",k="Gönder",y={Analytics:e,"Auth.components.Oops.text":"Hesabın donduruldu.","Auth.components.Oops.text.admin":"<PERSON><PERSON>ı olduğunu düşünüyorsanız lütfen yöneticinize ulaşın.","Auth.components.Oops.title":"Haydaa...","Auth.form.active.label":"Aktif","Auth.form.button.forgot-password":"E-posta gönder","Auth.form.button.go-home":"ANASAYFAYA GERİ DÖN","Auth.form.button.login":"Giriş","Auth.form.button.password-recovery":"Şifre Kurtarma","Auth.form.button.register":"Başlamaya hazır","Auth.form.email.label":"E-posta","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"Hesabınız yönetici tarafından engellendi.","Auth.form.error.code.provide":"Geçersiz sağlanmış kod.","Auth.form.error.confirmed":"Tanımladığınız e-posta onaylanmadı.","Auth.form.error.email.invalid":"E-postası geçersiz.","Auth.form.error.email.provide":"Lütfen kullanıcı adınızı veya e-postanızı belirtin.","Auth.form.error.email.taken":"E-posta zaten alınmış","Auth.form.error.invalid":"Kimlik veya şifre geçersiz.","Auth.form.error.params.provide":"Geçersiz sağlanmış kod parametresi.","Auth.form.error.password.format":"Şifreniz `$` sembolünü üç kezden fazla içeremez.","Auth.form.error.password.local":"Bu kullanıcı hiçbir bir şifre belirlemedi; hesap oluşturma sırasında kullanılan sağlayıcı aracılığıyla lütfen giriş yapınız..","Auth.form.error.password.matching":"Parolalar uyuşmuyor.","Auth.form.error.password.provide":"Lütfen şifrenizi girin.","Auth.form.error.ratelimit":"Çok fazla deneme var. Lütfen bir dakika sonra tekrar deneyin.","Auth.form.error.user.not-exist":"Bu e-posta bulunmamaktadır..","Auth.form.error.username.taken":"Kullanıcı adı zaten alınmış","Auth.form.firstname.label":"Adın","Auth.form.firstname.placeholder":"ör. Zeynep","Auth.form.forgot-password.email.label":"E-postanızı giriniz","Auth.form.forgot-password.email.label.success":"E-posta başarıyla gönderildi, ","Auth.form.lastname.label":"Soyadın","Auth.form.lastname.placeholder":"ör. Yılmaz","Auth.form.password.hide-password":"Şifreyi gizle","Auth.form.password.hint":"8 karakterden uzun olmalı ve en az 1 büyük harf, 1 küçük harf ve 1 sayı içermeli","Auth.form.password.show-password":"Şifreyi göster","Auth.form.register.news.label":"Beni gelecekteki özellikler ve geliştirmeler hakkında bilgilendir (bunu seçerek {terms} ve {policy}'leri kabul etmiş sayılırsınız)","Auth.form.register.subtitle":"Bilgiler yalnızca Strapi kimlik doğrulaması için kullanılacak. Tüm veriler sizin veritabanınızda saklanacak.","Auth.form.rememberMe.label":"Beni hatırla","Auth.form.username.label":"Kullanıcı Adı","Auth.form.username.placeholder":"Kai Doe","Auth.form.welcome.subtitle":"Strapi hesabına giriş yap","Auth.form.welcome.title":"Strapi'ye hoşgeldiniz!","Auth.link.forgot-password":"Parolanızı mı unuttunuz ?","Auth.link.ready":"Zaten kayıtlı mısınız?","Auth.link.signin":"Giriş yap","Auth.link.signin.account":"Hesabın var mı?","Auth.login.sso.divider":"Ya da bunlarla giriş yap","Auth.login.sso.loading":"Sağlayıcılar yükleniyor...","Auth.login.sso.subtitle":"Hesabına SSO ile giriş yap","Auth.privacy-policy-agreement.policy":"gizlilik sözleşmesi","Auth.privacy-policy-agreement.terms":"koşullar","Auth.reset-password.title":"Şifreni sıfırla","Content Manager":"İçerik Yönetimi","Content Type Builder":"İçerik-Tipi Kurucusu",Documentation:i,Email:n,"Files Upload":"Dosya yükleme","HomePage.head.title":"Anasayfa","HomePage.roadmap":"Yol haritamızı görüntüleyin","HomePage.welcome.congrats":"Tebrikler!","HomePage.welcome.congrats.content":"İlk yönetici olarak giriş yaptınız. Strapi'nin güçlü özelliklerini keşfetmek için,","HomePage.welcome.congrats.content.bold":"ilk İçerik-Tipi'ni yaratmanızı öneriyoruz.","Media Library":"Ortam Kütüphanesi","New entry":"Yeni kayıt",Password:a,Provider:t,ResetPasswordToken:o,Role:l,"Settings.PageTitle":"Ayarlar - {name}","Settings.tokens.Button.cancel":"İptal","Settings.tokens.Button.regenerate":"Yeniden üret","Settings.apiTokens.ListView.headers.createdAt":"Oluşturuldu","Settings.apiTokens.ListView.headers.description":"Tanım","Settings.apiTokens.ListView.headers.lastUsedAt":"En son kullanıldı","Settings.apiTokens.ListView.headers.name":"İsim","Settings.apiTokens.ListView.headers.type":"Token tipi","Settings.tokens.RegenerateDialog.title":"Tokenı yeniden üret.","Settings.apiTokens.addFirstToken":"İlk API Tokenınını ekle","Settings.apiTokens.addNewToken":"Yeni API Tokenı ekle","Settings.tokens.copy.editMessage":"Güvenlik sebebiyle, tokenı yalnızca bir kere görebilirsin.","Settings.tokens.copy.editTitle":"Bu tokena artık erişilemez.","Settings.tokens.copy.lastWarning":"Bu tokenı kopyalamayı unutma. Bir daha erişemeyeceksin!","Settings.apiTokens.create":"Yeni API Tokenı oluştur","Settings.apiTokens.createPage.permissions.description":"Sadece bir yol ile bağlanmış eylemler listelenmektedir.","Settings.apiTokens.createPage.permissions.title":"İzinler","Settings.apiTokens.description":"API'ı kullanmak için oluşturulmuş token listesi","Settings.tokens.duration.30-days":"30 gün","Settings.tokens.duration.7-days":"7 gün","Settings.tokens.duration.90-days":"90 gün","Settings.tokens.duration.expiration-date":"Sona erme tarihi","Settings.tokens.duration.unlimited":"Sınırsız","Settings.apiTokens.emptyStateLayout":"Henüz hiç içeriğin yok...","Settings.tokens.form.duration":"Token süresi","Settings.tokens.form.type":"Token tipi","Settings.tokens.notification.copied":"Token panoya kopyalandı.","Settings.tokens.popUpWarning.message":"Bu tokenı yeniden üretmek istediğinden emin misin?","Settings.apiTokens.title":"API Tokenları","Settings.tokens.types.full-access":"Tam yetki","Settings.tokens.types.read-only":"Salt-okunur","Settings.application.customization":"Özelleştirme","Settings.application.customization.carousel-hint":"Yönetim paneli logosunu değiştir. (Görsel boyutu sınırı: {dimension}x{dimension}, Dosya boyutu sınırı: {size}KB)","Settings.application.customization.carousel-slide.label":"Logo slaytı","Settings.application.customization.carousel.change-action":"Logoyu değiştir","Settings.application.customization.carousel.reset-action":"Logoyu sıfırla","Settings.application.customization.carousel.title":"Logo","Settings.application.customization.modal.cancel":"İptal","Settings.application.customization.modal.pending":"Bekleyen logo","Settings.application.customization.modal.pending.card-badge":"görsel","Settings.application.customization.modal.pending.choose-another":"Başka bir logo seç","Settings.application.customization.modal.pending.subtitle":"Yüklemeden önce seçilen logoyu yönet","Settings.application.customization.modal.pending.title":"Logo yüklemeye hazır","Settings.application.customization.modal.pending.upload":"Logo yükle","Settings.application.customization.modal.tab.label":"Dosyaları nasıl yüklemek istersin?","Settings.application.customization.modal.upload":"Logo yükle","Settings.application.customization.modal.upload.cta.browse":"Dosyalara gözat","Settings.application.customization.modal.upload.drag-drop":"Buraya sürükle bırak ya da","Settings.application.customization.modal.upload.error-format":"Desteklenmeyen biçim algılandı (desteklenen biçimler: jpeg, jpg, png, svg).","Settings.application.customization.modal.upload.error-network":"Ağ hatası","Settings.application.customization.modal.upload.error-size":"Yüklenen dosya çok büyük (Görsel boyutu sınırı: {dimension}x{dimension}, Dosya boyutu sınırı: {size}KB)","Settings.application.customization.modal.upload.file-validation":"Görsel boyutu sınırı: {dimension}x{dimension}, Dosya boyutu sınırı: {size}KB","Settings.application.customization.modal.upload.from-computer":"Bilgisayarımdan","Settings.application.customization.modal.upload.from-url":"URLden","Settings.application.customization.modal.upload.from-url.input-label":"URL","Settings.application.customization.modal.upload.next":"İleri","Settings.application.description":"Yönetim panelinin tüm bilgileri","Settings.application.edition-title":"mevcut sürüm","Settings.application.get-help":"Yardım al","Settings.application.link-pricing":"Tüm ücret planlarını gör","Settings.application.link-upgrade":"Admin panelini yükselt","Settings.application.node-version":"node versiyonu","Settings.application.strapi-version":"strapi versiyonu","Settings.application.strapiVersion":"strapi versiyonu","Settings.application.title":"Kuşbakışı","Settings.error":"Hata","Settings.global":"Genel Ayarlar","Settings.permissions":"Yönetim paneli","Settings.permissions.category":"{category} için izin ayarları","Settings.permissions.category.plugins":"{category} eklentisi için izin ayarları","Settings.permissions.conditions.anytime":"Her zaman","Settings.permissions.conditions.apply":"Uygula","Settings.permissions.conditions.can":"Yapabilir","Settings.permissions.conditions.conditions":"Koşullar","Settings.permissions.conditions.links":"Bağlantılar","Settings.permissions.conditions.no-actions":"Koşulları belirtmeden önce eylemleri (oluştur, oku, güncelle, ...) seçmelisin.","Settings.permissions.conditions.none-selected":"Her zaman","Settings.permissions.conditions.or":"YA DA","Settings.permissions.conditions.when":"Olduğunda","Settings.permissions.select-all-by-permission":"Tüm {label} izinlerini seç","Settings.permissions.select-by-permission":"{label} iznini seç","Settings.permissions.users.active":"Aktif","Settings.permissions.users.create":"Kullanıcı davet et","Settings.permissions.users.email":"E-Posta","Settings.permissions.users.firstname":"Adı","Settings.permissions.users.form.sso":"SSO ile bağlan","Settings.permissions.users.form.sso.description":"Açıldığında kullanıcılar SSO ile giriş yapabilir","Settings.permissions.users.inactive":"Pasif","Settings.permissions.users.lastname":"Soyadı","Settings.permissions.users.listview.header.subtitle":"Strapi yönetim paneline erişimi olan kullanıcılar","Settings.permissions.users.roles":"Roller","Settings.permissions.users.strapi-author":"Yazar","Settings.permissions.users.strapi-editor":"Editör","Settings.permissions.users.strapi-super-admin":"Süper Yönetici","Settings.permissions.users.tabs.label":"Sekme İzinleri","Settings.permissions.users.user-status":"Kullanıcı durumu","Settings.permissions.users.username":"Kullanıcı adı","Settings.profile.form.notify.data.loaded":"Profil verilerin yüklendi","Settings.profile.form.section.experience.clear.select":"Seçilmiş arayüz dilini temizle","Settings.profile.form.section.experience.here":"buradan","Settings.profile.form.section.experience.interfaceLanguage":"Arayüz dili","Settings.profile.form.section.experience.interfaceLanguage.hint":"Bu yalnızca senin arayüzünü seçilen dilde gösterecek.","Settings.profile.form.section.experience.interfaceLanguageHelp":"Tercih değişiklikleri yalnızca sana uygulanır. Daha fazla bilgi için {here}.","Settings.profile.form.section.experience.mode.hint":"Arayüzünü seçilen modda gösterir.","Settings.profile.form.section.experience.mode.label":"Arayüz modu","Settings.profile.form.section.experience.mode.option-label":"{name} modu","Settings.profile.form.section.experience.title":"Deneyim","Settings.profile.form.section.head.title":"Kullanıcı profili","Settings.profile.form.section.profile.page.title":"Profil sayfası","Settings.roles.create.description":"Role verilen hakları tanımla","Settings.roles.create.title":"Rol oluştur","Settings.roles.created":"Rol oluşturuldu","Settings.roles.edit.title":"Rolü düzenle","Settings.roles.form.button.users-with-role":"Bu rolde {number} kullanıcı","Settings.roles.form.created":"Oluşturuldu","Settings.roles.form.description":"Rolün adı ve tanımı","Settings.roles.form.permission.property-label":"{label} izinleri","Settings.roles.form.permissions.attributesPermissions":"Alanların izinleri","Settings.roles.form.permissions.create":"Oluştur","Settings.roles.form.permissions.delete":"Sil","Settings.roles.form.permissions.publish":"Yayınla","Settings.roles.form.permissions.read":"Oku","Settings.roles.form.permissions.update":"Güncelle","Settings.roles.list.button.add":"Yeni rol ekle","Settings.roles.list.description":"Rollerin listesi","Settings.roles.title.singular":"rol","Settings.sso.description":"Single Sign-On (SSO) özelliğini ayarla.","Settings.sso.form.registration.description":"SSO girişi sırasında hesabı olmayanlara yeni hesap oluştur","Settings.sso.form.registration.label":"Otomatik kayıt","Settings.sso.title":"Single Sign-On","Settings.webhooks.create":"Webhook oluştur","Settings.webhooks.create.header":"Yeni başlık yarat","Settings.webhooks.created":"Webhook oluşturuldu","Settings.webhooks.event.publish-tooltip":"Bu eylem yalnızca Taslak/Yayımla sistemi açık olduğunda vardır","Settings.webhooks.events.create":"Oluştur","Settings.webhooks.events.update":"Güncelle","Settings.webhooks.form.events":"Etkinlikler","Settings.webhooks.form.headers":"Başlıklar","Settings.webhooks.form.url":"Url","Settings.webhooks.headers.remove":"{number} başlık satırını kaldır","Settings.webhooks.key":"Anahtar","Settings.webhooks.list.button.add":"Yeni webhook ekle","Settings.webhooks.list.description":"POST değişiklikleri bildirimi al.","Settings.webhooks.list.empty.description":"İlkini bu listeye ekleyin.","Settings.webhooks.list.empty.link":"Dökümantasyonumuzu görüntüleyin","Settings.webhooks.list.empty.title":"Henüz bir webhook yok","Settings.webhooks.list.th.actions":"eylemler","Settings.webhooks.list.th.status":"durum","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooklar","Settings.webhooks.to.delete":"{webhooksToDeleteLength} dosya seçildi","Settings.webhooks.trigger":"Tetikleyici","Settings.webhooks.trigger.cancel":"Tetikleyiciyi iptal et","Settings.webhooks.trigger.pending":"Bekleniyor...","Settings.webhooks.trigger.save":"Lütfen tetikleyiciyi kaydedin","Settings.webhooks.trigger.success":"Başarılı!","Settings.webhooks.trigger.success.label":"Tetikleyici başarılı","Settings.webhooks.trigger.test":"Test-tetikleyici","Settings.webhooks.trigger.title":"Tetikleyiciden önce kaydet","Settings.webhooks.value":"Değer","Usecase.back-end":"Arkayüz Geliştiricisi","Usecase.button.skip":"Bu soruyu atla","Usecase.content-creator":"İçerik Üreticisi","Usecase.front-end":"Önyüz Geliştiricisi","Usecase.full-stack":"Tümyüz Geliştiricisi","Usecase.input.work-type":"Ne işle meşgulsun?","Usecase.notification.success.project-created":"Proje başarıyla oluşturuldu","Usecase.other":"Diğer","Usecase.title":"Biraz kendinden bahset",Username:r,Users:s,"Users & Permissions":"Kullanıcılar & İzinler","admin.pages.MarketPlacePage.filters.categories":"Kategoriler","admin.pages.MarketPlacePage.filters.categoriesSelected":"{count} kategori seçildi","admin.pages.MarketPlacePage.filters.collections":"Koleksiyonlar","admin.pages.MarketPlacePage.filters.collectionsSelected":"{count} koleksiyon seçildi","admin.pages.MarketPlacePage.head":"Pazaryeri - Eklentiler","admin.pages.MarketPlacePage.missingPlugin.description":"Bize aradığın eklentiyi anlat ki biz de eklenti geliştirici topluluğumuzdaki ilham arayanlara iletelim!","admin.pages.MarketPlacePage.missingPlugin.title":"Aradığın eklentiyi bulamadın mı?","admin.pages.MarketPlacePage.offline.subtitle":"Strapi Market'e erişmek için Internet'e bağlı olmalısın.","admin.pages.MarketPlacePage.offline.title":"Çevrimdışısın","admin.pages.MarketPlacePage.plugin.copy":"Yükleme komutunu kopyala","admin.pages.MarketPlacePage.plugin.copy.success":"Yükleme komutu terminaline yapıştırılmak için hazır","admin.pages.MarketPlacePage.plugin.downloads":"Bu eklenti haftada {downloadsCount} kez indirilmiş","admin.pages.MarketPlacePage.plugin.githubStars":"Bu eklenti Github'da {starsCount} yıldız almış","admin.pages.MarketPlacePage.plugin.info":"Daha fazla","admin.pages.MarketPlacePage.plugin.info.label":"{pluginName} hakkında daha fazla öğren","admin.pages.MarketPlacePage.plugin.info.text":"Daha","admin.pages.MarketPlacePage.plugin.installed":"Yüklendi","admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":"Strapi tarafından geliştirildi","admin.pages.MarketPlacePage.plugin.tooltip.verified":"Eklenti Strapi tarafından onaylandı","admin.pages.MarketPlacePage.plugin.version":`Strapi'yi "{strapiAppVersion}" versiyonundan "{versionRange}" versiyonuna yükselt`,"admin.pages.MarketPlacePage.plugin.version.null":'Yüklü olan "{strapiAppVersion}" Strapi versiyonu ile uyumluluğu doğrulanamıyor',"admin.pages.MarketPlacePage.plugins":"Eklentiler","admin.pages.MarketPlacePage.provider.downloads":"Bu sağlayıcı haftada {downloadsCount} kez indirilmiş","admin.pages.MarketPlacePage.provider.githubStars":"Bu sağlayıcı Github'da {starsCount} yıldız almış","admin.pages.MarketPlacePage.providers":"Sağlayıcılar","admin.pages.MarketPlacePage.search.clear":"Aramayı temizle","admin.pages.MarketPlacePage.search.empty":'"{target}" için sonuç yok',"admin.pages.MarketPlacePage.search.placeholder":"Arama","admin.pages.MarketPlacePage.sort.alphabetical":"Alfabetik sıraya diz","admin.pages.MarketPlacePage.sort.alphabetical.selected":"Alfabetik sıra","admin.pages.MarketPlacePage.sort.newest":"Yeniden eskiye diz","admin.pages.MarketPlacePage.sort.newest.selected":"Yeniden eskiye","admin.pages.MarketPlacePage.submit.plugin.link":"Eklenti gönder","admin.pages.MarketPlacePage.submit.provider.link":"Sağlayıcı gönder","admin.pages.MarketPlacePage.subtitle":"Strapi'den daha fazlasını al","admin.pages.MarketPlacePage.tab-group.label":"Strapi Eklenti ve Sağlayıcıları",anErrorOccurred:p,"app.component.CopyToClipboard.label":"Panoya kopyala","app.component.search.label":"{target} için arama yap","app.component.table.duplicate":"{target} kaydını yinele","app.component.table.edit":"{target} kaydını düzenle","app.component.table.select.one-entry":"{target} kaydını seç","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Strapi ve ekosistemi hakkındaki son haberleri oku.","app.components.BlockLink.code":"Kod örnekleri","app.components.BlockLink.documentation.content":"Başlıca konseptleri, rehberleri ve talimatları keşfet.","app.components.BlockLink.tutorial":"Eğitimler","app.components.BlockLink.tutorial.content":"Strapi'yi kullanmak ve özelleştirmek için adım adım talimatları takip et.","app.components.Button.cancel":"İptal","app.components.Button.confirm":"Onayla","app.components.Button.reset":"Sıfırla","app.components.ComingSoonPage.comingSoon":"Çok Yakında","app.components.ConfirmDialog.title":"Onay","app.components.DownloadInfo.download":"İndirme devam ediyor...","app.components.DownloadInfo.text":"Bu birkaç dakika sürebilir. Sabrınız için teşekkürler.","app.components.EmptyAttributes.title":"Alan henüz yok","app.components.EmptyStateLayout.content-document":"İçerik bulunamadı","app.components.EmptyStateLayout.content-permissions":"Bu içeriğe erişim yetkiniz yok.","app.components.GuidedTour.CM.create.content":"<p>Buradaki tüm içerikleri İçerik Yöneticisi ile oluştur ve yönet.</p><p>Ör: Blog websitesi örneğini bir adım daha öteye götürürsek, kişiler burada istedikleri gibi Makale yazabilir, kaydedip yayımlayabilir.</p><p>💡 Bir ipucu - Oluşturduğun içeriklerde yayınla butonuna basmayı unutma.</p>","app.components.GuidedTour.CM.create.title":"⚡️ İçerik oluştur","app.components.GuidedTour.CM.success.content":"<p>Müthiş! Son bir adım kaldı.</p><b>🚀 İçeriği çalışırken gör</b>","app.components.GuidedTour.CM.success.cta.title":"APIyi test et","app.components.GuidedTour.CM.success.title":"Adım 2: Tamamlandı ✅","app.components.GuidedTour.CTB.create.content":"<p>Koleksiyon tipleri birden çok girdiyi yönetmene yardımcı olur. Tekil tipler tek bir girdiyi yönetmek için uygundur.</p> <p>Ör: Bir blog sayfası için, Makaleler Koleksiyon tipinde olabilecekken, Ana Sayfa Tekil tipte olacaktır.</p>","app.components.GuidedTour.CTB.create.cta.title":"Bir Koleksiyon tipi kur","app.components.GuidedTour.CTB.create.title":"🧠 İlk Koleksiyon tipini oluştur","app.components.GuidedTour.CTB.success.content":"<p>İyi gidiyorsun!</p><b>⚡️ Dünya ile ne paylaşmak isterdin?</b>","app.components.GuidedTour.CTB.success.title":"Adım 1: Tamamlandı ✅","app.components.GuidedTour.apiTokens.create.content":"<p>Bir kimlik doğrulama tokenı üret ve yeni oluşturduğun içeriğe ulaş.</p>","app.components.GuidedTour.apiTokens.create.cta.title":"Bir API Token üret","app.components.GuidedTour.apiTokens.create.title":"🚀 İçeriği çalışırken gör","app.components.GuidedTour.apiTokens.success.content":"<p>Bir HTTP isteiği yaparak içeriği çalışırlen gör:</p><ul><li><p>URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>With the header: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>İçeriklerle etkileşimin farklı yöntemler için <documentationLink>dokümantasyonu</documentationLink> oku.</p>","app.components.GuidedTour.apiTokens.success.cta.title":"Ana sayfaya geri dön","app.components.GuidedTour.apiTokens.success.title":"Adım 3: Tamamlandı ✅","app.components.GuidedTour.create-content":"İçerik oluştur","app.components.GuidedTour.home.CM.title":"⚡️ Dünya ile ne paylaşmak isterdin?","app.components.GuidedTour.home.CTB.cta.title":"İçerik tipi kurucusuna git","app.components.GuidedTour.home.CTB.title":"🧠 İçerik yapısını kur","app.components.GuidedTour.home.apiTokens.cta.title":"APIyi test et","app.components.GuidedTour.skip":"Turu atla","app.components.GuidedTour.title":"Başlamak için 3 adım","app.components.HomePage.button.blog":"BLOG SAYFASINDA DAHA FAZLASINI GÖRÜN","app.components.HomePage.community":"Topluluğumuza ulaşın","app.components.HomePage.community.content":"Farklı kanallarda takım üyeleri, katkıda bulunanlar ve geliştiricilere ulaşın.","app.components.HomePage.create":"İlk içerik tipini oluştur","app.components.HomePage.welcome":"Panele hoşgeldiniz.","app.components.HomePage.welcome.again":"Hoşgeldiniz ","app.components.HomePage.welcomeBlock.content":"Sizi topluluk üyelerinden biri olarak görmekten mutluyuz. Sürekli olarak geri bildirim alabilmemiz için bize doğrudan mesaj göndermeye çekinmeyin ","app.components.HomePage.welcomeBlock.content.again":"Projenizde ilerleme kaydedeceğinizi umuyoruz... Strapi ile ilgili en yeni yenilikleri okumaktan çekinmeyin. Ürünü geri bildirimlerinize göre geliştirmek için elimizden geleni yapıyoruz.","app.components.HomePage.welcomeBlock.content.issues":"sorunlar","app.components.HomePage.welcomeBlock.content.raise":" yada yükselt ","app.components.ImgPreview.hint":"Dosyanızı bu alana sürükleyip bırakın ya da bir dosya yüklemek için {browse}","app.components.ImgPreview.hint.browse":"gözat","app.components.InputFile.newFile":"Yeni dosya ekle","app.components.InputFileDetails.open":"Yeni sekmede aç","app.components.InputFileDetails.originalName":"Orjinal isim:","app.components.InputFileDetails.remove":"Bu dosyayı sil","app.components.InputFileDetails.size":"Boyut:","app.components.InstallPluginPage.Download.description":"Eklentiyi indirmek ve yüklemek bir kaç saniye sürebilir.","app.components.InstallPluginPage.Download.title":"İndiriliyor...","app.components.InstallPluginPage.description":"Uygulamanızı rahatlıkla genişletin.","app.components.LeftMenu.collapse":"Menüyü ufalt","app.components.LeftMenu.expand":"Menüyü büyüt","app.components.LeftMenu.general":"Genel","app.components.LeftMenu.logo.alt":"Uygulama logosu","app.components.LeftMenu.logout":"Çıkış","app.components.LeftMenu.navbrand.title":"Strapi Panosu","app.components.LeftMenu.navbrand.workplace":"İş Yeri","app.components.LeftMenu.plugins":"Eklentiler","app.components.LeftMenu.trialCountdown":"Sizin deneme süreniz {date} günü sona erecektir.","app.components.LeftMenuFooter.help":"Yardım","app.components.LeftMenuFooter.poweredBy":"Gururla sunar ","app.components.LeftMenuLinkContainer.collectionTypes":"Koleksiyon Tipleri","app.components.LeftMenuLinkContainer.configuration":"Yapılandırma","app.components.LeftMenuLinkContainer.general":"Genel","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Yüklenen eklenti bulunmamaktadır.","app.components.LeftMenuLinkContainer.plugins":"Eklentiler","app.components.LeftMenuLinkContainer.singleTypes":"Tekil Tipler","app.components.ListPluginsPage.deletePlugin.description":"Eklentiyi kaldırmak bir kaç saniye alabilir.","app.components.ListPluginsPage.deletePlugin.title":"Kaldırılıyor","app.components.ListPluginsPage.description":"Projedeki yüklenen eklentiler.","app.components.ListPluginsPage.head.title":"Eklenti Listesi","app.components.Logout.logout":"Çıkış Yap","app.components.Logout.profile":"Profil","app.components.MarketplaceBanner":"Strapi Awesome'da projeni hayata geçirmek için harika şeyleri ve topluluk tarafından geliştirilmiş eklentileri keşfet.","app.components.MarketplaceBanner.image.alt":"bir strapi roket logosu","app.components.MarketplaceBanner.link":"Şimdi gözden geçir","app.components.NotFoundPage.back":"Anasayfaya geri dön","app.components.NotFoundPage.description":"Bulunamadı","app.components.Official":"Resmi","app.components.Onboarding.help.button":"Yardım butonu","app.components.Onboarding.label.completed":"% tamamlandı","app.components.Onboarding.title":"Başlangıç Videolaro","app.components.PluginCard.Button.label.download":"İndir","app.components.PluginCard.Button.label.install":"Zaten yüklenmiş","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"autoReload özelliği aktif edilmeli. Lütfen uygulamayı `yarn develop` ile başlatın.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Anladım!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Güvenlik nedeniyle bir eklenti yalnızca geliştirme ortamında indirilebilir.","app.components.PluginCard.PopUpWarning.install.impossible.title":"İndirme imkansız","app.components.PluginCard.compatible":"Uygulamanızla uyumlu","app.components.PluginCard.compatibleCommunity":"Toplulukla uyumlu","app.components.PluginCard.more-details":"Daha fazla detay","app.components.ToggleCheckbox.off-label":"Yanlış","app.components.ToggleCheckbox.on-label":"Doğru","app.components.Users.ModalCreateBody.block-title.roles.description":"Bir kullanıcı bir ya da daha fazla role sahip olabilir","app.components.listPlugins.button":"Yeni eklenti ekle","app.components.listPlugins.title.none":"Yüklenen eklenti bulunmamaktadır.","app.components.listPluginsPage.deletePlugin.error":"Eklenti kaldırılırken bir hata oluştu","app.containers.App.notification.error.init":"API isteği sırasında bir hata oluştu","app.links.configure-view":"Ekranı düzenle","app.page.not.found":"Haydaa! Aradığın sayfayı bulamıyor gibiyiz...","app.static.links.cheatsheet":"ÖzetYardım","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Filtre ekle","app.utils.close-label":"Kapat","app.utils.defaultMessage":" ","app.utils.delete":"Sil","app.utils.duplicate":"Yinele","app.utils.edit":"Düzenle","app.utils.errors.file-too-big.message":"Dosya çok büyük","app.utils.filter-value":"Değeri filtrele","app.utils.filters":"Filtreler","app.utils.notify.data-loaded":"{target} yüklendi","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Yayınla","app.utils.select-all":"Tümünü seç","app.utils.select-field":"Alanı seç","app.utils.select-filter":"Filtreyi seç","app.utils.unpublish":"Yayından Kaldır",clearLabel:m,"coming.soon":"Bu içerik şuanda düzenleniyor. Bir kaç hafta sonra yayında olacak!","component.Input.error.validation.integer":"Değer sayı olmalı","components.AutoReloadBlocker.description":"Strapi'yi aşağıdaki komutlardan biri ile çalıştırın:","components.AutoReloadBlocker.header":"Bu eklenti için tekrar yükleme özelliği gerekiyor.","components.ErrorBoundary.title":"Bir şeyler yanlış gitti...","components.FilterOptions.FILTER_TYPES.$contains":"içerir","components.FilterOptions.FILTER_TYPES.$containsi":"içerir (büyük/küçük harfe duyarsız)","components.FilterOptions.FILTER_TYPES.$endsWith":"ile biter","components.FilterOptions.FILTER_TYPES.$endsWithi":"ile biter (büyük/küçük harfe duyarsız)","components.FilterOptions.FILTER_TYPES.$eq":"eşittir","components.FilterOptions.FILTER_TYPES.$eqi":"eşittir (büyük/küçük harfe duyarsız)","components.FilterOptions.FILTER_TYPES.$gt":"büyüktür","components.FilterOptions.FILTER_TYPES.$gte":"büyük eşittir","components.FilterOptions.FILTER_TYPES.$lt":"küçüktür","components.FilterOptions.FILTER_TYPES.$lte":"küçük eşittir","components.FilterOptions.FILTER_TYPES.$ne":"eşit değildir","components.FilterOptions.FILTER_TYPES.$nei":"eşit değildir (büyük/küçük harfe duyarsız)","components.FilterOptions.FILTER_TYPES.$notContains":"içermez","components.FilterOptions.FILTER_TYPES.$notContainsi":"içermez (büyük/küçük harfe duyarsız)","components.FilterOptions.FILTER_TYPES.$notNull":"null değildir","components.FilterOptions.FILTER_TYPES.$null":"null'dur","components.FilterOptions.FILTER_TYPES.$startsWith":"başlar","components.FilterOptions.FILTER_TYPES.$startsWithi":"başlar (büyük/küçük harfe duyarsız)","components.Input.error.attribute.key.taken":"Bu değer zaten var.","components.Input.error.attribute.sameKeyAndName":"Eşit olamaz","components.Input.error.attribute.taken":"Bu alan ismi zaten var.","components.Input.error.contain.lowercase":"Şifre en az bir küçük harf içermelidir","components.Input.error.contain.number":"Şifre en az bir sayı içermelidir","components.Input.error.contain.uppercase":"Şifre en az bir büyük harf içermelidir","components.Input.error.contentTypeName.taken":"Bu isim zaten var.","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"Şifreler uyuşmuyor","components.Input.error.validation.email":"Geçersiz e-posta adresi.","components.Input.error.validation.json":"Bu JSON biçimi ile eşleşmiyor","components.Input.error.validation.lowercase":"Değerin tamamı küçük harf olmalıdır","components.Input.error.validation.max":"Değer çok yüksek {max}.","components.Input.error.validation.maxLength":"Değer çok uzun {max}.","components.Input.error.validation.min":"Değer çok az {min}.","components.Input.error.validation.minLength":"Değer çok kısa {min}.","components.Input.error.validation.minSupMax":"Üstü olamaz","components.Input.error.validation.regex":"Regex ile eşleşmiyor.","components.Input.error.validation.required":"Zorunlu alandır.","components.Input.error.validation.unique":"Değer zaten kullanılmış.","components.InputSelect.option.placeholder":"Buradan seçin","components.ListRow.empty":"Gösterilecek veri bulunmamaktadır.","components.NotAllowedInput.text":"Bu alanı görmek için yetkin yok","components.OverlayBlocker.description":"Sunucunun yeniden başlatılması gereken bir özellik kullanıyorsunuz. Lütfen sunucu çalışana kadar bekleyin.","components.OverlayBlocker.description.serverError":"Sunucu yeniden başlatılmalı, lütfen terminal üzerinden logları kontrol edin.","components.OverlayBlocker.title":"Yeniden başlatılmayı bekliyor...","components.OverlayBlocker.title.serverError":"Yeniden başlatma beklendiğinden uzun sürüyor","components.PageFooter.select":"sayfa başına kayıt","components.ProductionBlocker.description":"Güvenlik nedeniyle, bu eklentiyi diğer ortamlarda devre dışı bırakmamız gerekir.","components.ProductionBlocker.header":"Bu eklenti yalnızca geliştirme aşamasında mevcuttur.","components.Search.placeholder":"Arama...","components.TableHeader.sort":"Şuna göre diz: {label}","components.Wysiwyg.ToggleMode.markdown-mode":"Markdown modu","components.Wysiwyg.ToggleMode.preview-mode":"Önizleme modu","components.Wysiwyg.collapse":"Daralt","components.Wysiwyg.selectOptions.H1":"H1 başlık","components.Wysiwyg.selectOptions.H2":"H2 başlık","components.Wysiwyg.selectOptions.H3":"H3 başlık","components.Wysiwyg.selectOptions.H4":"H4 başlık","components.Wysiwyg.selectOptions.H5":"H5 başlık","components.Wysiwyg.selectOptions.H6":"H6 başlık","components.Wysiwyg.selectOptions.title":"Başlık ekle","components.WysiwygBottomControls.charactersIndicators":"karakter","components.WysiwygBottomControls.fullscreen":"Genişlet","components.WysiwygBottomControls.uploadFiles":"Dosyanızı bu alana sürükleyip bırakın ya da bir dosya yüklemek için {browse}","components.WysiwygBottomControls.uploadFiles.browse":"Bunları seç","components.pagination.go-to":"{page} nolu sayfaya git","components.pagination.go-to-next":"Sonraki sayfaya git","components.pagination.go-to-previous":"Önceki sayfaya git","components.pagination.remaining-links":"Ve {number} diğer bağlantı","components.popUpWarning.button.cancel":"Hayır, iptal et","components.popUpWarning.button.confirm":"Evet, onayla","components.popUpWarning.message":"Bunu silmek istediğinizden emin misiniz?","components.popUpWarning.title":"Lütfen onaylayın",dark:u,"form.button.continue":"Devam","form.button.done":"Tamam","global.actions":"Eylemler","global.back":"Geri","global.cancel":"İptal","global.change-password":"Şifreyi değiştir","global.content-manager":"İçerik Yöneticisi","global.continue":"Devam","global.delete":"Sil","global.delete-target":"Sil: {target}","global.description":"Tanım","global.details":"Detaylar","global.disabled":"Devredışı","global.documentation":"Dokümantasyon","global.enabled":"Etkin","global.finish":"Bitir","global.marketplace":"Pazaryeri","global.name":"İsim","global.none":"Hiçbiri","global.password":"Şifre","global.plugins":"Eklentiler","global.plugins.content-manager":"İçerik Yöneticisi","global.plugins.content-manager.description":"Veritabanındaki verileri görüntüleme, düzenleme ve silmenin kolay yolu.","global.plugins.content-type-builder":"İçerik Tipi Kurucusu","global.plugins.content-type-builder.description":"APInin veri yapısını modelle. Sadece bir iki dakikada yeni alanlar ve ilişkiler oluştur. Projendeki dosyalar otomatik olarak oluşturulur ve güncellenir.","global.plugins.documentation":"Dokümantasyon","global.plugins.documentation.description":"Bir OpenAPI Dokümanı oluştur ve SWAGGER UI ile APIni görselleştir.","global.plugins.email":"E-Posta","global.plugins.email.description":"Uygulamanı e-posta gönderecek şekilde ayarla.","global.plugins.graphql":"GraphQL","global.plugins.graphql.description":"Varsayılan API metodları ile bir GraphQL uç noktası ekler.","global.plugins.i18n":"Uluslararasılaştırma","global.plugins.i18n.description":"Bu eklenti, hem Yönetim paneli hem de API üzerinden, farklı dillerdeki içeriği oluşturma, okuma ve güncelleme imkanı sağlar.","global.plugins.sentry":"Sentry","global.plugins.sentry.description":"Strapi hata olaylarını Sentry'e ilet.","global.plugins.upload":"Ortam Kütüphanesi","global.plugins.upload.description":"Ortam dosyaları yönetimi.","global.plugins.users-permissions":"Roller ve İzinler","global.plugins.users-permissions.description":"Servisinizi JWT'ye dayalı tam bir kimlik doğrulama işlemi ile koruyun. Bu eklenti, kullanıcı grupları arasındaki izinleri yönetmenize izin veren bir ACL stratejisiyle de gelir.","global.profile":"Profil","global.prompt.unsaved":"Bu sayfadan ayrılmak istediğinize emin misiniz? Tüm düzenlemeleriniz kaybolacak","global.reset-password":"Şifreni sıfırla","global.roles":"Roller","global.save":"Kaydet","global.search":"Arama","global.see-more":"Daha fazla","global.select":"Seç","global.select-all-entries":"Tüm girdileri seç","global.settings":"Ayarlar","global.type":"Tip","global.users":"Kullanıcılar",light:c,"notification.contentType.relations.conflict":"İçerik tipinde çakışan ilişkiler var","notification.default.title":"Bilgi:","notification.error":"Bir hata oluştu","notification.error.layout":"Düzen alınamadı","notification.form.error.fields":"Form birden fazla hata içeriyor","notification.form.success.fields":"Değişiklikler kaydedildi","notification.link-copied":"Bağlantı panoya kopyalandı","notification.permission.not-allowed-read":"Bu dokümanı görme yetkin yok","notification.success.delete":"Öğe silindi","notification.success.saved":"Kaydedildi","notification.success.title":"Başarılı:","notification.success.apitokencreated":"API Token başarıyla oluşturuldu","notification.success.apitokenedited":"API Token başarıyla düzenlendi","notification.version.update.message":"Strapi'nin yeni versiyonu çıktı!","notification.warning.404":"404 - Bulunamadı","notification.warning.title":"Dikkat:",or:d,"request.error.model.unknown":"Bu model bulunmamaktadır.",skipToContent:g,submit:k};export{e as Analytics,i as Documentation,n as Email,a as Password,t as Provider,o as ResetPasswordToken,l as Role,r as Username,s as Users,p as anErrorOccurred,m as clearLabel,u as dark,y as default,c as light,d as or,g as skipToContent,k as submit};
