import{aL as L,j as e,P as d,a as C,w as V,z as v,V as D,b as N,bv as F,v as R,r as o,aC as _,B as f,bw as A,at as h,a3 as x,av as y,as as w,s as j}from"./strapi-z7ApxZZq.js";import{u as B,T as U}from"./Table-CPnHPqLc.js";import{u as H,a as z}from"./apiTokens-BDLby-vQ.js";import{A as r}from"./constants-Q2dfXdfa.js";const G=[{name:"name",label:{id:"Settings.apiTokens.ListView.headers.name",defaultMessage:"Name"},sortable:!0},{name:"description",label:{id:"Settings.apiTokens.ListView.headers.description",defaultMessage:"Description"},sortable:!1},{name:"createdAt",label:{id:"Settings.apiTokens.ListView.headers.createdAt",defaultMessage:"Created at"},sortable:!1},{name:"lastUsedAt",label:{id:"Settings.apiTokens.ListView.headers.lastUsedAt",defaultMessage:"Last used"},sortable:!1}],O=()=>{const{formatMessage:s}=C(),{toggleNotification:a}=V(),S=L(t=>t.admin_app.permissions.settings?.["api-tokens"]),{allowedActions:{canRead:u,canCreate:c,canDelete:b,canUpdate:P}}=v(S),p=D(),{trackUsage:i}=N(),m=F("ListView",t=>t.startSection),{_unstableFormatAPIError:l}=R();o.useEffect(()=>{m("apiTokens")},[m]),o.useEffect(()=>{p({search:_.stringify({sort:"name:ASC"},{encode:!1})})},[p]);const I=G.map(t=>({...t,label:s(t.label)}));B(()=>{i("willAccessTokenList",{tokenType:r})});const{data:n=[],isLoading:k,error:g}=H();o.useEffect(()=>{g&&a({type:"danger",message:l(g)})},[g,l,a]),o.useEffect(()=>{i("didAccessTokenList",{number:n.length,tokenType:r})},[n,i]);const[E]=z(),M=async t=>{try{const T=await E(t);if("error"in T){a({type:"danger",message:l(T.error)});return}i("didDeleteToken")}catch{a({type:"danger",message:s({id:"notification.error",defaultMessage:"Something went wrong"})})}};return e.jsxs(e.Fragment,{children:[e.jsx(d.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"API Tokens"})}),e.jsx(f.Header,{title:s({id:"Settings.apiTokens.title",defaultMessage:"API Tokens"}),subtitle:s({id:"Settings.apiTokens.description",defaultMessage:"List of generated tokens to consume the API"}),primaryAction:c&&e.jsx(A.apiTokens.CreateAnAPIToken,{children:e.jsx(h,{tag:x,"data-testid":"create-api-token-button",startIcon:e.jsx(y,{}),size:"S",onClick:()=>i("willAddTokenFromList",{tokenType:r}),to:"/settings/api-tokens/create",children:s({id:"Settings.apiTokens.create",defaultMessage:"Create new API Token"})})})}),u?e.jsx(d.Main,{"aria-busy":k,children:e.jsx(A.apiTokens.Introduction,{children:e.jsxs(f.Content,{children:[n.length>0&&e.jsx(U,{permissions:{canRead:u,canDelete:b,canUpdate:P},headers:I,isLoading:k,onConfirmDelete:M,tokens:n,tokenType:r}),c&&n.length===0?e.jsx(w,{icon:e.jsx(j,{width:"16rem"}),content:s({id:"Settings.apiTokens.addFirstToken",defaultMessage:"Add your first API Token"}),action:e.jsx(h,{tag:x,variant:"secondary",startIcon:e.jsx(y,{}),to:"/settings/api-tokens/create",children:s({id:"Settings.apiTokens.addNewToken",defaultMessage:"Add new API Token"})})}):null,!c&&n.length===0?e.jsx(w,{icon:e.jsx(j,{width:"16rem"}),content:s({id:"Settings.apiTokens.emptyStateLayout",defaultMessage:"You don’t have any content yet..."})}):null]})})}):e.jsx(d.NoPermissions,{})]})},Q=()=>{const s=L(a=>a.admin_app.permissions.settings?.["api-tokens"].main);return e.jsx(d.Protect,{permissions:s,children:e.jsx(O,{})})};export{O as ListView,Q as ProtectedListView};
