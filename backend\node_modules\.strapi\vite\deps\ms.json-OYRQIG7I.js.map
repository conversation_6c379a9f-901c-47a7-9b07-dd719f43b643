{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/ms.json.mjs"], "sourcesContent": ["var configurations = \"konfigurasi\";\nvar from = \"dari\";\nvar ms = {\n    \"attribute.boolean\": \"Boolean\",\n    \"attribute.boolean.description\": \"Ya atau tidak, 1 atau 0, benar atau salah\",\n    \"attribute.component\": \"Komponen\",\n    \"attribute.component.description\": \"<PERSON><PERSON> yang boleh diulang atau digunakan semula\",\n    \"attribute.date\": \"Tarikh\",\n    \"attribute.date.description\": \"Pemilih tarikh dengan jam, minit dan saat\",\n    \"attribute.datetime\": \"Masa tarikh\",\n    \"attribute.dynamiczone\": \"Zon dinamik\",\n    \"attribute.dynamiczone.description\": \"Memilih komponen secara dinamik semasa mengedit kandungan\",\n    \"attribute.email\": \"E-mel\",\n    \"attribute.email.description\": \"Ruang e-mel dengan pengesahan format\",\n    \"attribute.enumeration\": \"Pilihan\",\n    \"attribute.enumeration.description\": \"Senarai untuk dipilih\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"Data dalam format JSON\",\n    \"attribute.media\": \"Media\",\n    \"attribute.media.description\": \"Fail seperti gambar, video, dll\",\n    \"attribute.number\": \"Nombor\",\n    \"attribute.number.description\": \"Nombor (nombor bulat, apungan, perpuluhan)\",\n    \"attribute.password\": \"Kata Laluan\",\n    \"attribute.password.description\": \"Ruang kata laluan dengan penyulitan\",\n    \"attribute.relation\": \"Perhubung\",\n    \"attribute.relation.description\": \"Menghubungkan Jenis Koleksi\",\n    \"attribute.richtext\": \"Teks beraneka\",\n    \"attribute.richtext.description\": \"Penyunting teks beraneka dengan pilihan format\",\n    \"attribute.text\": \"Teks\",\n    \"attribute.text.description\": \"Teks kecil atau panjang seperti tajuk atau penerangan\",\n    \"attribute.time\": \"Masa\",\n    \"attribute.timestamp\": \"Cap waktu\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Pengecam unik\",\n    \"button.attributes.add.another\": \"Tambah ruang baru\",\n    \"button.component.add\": \"Tambah komponen\",\n    \"button.component.create\": \"Tambah komponen\",\n    \"button.model.create\": \"Tambah jenis koleksi\",\n    \"button.single-types.create\": \"Tambah jenis tunggal\",\n    \"component.repeatable\": \"(boleh diulang)\",\n    \"components.componentSelect.no-component-available\": \"Anda telah tambah semua komponen anda\",\n    \"components.componentSelect.no-component-available.with-search\": \"Tidak ada komponen dijumpai dengan carian anda\",\n    \"components.componentSelect.value-component\": \"{number} komponen dipilih (taip untuk mencari komponen)\",\n    \"components.componentSelect.value-components\": \"{number} komponen dipilih\",\n    configurations: configurations,\n    \"contentType.collectionName.description\": \"Berguna apabila nama Jenis Kandungan dan nama jadual anda berbeza\",\n    \"contentType.collectionName.label\": \"Nama koleksi\",\n    \"contentType.displayName.label\": \"Nama paparan\",\n    \"contentType.kind.change.warning\": \"Anda baru sahaja menukar sesuatu jenis kandungan: API akan diset semula (laluan, pengawal, dan perkhidmatan akan ditulis semula) .\",\n    \"error.attributeName.reserved-name\": \"Nama ini tidak boleh digunakan dalam jenis kandungan anda kerana ia boleh merosakkan fungsi lain\",\n    \"error.contentTypeName.reserved-name\": \"Nama ini tidak boleh digunakan dalam projek anda kerana ia boleh merosakkan fungsi lain\",\n    \"error.validation.enum-duplicate\": \"dua data yang sama tidak dibenarkan\",\n    \"error.validation.minSupMax\": \"Tidak boleh lebih dari maksimum\",\n    \"error.validation.regex\": \"Corak regex tidak sah\",\n    \"error.validation.relation.targetAttribute-taken\": \"Nama ini telah wujud dalam sasaran\",\n    \"form.attribute.component.option.add\": \"Tambah komponen\",\n    \"form.attribute.component.option.create\": \"Cip komponen baru\",\n    \"form.attribute.component.option.create.description\": \"Komponen dikongsi antara pelbagai jenis dan komponen, ia akan tersedia dan dapat diakses di mana sahaja .\",\n    \"form.attribute.component.option.repeatable\": \"Komponen yang boleh diulang\",\n    \"form.attribute.component.option.repeatable.description\": \"Sesuai untuk pelbagai pengganti (array) ramuan, tag meta, dan lain-lain ..\",\n    \"form.attribute.component.option.reuse-existing\": \"Gunakan komponen yang ada\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Gunakan semula komponen yang telah dibuat untuk memastikan data anda tetap konsisten di semua jenis kandungan\",\n    \"form.attribute.component.option.single\": \"Komponen tunggal\",\n    \"form.attribute.component.option.single.description\": \"Sesuai untuk ruang kumpulan seperti alamat penuh, maklumat utama dan lain-lain ...\",\n    \"form.attribute.item.customColumnName\": \"Gubah nama lajur\",\n    \"form.attribute.item.customColumnName.description\": \"Ini berguna untuk menamakan semula nama lajur didalam pangkalan data dengan format yang lebih komprehensif untuk respons API\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Nama ruang\",\n    \"form.attribute.item.enumeration.graphql\": \"Ganti nama untuk GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Membolehkan anda menggantikan nama yang dihasilkan secara lalai untuk GraphQL\",\n    \"form.attribute.item.enumeration.placeholder\": \"Cth: \\nmalam \\nnoon \\nevening\",\n    \"form.attribute.item.enumeration.rules\": \"Pilihan (satu baris setiap pilihan)\",\n    \"form.attribute.item.maximum\": \"Nilai maksimum\",\n    \"form.attribute.item.maximumLength\": \"Panjang maksimum\",\n    \"form.attribute.item.minimum\": \"Nilai minimum\",\n    \"form.attribute.item.minimumLength\": \"Panjang minimum\",\n    \"form.attribute.item.number.type\": \"Format nombor\",\n    \"form.attribute.item.number.type.biginteger\": \"nombor bulat besar (cth: 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"perpuluhan (cth: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"apungan (cth: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"integer (cth: 10)\",\n    \"form.attribute.item.privateField\": \"Ruang bersifat persendirian\",\n    \"form.attribute.item.privateField.description\": \"Ruang ini tidak akan muncul dalam respons API\",\n    \"form.attribute.item.requiredField\": \"Ruang yang wajib\",\n    \"form.attribute.item.requiredField.description\": \"Anda tidak akan dapat membuat entri jika ruang ini kosong\",\n    \"form.attribute.item.text.regex\": \"Corak RegExp\",\n    \"form.attribute.item.text.regex.description\": \"Teks regular expression\",\n    \"form.attribute.item.uniqueField\": \"Ruang yang unik\",\n    \"form.attribute.item.uniqueField.description\": \"Anda tidak boleh buat entri jika ada entri yang sama dengan yang kandungan\",\n    \"form.attribute.media.allowed-types\": \"Pilih jenis media yang dibenarkan\",\n    \"form.attribute.media.allowed-types.option-files\": \"Fail\",\n    \"form.attribute.media.allowed-types.option-images\": \"Gambar\",\n    \"form.attribute.media.allowed-types.option-videos\": \"Video\",\n    \"form.attribute.media.option.multiple\": \"Pelbagai media\",\n    \"form.attribute.media.option.multiple.description\": \"Sesuai untuk slaid, karusel atau muat turun fail yang banyak\",\n    \"form.attribute.media.option.single\": \"Media tunggal\",\n    \"form.attribute.media.option.single.description\": \"Sesuai untuk avatar, gambar profil atau sampul\",\n    \"form.attribute.settings.default\": \"Isi yang asal\",\n    \"form.attribute.text.option.long-text\": \"Teks panjang\",\n    \"form.attribute.text.option.long-text.description\": \"Sesuai untuk penerangan, biografi. Pencarian yang tepat dinyahaktifkan.\",\n    \"form.attribute.text.option.short-text\": \"Teks pendek\",\n    \"form.attribute.text.option.short-text.description\": \"Sesuai untuk tajuk, nama, pautan (URL). Ia juga membenarkan carian yang tepat .\",\n    \"form.button.add-components-to-dynamiczone\": \"Tambah komponen ke zon\",\n    \"form.button.add-field\": \"Tambah ruang lain\",\n    \"form.button.add-first-field-to-created-component\": \"Tambah ruang pertama ke komponen\",\n    \"form.button.add.field.to.collectionType\": \"Tambah ruang lain ke jenis koleksi ini\",\n    \"form.button.add.field.to.component\": \"Tambah ruang lain ke komponen ini\",\n    \"form.button.add.field.to.contentType\": \"Tambah ruang lain ke jenis kandungan ini\",\n    \"form.button.add.field.to.singleType\": \"Tambah ruang lain untuk jenis tunggal ini\",\n    \"form.button.cancel\": \"Batal\",\n    \"form.button.collection-type.description\": \"Sesuai untuk data yang banyak seperti artikel, produk, komen dan lain-lain\",\n    \"form.button.configure-component\": \"Tetapkan komponen\",\n    \"form.button.configure-view\": \"Susun paparan\",\n    \"form.button.select-component\": \"Pilih komponen\",\n    \"form.button.single-type.description\": \"Sesuai untuk data tunggal seperti mengenai kami, laman utama dan lain-lain\",\n    from: from,\n    \"modalForm.attribute.form.base.name.description\": \"Tidak boleh ada jarak dalam nama\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"cth. slug, urlSeo, urlCanonical\",\n    \"modalForm.attribute.target-field\": \"Ruang terpasang\",\n    \"modalForm.attributes.select-component\": \"Pilih komponen\",\n    \"modalForm.attributes.select-components\": \"Pilih komponen\",\n    \"modalForm.component.header-create\": \"Cipta komponen\",\n    \"modalForm.components.create-component.category.label\": \"Pilih kategori atau masukkan nama untuk buat yang baru\",\n    \"modalForm.components.icon.label\": \"Ikon\",\n    \"modalForm.editCategory.base.name.description\": \"Tidak boleh ada jarak dalam nama kategori\",\n    \"modalForm.header-edit\": \"Edit {name}\",\n    \"modalForm.header.categories\": \"Kategori\",\n    \"modalForm.header.back\": \"belakang\",\n    \"modalForm.singleType.header-create\": \"Cipta jenis tunggal\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Tambah komponen baru ke zon dinamik\",\n    \"modalForm.sub-header.attribute.create\": \"Tambah {type} baru\",\n    \"modalForm.sub-header.attribute.create.step\": \"Tambah komponen baru ({step} / 2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Edit {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Pilih ruang untuk jenis koleksi anda\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Pilih ruang untuk komponen anda\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Pilih ruang untuk jenis tunggal anda\",\n    \"modelPage.attribute.relation-polymorphic\": \"Perhubung (polimorfik)\",\n    \"modelPage.attribute.relationWith\": \"Terhubung dengan\",\n    \"notification.info.autoreaload-disable\": \"Ciri autoReload diperlukan untuk menggunakan plugin ini. Mulakan pelayan anda dengan `strapi Develop`\",\n    \"notification.info.creating.notSaved\": \"Sila simpan kerja anda sebelum tambah jenis koleksi atau komponen baru\",\n    \"plugin.description.long\": \"Memodelkan struktur data API anda. Buat ruang dan hubungan baru hanya dalam satu minit. Fail dibuat dan dikemas kini secara automatik dalam projek anda .\",\n    \"plugin.description.short\": \"Memodelkan struktur data API anda .\",\n    \"popUpForm.navContainer.advanced\": \"Tetapan lanjut\",\n    \"popUpForm.navContainer.base\": \"Tetapan asas\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"Adakah anda pasti mahu membatalkan pengubahsuaian anda ?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Adakah anda pasti mahu membatalkan pengubahsuaian anda? Beberapa komponen telah ditambah atau diubah suai ...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"Adakah anda pasti mahu memadamkan kategori ini? Semua komponen juga akan dihapuskan .\",\n    \"popUpWarning.bodyMessage.component.delete\": \"Adakah anda pasti mahu memadamkan komponen ini ?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Adakah anda pasti mahu memadamkan jenis koleksi ini ?\",\n    \"prompt.unsaved\": \"Adakah anda pasti mahu keluar? Semua pengubahsuaian anda akan hilang .\",\n    \"relation.attributeName.placeholder\": \"Cth: pengarang, kategori, teg\",\n    \"relation.manyToMany\": \"mempunyai dan dimiliki oleh banyak\",\n    \"relation.manyToOne\": \"ada banyak\",\n    \"relation.manyWay\": \"ada banyak\",\n    \"relation.oneToMany\": \"dimiliki oleh banyak\",\n    \"relation.oneToOne\": \"mempunyai dan dimiliki oleh satu\",\n    \"relation.oneWay\": \"mempunyai satu\"\n};\n\nexport { configurations, ms as default, from };\n//# sourceMappingURL=ms.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC;AAAA,EACA,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACvB;", "names": []}