import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/translations/ms.json.mjs
var Analytics = "Analisis";
var Documentation = "Dokumen";
var Email = "Email";
var Password = "Kata Laluan";
var Provider = "Penyedia";
var ResetPasswordToken = "Token penetapan semula kata laluan";
var Role = "Peranan";
var Username = "Nama Penggguna";
var Users = "Para Pengguna";
var ms = {
  Analytics,
  "Auth.form.button.forgot-password": "Hantar e-mel",
  "Auth.form.button.login": "Log masuk",
  "Auth.form.button.register": "Sedia untuk mulakan",
  "Auth.form.error.blocked": "Akaun anda telah disekat oleh pengelola.",
  "Auth.form.error.code.provide": "Kod yang salah terlah diberikan.",
  "Auth.form.error.confirmed": "E-mel akaun anda tidak disahkan lagi.",
  "Auth.form.error.email.invalid": "E-mel ini tidak sah.",
  "Auth.form.error.email.provide": "Sila berikan nama pengguna atau e-mel anda.",
  "Auth.form.error.email.taken": "E-mel sudah diambil.",
  "Auth.form.error.invalid": "Username/Email atau kata laluan tidak tepat.",
  "Auth.form.error.params.provide": "Parameter yang tidak tepat telah diberikan",
  "Auth.form.error.password.format": "Kata laluan anda tidak boleh mengandungi simbol `$` lebih dari tiga kali.",
  "Auth.form.error.password.local": "Pengguna ini tidak pernah menetapkan kata laluan local, sila log masuk melalui provider yang digunakan sewaktu pembuatan akaun.",
  "Auth.form.error.password.matching": "Kata laluan tidak sepadan.",
  "Auth.form.error.password.provide": "Sila isikan kata laluan anda.",
  "Auth.form.error.ratelimit": "Terlalu banyak percubaan, sila cuba sebentar lagi.",
  "Auth.form.error.user.not-exist": "E-mel ini tidak wujud.",
  "Auth.form.error.username.taken": "Nama pengguna sudah diambil.",
  "Auth.form.forgot-password.email.label": "Masukkan emel anda",
  "Auth.form.forgot-password.email.label.success": "E-mel berjaya dihantar ke",
  "Auth.form.register.news.label": "Ikuti perkembangan terkini mengenai ciri baru dan penambahbaikan yang akan datang (dengan melakukan ini, anda menerima {terms} dan {policy}).",
  "Auth.link.forgot-password": "Lupa kata laluan anda ?",
  "Auth.link.ready": "Sedia untuk log masuk?",
  "Auth.privacy-policy-agreement.policy": "dasar privasi",
  "Auth.privacy-policy-agreement.terms": "syarat",
  "Content Manager": "Pengurus Kandungan",
  "Content Type Builder": "Pembina Jenis Kandungan",
  Documentation,
  Email,
  "Files Upload": "Fail Muat Naik",
  "HomePage.head.title": "Halaman Utama",
  "HomePage.roadmap": "Lihat jadual kerja kami",
  "HomePage.welcome.congrats": "Tahniah!",
  "HomePage.welcome.congrats.content": "Anda telah masuk sebagai pengelola yang pertama. Untuk meneroka ciri yang bagus disediakan oleh Strapi,",
  "HomePage.welcome.congrats.content.bold": "kami mengesyorkan anda untuk buat Jenis Koleksi yang pertama.",
  "New entry": "Entri baru",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "Peranan & Keizinan",
  "Settings.error": "Ralat",
  "Settings.global": "Tetapan global",
  "Settings.webhooks.create": "Buat satu webhook",
  "Settings.webhooks.create.header": "Buat header baru",
  "Settings.webhooks.created": "Webhook telah dibuat",
  "Settings.webhooks.events.create": "Cipta",
  "Settings.webhooks.form.events": "Sewaktu",
  "Settings.webhooks.form.headers": "Tajuk",
  "Settings.webhooks.form.url": "Url",
  "Settings.webhooks.key": "Kata Kunci",
  "Settings.webhooks.list.button.add": "Tambah webhook baru",
  "Settings.webhooks.list.description": "Dapatkan pemberitahuan perubahan untuk POST.",
  "Settings.webhooks.list.empty.description": "Tambah satu dalam senarai.",
  "Settings.webhooks.list.empty.link": "Lihat dokumen kami",
  "Settings.webhooks.list.empty.title": "Belum ada webhook",
  "Settings.webhooks.singular": "webhook",
  "Settings.webhooks.title": "Webhooks",
  "Settings.webhooks.trigger": "Cetus",
  "Settings.webhooks.trigger.cancel": "Batalkan pencetusan",
  "Settings.webhooks.trigger.pending": "Belum selesai…",
  "Settings.webhooks.trigger.save": "Sila simpan untuk cetuskan",
  "Settings.webhooks.trigger.success": "Berjaya!",
  "Settings.webhooks.trigger.success.label": "Cetusan berjaya",
  "Settings.webhooks.trigger.test": "Uji Cetusan",
  "Settings.webhooks.trigger.title": "Simpan sebelum cetus",
  "Settings.webhooks.value": "Kandungan",
  Username,
  Users,
  "Users & Permissions": "Para Pengguna & Keizinan",
  "app.components.BlockLink.code": "Contoh Kod",
  "app.components.Button.cancel": "Batal",
  "app.components.Button.reset": "Set Semula",
  "app.components.ComingSoonPage.comingSoon": "Akan Datang",
  "app.components.DownloadInfo.download": "Muat turun sedang dijalankan...",
  "app.components.DownloadInfo.text": "Ini akan mengambil masa, terima kasih atas kesabaran anda.",
  "app.components.EmptyAttributes.title": "Tiada ruang disini",
  "app.components.HomePage.button.blog": "LIHAT LEBIH LAGI DI BLOG",
  "app.components.HomePage.community": "Cari komuniti di web",
  "app.components.HomePage.community.content": "Bincang dengan ahli kumpulan, penyumbang dan pembangun di saluran berbeza.",
  "app.components.HomePage.create": "Cipta Jenis Kandungan anda",
  "app.components.HomePage.welcome": "Selamat datang!",
  "app.components.HomePage.welcome.again": "Selamat datang ",
  "app.components.HomePage.welcomeBlock.content": "Kami mengalu-alukan kedatangan anda di komuniti. Kami sentiasa mencari penambahbaikan, jadi jangan segan silu untuk mesej kami di ",
  "app.components.HomePage.welcomeBlock.content.again": "Kami harap anda membuat progress pada projek anda... Luangkan masa untuk membaca berita baru kami tentang strapi. Kami memberikan yang terbaik untuk menambah baik produk ini berdasarkan maklum balas anda.",
  "app.components.HomePage.welcomeBlock.content.issues": "isu-isu.",
  "app.components.HomePage.welcomeBlock.content.raise": " atau berikan ",
  "app.components.ImgPreview.hint": "Tarik & Lepas fail anda kedalam kawasan ini atau {browse} fail untuk muat naik",
  "app.components.ImgPreview.hint.browse": "pilih fail",
  "app.components.InputFile.newFile": "Tambah fail baru",
  "app.components.InputFileDetails.open": "Buka di tab baru",
  "app.components.InputFileDetails.originalName": "Nama asal:",
  "app.components.InputFileDetails.remove": "Buang fail ini",
  "app.components.InputFileDetails.size": "Saiz:",
  "app.components.InstallPluginPage.Download.description": "Ia mungkin mengambil beberapa saat untuk memuat turun dan memasang plugin.",
  "app.components.InstallPluginPage.Download.title": "Memuat Turun...",
  "app.components.InstallPluginPage.description": "Kembangkan aplikasi anda dengan mudah.",
  "app.components.LeftMenuFooter.help": "Bantuan",
  "app.components.LeftMenuFooter.poweredBy": "Dikuasakan oleh ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "Jenis Koleksi",
  "app.components.LeftMenuLinkContainer.configuration": "Konfigurasi",
  "app.components.LeftMenuLinkContainer.general": "Umum",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "Belum ada plugin yang dipasang",
  "app.components.LeftMenuLinkContainer.plugins": "Plugin-plugin",
  "app.components.LeftMenuLinkContainer.singleTypes": "Jenis Tunggal",
  "app.components.ListPluginsPage.description": "Senarai plugin yang dipasang didalam projek ini.",
  "app.components.ListPluginsPage.head.title": "Senarai plugin",
  "app.components.Logout.logout": "Log Keluar",
  "app.components.Logout.profile": "Profil",
  "app.components.NotFoundPage.back": "Kembali ke laman utama",
  "app.components.NotFoundPage.description": "Tidak dijumpai",
  "app.components.Official": "Rasmi",
  "app.components.Onboarding.label.completed": "% siap",
  "app.components.Onboarding.title": "Video-video untuk bermula",
  "app.components.PluginCard.Button.label.download": "Muat turun",
  "app.components.PluginCard.Button.label.install": "Sudah dipasang",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "Ciri AutoReload perlu diaktifkan. Sila mulakkan aplikasi anda dengan `yarn develop`.",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "Saya Faham!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "Untuk tujuan keselamatan, plugin hanya boleh di muat turun dalam environment 'development'.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "Memuat turun adalah mustahil",
  "app.components.PluginCard.compatible": "Serasi dengan applikasi anda",
  "app.components.PluginCard.compatibleCommunity": "Serasi dengan komuniti",
  "app.components.PluginCard.more-details": "Butiran selanjutnya",
  "app.components.listPlugins.button": "Tambah Plugin Baru",
  "app.components.listPlugins.title.none": "Tiada plugin dipasang",
  "app.components.listPluginsPage.deletePlugin.error": "Satu ralat muncul ketika membuang plugin tersebut",
  "app.containers.App.notification.error.init": "Ralat berlaku semasa permintaan API",
  "app.links.configure-view": "Susun paparan",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.defaultMessage": " ",
  "app.utils.filters": "Tapisan",
  "app.utils.placeholder.defaultMessage": " ",
  "component.Input.error.validation.integer": "Nilainya haruslah dalam integer",
  "components.AutoReloadBlocker.description": "Jalankan Strapi dengan salah satu arahan berikut:",
  "components.AutoReloadBlocker.header": "ciri Reload diperlukan untuk plugin ini.",
  "components.ErrorBoundary.title": "Ada sesuatu yang tidak kena...",
  "components.Input.error.attribute.key.taken": "Nilai ini sudah wujud",
  "components.Input.error.attribute.sameKeyAndName": "Tak boleh sama",
  "components.Input.error.attribute.taken": "Nama kotak ini sudah wujud",
  "components.Input.error.contentTypeName.taken": "Nama ini sudah wujud",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "Kata laluan tidak sepadan",
  "components.Input.error.validation.email": "Ini bukan email",
  "components.Input.error.validation.json": "Ini tak sepadan dengan format JSON",
  "components.Input.error.validation.max": "Nilai isinya terlalu tinggi {max}.",
  "components.Input.error.validation.maxLength": "Panjang isinya terlalu panjang {max}.",
  "components.Input.error.validation.min": "Nilai isinya terlalu rendah {min}.",
  "components.Input.error.validation.minLength": "Panjang isinya terlalu pendek {min}.",
  "components.Input.error.validation.minSupMax": "Tidak boleh lebih tinggi",
  "components.Input.error.validation.regex": "Nilai isinya tidak sepadan dengan regex.",
  "components.Input.error.validation.required": "Nilai ini adalah wajib.",
  "components.Input.error.validation.unique": "Nilai ini telah digunakan.",
  "components.InputSelect.option.placeholder": "Pilih disini",
  "components.ListRow.empty": "Tiada data untuk pamirkan.",
  "components.OverlayBlocker.description": "Anda menggunakan ciri yang memerlukan pelayan untuk dimulakan semula. Sila tunggu sehinggal pelayan habis.",
  "components.OverlayBlocker.description.serverError": "Pelayan sepatutnya telah dimulakan semula, sila periksa log anda di terminal.",
  "components.OverlayBlocker.title": "Menunggu untuk dimulakan semula...",
  "components.OverlayBlocker.title.serverError": "Permulaan semula mengambil masa lebih lama daripada yang dijangkakan",
  "components.PageFooter.select": "entri dipaparkan setiap halaman",
  "components.ProductionBlocker.description": "Untuk tujuan keslamatan kami perlu menyahkan plugin didalam environment lain.",
  "components.ProductionBlocker.header": "Plugin in hanya tersedia dalam pembangunan(development).",
  "components.Search.placeholder": "Cari...",
  "components.Wysiwyg.collapse": "Tutup",
  "components.Wysiwyg.selectOptions.H1": "Tajuk H1",
  "components.Wysiwyg.selectOptions.H2": "Tajuk H2",
  "components.Wysiwyg.selectOptions.H3": "Tajuk H3",
  "components.Wysiwyg.selectOptions.H4": "Tajuk H4",
  "components.Wysiwyg.selectOptions.H5": "Tajuk H5",
  "components.Wysiwyg.selectOptions.H6": "Tajuk H6",
  "components.Wysiwyg.selectOptions.title": "Tambah tajuk",
  "components.WysiwygBottomControls.charactersIndicators": "aksara",
  "components.WysiwygBottomControls.fullscreen": "Besarkan",
  "components.WysiwygBottomControls.uploadFiles": "Tarik & Lepas fail, tampal dari clipboard atau {browse}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "pilih",
  "components.popUpWarning.message": "Anda yakin untuk memadam?",
  "components.popUpWarning.title": "Sila sahkan",
  "form.button.done": "Siap",
  "global.prompt.unsaved": "Adakah anda pasti untuk meninggalkan halaman ini? Segala perubahan anda akan hilang",
  "notification.contentType.relations.conflict": "Jenis kandungan ada hubungan(relations) yang saling bertentangan",
  "notification.error": "satu ralat muncul",
  "notification.error.layout": "Tidak dapat kesan susunan atur",
  "notification.form.error.fields": "Borang mengandungi beberapa kesalahan",
  "notification.form.success.fields": "Perubahan disimpan",
  "notification.success.delete": "Item telah dipadamkan",
  "request.error.model.unknown": "model ini tidak wujud"
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  ms as default
};
//# sourceMappingURL=ms.json-3Z4426VX.js.map
