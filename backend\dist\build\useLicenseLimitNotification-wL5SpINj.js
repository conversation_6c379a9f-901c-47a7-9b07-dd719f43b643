import{a as g,bC as d,w as p,aA as L,r as I}from"./strapi-z7ApxZZq.js";import{i as E}from"./isNil-HHOJ63lS.js";const u="strapi-notification-seat-limit",S="https://strapi.io/billing/request-seats",_="https://strapi.io/billing/manage-seats",w=()=>{const{formatMessage:e}=g(),{license:o,isError:n,isLoading:r}=d(),{toggleNotification:l}=p(),{pathname:i}=L(),{enforcementUserCount:c,permittedSeats:s,licenseLimitStatus:t,type:a}=o??{};I.useEffect(()=>{if(n||r)return;const f=!E(s)&&!window.sessionStorage.getItem(`${u}-${i}`)&&t==="OVER_LIMIT";let m;t==="OVER_LIMIT"&&(m="danger"),f&&l({type:m,message:e({id:"notification.ee.warning.over-.message",defaultMessage:"Add seats to {licenseLimitStatus, select, OVER_LIMIT {invite} other {re-enable}} Users. If you already did it but it's not reflected in Strapi yet, make sure to restart your app."},{licenseLimitStatus:t}),title:e({id:"notification.ee.warning.at-seat-limit.title",defaultMessage:"{licenseLimitStatus, select, OVER_LIMIT {Over} other {At}} seat limit ({enforcementUserCount}/{permittedSeats})"},{licenseLimitStatus:t,enforcementUserCount:c,permittedSeats:s}),link:{url:a==="gold"?S:_,label:e({id:"notification.ee.warning.seat-limit.link",defaultMessage:a==="gold"?"Contact sales":"Manage seats"})},blockTransition:!0,onClose(){window.sessionStorage.setItem(`${u}-${i}`,"true")}})},[l,o,i,e,r,s,t,c,n,a])};export{w as u};
