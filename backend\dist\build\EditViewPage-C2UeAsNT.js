import{bx as le,j as e,cL as de,a as _,$ as A,e as E,T as y,H as x,k as G,eo as v,fK as ue,bp as V,cg as pe,fL as ge,aP as me,by as he,aQ as D,aR as B,fM as fe,bL as xe,d8 as ye,fN as Te,bG as ke,d9 as Ae,bV as Ee,aL as Y,P as w,w as be,aA as Ie,r as m,b as je,bv as Ce,z as Se,aw as Pe,v as _e,V as ve,be as Re,bf as Le,B as Oe,aT as q}from"./strapi-z7ApxZZq.js";import{b as we,c as Me,d as Ne}from"./apiTokens-BDLby-vQ.js";import{A as P}from"./constants-Q2dfXdfa.js";import{a as $e,b as Ue,L as De,c as Be,F as Ge,U as Fe,T as He}from"./TokenTypeSelect-CcKLfmXH.js";import{t as Ve,m as qe}from"./tail-ClM4A4bE.js";import"./transferTokens-DDbhemls.js";import"./index-C3HeomYJ.js";import"./index-BRVyLNfZ.js";import"./_baseMap-BaaWdrf-.js";import"./_baseEach-ZnnftuGj.js";const Ke=le.injectEndpoints({endpoints:t=>({getPermissions:t.query({query:()=>"/admin/content-api/permissions",transformResponse:s=>s.data}),getRoutes:t.query({query:()=>"/admin/content-api/routes",transformResponse:s=>s.data})}),overrideExisting:!1}),{useGetPermissionsQuery:Qe,useGetRoutesQuery:We}=Ke,[Ye,ze]=de("ApiTokenPermissionsContext"),Je=({children:t,...s})=>e.jsx(Ye,{...s,children:t}),F=()=>ze("useApiTokenPermissions"),Xe=({errors:t={},onChange:s,canEditInputs:a,isCreating:i,values:n={},apiToken:u={},onDispatch:c,setHasChangedPermissions:h})=>{const{formatMessage:r}=_(),p=({target:{value:l}})=>{h(!1),l==="full-access"&&c({type:"SELECT_ALL_ACTIONS"}),l==="read-only"&&c({type:"ON_CHANGE_READ_ONLY"})},b=[{value:"read-only",label:{id:"Settings.tokens.types.read-only",defaultMessage:"Read-only"}},{value:"full-access",label:{id:"Settings.tokens.types.full-access",defaultMessage:"Full access"}},{value:"custom",label:{id:"Settings.tokens.types.custom",defaultMessage:"Custom"}}];return e.jsx(A,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:e.jsxs(E,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(y,{variant:"delta",tag:"h2",children:r({id:"global.details",defaultMessage:"Details"})}),e.jsxs(x.Root,{gap:5,children:[e.jsx(x.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx($e,{error:t.name,value:n.name,canEditInputs:a,onChange:s})},"name"),e.jsx(x.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(Ue,{error:t.description,value:n.description,canEditInputs:a,onChange:s})},"description"),e.jsx(x.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(De,{isCreating:i,error:t.lifespan,value:n.lifespan,onChange:s,token:u})},"lifespan"),e.jsx(x.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(Be,{value:n.type,error:t.type,label:{id:"Settings.tokens.form.type",defaultMessage:"Token type"},onChange:l=>{p({target:{value:l}}),s({target:{name:"type",value:l}})},options:b,canEditInputs:a})},"type")]})]})})},Ze=t=>{switch(t){case"POST":return{text:"success600",border:"success200",background:"success100"};case"GET":return{text:"secondary600",border:"secondary200",background:"secondary100"};case"PUT":return{text:"warning600",border:"warning200",background:"warning100"};case"DELETE":return{text:"danger600",border:"danger200",background:"danger100"};default:return{text:"neutral600",border:"neutral200",background:"neutral100"}}},es=G(A)`
  margin: -1px;
  border-radius: ${({theme:t})=>t.spaces[1]} 0 0 ${({theme:t})=>t.spaces[1]};
`,ss=({route:t={handler:"Nocontroller.error",method:"GET",path:"/there-is-no-path"}})=>{const{formatMessage:s}=_(),{method:a,handler:i,path:n}=t,u=n?Ve(n.split("/")):[],[c="",h=""]=i?i.split("."):[],r=Ze(t.method);return e.jsxs(E,{direction:"column",alignItems:"stretch",gap:2,children:[e.jsxs(y,{variant:"delta",tag:"h3",children:[s({id:"Settings.apiTokens.createPage.BoundRoute.title",defaultMessage:"Bound route to"})," ",e.jsx("span",{children:c}),e.jsxs(y,{variant:"delta",textColor:"primary600",children:[".",h]})]}),e.jsxs(E,{hasRadius:!0,background:"neutral0",borderColor:"neutral200",gap:0,children:[e.jsx(es,{background:r.background,borderColor:r.border,padding:2,children:e.jsx(y,{fontWeight:"bold",textColor:r.text,children:a})}),e.jsx(A,{paddingLeft:2,paddingRight:2,children:qe(u,p=>e.jsxs(y,{textColor:p.includes(":")?"neutral600":"neutral900",children:["/",p]},p))})]})]})},ts=()=>{const{value:{selectedAction:t,routes:s}}=F(),{formatMessage:a}=_(),i=t?.split(".")[0];return e.jsx(x.Item,{col:5,background:"neutral150",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,style:{minHeight:"100%"},direction:"column",alignItems:"stretch",children:t?e.jsx(E,{direction:"column",alignItems:"stretch",gap:2,children:i&&i in s&&s[i].map(n=>n.config.auth?.scope?.includes(t)||n.handler===t?e.jsx(ss,{route:n},n.handler):null)}):e.jsxs(E,{direction:"column",alignItems:"stretch",gap:2,children:[e.jsx(y,{variant:"delta",tag:"h3",children:a({id:"Settings.apiTokens.createPage.permissions.header.title",defaultMessage:"Advanced settings"})}),e.jsx(y,{tag:"p",textColor:"neutral600",children:a({id:"Settings.apiTokens.createPage.permissions.header.hint",defaultMessage:"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route"})})]})})},K=ge`
  background: ${t=>t.theme.colors.primary100};

  #cog {
    opacity: 1;
  }
`,ns=G(A)`
  display: flex;
  justify-content: space-between;
  align-items: center;

  #cog {
    opacity: 0;
    path {
      fill: ${t=>t.theme.colors.primary600};
    }
  }

  /* Show active style both on hover and when the action is selected */
  ${t=>t.$isActive&&K}
  &:hover {
    ${K}
  }
`,as=G.div`
  flex: 1;
  align-self: center;
  border-top: 1px solid ${({theme:t})=>t.colors.neutral150};
`,is=({controllers:t=[],label:s,orderNumber:a=0,disabled:i=!1})=>{const{value:{onChangeSelectAll:n,onChange:u,selectedActions:c,setSelectedAction:h,selectedAction:r}}=F(),{formatMessage:p}=_(),b=l=>l===r;return e.jsxs(v.Item,{value:`${s}-${a}`,children:[e.jsx(v.Header,{variant:a%2?"primary":"secondary",children:e.jsx(v.Trigger,{children:ue(s)})}),e.jsx(v.Content,{children:t?.map(l=>{const R=l.actions.every(g=>c.includes(g.actionId)),M=l.actions.some(g=>c.includes(g.actionId));return e.jsxs(A,{children:[e.jsxs(E,{justifyContent:"space-between",alignItems:"center",padding:4,children:[e.jsx(A,{paddingRight:4,children:e.jsx(y,{variant:"sigma",textColor:"neutral600",children:l?.controller})}),e.jsx(as,{}),e.jsx(A,{paddingLeft:4,children:e.jsx(V,{checked:!R&&M?"indeterminate":R,onCheckedChange:()=>{n({target:{value:[...l.actions]}})},disabled:i,children:p({id:"app.utils.select-all",defaultMessage:"Select all"})})})]}),e.jsx(x.Root,{gap:4,padding:4,children:l?.actions&&l?.actions.map(g=>e.jsx(x.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(ns,{$isActive:b(g.actionId),padding:2,hasRadius:!0,children:[e.jsx(V,{checked:c.includes(g.actionId),name:g.actionId,onCheckedChange:()=>{u({target:{value:g.actionId}})},disabled:i,children:e.jsx("span",{style:{overflowWrap:"anywhere"},children:g.action})}),e.jsx("button",{type:"button","data-testid":"action-cog",onClick:()=>h({target:{value:g.actionId}}),style:{display:"inline-flex",alignItems:"center"},children:e.jsx(pe,{id:"cog"})})]})},g.actionId))})]},`${s}.${l?.controller}`)})})]})},os=({section:t=null,...s})=>e.jsx(A,{children:e.jsx(v.Root,{size:"M",children:t&&t.map((a,i)=>e.jsx(is,{label:a.label,controllers:a.controllers,orderNumber:i,...s},a.apiId))})}),rs=({...t})=>{const{value:{data:s}}=F(),{formatMessage:a}=_();return e.jsxs(x.Root,{gap:0,shadow:"filterShadow",hasRadius:!0,background:"neutral0",children:[e.jsxs(x.Item,{col:7,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,direction:"column",alignItems:"stretch",gap:6,children:[e.jsxs(E,{direction:"column",alignItems:"stretch",gap:2,children:[e.jsx(y,{variant:"delta",tag:"h2",children:a({id:"Settings.apiTokens.createPage.permissions.title",defaultMessage:"Permissions"})}),e.jsx(y,{tag:"p",textColor:"neutral600",children:a({id:"Settings.apiTokens.createPage.permissions.description",defaultMessage:"Only actions bound by a route are listed below."})})]}),s?.permissions&&e.jsx(os,{section:s?.permissions,...t})]}),e.jsx(ts,{})]})},cs=me().shape({name:D().max(100).required(B.required.id),type:D().oneOf(["read-only","full-access","custom"]).required(B.required.id),description:D().nullable(),lifespan:he().integer().min(0).nullable().defined(B.required.id)});function ls(t,s,a,i){for(var n=a-1,u=t.length;++n<u;)if(i(t[n],s))return n;return-1}var ds=ls,us=xe,ps=Te,gs=ds,ms=ye,hs=fe,fs=Array.prototype,Q=fs.splice;function xs(t,s,a,i){var n=i?gs:ps,u=-1,c=s.length,h=t;for(t===s&&(s=hs(s)),a&&(h=us(t,ms(a)));++u<c;)for(var r=0,p=s[u],b=a?a(p):p;(r=n(h,b,r,i))>-1;)h!==t&&Q.call(h,r,1),Q.call(t,r,1);return t}var ys=xs,Ts=ys;function ks(t,s){return t&&t.length&&s&&s.length?Ts(t,s):t}var As=ks,Es=Ae,bs=As,Is=Es(bs),js=Is;const W=ke(js),Cs=t=>{const s={allActionsIds:[],permissions:[]};return s.permissions=Object.entries(t).map(([a,i])=>({apiId:a,label:a.split("::")[1],controllers:Object.keys(i.controllers).map(n=>({controller:n,actions:n in i.controllers?i.controllers[n].map(u=>{const c=`${a}.${n}.${u}`;return a.includes("api::")&&s.allActionsIds.push(c),{action:u,actionId:c}}).flat():[]})).flat()})),s},Ss={data:{allActionsIds:[],permissions:[]},routes:{},selectedAction:"",selectedActions:[]},Ps=(t,s)=>Ee(t,a=>{switch(s.type){case"ON_CHANGE":{a.selectedActions.includes(s.value)?W(a.selectedActions,s.value):a.selectedActions.push(s.value);break}case"SELECT_ALL_IN_PERMISSION":{s.value.every(n=>a.selectedActions.includes(n.actionId))?s.value.forEach(n=>{W(a.selectedActions,n.actionId)}):s.value.forEach(n=>{a.selectedActions.push(n.actionId)});break}case"SELECT_ALL_ACTIONS":{a.selectedActions=[...a.data.allActionsIds];break}case"ON_CHANGE_READ_ONLY":{const i=a.data.allActionsIds.filter(n=>n.includes("find")||n.includes("findOne"));a.selectedActions=[...i];break}case"UPDATE_PERMISSIONS_LAYOUT":{a.data=Cs(s.value);break}case"UPDATE_ROUTES":{a.routes={...s.value};break}case"UPDATE_PERMISSIONS":{a.selectedActions=[...s.value];break}case"SET_SELECTED_ACTION":{a.selectedAction=s.value;break}default:return a}}),_s=()=>{const{formatMessage:t}=_(),{toggleNotification:s}=be(),{state:a}=Ie(),i=Y(o=>o.admin_app.permissions),[n,u]=m.useState(a?.apiToken?.accessKey?{...a.apiToken}:null),[c,h]=m.useState(!!a?.apiToken?.accessKey),r=m.useRef(null),{trackUsage:p}=je(),b=Ce("EditView",o=>o.setCurrentStep),{allowedActions:{canCreate:l,canUpdate:R,canRegenerate:M}}=Se(i.settings?.["api-tokens"]),[g,f]=m.useReducer(Ps,Ss),L=Pe("/settings/api-tokens/:id")?.params?.id,T=L==="create",{_unstableFormatAPIError:k,_unstableFormatValidationErrors:H}=_e(),z=ve(),j=Qe(),C=We();m.useEffect(()=>{j.error&&s({type:"danger",message:k(j.error)})},[j.error,k,s]),m.useEffect(()=>{C.error&&s({type:"danger",message:k(C.error)})},[C.error,k,s]),m.useEffect(()=>{j.data&&f({type:"UPDATE_PERMISSIONS_LAYOUT",value:j.data})},[j.data]),m.useEffect(()=>{C.data&&f({type:"UPDATE_ROUTES",value:C.data})},[C.data]),m.useEffect(()=>{n&&(n.type==="read-only"&&f({type:"ON_CHANGE_READ_ONLY"}),n.type==="full-access"&&f({type:"SELECT_ALL_ACTIONS"}),n.type==="custom"&&f({type:"UPDATE_PERMISSIONS",value:n?.permissions}))},[n]),m.useEffect(()=>{p(T?"didAddTokenFromList":"didEditTokenFromList",{tokenType:P})},[T,p]);const{data:I,error:N,isLoading:J}=we(L,{skip:!L||T||!!n});m.useEffect(()=>{N&&s({type:"danger",message:k(N)})},[N,k,s]),m.useEffect(()=>{I&&(u(I),I.type==="read-only"&&f({type:"ON_CHANGE_READ_ONLY"}),I.type==="full-access"&&f({type:"SELECT_ALL_ACTIONS"}),I.type==="custom"&&f({type:"UPDATE_PERMISSIONS",value:I?.permissions}))},[I]),m.useEffect(()=>{if(c)return r.current=setTimeout(()=>{h(!1)},3e4),()=>{r.current&&(clearTimeout(r.current),r.current=null)}},[c]);const[X]=Me(),[Z]=Ne(),ee=async(o,S)=>{p(T?"willCreateToken":"willEditToken",{tokenType:P});try{if(T){const d=await X({...o,lifespan:o?.lifespan&&o.lifespan!=="0"?parseInt(o.lifespan.toString(),10):null,permissions:o.type==="custom"?g.selectedActions:null});if("error"in d){q(d.error)&&d.error.name==="ValidationError"?S.setErrors(H(d.error)):s({type:"danger",message:k(d.error)});return}s({type:"success",message:t({id:"notification.success.apitokencreated",defaultMessage:"API Token successfully created"})}),p("didCreateToken",{type:d.data.type,tokenType:P}),z(`../api-tokens/${d.data.id.toString()}`,{state:{apiToken:d.data},replace:!0}),b("apiTokens.success")}else{const d=await Z({id:L,name:o.name,description:o.description,type:o.type,permissions:o.type==="custom"?g.selectedActions:null});if("error"in d){q(d.error)&&d.error.name==="ValidationError"?S.setErrors(H(d.error)):s({type:"danger",message:k(d.error)});return}s({type:"success",message:t({id:"notification.success.apitokenedited",defaultMessage:"API Token successfully edited"})}),p("didEditToken",{type:d.data.type,tokenType:P})}}catch{s({type:"danger",message:t({id:"notification.error",defaultMessage:"Something went wrong"})})}},[se,$]=m.useState(!1),te=({target:{value:o}})=>{$(!0),f({type:"ON_CHANGE",value:o})},ne=({target:{value:o}})=>{$(!0),f({type:"SELECT_ALL_IN_PERMISSION",value:o})},ae=({target:{value:o}})=>{f({type:"SET_SELECTED_ACTION",value:o})},ie=()=>{h(o=>!o),r.current&&(clearTimeout(r.current),r.current=null)},oe={...g,onChange:te,onChangeSelectAll:ne,setSelectedAction:ae},U=R&&!T||l&&T,re=!!n?.accessKey;return J?e.jsx(w.Loading,{}):e.jsx(Je,{value:oe,children:e.jsxs(w.Main,{children:[e.jsx(w.Title,{children:t({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"API Tokens"})}),e.jsx(Re,{validationSchema:cs,validateOnChange:!1,initialValues:{name:n?.name||"",description:n?.description||"",type:n?.type,lifespan:n?.lifespan},enableReinitialize:!0,onSubmit:(o,S)=>ee(o,S),children:({errors:o,handleChange:S,isSubmitting:d,values:O,setFieldValue:ce})=>(se&&O?.type!=="custom"&&ce("type","custom"),e.jsxs(Le,{children:[e.jsx(Ge,{title:{id:"Settings.apiTokens.createPage.title",defaultMessage:"Create API Token"},token:n,setToken:u,toggleToken:ie,showToken:c,canEditInputs:U,canRegenerate:M,canShowToken:re,isSubmitting:d,regenerateUrl:"/admin/api-tokens/"}),e.jsx(Oe.Content,{children:e.jsxs(E,{direction:"column",alignItems:"stretch",gap:6,children:[n?.accessKey&&c&&e.jsx(e.Fragment,{children:window.strapi.future.isEnabled("unstableGuidedTour")?e.jsx(Fe,{token:n.accessKey,tokenType:P}):e.jsx(He,{token:n.accessKey,tokenType:P})}),e.jsx(Xe,{errors:o,onChange:S,canEditInputs:U,isCreating:T,values:O,apiToken:n,onDispatch:f,setHasChangedPermissions:$}),e.jsx(rs,{disabled:!U||O?.type==="read-only"||O?.type==="full-access"})]})})]}))})]})})},Gs=()=>{const t=Y(s=>s.admin_app.permissions.settings?.["api-tokens"].read);return e.jsx(w.Protect,{permissions:t,children:e.jsx(_s,{})})};export{_s as EditView,Gs as ProtectedEditView};
