const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["AdminSeatInfo-DbVHPPM5.js","strapi-z7ApxZZq.js","strapi-D6821bIJ.css","selectors-B6uMLQu7.js"])))=>i.map(i=>d[i]);
import{r as h,a as L,j as e,cc as m,cL as G,cM as K,cN as Z,$ as x,cO as J,I as P,av as Q,cF as X,cP as R,e as I,T as u,D as b,M as v,cQ as Y,aJ as q,cR as ee,cS as te,cT as ae,cU as ie,cV as ne,cW as se,cX as oe,cY as le,k as re,cZ as de,c_ as ce,b as ge,c$ as ue,n as pe,ck as w,bi as me,z as he,B as A,P as U,E as xe,H as z,bH as _,bI as $,_ as fe}from"./strapi-z7ApxZZq.js";import{s as je}from"./selectors-B6uMLQu7.js";const y=750,F=100,H=["image/jpeg","image/png","image/svg+xml"],Se={id:"Settings.application.customization.modal.upload.error-format",defaultMessage:"Wrong format uploaded (accepted formats only: jpeg, jpg, png, svg)."},O={id:"Settings.application.customization.modal.upload.error-size",defaultMessage:"The file uploaded is too large (max dimension: {dimension}x{dimension}, max file size: {size}KB)"},N=async a=>{if(!H.includes(a.type))throw new E("File format",Se);const i=await new Promise(l=>{const r=new FileReader;r.onload=()=>{const d=new Image;d.onload=()=>{l({width:d.width,height:d.height})},d.src=r.result},r.readAsDataURL(a)});if(!(i.width<=y&&i.height<=y))throw new E("File sizing",O);const n={ext:a.name.split(".").pop(),size:a.size/1e3,name:a.name,url:URL.createObjectURL(a),rawFile:a,width:i.width,height:i.height};if(!(n.size<=F))throw new E("File sizing",O);return n};class E extends Error{constructor(t,i,g){super(t,g),this.displayMessage=i}}const[Me,T]=G("LogoInput"),V=({canUpdate:a,customLogo:t,defaultLogo:i,hint:g,label:n,onChangeLogo:s})=>{const[l,r]=h.useState(),[d,f]=h.useState(),{formatMessage:c}=L(),C=()=>{r(void 0),f(void 0)};return e.jsx(m.Root,{open:!!d,onOpenChange:j=>{j===!1&&C()},children:e.jsxs(Me,{setLocalImage:r,localImage:l,goToStep:f,onClose:C,children:[e.jsx(K,{label:n,selectedSlide:0,hint:g,previousLabel:"",nextLabel:"",onNext:()=>{},onPrevious:()=>{},secondaryLabel:t?.name||"logo.png",actions:e.jsxs(J,{children:[e.jsx(m.Trigger,{children:e.jsx(P,{disabled:!a,onClick:()=>f("upload"),label:c({id:"Settings.application.customization.carousel.change-action",defaultMessage:"Change logo"}),children:e.jsx(Q,{})})}),t?.url&&e.jsx(P,{disabled:!a,onClick:()=>s(null),label:c({id:"Settings.application.customization.carousel.reset-action",defaultMessage:"Reset logo"}),children:e.jsx(X,{})})]}),children:e.jsx(Z,{label:c({id:"Settings.application.customization.carousel-slide.label",defaultMessage:"Logo slide"}),children:e.jsx(x,{maxHeight:"40%",maxWidth:"40%",tag:"img",src:t?.url||i,alt:c({id:"Settings.application.customization.carousel.title",defaultMessage:"Logo"})})})}),e.jsxs(m.Content,{children:[e.jsx(m.Header,{children:e.jsx(m.Title,{children:c(d==="upload"?{id:"Settings.application.customization.modal.upload",defaultMessage:"Upload logo"}:{id:"Settings.application.customization.modal.pending",defaultMessage:"Pending logo"})})}),d==="upload"?e.jsx(Ce,{}):e.jsx(Le,{onChangeLogo:s})]})]})})},Ce=()=>{const{formatMessage:a}=L();return e.jsxs(R.Root,{variant:"simple",defaultValue:"computer",children:[e.jsx(x,{paddingLeft:8,paddingRight:8,children:e.jsxs(R.List,{"aria-label":a({id:"Settings.application.customization.modal.tab.label",defaultMessage:"How do you want to upload your assets?"}),children:[e.jsx(R.Trigger,{value:"computer",children:a({id:"Settings.application.customization.modal.upload.from-computer",defaultMessage:"From computer"})}),e.jsx(R.Trigger,{value:"url",children:a({id:"Settings.application.customization.modal.upload.from-url",defaultMessage:"From url"})})]})}),e.jsx(R.Content,{value:"computer",children:e.jsx(be,{})}),e.jsx(R.Content,{value:"url",children:e.jsx(Ie,{})})]})},Ie=()=>{const{formatMessage:a}=L(),[t,i]=h.useState(""),[g,n]=h.useState(),{setLocalImage:s,goToStep:l,onClose:r}=T("URLForm"),d=c=>{i(c.target.value)},f=async c=>{c.preventDefault();const j=new FormData(c.target).get("logo-url");if(j)try{const p=await de.get(j.toString(),{responseType:"blob",timeout:8e3}),S=new File([p.data],p.config.url??"",{type:p.headers["content-type"]}),M=await N(S);s(M),l("pending")}catch(p){if(p instanceof ce)n(a({id:"Settings.application.customization.modal.upload.error-network",defaultMessage:"Network error"}));else if(p instanceof E)n(a(p.displayMessage,{size:F,dimension:y}));else throw p}};return e.jsxs("form",{onSubmit:f,children:[e.jsx(x,{paddingLeft:8,paddingRight:8,paddingTop:6,paddingBottom:6,children:e.jsxs(v.Root,{error:g,name:"logo-url",children:[e.jsx(v.Label,{children:a({id:"Settings.application.customization.modal.upload.from-url.input-label",defaultMessage:"URL"})}),e.jsx(q,{onChange:d,value:t}),e.jsx(v.Error,{})]})}),e.jsxs(m.Footer,{children:[e.jsx(b,{onClick:r,variant:"tertiary",children:a({id:"app.components.Button.cancel",defaultMessage:"Cancel"})}),e.jsx(b,{type:"submit",children:a({id:"Settings.application.customization.modal.upload.next",defaultMessage:"Next"})})]})]})},be=()=>{const{formatMessage:a}=L(),[t,i]=h.useState(!1),[g,n]=h.useState(),s=h.useRef(null),l=h.useId(),{setLocalImage:r,goToStep:d,onClose:f}=T("ComputerForm"),c=()=>{i(!0)},C=()=>{i(!1)},j=S=>{S.preventDefault(),s.current.click()},p=async()=>{if(C(),!s.current.files)return;const[S]=s.current.files;try{const M=await N(S);r(M),d("pending")}catch(M){if(M instanceof E)n(a(M.displayMessage,{size:F,dimension:y})),s.current.focus();else throw M}};return e.jsxs(e.Fragment,{children:[e.jsx("form",{children:e.jsx(x,{paddingLeft:8,paddingRight:8,paddingTop:6,paddingBottom:6,children:e.jsx(v.Root,{name:l,error:g,children:e.jsxs(I,{direction:"column",alignItems:"stretch",gap:2,children:[e.jsxs(I,{paddingTop:9,paddingBottom:7,hasRadius:!0,justifyContent:"center",direction:"column",background:t?"primary100":"neutral100",borderColor:t?"primary500":g?"danger600":"neutral300",borderStyle:"dashed",borderWidth:"1px",position:"relative",onDragEnter:c,onDragLeave:C,children:[e.jsx(Y,{fill:"primary600",width:"6rem",height:"6rem","aria-hidden":!0}),e.jsx(x,{paddingTop:3,paddingBottom:5,children:e.jsx(u,{variant:"delta",tag:"label",htmlFor:l,children:a({id:"Settings.application.customization.modal.upload.drag-drop",defaultMessage:"Drag and Drop here or"})})}),e.jsx(x,{position:"relative",children:e.jsx(ze,{accept:H.join(", "),type:"file",name:"files",tabIndex:-1,onChange:p,ref:s,id:l})}),e.jsx(b,{type:"button",onClick:j,children:a({id:"Settings.application.customization.modal.upload.cta.browse",defaultMessage:"Browse files"})}),e.jsx(x,{paddingTop:6,children:e.jsx(u,{variant:"pi",textColor:"neutral600",children:a({id:"Settings.application.customization.modal.upload.file-validation",defaultMessage:"Max dimension: {dimension}x{dimension}, Max size: {size}KB"},{size:F,dimension:y})})})]}),e.jsx(v.Error,{})]})})})}),e.jsx(m.Footer,{children:e.jsx(b,{onClick:f,variant:"tertiary",children:a({id:"app.components.Button.cancel",defaultMessage:"Cancel"})})})]})},ze=re(v.Input)`
  opacity: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
`,Le=({onChangeLogo:a})=>{const{formatMessage:t}=L(),{localImage:i,setLocalImage:g,goToStep:n,onClose:s}=T("PendingLogoDialog"),l=()=>{g(void 0),n("upload")},r=()=>{i&&a(i),s()};return e.jsxs(e.Fragment,{children:[e.jsx(m.Body,{children:e.jsxs(x,{paddingLeft:8,paddingRight:8,paddingTop:6,paddingBottom:6,children:[e.jsxs(I,{justifyContent:"space-between",paddingBottom:6,children:[e.jsxs(I,{direction:"column",alignItems:"flex-start",children:[e.jsx(u,{variant:"pi",fontWeight:"bold",children:t({id:"Settings.application.customization.modal.pending.title",defaultMessage:"Logo ready to upload"})}),e.jsx(u,{variant:"pi",textColor:"neutral500",children:t({id:"Settings.application.customization.modal.pending.subtitle",defaultMessage:"Manage the chosen logo before uploading it"})})]}),e.jsx(b,{onClick:l,variant:"secondary",children:t({id:"Settings.application.customization.modal.pending.choose-another",defaultMessage:"Choose another logo"})})]}),e.jsx(x,{maxWidth:"18rem",children:i?.url?e.jsx(Re,{asset:i}):null})]})}),e.jsxs(m.Footer,{children:[e.jsx(m.Close,{children:e.jsx(b,{onClick:s,variant:"tertiary",children:t({id:"Settings.application.customization.modal.cancel",defaultMessage:"Cancel"})})}),e.jsx(b,{onClick:r,children:t({id:"Settings.application.customization.modal.pending.upload",defaultMessage:"Upload logo"})})]})]})},Re=({asset:a})=>{const{formatMessage:t}=L();return e.jsxs(ee,{children:[e.jsx(te,{children:e.jsx(ae,{size:"S",src:a.url})}),e.jsxs(ie,{children:[e.jsxs(ne,{children:[e.jsx(se,{children:a.name}),e.jsx(oe,{children:`${a.ext?.toUpperCase()} - ${a.width}✕${a.height}`})]}),e.jsx(le,{children:t({id:"Settings.application.customization.modal.pending.card-badge",defaultMessage:"image"})})]})]})},ve=()=>null,Ee=()=>{const{trackUsage:a}=ge(),{formatMessage:t}=L(),{logos:i,updateProjectSettings:g}=ue("ApplicationInfoPage"),[n,s]=h.useState({menu:i.menu,auth:i.auth}),{settings:l}=pe(je),r=w("ApplicationInfoPage",o=>o.communityEdition),d=w("ApplicationInfoPage",o=>o.latestStrapiReleaseTag),f=w("ApplicationInfoPage",o=>o.nodeVersion),c=w("ApplicationInfoPage",o=>o.shouldUpdateStrapi),C=w("ApplicationInfoPage",o=>o.strapiVersion),j=me(ve,async()=>(await fe(async()=>{const{AdminSeatInfoEE:o}=await import("./AdminSeatInfo-DbVHPPM5.js");return{AdminSeatInfoEE:o}},__vite__mapDeps([0,1,2,3]))).AdminSeatInfoEE),{allowedActions:{canRead:p,canUpdate:S}}=he(l?l["project-settings"]:{}),M=o=>{o.preventDefault(),g({authLogo:n.auth.custom??null,menuLogo:n.menu.custom??null})},k=o=>D=>{D===null&&a("didClickResetLogo",{logo:o}),s(B=>({...B,[o]:{...B[o],custom:D}}))};if(h.useEffect(()=>{s({menu:i.menu,auth:i.auth})},[i]),!j)return null;const W=n.auth.custom===i.auth.custom&&n.menu.custom===i.menu.custom;return e.jsxs(A.Root,{children:[e.jsx(U.Title,{children:t({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:t({id:"Settings.application.header",defaultMessage:"Application"})})}),e.jsx(U.Main,{children:e.jsxs("form",{onSubmit:M,children:[e.jsx(A.Header,{title:t({id:"Settings.application.title",defaultMessage:"Overview"}),subtitle:t({id:"Settings.application.description",defaultMessage:"Administration panel’s global information"}),primaryAction:S&&e.jsx(b,{disabled:W,type:"submit",startIcon:e.jsx(xe,{}),children:t({id:"global.save",defaultMessage:"Save"})})}),e.jsx(A.Content,{children:e.jsxs(I,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsxs(I,{direction:"column",alignItems:"stretch",gap:4,hasRadius:!0,background:"neutral0",shadow:"tableShadow",paddingTop:6,paddingBottom:6,paddingRight:7,paddingLeft:7,children:[e.jsx(u,{variant:"delta",tag:"h3",children:t({id:"global.details",defaultMessage:"Details"})}),e.jsxs(z.Root,{gap:5,tag:"dl",children:[e.jsxs(z.Item,{col:6,s:12,direction:"column",alignItems:"start",children:[e.jsx(u,{variant:"sigma",textColor:"neutral600",tag:"dt",children:t({id:"Settings.application.strapiVersion",defaultMessage:"strapi version"})}),e.jsxs(I,{gap:3,direction:"column",alignItems:"start",tag:"dd",children:[e.jsxs(u,{children:["v",C]}),c&&e.jsx(_,{href:`https://github.com/strapi/strapi/releases/tag/${d}`,endIcon:e.jsx($,{}),children:t({id:"Settings.application.link-upgrade",defaultMessage:"Upgrade your admin panel"})})]})]}),e.jsxs(z.Item,{col:6,s:12,direction:"column",alignItems:"start",children:[e.jsx(u,{variant:"sigma",textColor:"neutral600",tag:"dt",children:t({id:"Settings.application.edition-title",defaultMessage:"current edition"})}),e.jsxs(I,{gap:3,direction:"column",alignItems:"start",tag:"dd",children:[e.jsx(u,{children:t({id:"Settings.application.ee-or-ce",defaultMessage:"{communityEdition, select, true {Community Edition} other {Enterprise Edition}}"},{communityEdition:r})}),e.jsx(_,{href:"https://strapi.io/pricing-self-hosted",endIcon:e.jsx($,{}),children:t({id:"Settings.application.link-pricing",defaultMessage:"See all pricing plans"})})]})]}),e.jsxs(z.Item,{col:6,s:12,direction:"column",alignItems:"start",children:[e.jsx(u,{variant:"sigma",textColor:"neutral600",tag:"dt",children:t({id:"Settings.application.node-version",defaultMessage:"node version"})}),e.jsx(u,{tag:"dd",children:f})]}),e.jsx(j,{})]})]}),p&&e.jsxs(x,{hasRadius:!0,background:"neutral0",shadow:"tableShadow",paddingTop:6,paddingBottom:6,paddingRight:7,paddingLeft:7,children:[e.jsx(u,{variant:"delta",tag:"h3",children:t({id:"Settings.application.customization",defaultMessage:"Customization"})}),e.jsx(u,{variant:"pi",textColor:"neutral600",children:t({id:"Settings.application.customization.size-details",defaultMessage:"Max dimension: {dimension}×{dimension}, Max file size: {size}KB"},{dimension:y,size:F})}),e.jsxs(z.Root,{paddingTop:4,gap:4,children:[e.jsx(z.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsx(V,{canUpdate:S,customLogo:n.menu.custom,defaultLogo:n.menu.default,hint:t({id:"Settings.application.customization.menu-logo.carousel-hint",defaultMessage:"Replace the logo in the main navigation"}),label:t({id:"Settings.application.customization.carousel.menu-logo.title",defaultMessage:"Menu logo"}),onChangeLogo:k("menu")})}),e.jsx(z.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsx(V,{canUpdate:S,customLogo:n.auth.custom,defaultLogo:n.auth.default,hint:t({id:"Settings.application.customization.auth-logo.carousel-hint",defaultMessage:"Replace the logo in the authentication pages"}),label:t({id:"Settings.application.customization.carousel.auth-logo.title",defaultMessage:"Auth logo"}),onChangeLogo:k("auth")})})]})]})]})})]})})]})};export{Ee as ApplicationInfoPage};
