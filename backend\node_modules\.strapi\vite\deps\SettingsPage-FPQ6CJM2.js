import {
  PERMISSIONS,
  getTrad
} from "./chunk-WRTY2ETJ.js";
import "./chunk-MLDZODJM.js";
import "./chunk-GXEE7KZL.js";
import "./chunk-6EC7MKBK.js";
import {
  useMutation,
  useQuery
} from "./chunk-55Q6LON3.js";
import "./chunk-QVDAYSOU.js";
import "./chunk-A34HN3WE.js";
import "./chunk-APY4KZ5L.js";
import "./chunk-OOAHAGSN.js";
import "./chunk-BEZCWAXF.js";
import "./chunk-MFYBRT3Z.js";
import "./chunk-73ABBQBL.js";
import "./chunk-WNXMQTNQ.js";
import "./chunk-VJ3LKUI5.js";
import {
  useFetchClient
} from "./chunk-3OKC5SXR.js";
import "./chunk-ITIYAEI7.js";
import "./chunk-K4LYEYIN.js";
import "./chunk-XGEFBNZK.js";
import "./chunk-YW3XCEFV.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-UCU7ROGU.js";
import "./chunk-K65KIEAL.js";
import "./chunk-F5JI4FJS.js";
import "./chunk-TFPYBHFW.js";
import {
  require_set
} from "./chunk-MMKYPT4K.js";
import "./chunk-U63NXCTP.js";
import "./chunk-SGQXSZWC.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-7US6K6XC.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-DZIOEGNZ.js";
import "./chunk-L3WQFZOD.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-67LA7IEH.js";
import "./chunk-UHKKWDMK.js";
import "./chunk-GM54BMM2.js";
import "./chunk-2DNMQP4H.js";
import "./chunk-L32VSWBJ.js";
import "./chunk-XVW7MCOW.js";
import "./chunk-2U7FOQNK.js";
import "./chunk-WIFIVZU3.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import "./chunk-T3UNFN7Y.js";
import {
  Layouts
} from "./chunk-DETWX3NC.js";
import "./chunk-PQINNV4N.js";
import {
  require_isEqual
} from "./chunk-VYSYYPOB.js";
import {
  Page
} from "./chunk-FTSHQ5RF.js";
import "./chunk-RZWN45NY.js";
import "./chunk-FOUXGIF2.js";
import {
  require_lib
} from "./chunk-TXOJWASW.js";
import "./chunk-BUMITFEB.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import {
  fn
} from "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-PW6GS6S3.js";
import {
  Box,
  Button,
  Field,
  Flex,
  Grid,
  Toggle,
  Typography,
  useIntl
} from "./chunk-Y6YT4U2T.js";
import "./chunk-5ZC4PE57.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$4F
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/upload/dist/admin/pages/SettingsPage/SettingsPage.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);
var import_qs = __toESM(require_lib(), 1);

// node_modules/@strapi/upload/dist/admin/pages/SettingsPage/init.mjs
var init = (initialState2) => {
  return initialState2;
};

// node_modules/@strapi/upload/dist/admin/pages/SettingsPage/reducer.mjs
var import_set = __toESM(require_set(), 1);
var initialState = {
  initialData: {
    responsiveDimensions: true,
    sizeOptimization: true,
    autoOrientation: false,
    videoPreview: false
  },
  modifiedData: {
    responsiveDimensions: true,
    sizeOptimization: true,
    autoOrientation: false,
    videoPreview: false
  }
};
var reducer = (state, action) => fn(state, (drafState) => {
  switch (action.type) {
    case "GET_DATA_SUCCEEDED": {
      drafState.initialData = action.data;
      drafState.modifiedData = action.data;
      break;
    }
    case "ON_CHANGE": {
      (0, import_set.default)(drafState, [
        "modifiedData",
        ...action.keys.split(".")
      ], action.value);
      break;
    }
    default:
      return state;
  }
});

// node_modules/@strapi/upload/dist/admin/pages/SettingsPage/SettingsPage.mjs
var SettingsPage = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { get, put } = useFetchClient();
  const [{ initialData, modifiedData }, dispatch] = React.useReducer(reducer, initialState, init);
  const { data, isLoading, refetch } = useQuery({
    queryKey: [
      "upload",
      "settings"
    ],
    async queryFn() {
      const { data: { data: data2 } } = await get("/upload/settings");
      return data2;
    }
  });
  React.useEffect(() => {
    if (data) {
      dispatch({
        type: "GET_DATA_SUCCEEDED",
        data
      });
    }
  }, [
    data
  ]);
  const isSaveButtonDisabled = (0, import_isEqual.default)(initialData, modifiedData);
  const { mutateAsync, isLoading: isSubmitting } = useMutation(async (body) => {
    const { data: data2 } = await put("/upload/settings", body);
    return data2;
  }, {
    onSuccess() {
      refetch();
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "notification.form.success.fields"
        })
      });
    },
    onError(err) {
      console.error(err);
    }
  });
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSaveButtonDisabled) {
      return;
    }
    await mutateAsync(modifiedData);
  };
  const handleChange = ({ target: { name, value } }) => {
    dispatch({
      type: "ON_CHANGE",
      keys: name,
      value
    });
  };
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Page.Main, {
    tabIndex: -1,
    children: [
      (0, import_jsx_runtime.jsx)(Page.Title, {
        children: formatMessage({
          id: getTrad("page.title"),
          defaultMessage: "Settings - Media Libray"
        })
      }),
      (0, import_jsx_runtime.jsxs)("form", {
        onSubmit: handleSubmit,
        children: [
          (0, import_jsx_runtime.jsx)(Layouts.Header, {
            title: formatMessage({
              id: getTrad("settings.header.label"),
              defaultMessage: "Media Library"
            }),
            primaryAction: (0, import_jsx_runtime.jsx)(Button, {
              disabled: isSaveButtonDisabled,
              loading: isSubmitting,
              type: "submit",
              startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4F, {}),
              size: "S",
              children: formatMessage({
                id: "global.save",
                defaultMessage: "Save"
              })
            }),
            subtitle: formatMessage({
              id: getTrad("settings.sub-header.label"),
              defaultMessage: "Configure the settings for the Media Library"
            })
          }),
          (0, import_jsx_runtime.jsx)(Layouts.Content, {
            children: (0, import_jsx_runtime.jsx)(Layouts.Root, {
              children: (0, import_jsx_runtime.jsx)(Flex, {
                direction: "column",
                alignItems: "stretch",
                gap: 12,
                children: (0, import_jsx_runtime.jsx)(Box, {
                  background: "neutral0",
                  padding: 6,
                  shadow: "filterShadow",
                  hasRadius: true,
                  children: (0, import_jsx_runtime.jsxs)(Flex, {
                    direction: "column",
                    alignItems: "stretch",
                    gap: 4,
                    children: [
                      (0, import_jsx_runtime.jsx)(Flex, {
                        children: (0, import_jsx_runtime.jsx)(Typography, {
                          variant: "delta",
                          tag: "h2",
                          children: formatMessage({
                            id: getTrad("settings.blockTitle"),
                            defaultMessage: "Asset management"
                          })
                        })
                      }),
                      (0, import_jsx_runtime.jsxs)(Grid.Root, {
                        gap: 6,
                        children: [
                          (0, import_jsx_runtime.jsx)(Grid.Item, {
                            col: 6,
                            s: 12,
                            direction: "column",
                            alignItems: "stretch",
                            children: (0, import_jsx_runtime.jsxs)(Field.Root, {
                              hint: formatMessage({
                                id: getTrad("settings.form.responsiveDimensions.description"),
                                defaultMessage: "Enabling this option will generate multiple formats (small, medium and large) of the uploaded asset."
                              }),
                              name: "responsiveDimensions",
                              children: [
                                (0, import_jsx_runtime.jsx)(Field.Label, {
                                  children: formatMessage({
                                    id: getTrad("settings.form.responsiveDimensions.label"),
                                    defaultMessage: "Responsive friendly upload"
                                  })
                                }),
                                (0, import_jsx_runtime.jsx)(Toggle, {
                                  checked: modifiedData == null ? void 0 : modifiedData.responsiveDimensions,
                                  offLabel: formatMessage({
                                    id: "app.components.ToggleCheckbox.off-label",
                                    defaultMessage: "Off"
                                  }),
                                  onLabel: formatMessage({
                                    id: "app.components.ToggleCheckbox.on-label",
                                    defaultMessage: "On"
                                  }),
                                  onChange: (e) => {
                                    handleChange({
                                      target: {
                                        name: "responsiveDimensions",
                                        value: e.target.checked
                                      }
                                    });
                                  }
                                }),
                                (0, import_jsx_runtime.jsx)(Field.Hint, {})
                              ]
                            })
                          }),
                          (0, import_jsx_runtime.jsx)(Grid.Item, {
                            col: 6,
                            s: 12,
                            direction: "column",
                            alignItems: "stretch",
                            children: (0, import_jsx_runtime.jsxs)(Field.Root, {
                              hint: formatMessage({
                                id: getTrad("settings.form.sizeOptimization.description"),
                                defaultMessage: "Enabling this option will reduce the image size and slightly reduce its quality."
                              }),
                              name: "sizeOptimization",
                              children: [
                                (0, import_jsx_runtime.jsx)(Field.Label, {
                                  children: formatMessage({
                                    id: getTrad("settings.form.sizeOptimization.label"),
                                    defaultMessage: "Size optimization"
                                  })
                                }),
                                (0, import_jsx_runtime.jsx)(Toggle, {
                                  checked: modifiedData == null ? void 0 : modifiedData.sizeOptimization,
                                  offLabel: formatMessage({
                                    id: "app.components.ToggleCheckbox.off-label",
                                    defaultMessage: "Off"
                                  }),
                                  onLabel: formatMessage({
                                    id: "app.components.ToggleCheckbox.on-label",
                                    defaultMessage: "On"
                                  }),
                                  onChange: (e) => {
                                    handleChange({
                                      target: {
                                        name: "sizeOptimization",
                                        value: e.target.checked
                                      }
                                    });
                                  }
                                }),
                                (0, import_jsx_runtime.jsx)(Field.Hint, {})
                              ]
                            })
                          }),
                          (0, import_jsx_runtime.jsx)(Grid.Item, {
                            col: 6,
                            s: 12,
                            direction: "column",
                            alignItems: "stretch",
                            children: (0, import_jsx_runtime.jsxs)(Field.Root, {
                              hint: formatMessage({
                                id: getTrad("settings.form.autoOrientation.description"),
                                defaultMessage: "Enabling this option will automatically rotate the image according to EXIF orientation tag."
                              }),
                              name: "autoOrientation",
                              children: [
                                (0, import_jsx_runtime.jsx)(Field.Label, {
                                  children: formatMessage({
                                    id: getTrad("settings.form.autoOrientation.label"),
                                    defaultMessage: "Auto orientation"
                                  })
                                }),
                                (0, import_jsx_runtime.jsx)(Toggle, {
                                  checked: modifiedData == null ? void 0 : modifiedData.autoOrientation,
                                  offLabel: formatMessage({
                                    id: "app.components.ToggleCheckbox.off-label",
                                    defaultMessage: "Off"
                                  }),
                                  onLabel: formatMessage({
                                    id: "app.components.ToggleCheckbox.on-label",
                                    defaultMessage: "On"
                                  }),
                                  onChange: (e) => {
                                    handleChange({
                                      target: {
                                        name: "autoOrientation",
                                        value: e.target.checked
                                      }
                                    });
                                  }
                                }),
                                (0, import_jsx_runtime.jsx)(Field.Hint, {})
                              ]
                            })
                          })
                        ]
                      })
                    ]
                  })
                })
              })
            })
          })
        ]
      })
    ]
  });
};
var ProtectedSettingsPage = () => (0, import_jsx_runtime.jsx)(Page.Protect, {
  permissions: PERMISSIONS.settings,
  children: (0, import_jsx_runtime.jsx)(SettingsPage, {})
});
export {
  ProtectedSettingsPage,
  SettingsPage
};
//# sourceMappingURL=SettingsPage-FPQ6CJM2.js.map
