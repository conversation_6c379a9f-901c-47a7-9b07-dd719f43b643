import{dX as $e,j as e,eE as $,a as I,D as A,T as f,d0 as H,eP as re,ee as W,eQ as Oe,k as q,e as R,at as xe,aC as Ne,a3 as je,r as U,cA as We,cB as Ue,aA as be,A as ye,cc as N,be as Fe,bf as He,M as w,aJ as Ge,$ as V,bp as _e,eR as Qe,eS as Ye,eT as qe,eU as Ze,N as Je,O as Xe,o as Ke,w as ee,V as Re,v as te,u as Me,eV as Ve,x as ve,eW as et,bC as tt,b as Ce,z as oe,eX as se,P as F,aE as Te,B as X,av as st,G as at,bH as De,cP as G,b7 as nt,cu as Y,dL as it,as as we,s as Se,H as ue,au as rt,a6 as le,J as K,b9 as ze,eY as de,eZ as ot,e_ as lt,aB as ce,bt as dt,bu as ct,e$ as ut,f0 as gt,cf as ge,g as ht,bm as mt,ep as pt,bg as ft,a7 as xt,f1 as jt,f2 as bt,aH as yt,aI as Rt,bE as _,Y as Mt,Z as Q,f3 as vt,f4 as ne,f5 as Ct,f6 as Tt,R as Dt,Q as he}from"./strapi-z7ApxZZq.js";import{f as ie}from"./index-C3HeomYJ.js";import{t as wt,a as St,R as zt,z as At}from"./schemas-CbOl1I6u.js";import{f as It}from"./index-DSQJUh5z.js";import"./index-BRVyLNfZ.js";function Ae(s,t,r){var a=wt(s,r),i=St(t,a,!0),n=new Date(a.getTime()-i),u=new Date(0);return u.setFullYear(n.getUTCFullYear(),n.getUTCMonth(),n.getUTCDate()),u.setHours(n.getUTCHours(),n.getUTCMinutes(),n.getUTCSeconds(),n.getUTCMilliseconds()),u}const Lt=q(R)`
  width: 100%;
  max-width: 256px;

  & > * {
    border-bottom: 1px solid ${({theme:s})=>s.colors.neutral150};
  }

  & > *:last-child {
    border-bottom: none;
  }
`,Et=({action:s,status:t,hasErrors:r,requiredStage:a,entryStage:i})=>{const{formatMessage:n}=I();return s==="publish"?r||a&&a.id!==i?.id?e.jsx($.Trigger,{children:e.jsx(A,{variant:"ghost",startIcon:e.jsx(re,{fill:"danger600"}),endIcon:e.jsx(H,{}),children:e.jsx(f,{textColor:"danger600",variant:"omega",fontWeight:"bold",children:n({id:"content-releases.pages.ReleaseDetails.entry-validation.not-ready",defaultMessage:"Not ready to publish"})})})}):t==="draft"?e.jsx($.Trigger,{children:e.jsx(A,{variant:"ghost",startIcon:e.jsx(W,{fill:"success600"}),endIcon:e.jsx(H,{}),children:e.jsx(f,{textColor:"success600",variant:"omega",fontWeight:"bold",children:n({id:"content-releases.pages.ReleaseDetails.entry-validation.ready-to-publish",defaultMessage:"Ready to publish"})})})}):t==="modified"?e.jsx($.Trigger,{children:e.jsx(A,{variant:"ghost",startIcon:e.jsx(Oe,{fill:"alternative600"}),endIcon:e.jsx(H,{}),children:e.jsx(f,{variant:"omega",fontWeight:"bold",textColor:"alternative600",children:n({id:"content-releases.pages.ReleaseDetails.entry-validation.modified",defaultMessage:"Ready to publish changes"})})})}):e.jsx($.Trigger,{children:e.jsx(A,{variant:"ghost",startIcon:e.jsx(W,{fill:"success600"}),endIcon:e.jsx(H,{}),children:e.jsx(f,{textColor:"success600",variant:"omega",fontWeight:"bold",children:n({id:"content-releases.pages.ReleaseDetails.entry-validation.already-published",defaultMessage:"Already published"})})})}):t==="published"?e.jsx($.Trigger,{children:e.jsx(A,{variant:"ghost",startIcon:e.jsx(W,{fill:"success600"}),endIcon:e.jsx(H,{}),children:e.jsx(f,{textColor:"success600",variant:"omega",fontWeight:"bold",children:n({id:"content-releases.pages.ReleaseDetails.entry-validation.ready-to-unpublish",defaultMessage:"Ready to unpublish"})})})}):e.jsx($.Trigger,{children:e.jsx(A,{variant:"ghost",startIcon:e.jsx(W,{fill:"success600"}),endIcon:e.jsx(H,{}),children:e.jsx(f,{textColor:"success600",variant:"omega",fontWeight:"bold",children:n({id:"content-releases.pages.ReleaseDetails.entry-validation.already-unpublished",defaultMessage:"Already unpublished"})})})})},Pt=({hasErrors:s,errors:t,kind:r,contentTypeUid:a,documentId:i,locale:n})=>{const{formatMessage:u}=I();return e.jsxs(R,{direction:"column",gap:1,width:"100%",padding:5,children:[e.jsxs(R,{gap:2,width:"100%",children:[e.jsx(f,{fontWeight:"bold",children:u({id:"content-releases.pages.ReleaseDetails.entry-validation.fields",defaultMessage:"Fields"})}),s?e.jsx(re,{fill:"danger600"}):e.jsx(W,{fill:"success600"})]}),e.jsx(f,{width:"100%",textColor:"neutral600",children:s?u({id:"content-releases.pages.ReleaseDetails.entry-validation.fields.error",defaultMessage:"{errors} errors on fields."},{errors:t?Object.keys(t).length:0}):u({id:"content-releases.pages.ReleaseDetails.entry-validation.fields.success",defaultMessage:"All fields are filled correctly."})}),s&&e.jsx(xe,{tag:je,to:{pathname:`/content-manager/${r==="collectionType"?"collection-types":"single-types"}/${a}/${i}`,search:n?Ne.stringify({plugins:{i18n:{locale:n}}}):""},variant:"secondary",fullWidth:!0,state:{forceValidation:!0},children:u({id:"content-releases.pages.ReleaseDetails.entry-validation.fields.see-errors",defaultMessage:"See errors"})})]})},Bt=({contentTypeHasReviewWorkflow:s,requiredStage:t,entryStage:r})=>s?t&&t.id!==r?.id?e.jsx(re,{fill:"danger600"}):e.jsx(W,{fill:"success600"}):e.jsx(W,{fill:"neutral200"}),kt=({contentTypeHasReviewWorkflow:s,requiredStage:t,entryStage:r,formatMessage:a})=>s?t&&t.id!==r?.id?a({id:"content-releases.pages.ReleaseDetails.entry-validation.review-stage.not-ready",defaultMessage:"This entry is not at the required stage for publishing. ({stageName})"},{stageName:t?.name??""}):t&&t.id===r?.id?a({id:"content-releases.pages.ReleaseDetails.entry-validation.review-stage.ready",defaultMessage:"This entry is at the required stage for publishing. ({stageName})"},{stageName:t?.name??""}):a({id:"content-releases.pages.ReleaseDetails.entry-validation.review-stage.stage-not-required",defaultMessage:"No required stage for publication"}):a({id:"content-releases.pages.ReleaseDetails.entry-validation.review-stage.not-enabled",defaultMessage:"This entry is not associated to any workflow."}),$t=({contentTypeHasReviewWorkflow:s,requiredStage:t,entryStage:r})=>{const{formatMessage:a}=I(),i=Bt({contentTypeHasReviewWorkflow:s,requiredStage:t,entryStage:r});return e.jsxs(R,{direction:"column",gap:1,width:"100%",padding:5,children:[e.jsxs(R,{gap:2,width:"100%",children:[e.jsx(f,{fontWeight:"bold",children:a({id:"content-releases.pages.ReleaseDetails.entry-validation.review-stage",defaultMessage:"Review stage"})}),i]}),e.jsx(f,{textColor:"neutral600",children:kt({contentTypeHasReviewWorkflow:s,requiredStage:t,entryStage:r,formatMessage:a})})]})},Ot=({schema:s,entry:t,status:r,action:a})=>{const{validate:i,isLoading:n}=$e({collectionType:s?.kind??"",model:s?.uid??""},{skip:!0}),u=n?null:i(t),c=u?Object.keys(u).length>0:!1,p=s?.hasReviewWorkflow??!1,g=s?.stageRequiredToPublish,M=t.strapi_stage;return n?null:e.jsxs($.Root,{children:[e.jsx(Et,{action:a,status:r,hasErrors:c,requiredStage:g,entryStage:M}),e.jsx($.Content,{children:e.jsxs(Lt,{direction:"column",children:[e.jsx(Pt,{hasErrors:c,errors:u,contentTypeUid:s?.uid,kind:s?.kind,documentId:t.documentId,locale:t.locale}),e.jsx($t,{contentTypeHasReviewWorkflow:p,requiredStage:g,entryStage:M})]})})]})},Nt=["years","months","days","hours","minutes","seconds"],Ie=U.forwardRef(({timestamp:s,customIntervals:t=[],...r},a)=>{const{formatRelativeTime:i,formatDate:n,formatTime:u}=I(),c=We({start:s,end:Date.now()}),p=Nt.find(l=>c[l]>0&&Object.keys(c).includes(l)),g=Ue(s)?-c[p]:c[p],M=t.find(l=>c[l.unit]<l.threshold),C=M?M.text:i(g,p,{numeric:"auto"});return e.jsx("time",{ref:a,dateTime:s.toISOString(),role:"time",title:`${n(s)} ${u(s)}`,...r,children:C})}),Le=({handleClose:s,open:t,handleSubmit:r,initialValues:a,isLoading:i=!1})=>{const{formatMessage:n}=I(),{pathname:u}=be(),c=u===`/plugins/${qe}`,{timezoneList:p,systemTimezone:g={value:"UTC+00:00-Africa/Abidjan "}}=ye(a.scheduledAt?new Date(a.scheduledAt):new Date),M=l=>{const{date:h,time:T,timezone:m}=l;if(!h||!T||!m)return null;const b=m.split("&")[1];return At(`${h} ${T}`,b)},C=()=>p.find(h=>h.value.split("&")[1]===a.timezone)?.value||g.value;return e.jsx(N.Root,{open:t,onOpenChange:s,children:e.jsxs(N.Content,{children:[e.jsx(N.Header,{children:e.jsx(N.Title,{children:n({id:"content-releases.modal.title",defaultMessage:"{isCreatingRelease, select, true {New release} other {Edit release}}"},{isCreatingRelease:c})})}),e.jsx(Fe,{onSubmit:l=>{r({...l,timezone:l.timezone?l.timezone.split("&")[1]:null,scheduledAt:l.isScheduled?M(l):null})},initialValues:{...a,timezone:a.timezone?C():g.value},validationSchema:zt,validateOnChange:!1,children:({values:l,errors:h,handleChange:T,setFieldValue:m})=>e.jsxs(He,{children:[e.jsx(N.Body,{children:e.jsxs(R,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsxs(w.Root,{name:"name",error:h.name&&n({id:h.name,defaultMessage:h.name}),required:!0,children:[e.jsx(w.Label,{children:n({id:"content-releases.modal.form.input.label.release-name",defaultMessage:"Name"})}),e.jsx(Ge,{value:l.name,onChange:T}),e.jsx(w.Error,{})]}),e.jsx(V,{width:"max-content",children:e.jsx(_e,{name:"isScheduled",checked:l.isScheduled,onCheckedChange:b=>{m("isScheduled",b),b?(m("date",a.date),m("time",a.time),m("timezone",a.timezone??g?.value)):(m("date",null),m("time",""),m("timezone",null))},children:e.jsx(f,{textColor:l.isScheduled?"primary600":"neutral800",fontWeight:l.isScheduled?"semiBold":"regular",children:n({id:"modal.form.input.label.schedule-release",defaultMessage:"Schedule release"})})})}),l.isScheduled&&e.jsxs(e.Fragment,{children:[e.jsxs(R,{gap:4,alignItems:"start",children:[e.jsx(V,{width:"100%",children:e.jsxs(w.Root,{name:"date",error:h.date&&n({id:h.date,defaultMessage:h.date}),required:!0,children:[e.jsx(w.Label,{children:n({id:"content-releases.modal.form.input.label.date",defaultMessage:"Date"})}),e.jsx(Qe,{onChange:b=>{const D=b?It(b,{representation:"date"}):null;m("date",D)},clearLabel:n({id:"content-releases.modal.form.input.clearLabel",defaultMessage:"Clear"}),onClear:()=>{m("date",null)},value:l.date?new Date(l.date):new Date,minDate:Ae(new Date,l.timezone.split("&")[1])}),e.jsx(w.Error,{})]})}),e.jsx(V,{width:"100%",children:e.jsxs(w.Root,{name:"time",error:h.time&&n({id:h.time,defaultMessage:h.time}),required:!0,children:[e.jsx(w.Label,{children:n({id:"content-releases.modal.form.input.label.time",defaultMessage:"Time"})}),e.jsx(Ye,{onChange:b=>{m("time",b)},clearLabel:n({id:"content-releases.modal.form.input.clearLabel",defaultMessage:"Clear"}),onClear:()=>{m("time","")},value:l.time||void 0}),e.jsx(w.Error,{})]})})]}),e.jsx(Wt,{timezoneOptions:p})]})]})}),e.jsxs(N.Footer,{children:[e.jsx(N.Close,{children:e.jsx(A,{variant:"tertiary",name:"cancel",children:n({id:"cancel",defaultMessage:"Cancel"})})}),e.jsx(A,{name:"submit",loading:i,type:"submit",children:n({id:"content-releases.modal.form.button.submit",defaultMessage:"{isCreatingRelease, select, true {Continue} other {Save}}"},{isCreatingRelease:c})})]})]})})]})})},Wt=({timezoneOptions:s})=>{const{values:t,errors:r,setFieldValue:a}=Ze(),{formatMessage:i}=I(),[n,u]=U.useState(s);return U.useEffect(()=>{if(t.date){const{timezoneList:c}=ye(new Date(t.date));u(c);const p=t.timezone&&c.find(g=>g.value.split("&")[1]===t.timezone.split("&")[1]);p&&a("timezone",p.value)}},[a,t.date,t.timezone]),e.jsxs(w.Root,{name:"timezone",error:r.timezone&&i({id:r.timezone,defaultMessage:r.timezone}),required:!0,children:[e.jsx(w.Label,{children:i({id:"content-releases.modal.form.input.label.timezone",defaultMessage:"Timezone"})}),e.jsx(Je,{autocomplete:{type:"list",filter:"contains"},value:t.timezone||void 0,textValue:t.timezone?t.timezone.replace(/&/," "):void 0,onChange:c=>{a("timezone",c)},onTextValueChange:c=>{a("timezone",c)},onClear:()=>{a("timezone","")},children:n.map(c=>e.jsx(Xe,{value:c.value,children:c.value.replace(/&/," ")},c.value))}),e.jsx(w.Error,{})]})},Ut=Ke,Ee=s=>typeof s<"u"&&s.name!==void 0,Ft=q(De)`
  display: block;
`,Ht=q(Ie)`
  display: inline-block;
  &::first-letter {
    text-transform: uppercase;
  }
`,Pe=s=>{let t;switch(s){case"ready":t="success";break;case"blocked":t="warning";break;case"failed":t="danger";break;case"done":t="primary";break;case"empty":default:t="neutral"}return{textColor:`${t}600`,backgroundColor:`${t}100`,borderColor:`${t}200`}},me=({sectionTitle:s,releases:t=[],isError:r=!1})=>{const{formatMessage:a}=I();return r?e.jsx(F.Error,{}):t?.length===0?e.jsx(we,{content:a({id:"content-releases.page.Releases.tab.emptyEntries",defaultMessage:"No releases"},{target:s}),icon:e.jsx(Se,{width:"16rem"})}):e.jsx(ue.Root,{gap:4,children:t.map(({id:i,name:n,scheduledAt:u,status:c})=>e.jsx(ue.Item,{col:3,s:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(Ft,{tag:rt,to:`${i}`,isExternal:!1,children:e.jsxs(R,{direction:"column",justifyContent:"space-between",padding:4,hasRadius:!0,background:"neutral0",shadow:"tableShadow",height:"100%",width:"100%",alignItems:"start",gap:4,children:[e.jsxs(R,{direction:"column",alignItems:"start",gap:1,children:[e.jsx(f,{textColor:"neutral800",tag:"h3",variant:"delta",fontWeight:"bold",children:n}),e.jsx(f,{variant:"pi",textColor:"neutral600",children:u?e.jsx(Ht,{timestamp:new Date(u)}):a({id:"content-releases.pages.Releases.not-scheduled",defaultMessage:"Not scheduled"})})]}),e.jsx(le,{...Pe(c),children:c})]})})},i))})},Gt=q(it)`
  button {
    display: none;
  }
  p + div {
    margin-left: auto;
  }
`,_t={name:"",date:ie(new Date,"yyyy-MM-dd"),time:"",isScheduled:!0,scheduledAt:null,timezone:null},Qt=()=>{const s=be(),[t,r]=U.useState(!1),{toggleNotification:a}=ee(),{formatMessage:i}=I(),n=Re(),{formatAPIError:u}=te(),[{query:c},p]=Me(),g=Ve(c),{data:M,isLoading:C}=ve(),[l,{isLoading:h}]=et(),{getFeature:T}=tt(),{maximumReleases:m=3}=T("cms-content-releases"),{trackUsage:b}=Ce(),{allowedActions:{canCreate:D}}=oe(se),{isLoading:L,isSuccess:S,isError:d}=g,O=g?.currentData?.meta?.activeTab||"pending";U.useEffect(()=>{s?.state?.errors&&(a({type:"danger",title:i({id:"content-releases.pages.Releases.notification.error.title",defaultMessage:"Your request could not be processed."}),message:i({id:"content-releases.pages.Releases.notification.error.message",defaultMessage:"Please try again or open another release."})}),n("",{replace:!0,state:null}))},[i,s?.state?.errors,n,a]);const v=()=>{r(z=>!z)};if(L||C)return e.jsx(F.Loading,{});const y=S&&g.currentData?.meta?.pendingReleasesCount||0,B=y>=m,k=z=>{p({...c,page:1,pageSize:g?.currentData?.meta?.pagination?.pageSize||16,filters:{releasedAt:{$notNull:z!=="pending"}}})},E=async({name:z,scheduledAt:o,timezone:x})=>{const j=await l({name:z,scheduledAt:o,timezone:x});"data"in j?(a({type:"success",message:i({id:"content-releases.modal.release-created-notification-success",defaultMessage:"Release created."})}),b("didCreateRelease"),n(j.data.data.id.toString())):K(j.error)?a({type:"danger",message:u(j.error)}):a({type:"danger",message:i({id:"notification.error",defaultMessage:"An error occurred"})})};return e.jsxs(Te,{"aria-busy":L||C,children:[e.jsx(X.Header,{title:i({id:"content-releases.pages.Releases.title",defaultMessage:"Releases"}),secondaryAction:e.jsx(at,{label:i({id:"components.premiumFeature.title",defaultMessage:"Premium feature"})}),subtitle:i({id:"content-releases.pages.Releases.header-subtitle",defaultMessage:"Create and manage content updates"}),primaryAction:D?e.jsx(A,{startIcon:e.jsx(st,{}),onClick:v,disabled:B,children:i({id:"content-releases.header.actions.add-release",defaultMessage:"New release"})}):null}),e.jsx(X.Content,{children:e.jsxs(e.Fragment,{children:[B&&e.jsx(Gt,{marginBottom:6,action:e.jsx(De,{href:"https://strapi.io/pricing-cloud",isExternal:!0,children:i({id:"content-releases.pages.Releases.max-limit-reached.action",defaultMessage:"Explore plans"})}),title:i({id:"content-releases.pages.Releases.max-limit-reached.title",defaultMessage:"You have reached the {number} pending {number, plural, one {release} other {releases}} limit."},{number:m}),onClose:()=>{},closeLabel:"",children:i({id:"content-releases.pages.Releases.max-limit-reached.message",defaultMessage:"Upgrade to manage an unlimited number of releases."})}),e.jsxs(G.Root,{variant:"simple",onValueChange:k,value:O,children:[e.jsxs(V,{paddingBottom:8,children:[e.jsxs(G.List,{"aria-label":i({id:"content-releases.pages.Releases.tab-group.label",defaultMessage:"Releases list"}),children:[e.jsx(G.Trigger,{value:"pending",children:i({id:"content-releases.pages.Releases.tab.pending",defaultMessage:"Pending ({count})"},{count:y})}),e.jsx(G.Trigger,{value:"done",children:i({id:"content-releases.pages.Releases.tab.done",defaultMessage:"Done"})})]}),e.jsx(nt,{})]}),e.jsx(G.Content,{value:"pending",children:e.jsx(me,{sectionTitle:"pending",releases:g?.currentData?.data,isError:d})}),e.jsx(G.Content,{value:"done",children:e.jsx(me,{sectionTitle:"done",releases:g?.currentData?.data,isError:d})})]}),e.jsxs(Y.Root,{...g?.currentData?.meta?.pagination,defaultPageSize:g?.currentData?.meta?.pagination?.pageSize,children:[e.jsx(Y.PageSize,{options:["8","16","32","64"]}),e.jsx(Y.Links,{})]})]})}),e.jsx(Le,{open:t,handleClose:v,handleSubmit:E,isLoading:h,initialValues:{..._t,timezone:M?.data.defaultTimezone?M.data.defaultTimezone.split("&")[1]:null}})]})},Yt=q(R)`
  align-self: stretch;
  border-bottom-right-radius: ${({theme:s})=>s.borderRadius};
  border-bottom-left-radius: ${({theme:s})=>s.borderRadius};
  border-top: 1px solid ${({theme:s})=>s.colors.neutral150};
`,pe=({toggleEditReleaseModal:s,toggleWarningSubmit:t,children:r})=>{const{formatMessage:a,formatDate:i,formatTime:n}=I(),{releaseId:u}=ze(),{data:c,isLoading:p,error:g}=de({id:u},{skip:!u}),[M,{isLoading:C}]=ut(),{toggleNotification:l}=ee(),{formatAPIError:h}=te(),{allowedActions:T}=oe(se),{canUpdate:m,canDelete:b,canPublish:D}=T,L=Ut(),{trackUsage:S}=Ce(),d=c?.data,O=x=>async()=>{const j=await M({id:x});if("data"in j){l({type:"success",message:a({id:"content-releases.pages.ReleaseDetails.publish-notification-success",defaultMessage:"Release was published successfully."})});const{totalEntries:P,totalPublishedEntries:Z,totalUnpublishedEntries:J}=j.data.meta;S("didPublishRelease",{totalEntries:P,totalPublishedEntries:Z,totalUnpublishedEntries:J})}else K(j.error)?l({type:"danger",message:h(j.error)}):l({type:"danger",message:a({id:"notification.error",defaultMessage:"An error occurred"})})},v=()=>{L(Tt.util.invalidateTags([{type:"ReleaseAction",id:"LIST"},{type:"Release",id:u}]))},y=()=>d?.createdBy?d.createdBy.username?d.createdBy.username:d.createdBy.firstname?`${d.createdBy.firstname} ${d.createdBy.lastname||""}`.trim():d.createdBy.email:null;if(p)return e.jsx(F.Loading,{});if(Ee(g)&&"code"in g||!d)return e.jsx(ce,{to:"..",state:{errors:[{code:g?.code}]}});const B=d.actions.meta.count||0,k=!!y(),E=d.scheduledAt&&d.timezone,z=a({id:"content-releases.pages.Details.header-subtitle",defaultMessage:"{number, plural, =0 {No entries} one {# entry} other {# entries}}"},{number:B}),o=E?a({id:"content-releases.pages.ReleaseDetails.header-subtitle.scheduled",defaultMessage:"Scheduled for {date} at {time} ({offset})"},{date:i(new Date(d.scheduledAt),{weekday:"long",day:"numeric",month:"long",year:"numeric",timeZone:d.timezone}),time:n(new Date(d.scheduledAt),{timeZone:d.timezone,hourCycle:"h23"}),offset:gt(d.timezone,new Date(d.scheduledAt))}):"";return e.jsxs(Te,{"aria-busy":p,children:[e.jsx(X.Header,{title:d.name,subtitle:e.jsxs(R,{gap:2,lineHeight:6,children:[e.jsx(f,{textColor:"neutral600",variant:"epsilon",children:z+(E?` - ${o}`:"")}),e.jsx(le,{...Pe(d.status),children:d.status})]}),navigationAction:e.jsx(ft,{fallback:".."}),primaryAction:!d.releasedAt&&e.jsxs(R,{gap:2,children:[e.jsxs(qt,{label:e.jsx(pt,{}),variant:"tertiary",endIcon:null,paddingLeft:"7px",paddingRight:"7px","aria-label":a({id:"content-releases.header.actions.open-release-actions",defaultMessage:"Release edit and delete menu"}),popoverPlacement:"bottom-end",children:[e.jsx(ge.Item,{disabled:!m,onSelect:s,startIcon:e.jsx(ht,{}),children:a({id:"content-releases.header.actions.edit",defaultMessage:"Edit"})}),e.jsx(ge.Item,{disabled:!b,onSelect:t,variant:"danger",startIcon:e.jsx(mt,{}),children:a({id:"content-releases.header.actions.delete",defaultMessage:"Delete"})}),e.jsxs(Yt,{direction:"column",justifyContent:"center",alignItems:"flex-start",gap:1,padding:4,children:[e.jsx(f,{variant:"pi",fontWeight:"bold",children:a({id:"content-releases.header.actions.created",defaultMessage:"Created"})}),e.jsxs(f,{variant:"pi",color:"neutral300",children:[e.jsx(Ie,{timestamp:new Date(d.createdAt)}),a({id:"content-releases.header.actions.created.description",defaultMessage:"{hasCreatedByUser, select, true { by {createdBy}} other { by deleted user}}"},{createdBy:y(),hasCreatedByUser:k})]})]})]}),e.jsx(A,{size:"S",variant:"tertiary",onClick:v,children:a({id:"content-releases.header.actions.refresh",defaultMessage:"Refresh"})}),D?e.jsx(A,{size:"S",variant:"default",onClick:O(d.id.toString()),loading:C,disabled:d.actions.meta.count===0,children:a({id:"content-releases.header.actions.publish",defaultMessage:"Publish"})}):null]})}),r]})},qt=q(Ct)`
  & > span {
    display: flex;
  }
`,Zt=["contentType","locale","action"],Jt=["contentType","action"],fe=s=>s==="locale"?{id:"content-releases.pages.ReleaseDetails.groupBy.option.locales",defaultMessage:"Locales"}:s==="action"?{id:"content-releases.pages.ReleaseDetails.groupBy.option.actions",defaultMessage:"Actions"}:{id:"content-releases.pages.ReleaseDetails.groupBy.option.content-type",defaultMessage:"Content-Types"},Xt=({releaseId:s})=>{const{formatMessage:t}=I(),[{query:r},a]=Me(),{toggleNotification:i}=ee(),{formatAPIError:n}=te(),{data:u,isLoading:c,error:p}=de({id:s}),{allowedActions:{canUpdate:g}}=oe(se),M=xt("ReleaseDetailsPage",o=>o.runHookWaterfall),{displayedHeaders:C,hasI18nEnabled:l}=M("ContentReleases/pages/ReleaseDetails/add-locale-in-releases",{displayedHeaders:[{label:{id:"content-releases.page.ReleaseDetails.table.header.label.name",defaultMessage:"name"},name:"name"}],hasI18nEnabled:!1}),h=u?.data,T=r?.groupBy||"contentType",{isLoading:m,isFetching:b,isError:D,data:L,error:S}=jt({...r,releaseId:s}),[d]=bt(),O=async(o,x,j)=>{const P=await d({params:{releaseId:s,actionId:x},body:{type:o.target.value},query:r,actionPath:j});"error"in P&&(K(P.error)?i({type:"danger",message:n(P.error)}):i({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred"})}))};if(m||c)return e.jsx(F.Loading,{});const v=L?.data,y=L?.meta,B=y?.contentTypes||{};if(y?.components,Ee(p)||!h){const o=[];return p&&"code"in p&&o.push({code:p.code}),S&&"code"in S&&o.push({code:S.code}),e.jsx(ce,{to:"..",state:{errors:o}})}if(D||!v)return e.jsx(F.Error,{});if(Object.keys(v).length===0)return e.jsx(X.Content,{children:e.jsx(we,{action:e.jsx(xe,{tag:je,to:{pathname:"/content-manager"},style:{textDecoration:"none"},variant:"secondary",children:t({id:"content-releases.page.Details.button.openContentManager",defaultMessage:"Open the Content Manager"})}),icon:e.jsx(Se,{width:"16rem"}),content:t({id:"content-releases.pages.Details.tab.emptyEntries",defaultMessage:"This release is empty. Open the Content Manager, select an entry and add it to the release."})})});const k=t({id:"content-releases.pages.ReleaseDetails.groupBy.aria-label",defaultMessage:"Group by"}),E=[...C,{label:{id:"content-releases.page.ReleaseDetails.table.header.label.content-type",defaultMessage:"content-type"},name:"content-type"},{label:{id:"content-releases.page.ReleaseDetails.table.header.label.action",defaultMessage:"action"},name:"action"},...h.releasedAt?[]:[{label:{id:"content-releases.page.ReleaseDetails.table.header.label.status",defaultMessage:"status"},name:"status"}]],z=l?Zt:Jt;return e.jsx(X.Content,{children:e.jsxs(R,{gap:8,direction:"column",alignItems:"stretch",children:[e.jsx(R,{children:e.jsx(yt,{placeholder:k,"aria-label":k,customizeContent:o=>t({id:"content-releases.pages.ReleaseDetails.groupBy.label",defaultMessage:"Group by {groupBy}"},{groupBy:o}),value:t(fe(T)),onChange:o=>a({groupBy:o}),children:z.map(o=>e.jsx(Rt,{value:o,children:t(fe(o))},o))})}),Object.keys(v).map(o=>e.jsxs(R,{gap:4,direction:"column",alignItems:"stretch",children:[e.jsx(R,{role:"separator","aria-label":o,children:e.jsx(le,{children:o})}),e.jsx(_.Root,{rows:v[o].map(x=>({...x,id:Number(x.entry.id)})),headers:E,isLoading:m||b,children:e.jsxs(_.Content,{children:[e.jsx(_.Head,{children:E.map(({label:x,name:j})=>e.jsx(_.HeaderCell,{label:t(x),name:j},j))}),e.jsx(_.Loading,{}),e.jsx(_.Body,{children:v[o].map(({id:x,contentType:j,locale:P,type:Z,entry:J,status:Be},ke)=>e.jsxs(Mt,{children:[e.jsx(Q,{width:"25%",maxWidth:"200px",children:e.jsx(f,{ellipsis:!0,children:`${j.mainFieldValue||J.id}`})}),l&&e.jsx(Q,{width:"10%",children:e.jsx(f,{children:`${P?.name?P.name:"-"}`})}),e.jsx(Q,{width:"10%",children:e.jsx(f,{children:j.displayName||""})}),e.jsx(Q,{width:"20%",children:h.releasedAt?e.jsx(f,{children:t({id:"content-releases.page.ReleaseDetails.table.action-published",defaultMessage:"This entry was <b>{isPublish, select, true {published} other {unpublished}}</b>."},{isPublish:Z==="publish",b:ae=>e.jsx(f,{fontWeight:"bold",children:ae})})}):e.jsx(vt,{selected:Z,handleChange:ae=>O(ae,x,[o,ke]),name:`release-action-${x}-type`,disabled:!g})}),!h.releasedAt&&e.jsxs(e.Fragment,{children:[e.jsx(Q,{width:"20%",minWidth:"200px",children:e.jsx(Ot,{action:Z,schema:B?.[j.uid],entry:J,status:Be})}),e.jsx(Q,{children:e.jsx(R,{justifyContent:"flex-end",children:e.jsxs(ne.Root,{children:[e.jsx(ne.ReleaseActionEntryLinkItem,{contentTypeUid:j.uid,documentId:J.documentId,locale:P?.code}),e.jsx(ne.DeleteReleaseActionItem,{releaseId:h.id,actionId:x})]})})})]})]},x))})]})})]},`releases-group-${o}`)),e.jsxs(Y.Root,{...y?.pagination,defaultPageSize:y?.pagination?.pageSize,children:[e.jsx(Y.PageSize,{}),e.jsx(Y.Links,{})]})]})})},Kt=()=>{const{formatMessage:s}=I(),{releaseId:t}=ze(),{toggleNotification:r}=ee(),{formatAPIError:a}=te(),i=Re(),[n,u]=U.useState(!1),[c,p]=U.useState(!1),{isLoading:g,data:M,isSuccess:C}=de({id:t},{skip:!t}),{data:l,isLoading:h}=ve(),[T,{isLoading:m}]=ot(),[b]=lt(),D=()=>{u(o=>!o)},L=()=>d?.timezone?d.timezone:l?.data.defaultTimezone?l.data.defaultTimezone:null,S=()=>p(o=>!o);if(g||h)return e.jsx(pe,{toggleEditReleaseModal:D,toggleWarningSubmit:S,children:e.jsx(F.Loading,{})});if(!t)return e.jsx(ce,{to:".."});const d=C&&M?.data||null,O=d?.name||"",v=L(),y=d?.scheduledAt&&v?Ae(d.scheduledAt,v):null,B=y?ie(y,"yyyy-MM-dd"):void 0,k=y?ie(y,"HH:mm"):"",E=async o=>{const x=await T({id:t,name:o.name,scheduledAt:o.scheduledAt,timezone:o.timezone});"data"in x?(r({type:"success",message:s({id:"content-releases.modal.release-updated-notification-success",defaultMessage:"Release updated."})}),D()):K(x.error)?r({type:"danger",message:a(x.error)}):r({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occurred"})})},z=async()=>{const o=await b({id:t});"data"in o?i(".."):K(o.error)?r({type:"danger",message:a(o.error)}):r({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occurred"})})};return e.jsxs(pe,{toggleEditReleaseModal:D,toggleWarningSubmit:S,children:[e.jsx(Xt,{releaseId:t}),e.jsx(Le,{open:n,handleClose:D,handleSubmit:E,isLoading:g||m,initialValues:{name:O||"",scheduledAt:y,date:B,time:k,isScheduled:!!y,timezone:v}}),e.jsx(dt.Root,{open:c,onOpenChange:S,children:e.jsx(ct,{onConfirm:z,children:s({id:"content-releases.dialog.confirmation-message",defaultMessage:"Are you sure you want to delete this release?"})})})]})},ns=()=>e.jsx(F.Protect,{permissions:se.main,children:e.jsxs(Dt,{children:[e.jsx(he,{index:!0,element:e.jsx(Qt,{})}),e.jsx(he,{path:":releaseId",element:e.jsx(Kt,{})})]})});export{ns as App};
