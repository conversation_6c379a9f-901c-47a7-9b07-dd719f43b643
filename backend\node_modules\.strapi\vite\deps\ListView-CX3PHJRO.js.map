{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/ListView.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport * as qs from 'qs';\nimport { useIntl } from 'react-intl';\nimport { Link, useNavigate } from 'react-router-dom';\n\nimport { useGuidedTour } from '../../../../components/GuidedTour/Provider';\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { tours as unstable_tours } from '../../../../components/UnstableGuidedTour/Tours';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useTracking } from '../../../../features/Tracking';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useOnce } from '../../../../hooks/useOnce';\nimport { useRBAC } from '../../../../hooks/useRBAC';\nimport { useDeleteAPITokenMutation, useGetAPITokensQuery } from '../../../../services/apiTokens';\nimport { API_TOKEN_TYPE } from '../../components/Tokens/constants';\nimport { Table } from '../../components/Tokens/Table';\n\nimport type { Data } from '@strapi/types';\n\nconst TABLE_HEADERS = [\n  {\n    name: 'name',\n    label: {\n      id: 'Settings.apiTokens.ListView.headers.name',\n      defaultMessage: 'Name',\n    },\n    sortable: true,\n  },\n  {\n    name: 'description',\n    label: {\n      id: 'Settings.apiTokens.ListView.headers.description',\n      defaultMessage: 'Description',\n    },\n    sortable: false,\n  },\n  {\n    name: 'createdAt',\n    label: {\n      id: 'Settings.apiTokens.ListView.headers.createdAt',\n      defaultMessage: 'Created at',\n    },\n    sortable: false,\n  },\n  {\n    name: 'lastUsedAt',\n    label: {\n      id: 'Settings.apiTokens.ListView.headers.lastUsedAt',\n      defaultMessage: 'Last used',\n    },\n    sortable: false,\n  },\n];\n\nexport const ListView = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['api-tokens']\n  );\n  const {\n    allowedActions: { canRead, canCreate, canDelete, canUpdate },\n  } = useRBAC(permissions);\n  const navigate = useNavigate();\n  const { trackUsage } = useTracking();\n  const startSection = useGuidedTour('ListView', (state) => state.startSection);\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  React.useEffect(() => {\n    startSection('apiTokens');\n  }, [startSection]);\n\n  React.useEffect(() => {\n    navigate({ search: qs.stringify({ sort: 'name:ASC' }, { encode: false }) });\n  }, [navigate]);\n\n  const headers = TABLE_HEADERS.map((header) => ({\n    ...header,\n    label: formatMessage(header.label),\n  }));\n\n  useOnce(() => {\n    trackUsage('willAccessTokenList', {\n      tokenType: API_TOKEN_TYPE,\n    });\n  });\n\n  const { data: apiTokens = [], isLoading, error } = useGetAPITokensQuery();\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  React.useEffect(() => {\n    trackUsage('didAccessTokenList', { number: apiTokens.length, tokenType: API_TOKEN_TYPE });\n  }, [apiTokens, trackUsage]);\n\n  const [deleteToken] = useDeleteAPITokenMutation();\n\n  const handleDelete = async (id: Data.ID) => {\n    try {\n      const res = await deleteToken(id);\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n\n        return;\n      }\n\n      trackUsage('didDeleteToken');\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'Something went wrong',\n        }),\n      });\n    }\n  };\n\n  return (\n    <>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          { name: 'API Tokens' }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        title={formatMessage({ id: 'Settings.apiTokens.title', defaultMessage: 'API Tokens' })}\n        subtitle={formatMessage({\n          id: 'Settings.apiTokens.description',\n          defaultMessage: 'List of generated tokens to consume the API',\n        })}\n        primaryAction={\n          canCreate && (\n            <unstable_tours.apiTokens.CreateAnAPIToken>\n              <LinkButton\n                tag={Link}\n                data-testid=\"create-api-token-button\"\n                startIcon={<Plus />}\n                size=\"S\"\n                onClick={() =>\n                  trackUsage('willAddTokenFromList', {\n                    tokenType: API_TOKEN_TYPE,\n                  })\n                }\n                to=\"/settings/api-tokens/create\"\n              >\n                {formatMessage({\n                  id: 'Settings.apiTokens.create',\n                  defaultMessage: 'Create new API Token',\n                })}\n              </LinkButton>\n            </unstable_tours.apiTokens.CreateAnAPIToken>\n          )\n        }\n      />\n      {!canRead ? (\n        <Page.NoPermissions />\n      ) : (\n        <Page.Main aria-busy={isLoading}>\n          <unstable_tours.apiTokens.Introduction>\n            <Layouts.Content>\n              {apiTokens.length > 0 && (\n                <Table\n                  permissions={{ canRead, canDelete, canUpdate }}\n                  headers={headers}\n                  isLoading={isLoading}\n                  onConfirmDelete={handleDelete}\n                  tokens={apiTokens}\n                  tokenType={API_TOKEN_TYPE}\n                />\n              )}\n              {canCreate && apiTokens.length === 0 ? (\n                <EmptyStateLayout\n                  icon={<EmptyDocuments width=\"16rem\" />}\n                  content={formatMessage({\n                    id: 'Settings.apiTokens.addFirstToken',\n                    defaultMessage: 'Add your first API Token',\n                  })}\n                  action={\n                    <LinkButton\n                      tag={Link}\n                      variant=\"secondary\"\n                      startIcon={<Plus />}\n                      to=\"/settings/api-tokens/create\"\n                    >\n                      {formatMessage({\n                        id: 'Settings.apiTokens.addNewToken',\n                        defaultMessage: 'Add new API Token',\n                      })}\n                    </LinkButton>\n                  }\n                />\n              ) : null}\n              {!canCreate && apiTokens.length === 0 ? (\n                <EmptyStateLayout\n                  icon={<EmptyDocuments width=\"16rem\" />}\n                  content={formatMessage({\n                    id: 'Settings.apiTokens.emptyStateLayout',\n                    defaultMessage: 'You don’t have any content yet...',\n                  })}\n                />\n              ) : null}\n            </Layouts.Content>\n          </unstable_tours.apiTokens.Introduction>\n        </Page.Main>\n      )}\n    </>\n  );\n};\n\nexport const ProtectedListView = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['api-tokens'].main\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListView />\n    </Page.Protect>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAMA,gBAAgB;EACpB;IACEC,MAAM;IACNC,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,UAAU;EACZ;EACA;IACEJ,MAAM;IACNC,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,UAAU;EACZ;EACA;IACEJ,MAAM;IACNC,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,UAAU;EACZ;EACA;IACEJ,MAAM;IACNC,OAAO;MACLC,IAAI;MACJC,gBAAgB;IAClB;IACAC,UAAU;EACZ;AACD;IAEYC,WAAW,MAAA;AACtB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,uBAAMC,UAAUH,YAAYI,aAA5BF,mBAAuC;GAAa;AAEjE,QAAM,EACJG,gBAAgB,EAAEC,SAASC,WAAWC,WAAWC,UAAS,EAAE,IAC1DC,QAAQV,WAAAA;AACZ,QAAMW,WAAWC,YAAAA;AACjB,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAMC,eAAeC,cAAc,YAAY,CAACd,UAAUA,MAAMa,YAAY;AAC5E,QAAM,EAAEE,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpDC,EAAMC,gBAAU,MAAA;AACdN,iBAAa,WAAA;KACZ;IAACA;EAAa,CAAA;AAEjBK,EAAMC,gBAAU,MAAA;AACdV,aAAS;MAAEW,QAAWC,aAAU;QAAEC,MAAM;SAAc;QAAEC,QAAQ;MAAM,CAAA;IAAG,CAAA;KACxE;IAACd;EAAS,CAAA;AAEb,QAAMe,UAAUrC,cAAcsC,IAAI,CAACC,YAAY;IAC7C,GAAGA;IACHrC,OAAOK,cAAcgC,OAAOrC,KAAK;IACnC;AAEAsC,UAAQ,MAAA;AACNhB,eAAW,uBAAuB;MAChCiB,WAAWC;IACb,CAAA;EACF,CAAA;AAEA,QAAM,EAAEC,MAAMC,YAAY,CAAA,GAAIC,WAAWC,MAAK,IAAKC,qBAAAA;AAEnDhB,EAAMC,gBAAU,MAAA;AACd,QAAIc,OAAO;AACTrC,yBAAmB;QACjBuC,MAAM;QACNC,SAASpB,eAAeiB,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAOjB;IAAgBpB;EAAmB,CAAA;AAE9CsB,EAAMC,gBAAU,MAAA;AACdR,eAAW,sBAAsB;MAAE0B,QAAQN,UAAUO;MAAQV,WAAWC;IAAe,CAAA;KACtF;IAACE;IAAWpB;EAAW,CAAA;AAE1B,QAAM,CAAC4B,WAAAA,IAAeC,0BAAAA;AAEtB,QAAMC,eAAe,OAAOnD,OAAAA;AAC1B,QAAI;AACF,YAAMoD,MAAM,MAAMH,YAAYjD,EAAAA;AAE9B,UAAI,WAAWoD,KAAK;AAClB9C,2BAAmB;UACjBuC,MAAM;UACNC,SAASpB,eAAe0B,IAAIT,KAAK;QACnC,CAAA;AAEA;MACF;AAEAtB,iBAAW,gBAAA;IACb,QAAQ;AACNf,yBAAmB;QACjBuC,MAAM;QACNC,SAAS1C,cAAc;UACrBJ,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,aACEoD,yBAAAC,6BAAA;;UACEC,wBAACC,KAAKC,OAAK;kBACRrD,cACC;UAAEJ,IAAI;UAAsBC,gBAAgB;WAC5C;UAAEH,MAAM;QAAa,CAAA;;UAGzByD,wBAACG,QAAQC,QAAM;QACbC,OAAOxD,cAAc;UAAEJ,IAAI;UAA4BC,gBAAgB;QAAa,CAAA;QACpF4D,UAAUzD,cAAc;UACtBJ,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACA6D,eACE/C,iBACEwC,wBAACQ,MAAetB,UAAUuB,kBAAgB;UACxC,cAAAT,wBAACU,YAAAA;YACCC,KAAKC;YACLC,eAAY;YACZC,eAAWd,wBAACe,eAAAA,CAAAA,CAAAA;YACZC,MAAK;YACLC,SAAS,MACPnD,WAAW,wBAAwB;cACjCiB,WAAWC;YACb,CAAA;YAEFkC,IAAG;sBAEFrE,cAAc;cACbJ,IAAI;cACJC,gBAAgB;YAClB,CAAA;;;;MAMT,CAACa,cACAyC,wBAACC,KAAKkB,eAEN,CAAA,CAAA,QAAAnB,wBAACC,KAAKmB,MAAI;QAACC,aAAWlC;QACpB,cAAAa,wBAACQ,MAAetB,UAAUoC,cAAY;wBACpCxB,yBAACK,QAAQoB,SAAO;;cACbrC,UAAUO,SAAS,SAClBO,wBAACwB,OAAAA;gBACCvE,aAAa;kBAAEM;kBAASE;kBAAWC;gBAAU;gBAC7CiB;gBACAQ;gBACAsC,iBAAiB7B;gBACjB8B,QAAQxC;gBACRH,WAAWC;;cAGdxB,aAAa0B,UAAUO,WAAW,QACjCO,wBAAC2B,kBAAAA;gBACCC,UAAM5B,wBAAC6B,cAAAA;kBAAeC,OAAM;;gBAC5BC,SAASlF,cAAc;kBACrBJ,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;gBACAsF,YACEhC,wBAACU,YAAAA;kBACCC,KAAKC;kBACLqB,SAAQ;kBACRnB,eAAWd,wBAACe,eAAAA,CAAAA,CAAAA;kBACZG,IAAG;4BAEFrE,cAAc;oBACbJ,IAAI;oBACJC,gBAAgB;kBAClB,CAAA;;cAIJ,CAAA,IAAA;cACH,CAACc,aAAa0B,UAAUO,WAAW,QAClCO,wBAAC2B,kBAAAA;gBACCC,UAAM5B,wBAAC6B,cAAAA;kBAAeC,OAAM;;gBAC5BC,SAASlF,cAAc;kBACrBJ,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;cAEA,CAAA,IAAA;;;;;;;AAOlB;IAEawF,oBAAoB,MAAA;AAC/B,QAAMjF,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,uBAAMC,UAAUH,YAAYI,aAA5BF,mBAAuC,cAAcgF;GAAAA;AAGlE,aACEnC,wBAACC,KAAKmC,SAAO;IAACnF;IACZ,cAAA+C,wBAACpD,UAAAA,CAAAA,CAAAA;;AAGP;", "names": ["TABLE_HEADERS", "name", "label", "id", "defaultMessage", "sortable", "ListView", "formatMessage", "useIntl", "toggleNotification", "useNotification", "permissions", "useTypedSelector", "state", "admin_app", "settings", "allowedActions", "canRead", "canCreate", "canDelete", "canUpdate", "useRBAC", "navigate", "useNavigate", "trackUsage", "useTracking", "startSection", "useGuidedTour", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "React", "useEffect", "search", "stringify", "sort", "encode", "headers", "map", "header", "useOnce", "tokenType", "API_TOKEN_TYPE", "data", "apiTokens", "isLoading", "error", "useGetAPITokensQuery", "type", "message", "number", "length", "deleteToken", "useDeleteAPITokenMutation", "handleDelete", "res", "_jsxs", "_Fragment", "_jsx", "Page", "Title", "Layouts", "Header", "title", "subtitle", "primaryAction", "unstable_tours", "CreateAnAPIToken", "LinkButton", "tag", "Link", "data-testid", "startIcon", "Plus", "size", "onClick", "to", "NoPermissions", "Main", "aria-busy", "Introduction", "Content", "Table", "onConfirmDelete", "tokens", "EmptyStateLayout", "icon", "EmptyDocuments", "width", "content", "action", "variant", "ProtectedListView", "main", "Protect"]}