import{aw as c,a as u,V as i,ax as p,r,ay as h,az as g,j as l,P as d}from"./strapi-z7ApxZZq.js";const m=()=>{const s=c("/auth/login/:authResponse"),{formatMessage:t}=u(),e=i(),o=p(),a=r.useCallback(()=>{e({pathname:"/auth/oops",search:`?info=${encodeURIComponent(t({id:"Auth.form.button.login.providers.error",defaultMessage:"We cannot connect you through the selected provider."}))}`})},[e,t]);return r.useEffect(()=>{if(s?.params.authResponse==="error"&&a(),s?.params.authResponse==="success"){const n=h("jwtToken");n?(o(g({token:n})),e("/auth/login")):a()}},[o,s,a,e]),l.jsx(d<PERSON>,{})};export{m as AuthResponse};
