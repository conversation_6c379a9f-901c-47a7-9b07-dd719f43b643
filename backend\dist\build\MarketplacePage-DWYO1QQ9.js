import{r as U,a as _,j as n,eE as Re,D as Ne,fh as At,e as N,$ as M,gf as qe,h as We,b1 as kt,b2 as Lt,b as $e,gg as Ot,T as z,b8 as _e,ee as It,gh as Nt,at as yt,bI as Pt,f8 as _t,gi as Dt,gj as Vt,k as xe,w as De,cw as Ft,E as Gt,bX as Ue,b7 as Bt,P as ce,B as ue,s as Ht,H as Xe,gk as Yt,aE as zt,aH as qt,aI as Wt,aM as Ut,c0 as Qe,aC as Ze,aL as Xt,u as Qt,ck as Zt,cP as K,c as Kt,cu as Ee,cx as Jt,gl as es}from"./strapi-z7ApxZZq.js";import{p as he,s as k,c as F,l as Ve,r as ye,a as jt,d as St,b as wt,i as ts,v as ss}from"./lt-BjNpJj8i.js";function rs(t,e){const[s,r]=U.useState(t);return U.useEffect(()=>{const a=setTimeout(()=>{r(t)},e);return()=>{clearTimeout(a)}},[t,e]),s}const as=({handleSelectClear:t,handleSelectChange:e,npmPackageType:s,possibleCategories:r,possibleCollections:a,query:i})=>{const{formatMessage:o}=_(),h=(u,S)=>{const l={[S]:(i[S]??[]).filter(d=>d!==u)};e(l)};return n.jsxs(Re.Root,{children:[n.jsx(Re.Trigger,{children:n.jsx(Ne,{variant:"tertiary",startIcon:n.jsx(At,{}),children:o({id:"app.utils.filters",defaultMessage:"Filters"})})}),n.jsx(Re.Content,{sideOffset:4,children:n.jsxs(N,{padding:3,direction:"column",alignItems:"stretch",gap:1,children:[n.jsx(Ke,{message:o({id:"admin.pages.MarketPlacePage.filters.collections",defaultMessage:"Collections"}),value:i?.collections||[],onChange:u=>{e({collections:u})},onClear:()=>t("collections"),possibleFilters:a,customizeContent:u=>o({id:"admin.pages.MarketPlacePage.filters.collectionsSelected",defaultMessage:"{count, plural, =0 {No collections} one {# collection} other {# collections}} selected"},{count:u?.length??0})}),s==="plugin"&&n.jsx(Ke,{message:o({id:"admin.pages.MarketPlacePage.filters.categories",defaultMessage:"Categories"}),value:i?.categories||[],onChange:u=>{e({categories:u})},onClear:()=>t("categories"),possibleFilters:r,customizeContent:u=>o({id:"admin.pages.MarketPlacePage.filters.categoriesSelected",defaultMessage:"{count, plural, =0 {No categories} one {# category} other {# categories}} selected"},{count:u?.length??0})})]})}),i.collections?.map(u=>n.jsx(M,{padding:1,children:n.jsx(qe,{icon:n.jsx(We,{}),onClick:()=>h(u,"collections"),children:u})},u)),s==="plugin"&&i.categories?.map(u=>n.jsx(M,{padding:1,children:n.jsx(qe,{icon:n.jsx(We,{}),onClick:()=>h(u,"categories"),children:u})},u))]})},Ke=({message:t,value:e,onChange:s,possibleFilters:r,onClear:a,customizeContent:i})=>n.jsx(kt,{"data-testid":`${t}-button`,"aria-label":t,placeholder:t,onChange:s,onClear:a,value:e,customizeContent:i,children:Object.entries(r).map(([o,h])=>n.jsx(Lt,{"data-testid":`${o}-${h}`,value:o,children:`${o} (${h})`},o))}),ns=he,is=(t,e)=>{const s=ns(t.trim().replace(/^[=v]+/,""),e);return s?s.version:null};var os=is;const Je=k,ls=(t,e,s,r,a)=>{typeof s=="string"&&(a=r,r=s,s=void 0);try{return new Je(t instanceof Je?t.version:t,s).inc(e,r,a).version}catch{return null}};var cs=ls;const et=he,us=(t,e)=>{const s=et(t,null,!0),r=et(e,null,!0),a=s.compare(r);if(a===0)return null;const i=a>0,o=i?s:r,h=i?r:s,u=!!o.prerelease.length;if(!!h.prerelease.length&&!u)return!h.patch&&!h.minor?"major":o.patch?"patch":o.minor?"minor":"major";const l=u?"pre":"";return s.major!==r.major?l+"major":s.minor!==r.minor?l+"minor":s.patch!==r.patch?l+"patch":"prerelease"};var hs=us;const ds=k,fs=(t,e)=>new ds(t,e).major;var ps=fs;const gs=k,ms=(t,e)=>new gs(t,e).minor;var vs=ms;const $s=k,xs=(t,e)=>new $s(t,e).patch;var ys=xs;const Ps=he,js=(t,e)=>{const s=Ps(t,e);return s&&s.prerelease.length?s.prerelease:null};var Ss=js;const ws=F,bs=(t,e,s)=>ws(e,t,s);var Rs=bs;const Es=F,Cs=(t,e)=>Es(t,e,!0);var Ts=Cs;const tt=k,Ms=(t,e,s)=>{const r=new tt(t,s),a=new tt(e,s);return r.compare(a)||r.compareBuild(a)};var Fe=Ms;const As=Fe,ks=(t,e)=>t.sort((s,r)=>As(s,r,e));var Ls=ks;const Os=Fe,Is=(t,e)=>t.sort((s,r)=>Os(r,s,e));var Ns=Is;const _s=F,Ds=(t,e,s)=>_s(t,e,s)>0;var Pe=Ds;const Vs=F,Fs=(t,e,s)=>Vs(t,e,s)===0;var bt=Fs;const Gs=F,Bs=(t,e,s)=>Gs(t,e,s)!==0;var Rt=Bs;const Hs=F,Ys=(t,e,s)=>Hs(t,e,s)>=0;var Ge=Ys;const zs=F,qs=(t,e,s)=>zs(t,e,s)<=0;var Be=qs;const Ws=bt,Us=Rt,Xs=Pe,Qs=Ge,Zs=Ve,Ks=Be,Js=(t,e,s,r)=>{switch(e){case"===":return typeof t=="object"&&(t=t.version),typeof s=="object"&&(s=s.version),t===s;case"!==":return typeof t=="object"&&(t=t.version),typeof s=="object"&&(s=s.version),t!==s;case"":case"=":case"==":return Ws(t,s,r);case"!=":return Us(t,s,r);case">":return Xs(t,s,r);case">=":return Qs(t,s,r);case"<":return Zs(t,s,r);case"<=":return Ks(t,s,r);default:throw new TypeError(`Invalid operator: ${e}`)}};var Et=Js;const er=k,tr=he,{safeRe:fe,t:pe}=ye,sr=(t,e)=>{if(t instanceof er)return t;if(typeof t=="number"&&(t=String(t)),typeof t!="string")return null;e=e||{};let s=null;if(!e.rtl)s=t.match(fe[pe.COERCE]);else{let r;for(;(r=fe[pe.COERCERTL].exec(t))&&(!s||s.index+s[0].length!==t.length);)(!s||r.index+r[0].length!==s.index+s[0].length)&&(s=r),fe[pe.COERCERTL].lastIndex=r.index+r[1].length+r[2].length;fe[pe.COERCERTL].lastIndex=-1}return s===null?null:tr(`${s[2]}.${s[3]||"0"}.${s[4]||"0"}`,e)};var rr=sr,Ce,st;function ar(){return st||(st=1,Ce=function(t){t.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}),Ce}var nr=j;j.Node=X;j.create=j;function j(t){var e=this;if(e instanceof j||(e=new j),e.tail=null,e.head=null,e.length=0,t&&typeof t.forEach=="function")t.forEach(function(a){e.push(a)});else if(arguments.length>0)for(var s=0,r=arguments.length;s<r;s++)e.push(arguments[s]);return e}j.prototype.removeNode=function(t){if(t.list!==this)throw new Error("removing node which does not belong to this list");var e=t.next,s=t.prev;return e&&(e.prev=s),s&&(s.next=e),t===this.head&&(this.head=e),t===this.tail&&(this.tail=s),t.list.length--,t.next=null,t.prev=null,t.list=null,e};j.prototype.unshiftNode=function(t){if(t!==this.head){t.list&&t.list.removeNode(t);var e=this.head;t.list=this,t.next=e,e&&(e.prev=t),this.head=t,this.tail||(this.tail=t),this.length++}};j.prototype.pushNode=function(t){if(t!==this.tail){t.list&&t.list.removeNode(t);var e=this.tail;t.list=this,t.prev=e,e&&(e.next=t),this.tail=t,this.head||(this.head=t),this.length++}};j.prototype.push=function(){for(var t=0,e=arguments.length;t<e;t++)or(this,arguments[t]);return this.length};j.prototype.unshift=function(){for(var t=0,e=arguments.length;t<e;t++)lr(this,arguments[t]);return this.length};j.prototype.pop=function(){if(this.tail){var t=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,t}};j.prototype.shift=function(){if(this.head){var t=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,t}};j.prototype.forEach=function(t,e){e=e||this;for(var s=this.head,r=0;s!==null;r++)t.call(e,s.value,r,this),s=s.next};j.prototype.forEachReverse=function(t,e){e=e||this;for(var s=this.tail,r=this.length-1;s!==null;r--)t.call(e,s.value,r,this),s=s.prev};j.prototype.get=function(t){for(var e=0,s=this.head;s!==null&&e<t;e++)s=s.next;if(e===t&&s!==null)return s.value};j.prototype.getReverse=function(t){for(var e=0,s=this.tail;s!==null&&e<t;e++)s=s.prev;if(e===t&&s!==null)return s.value};j.prototype.map=function(t,e){e=e||this;for(var s=new j,r=this.head;r!==null;)s.push(t.call(e,r.value,this)),r=r.next;return s};j.prototype.mapReverse=function(t,e){e=e||this;for(var s=new j,r=this.tail;r!==null;)s.push(t.call(e,r.value,this)),r=r.prev;return s};j.prototype.reduce=function(t,e){var s,r=this.head;if(arguments.length>1)s=e;else if(this.head)r=this.head.next,s=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var a=0;r!==null;a++)s=t(s,r.value,a),r=r.next;return s};j.prototype.reduceReverse=function(t,e){var s,r=this.tail;if(arguments.length>1)s=e;else if(this.tail)r=this.tail.prev,s=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var a=this.length-1;r!==null;a--)s=t(s,r.value,a),r=r.prev;return s};j.prototype.toArray=function(){for(var t=new Array(this.length),e=0,s=this.head;s!==null;e++)t[e]=s.value,s=s.next;return t};j.prototype.toArrayReverse=function(){for(var t=new Array(this.length),e=0,s=this.tail;s!==null;e++)t[e]=s.value,s=s.prev;return t};j.prototype.slice=function(t,e){e=e||this.length,e<0&&(e+=this.length),t=t||0,t<0&&(t+=this.length);var s=new j;if(e<t||e<0)return s;t<0&&(t=0),e>this.length&&(e=this.length);for(var r=0,a=this.head;a!==null&&r<t;r++)a=a.next;for(;a!==null&&r<e;r++,a=a.next)s.push(a.value);return s};j.prototype.sliceReverse=function(t,e){e=e||this.length,e<0&&(e+=this.length),t=t||0,t<0&&(t+=this.length);var s=new j;if(e<t||e<0)return s;t<0&&(t=0),e>this.length&&(e=this.length);for(var r=this.length,a=this.tail;a!==null&&r>e;r--)a=a.prev;for(;a!==null&&r>t;r--,a=a.prev)s.push(a.value);return s};j.prototype.splice=function(t,e,...s){t>this.length&&(t=this.length-1),t<0&&(t=this.length+t);for(var r=0,a=this.head;a!==null&&r<t;r++)a=a.next;for(var i=[],r=0;a&&r<e;r++)i.push(a.value),a=this.removeNode(a);a===null&&(a=this.tail),a!==this.head&&a!==this.tail&&(a=a.prev);for(var r=0;r<s.length;r++)a=ir(this,a,s[r]);return i};j.prototype.reverse=function(){for(var t=this.head,e=this.tail,s=t;s!==null;s=s.prev){var r=s.prev;s.prev=s.next,s.next=r}return this.head=e,this.tail=t,this};function ir(t,e,s){var r=e===t.head?new X(s,null,e,t):new X(s,e,e.next,t);return r.next===null&&(t.tail=r),r.prev===null&&(t.head=r),t.length++,r}function or(t,e){t.tail=new X(e,t.tail,null,t),t.head||(t.head=t.tail),t.length++}function lr(t,e){t.head=new X(e,null,t.head,t),t.tail||(t.tail=t.head),t.length++}function X(t,e,s,r){if(!(this instanceof X))return new X(t,e,s,r);this.list=r,this.value=t,e?(e.next=this,this.prev=e):this.prev=null,s?(s.prev=this,this.next=s):this.next=null}try{ar()(j)}catch{}const cr=nr,q=Symbol("max"),B=Symbol("length"),J=Symbol("lengthCalculator"),le=Symbol("allowStale"),W=Symbol("maxAge"),G=Symbol("dispose"),rt=Symbol("noDisposeOnSet"),C=Symbol("lruList"),I=Symbol("cache"),Ct=Symbol("updateAgeOnGet"),Te=()=>1;class ur{constructor(e){if(typeof e=="number"&&(e={max:e}),e||(e={}),e.max&&(typeof e.max!="number"||e.max<0))throw new TypeError("max must be a non-negative number");this[q]=e.max||1/0;const s=e.length||Te;if(this[J]=typeof s!="function"?Te:s,this[le]=e.stale||!1,e.maxAge&&typeof e.maxAge!="number")throw new TypeError("maxAge must be a number");this[W]=e.maxAge||0,this[G]=e.dispose,this[rt]=e.noDisposeOnSet||!1,this[Ct]=e.updateAgeOnGet||!1,this.reset()}set max(e){if(typeof e!="number"||e<0)throw new TypeError("max must be a non-negative number");this[q]=e||1/0,ie(this)}get max(){return this[q]}set allowStale(e){this[le]=!!e}get allowStale(){return this[le]}set maxAge(e){if(typeof e!="number")throw new TypeError("maxAge must be a non-negative number");this[W]=e,ie(this)}get maxAge(){return this[W]}set lengthCalculator(e){typeof e!="function"&&(e=Te),e!==this[J]&&(this[J]=e,this[B]=0,this[C].forEach(s=>{s.length=this[J](s.value,s.key),this[B]+=s.length})),ie(this)}get lengthCalculator(){return this[J]}get length(){return this[B]}get itemCount(){return this[C].length}rforEach(e,s){s=s||this;for(let r=this[C].tail;r!==null;){const a=r.prev;at(this,e,r,s),r=a}}forEach(e,s){s=s||this;for(let r=this[C].head;r!==null;){const a=r.next;at(this,e,r,s),r=a}}keys(){return this[C].toArray().map(e=>e.key)}values(){return this[C].toArray().map(e=>e.value)}reset(){this[G]&&this[C]&&this[C].length&&this[C].forEach(e=>this[G](e.key,e.value)),this[I]=new Map,this[C]=new cr,this[B]=0}dump(){return this[C].map(e=>me(this,e)?!1:{k:e.key,v:e.value,e:e.now+(e.maxAge||0)}).toArray().filter(e=>e)}dumpLru(){return this[C]}set(e,s,r){if(r=r||this[W],r&&typeof r!="number")throw new TypeError("maxAge must be a number");const a=r?Date.now():0,i=this[J](s,e);if(this[I].has(e)){if(i>this[q])return te(this,this[I].get(e)),!1;const u=this[I].get(e).value;return this[G]&&(this[rt]||this[G](e,u.value)),u.now=a,u.maxAge=r,u.value=s,this[B]+=i-u.length,u.length=i,this.get(e),ie(this),!0}const o=new hr(e,s,i,a,r);return o.length>this[q]?(this[G]&&this[G](e,s),!1):(this[B]+=o.length,this[C].unshift(o),this[I].set(e,this[C].head),ie(this),!0)}has(e){if(!this[I].has(e))return!1;const s=this[I].get(e).value;return!me(this,s)}get(e){return Me(this,e,!0)}peek(e){return Me(this,e,!1)}pop(){const e=this[C].tail;return e?(te(this,e),e.value):null}del(e){te(this,this[I].get(e))}load(e){this.reset();const s=Date.now();for(let r=e.length-1;r>=0;r--){const a=e[r],i=a.e||0;if(i===0)this.set(a.k,a.v);else{const o=i-s;o>0&&this.set(a.k,a.v,o)}}}prune(){this[I].forEach((e,s)=>Me(this,s,!1))}}const Me=(t,e,s)=>{const r=t[I].get(e);if(r){const a=r.value;if(me(t,a)){if(te(t,r),!t[le])return}else s&&(t[Ct]&&(r.value.now=Date.now()),t[C].unshiftNode(r));return a.value}},me=(t,e)=>{if(!e||!e.maxAge&&!t[W])return!1;const s=Date.now()-e.now;return e.maxAge?s>e.maxAge:t[W]&&s>t[W]},ie=t=>{if(t[B]>t[q])for(let e=t[C].tail;t[B]>t[q]&&e!==null;){const s=e.prev;te(t,e),e=s}},te=(t,e)=>{if(e){const s=e.value;t[G]&&t[G](s.key,s.value),t[B]-=s.length,t[I].delete(s.key),t[C].removeNode(e)}};class hr{constructor(e,s,r,a,i){this.key=e,this.value=s,this.length=r,this.now=a,this.maxAge=i||0}}const at=(t,e,s,r)=>{let a=s.value;me(t,a)&&(te(t,s),t[le]||(a=void 0)),a&&e.call(r,a.value,a.key,t)};var dr=ur,Ae,nt;function D(){if(nt)return Ae;nt=1;class t{constructor(c,$){if($=r($),c instanceof t)return c.loose===!!$.loose&&c.includePrerelease===!!$.includePrerelease?c:new t(c.raw,$);if(c instanceof a)return this.raw=c.value,this.set=[[c]],this.format(),this;if(this.options=$,this.loose=!!$.loose,this.includePrerelease=!!$.includePrerelease,this.raw=c.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(m=>this.parseRange(m.trim())).filter(m=>m.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const m=this.set[0];if(this.set=this.set.filter(v=>!O(v[0])),this.set.length===0)this.set=[m];else if(this.set.length>1){for(const v of this.set)if(v.length===1&&H(v[0])){this.set=[v];break}}}this.format()}format(){return this.range=this.set.map(c=>c.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange(c){const m=((this.options.includePrerelease&&y)|(this.options.loose&&p))+":"+c,v=s.get(m);if(v)return v;const g=this.options.loose,x=g?h[u.HYPHENRANGELOOSE]:h[u.HYPHENRANGE];c=c.replace(x,L(this.options.includePrerelease)),i("hyphen replace",c),c=c.replace(h[u.COMPARATORTRIM],S),i("comparator trim",c),c=c.replace(h[u.TILDETRIM],l),i("tilde trim",c),c=c.replace(h[u.CARETTRIM],d),i("caret trim",c);let w=c.split(" ").map(R=>Z(R,this.options)).join(" ").split(/\s+/).map(R=>T(R,this.options));g&&(w=w.filter(R=>(i("loose invalid filter",R,this.options),!!R.match(h[u.COMPARATORLOOSE])))),i("range list",w);const P=new Map,b=w.map(R=>new a(R,this.options));for(const R of b){if(O(R))return[R];P.set(R.value,R)}P.size>1&&P.has("")&&P.delete("");const A=[...P.values()];return s.set(m,A),A}intersects(c,$){if(!(c instanceof t))throw new TypeError("a Range is required");return this.set.some(m=>Q(m,$)&&c.set.some(v=>Q(v,$)&&m.every(g=>v.every(x=>g.intersects(x,$)))))}test(c){if(!c)return!1;if(typeof c=="string")try{c=new o(c,this.options)}catch{return!1}for(let $=0;$<this.set.length;$++)if(be(this.set[$],c,this.options))return!0;return!1}}Ae=t;const e=dr,s=new e({max:1e3}),r=wt,a=je(),i=St,o=k,{safeRe:h,t:u,comparatorTrimReplace:S,tildeTrimReplace:l,caretTrimReplace:d}=ye,{FLAG_INCLUDE_PRERELEASE:y,FLAG_LOOSE:p}=jt,O=f=>f.value==="<0.0.0-0",H=f=>f.value==="",Q=(f,c)=>{let $=!0;const m=f.slice();let v=m.pop();for(;$&&m.length;)$=m.every(g=>v.intersects(g,c)),v=m.pop();return $},Z=(f,c)=>(i("comp",f,c),f=Y(f,c),i("caret",f),f=se(f,c),i("tildes",f),f=ae(f,c),i("xrange",f),f=de(f,c),i("stars",f),f),E=f=>!f||f.toLowerCase()==="x"||f==="*",se=(f,c)=>f.trim().split(/\s+/).map($=>re($,c)).join(" "),re=(f,c)=>{const $=c.loose?h[u.TILDELOOSE]:h[u.TILDE];return f.replace($,(m,v,g,x,w)=>{i("tilde",f,m,v,g,x,w);let P;return E(v)?P="":E(g)?P=`>=${v}.0.0 <${+v+1}.0.0-0`:E(x)?P=`>=${v}.${g}.0 <${v}.${+g+1}.0-0`:w?(i("replaceTilde pr",w),P=`>=${v}.${g}.${x}-${w} <${v}.${+g+1}.0-0`):P=`>=${v}.${g}.${x} <${v}.${+g+1}.0-0`,i("tilde return",P),P})},Y=(f,c)=>f.trim().split(/\s+/).map($=>V($,c)).join(" "),V=(f,c)=>{i("caret",f,c);const $=c.loose?h[u.CARETLOOSE]:h[u.CARET],m=c.includePrerelease?"-0":"";return f.replace($,(v,g,x,w,P)=>{i("caret",f,v,g,x,w,P);let b;return E(g)?b="":E(x)?b=`>=${g}.0.0${m} <${+g+1}.0.0-0`:E(w)?g==="0"?b=`>=${g}.${x}.0${m} <${g}.${+x+1}.0-0`:b=`>=${g}.${x}.0${m} <${+g+1}.0.0-0`:P?(i("replaceCaret pr",P),g==="0"?x==="0"?b=`>=${g}.${x}.${w}-${P} <${g}.${x}.${+w+1}-0`:b=`>=${g}.${x}.${w}-${P} <${g}.${+x+1}.0-0`:b=`>=${g}.${x}.${w}-${P} <${+g+1}.0.0-0`):(i("no pr"),g==="0"?x==="0"?b=`>=${g}.${x}.${w}${m} <${g}.${x}.${+w+1}-0`:b=`>=${g}.${x}.${w}${m} <${g}.${+x+1}.0-0`:b=`>=${g}.${x}.${w} <${+g+1}.0.0-0`),i("caret return",b),b})},ae=(f,c)=>(i("replaceXRanges",f,c),f.split(/\s+/).map($=>we($,c)).join(" ")),we=(f,c)=>{f=f.trim();const $=c.loose?h[u.XRANGELOOSE]:h[u.XRANGE];return f.replace($,(m,v,g,x,w,P)=>{i("xRange",f,m,v,g,x,w,P);const b=E(g),A=b||E(x),R=A||E(w),ne=R;return v==="="&&ne&&(v=""),P=c.includePrerelease?"-0":"",b?v===">"||v==="<"?m="<0.0.0-0":m="*":v&&ne?(A&&(x=0),w=0,v===">"?(v=">=",A?(g=+g+1,x=0,w=0):(x=+x+1,w=0)):v==="<="&&(v="<",A?g=+g+1:x=+x+1),v==="<"&&(P="-0"),m=`${v+g}.${x}.${w}${P}`):A?m=`>=${g}.0.0${P} <${+g+1}.0.0-0`:R&&(m=`>=${g}.${x}.0${P} <${g}.${+x+1}.0-0`),i("xRange return",m),m})},de=(f,c)=>(i("replaceStars",f,c),f.trim().replace(h[u.STAR],"")),T=(f,c)=>(i("replaceGTE0",f,c),f.trim().replace(h[c.includePrerelease?u.GTE0PRE:u.GTE0],"")),L=f=>(c,$,m,v,g,x,w,P,b,A,R,ne,Za)=>(E(m)?$="":E(v)?$=`>=${m}.0.0${f?"-0":""}`:E(g)?$=`>=${m}.${v}.0${f?"-0":""}`:x?$=`>=${$}`:$=`>=${$}${f?"-0":""}`,E(b)?P="":E(A)?P=`<${+b+1}.0.0-0`:E(R)?P=`<${b}.${+A+1}.0-0`:ne?P=`<=${b}.${A}.${R}-${ne}`:f?P=`<${b}.${A}.${+R+1}-0`:P=`<=${P}`,`${$} ${P}`.trim()),be=(f,c,$)=>{for(let m=0;m<f.length;m++)if(!f[m].test(c))return!1;if(c.prerelease.length&&!$.includePrerelease){for(let m=0;m<f.length;m++)if(i(f[m].semver),f[m].semver!==a.ANY&&f[m].semver.prerelease.length>0){const v=f[m].semver;if(v.major===c.major&&v.minor===c.minor&&v.patch===c.patch)return!0}return!1}return!0};return Ae}var ke,it;function je(){if(it)return ke;it=1;const t=Symbol("SemVer ANY");class e{static get ANY(){return t}constructor(l,d){if(d=s(d),l instanceof e){if(l.loose===!!d.loose)return l;l=l.value}l=l.trim().split(/\s+/).join(" "),o("comparator",l,d),this.options=d,this.loose=!!d.loose,this.parse(l),this.semver===t?this.value="":this.value=this.operator+this.semver.version,o("comp",this)}parse(l){const d=this.options.loose?r[a.COMPARATORLOOSE]:r[a.COMPARATOR],y=l.match(d);if(!y)throw new TypeError(`Invalid comparator: ${l}`);this.operator=y[1]!==void 0?y[1]:"",this.operator==="="&&(this.operator=""),y[2]?this.semver=new h(y[2],this.options.loose):this.semver=t}toString(){return this.value}test(l){if(o("Comparator.test",l,this.options.loose),this.semver===t||l===t)return!0;if(typeof l=="string")try{l=new h(l,this.options)}catch{return!1}return i(l,this.operator,this.semver,this.options)}intersects(l,d){if(!(l instanceof e))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new u(l.value,d).test(this.value):l.operator===""?l.value===""?!0:new u(this.value,d).test(l.semver):(d=s(d),d.includePrerelease&&(this.value==="<0.0.0-0"||l.value==="<0.0.0-0")||!d.includePrerelease&&(this.value.startsWith("<0.0.0")||l.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&l.operator.startsWith(">")||this.operator.startsWith("<")&&l.operator.startsWith("<")||this.semver.version===l.semver.version&&this.operator.includes("=")&&l.operator.includes("=")||i(this.semver,"<",l.semver,d)&&this.operator.startsWith(">")&&l.operator.startsWith("<")||i(this.semver,">",l.semver,d)&&this.operator.startsWith("<")&&l.operator.startsWith(">")))}}ke=e;const s=wt,{safeRe:r,t:a}=ye,i=Et,o=St,h=k,u=D();return ke}const fr=D(),pr=(t,e,s)=>{try{e=new fr(e,s)}catch{return!1}return e.test(t)};var Se=pr;const gr=D(),mr=(t,e)=>new gr(t,e).set.map(s=>s.map(r=>r.value).join(" ").trim().split(" "));var vr=mr;const $r=k,xr=D(),yr=(t,e,s)=>{let r=null,a=null,i=null;try{i=new xr(e,s)}catch{return null}return t.forEach(o=>{i.test(o)&&(!r||a.compare(o)===-1)&&(r=o,a=new $r(r,s))}),r};var Pr=yr;const jr=k,Sr=D(),wr=(t,e,s)=>{let r=null,a=null,i=null;try{i=new Sr(e,s)}catch{return null}return t.forEach(o=>{i.test(o)&&(!r||a.compare(o)===1)&&(r=o,a=new jr(r,s))}),r};var br=wr;const Le=k,Rr=D(),ot=Pe,Er=(t,e)=>{t=new Rr(t,e);let s=new Le("0.0.0");if(t.test(s)||(s=new Le("0.0.0-0"),t.test(s)))return s;s=null;for(let r=0;r<t.set.length;++r){const a=t.set[r];let i=null;a.forEach(o=>{const h=new Le(o.semver.version);switch(o.operator){case">":h.prerelease.length===0?h.patch++:h.prerelease.push(0),h.raw=h.format();case"":case">=":(!i||ot(h,i))&&(i=h);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${o.operator}`)}}),i&&(!s||ot(s,i))&&(s=i)}return s&&t.test(s)?s:null};var Cr=Er;const Tr=D(),Mr=(t,e)=>{try{return new Tr(t,e).range||"*"}catch{return null}};var Ar=Mr;const kr=k,Tt=je(),{ANY:Lr}=Tt,Or=D(),Ir=Se,lt=Pe,ct=Ve,Nr=Be,_r=Ge,Dr=(t,e,s,r)=>{t=new kr(t,r),e=new Or(e,r);let a,i,o,h,u;switch(s){case">":a=lt,i=Nr,o=ct,h=">",u=">=";break;case"<":a=ct,i=_r,o=lt,h="<",u="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(Ir(t,e,r))return!1;for(let S=0;S<e.set.length;++S){const l=e.set[S];let d=null,y=null;if(l.forEach(p=>{p.semver===Lr&&(p=new Tt(">=0.0.0")),d=d||p,y=y||p,a(p.semver,d.semver,r)?d=p:o(p.semver,y.semver,r)&&(y=p)}),d.operator===h||d.operator===u||(!y.operator||y.operator===h)&&i(t,y.semver))return!1;if(y.operator===u&&o(t,y.semver))return!1}return!0};var He=Dr;const Vr=He,Fr=(t,e,s)=>Vr(t,e,">",s);var Gr=Fr;const Br=He,Hr=(t,e,s)=>Br(t,e,"<",s);var Yr=Hr;const ut=D(),zr=(t,e,s)=>(t=new ut(t,s),e=new ut(e,s),t.intersects(e,s));var qr=zr;const Wr=Se,Ur=F;var Xr=(t,e,s)=>{const r=[];let a=null,i=null;const o=t.sort((l,d)=>Ur(l,d,s));for(const l of o)Wr(l,e,s)?(i=l,a||(a=l)):(i&&r.push([a,i]),i=null,a=null);a&&r.push([a,null]);const h=[];for(const[l,d]of r)l===d?h.push(l):!d&&l===o[0]?h.push("*"):d?l===o[0]?h.push(`<=${d}`):h.push(`${l} - ${d}`):h.push(`>=${l}`);const u=h.join(" || "),S=typeof e.raw=="string"?e.raw:String(e);return u.length<S.length?u:e};const ht=D(),Ye=je(),{ANY:Oe}=Ye,oe=Se,ze=F,Qr=(t,e,s={})=>{if(t===e)return!0;t=new ht(t,s),e=new ht(e,s);let r=!1;e:for(const a of t.set){for(const i of e.set){const o=Kr(a,i,s);if(r=r||o!==null,o)continue e}if(r)return!1}return!0},Zr=[new Ye(">=0.0.0-0")],dt=[new Ye(">=0.0.0")],Kr=(t,e,s)=>{if(t===e)return!0;if(t.length===1&&t[0].semver===Oe){if(e.length===1&&e[0].semver===Oe)return!0;s.includePrerelease?t=Zr:t=dt}if(e.length===1&&e[0].semver===Oe){if(s.includePrerelease)return!0;e=dt}const r=new Set;let a,i;for(const p of t)p.operator===">"||p.operator===">="?a=ft(a,p,s):p.operator==="<"||p.operator==="<="?i=pt(i,p,s):r.add(p.semver);if(r.size>1)return null;let o;if(a&&i){if(o=ze(a.semver,i.semver,s),o>0)return null;if(o===0&&(a.operator!==">="||i.operator!=="<="))return null}for(const p of r){if(a&&!oe(p,String(a),s)||i&&!oe(p,String(i),s))return null;for(const O of e)if(!oe(p,String(O),s))return!1;return!0}let h,u,S,l,d=i&&!s.includePrerelease&&i.semver.prerelease.length?i.semver:!1,y=a&&!s.includePrerelease&&a.semver.prerelease.length?a.semver:!1;d&&d.prerelease.length===1&&i.operator==="<"&&d.prerelease[0]===0&&(d=!1);for(const p of e){if(l=l||p.operator===">"||p.operator===">=",S=S||p.operator==="<"||p.operator==="<=",a){if(y&&p.semver.prerelease&&p.semver.prerelease.length&&p.semver.major===y.major&&p.semver.minor===y.minor&&p.semver.patch===y.patch&&(y=!1),p.operator===">"||p.operator===">="){if(h=ft(a,p,s),h===p&&h!==a)return!1}else if(a.operator===">="&&!oe(a.semver,String(p),s))return!1}if(i){if(d&&p.semver.prerelease&&p.semver.prerelease.length&&p.semver.major===d.major&&p.semver.minor===d.minor&&p.semver.patch===d.patch&&(d=!1),p.operator==="<"||p.operator==="<="){if(u=pt(i,p,s),u===p&&u!==i)return!1}else if(i.operator==="<="&&!oe(i.semver,String(p),s))return!1}if(!p.operator&&(i||a)&&o!==0)return!1}return!(a&&S&&!i&&o!==0||i&&l&&!a&&o!==0||y||d)},ft=(t,e,s)=>{if(!t)return e;const r=ze(t.semver,e.semver,s);return r>0?t:r<0||e.operator===">"&&t.operator===">="?e:t},pt=(t,e,s)=>{if(!t)return e;const r=ze(t.semver,e.semver,s);return r<0?t:r>0||e.operator==="<"&&t.operator==="<="?e:t};var Jr=Qr;const Ie=ye,gt=jt,ea=k,mt=ts,ta=he,sa=ss,ra=os,aa=cs,na=hs,ia=ps,oa=vs,la=ys,ca=Ss,ua=F,ha=Rs,da=Ts,fa=Fe,pa=Ls,ga=Ns,ma=Pe,va=Ve,$a=bt,xa=Rt,ya=Ge,Pa=Be,ja=Et,Sa=rr,wa=je(),ba=D(),Ra=Se,Ea=vr,Ca=Pr,Ta=br,Ma=Cr,Aa=Ar,ka=He,La=Gr,Oa=Yr,Ia=qr,Na=Xr,_a=Jr;var ve={parse:ta,valid:sa,clean:ra,inc:aa,diff:na,major:ia,minor:oa,patch:la,prerelease:ca,compare:ua,rcompare:ha,compareLoose:da,compareBuild:fa,sort:pa,rsort:ga,gt:ma,lt:va,eq:$a,neq:xa,gte:ya,lte:Pa,cmp:ja,coerce:Sa,Comparator:wa,Range:ba,satisfies:Ra,toComparators:Ea,maxSatisfying:Ca,minSatisfying:Ta,minVersion:Ma,validRange:Aa,outside:ka,gtr:La,ltr:Oa,intersects:Ia,simplifyRange:Na,subset:_a,SemVer:ea,re:Ie.re,src:Ie.src,tokens:Ie.t,SEMVER_SPEC_VERSION:gt.SEMVER_SPEC_VERSION,RELEASE_TYPES:gt.RELEASE_TYPES,compareIdentifiers:mt.compareIdentifiers,rcompareIdentifiers:mt.rcompareIdentifiers};const Da=xe(z)`
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
`,Va=({npmPackage:t,isInstalled:e,useYarn:s,isInDevelopmentMode:r,npmPackageType:a,strapiAppVersion:i})=>{const{attributes:o}=t,{formatMessage:h}=_(),{trackUsage:u}=$e(),S=s?`yarn add ${o.npmPackageName}`:`npm install ${o.npmPackageName}`,l=h({id:"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi",defaultMessage:"Made by Strapi"}),d=`https://market.strapi.io/${Ot.plural(a)}/${o.slug}`,y=ve.validRange(o.strapiVersion),p=y?ve.satisfies(i??"",y):!1;return n.jsxs(N,{direction:"column",justifyContent:"space-between",paddingTop:4,paddingRight:4,paddingBottom:4,paddingLeft:4,hasRadius:!0,background:"neutral0",shadow:"tableShadow",height:"100%",alignItems:"normal","data-testid":"npm-package-card",children:[n.jsxs(M,{children:[n.jsxs(N,{direction:"row",justifyContent:"space-between",alignItems:"flex-start",children:[n.jsx(M,{tag:"img",src:o.logo.url,alt:`${o.name} logo`,hasRadius:!0,width:11,height:11}),n.jsx(Ba,{githubStars:o.githubStars,npmDownloads:o.npmDownloads,npmPackageType:a})]}),n.jsx(M,{paddingTop:4,children:n.jsx(z,{tag:"h3",variant:"delta",children:n.jsxs(N,{alignItems:"center",gap:o.validated&&!o.madeByStrapi?2:1,children:[o.name,o.validated&&!o.madeByStrapi&&n.jsx(_e,{description:h({id:"admin.pages.MarketPlacePage.plugin.tooltip.verified",defaultMessage:"Plugin verified by Strapi"}),children:n.jsx(It,{fill:"success600"})}),o.madeByStrapi&&n.jsx(_e,{description:l,children:n.jsx(M,{tag:"img",src:Nt,alt:l,width:6,height:"auto"})})]})})}),n.jsx(M,{paddingTop:2,children:n.jsx(Da,{tag:"p",variant:"omega",textColor:"neutral600",children:o.description})})]}),n.jsxs(N,{gap:2,style:{alignSelf:"flex-end"},paddingTop:6,children:[n.jsx(yt,{size:"S",href:d,isExternal:!0,endIcon:n.jsx(Pt,{}),"aria-label":h({id:"admin.pages.MarketPlacePage.plugin.info.label",defaultMessage:"Learn more about {pluginName}"},{pluginName:o.name}),variant:"tertiary",onClick:()=>u("didPluginLearnMore"),children:h({id:"admin.pages.MarketPlacePage.plugin.info.text",defaultMessage:"More"})}),n.jsx(Fa,{isInstalled:e,isInDevelopmentMode:r,isCompatible:p,commandToCopy:S,strapiAppVersion:i,strapiPeerDepVersion:o.strapiVersion,pluginName:o.name})]})]})},Fa=({isInstalled:t,isInDevelopmentMode:e,isCompatible:s,commandToCopy:r,strapiAppVersion:a,strapiPeerDepVersion:i,pluginName:o})=>{const{toggleNotification:h}=De(),{formatMessage:u}=_(),{trackUsage:S}=$e(),{copy:l}=Ft(),d=async()=>{await l(r)&&(S("willInstallPlugin"),h({type:"success",message:u({id:"admin.pages.MarketPlacePage.plugin.copy.success"})}))};return t?n.jsxs(N,{gap:2,paddingLeft:4,children:[n.jsx(Gt,{width:"1.2rem",height:"1.2rem",color:"success600"}),n.jsx(z,{variant:"omega",textColor:"success600",fontWeight:"bold",children:u({id:"admin.pages.MarketPlacePage.plugin.installed",defaultMessage:"Installed"})})]}):e&&s!==!1?n.jsx(Ga,{strapiAppVersion:a,strapiPeerDepVersion:i,handleCopy:d,pluginName:o}):null},Ga=({strapiPeerDepVersion:t,strapiAppVersion:e,handleCopy:s,pluginName:r})=>{const{formatMessage:a}=_(),i=ve.validRange(t),o=ve.satisfies(e??"",i??""),h=a({id:"admin.pages.MarketPlacePage.plugin.copy",defaultMessage:"Copy install command"});return e&&(!i||!o)?n.jsx(_e,{"data-testid":`tooltip-${r}`,label:a({id:"admin.pages.MarketPlacePage.plugin.version",defaultMessage:'Update your Strapi version: "{strapiAppVersion}" to: "{versionRange}"'},{strapiAppVersion:e,versionRange:i}),children:n.jsx("span",{children:n.jsx(Ne,{size:"S",startIcon:n.jsx(Ue,{}),variant:"secondary",onClick:s,disabled:!o,children:h})})}):n.jsx(Ne,{size:"S",startIcon:n.jsx(Ue,{}),variant:"secondary",onClick:s,children:h})},Ba=({githubStars:t=0,npmDownloads:e=0,npmPackageType:s})=>{const{formatMessage:r}=_();return n.jsxs(N,{gap:1,children:[!!t&&n.jsxs(n.Fragment,{children:[n.jsx(_t,{height:"1.2rem",width:"1.2rem","aria-hidden":!0}),n.jsx(Dt,{height:"1.2rem",width:"1.2rem",fill:"warning500","aria-hidden":!0}),n.jsx("p",{"aria-label":r({id:`admin.pages.MarketPlacePage.${s}.githubStars`,defaultMessage:"This {package} was starred {starsCount} on GitHub"},{starsCount:t,package:s}),children:n.jsx(z,{variant:"pi",textColor:"neutral800",children:t})}),n.jsx(Ha,{})]}),n.jsx(Vt,{height:"1.2rem",width:"1.2rem","aria-hidden":!0}),n.jsx("p",{"aria-label":r({id:`admin.pages.MarketPlacePage.${s}.downloads`,defaultMessage:"This {package} has {downloadsCount} weekly downloads"},{downloadsCount:e,package:s}),children:n.jsx(z,{variant:"pi",textColor:"neutral800",children:e})})]})},Ha=xe(Bt)`
  width: 1.2rem;
  transform: rotate(90deg);
`,vt=({status:t,npmPackages:e=[],installedPackageNames:s=[],useYarn:r,isInDevelopmentMode:a,npmPackageType:i,strapiAppVersion:o,debouncedSearch:h})=>{const{formatMessage:u}=_();if(t==="error")return n.jsx(ce.Error,{});if(t==="loading")return n.jsx(ce.Loading,{});const S=u({id:"admin.pages.MarketPlacePage.search.empty",defaultMessage:'No result for "{target}"'},{target:h});return e.length===0?n.jsxs(M,{position:"relative",children:[n.jsx(ue.Grid,{size:"M",children:Array(12).fill(null).map((l,d)=>n.jsx(Ya,{height:"234px",hasRadius:!0},d))}),n.jsx(M,{position:"absolute",top:11,width:"100%",children:n.jsxs(N,{alignItems:"center",justifyContent:"center",direction:"column",children:[n.jsx(Ht,{width:"160px",height:"88px"}),n.jsx(M,{paddingTop:6,children:n.jsx(z,{variant:"delta",tag:"p",textColor:"neutral600",children:S})})]})})]}):n.jsx(Xe.Root,{gap:4,children:e.map(l=>n.jsx(Xe.Item,{col:4,s:6,xs:12,style:{height:"100%"},direction:"column",alignItems:"stretch",children:n.jsx(Va,{npmPackage:l,isInstalled:s.includes(l.attributes.npmPackageName),useYarn:r,isInDevelopmentMode:a,npmPackageType:i,strapiAppVersion:o})},l.id))})},Ya=xe(M)`
  background: ${({theme:t})=>`linear-gradient(180deg, rgba(234, 234, 239, 0) 0%, ${t.colors.neutral150} 100%)`};
  opacity: 0.33;
`,Mt=({isOnline:t,npmPackageType:e="plugin"})=>{const{formatMessage:s}=_(),{trackUsage:r}=$e(),a=e==="provider"?"didSubmitProvider":"didSubmitPlugin";return n.jsx(ue.Header,{title:s({id:"global.marketplace",defaultMessage:"Marketplace"}),subtitle:s({id:"admin.pages.MarketPlacePage.subtitle",defaultMessage:"Get more out of Strapi"}),primaryAction:t&&n.jsx(yt,{startIcon:n.jsx(Yt,{}),variant:"tertiary",href:`https://market.strapi.io/submit-${e}`,onClick:()=>r(a),isExternal:!0,children:s({id:`admin.pages.MarketPlacePage.submit.${e}.link`,defaultMessage:`Submit ${e}`})})})},za=()=>{const{formatMessage:t}=_();return n.jsx(ue.Root,{children:n.jsxs(zt,{children:[n.jsx(Mt,{}),n.jsxs(N,{width:"100%",direction:"column",alignItems:"center",justifyContent:"center",paddingTop:"12rem",children:[n.jsx(M,{paddingBottom:2,children:n.jsx(z,{textColor:"neutral700",variant:"alpha",children:t({id:"admin.pages.MarketPlacePage.offline.title",defaultMessage:"You are offline"})})}),n.jsx(M,{paddingBottom:6,children:n.jsx(z,{textColor:"neutral700",variant:"epsilon",children:t({id:"admin.pages.MarketPlacePage.offline.subtitle",defaultMessage:"You need to be connected to the Internet to access Strapi Market."})})}),n.jsxs("svg",{width:"88",height:"88",viewBox:"0 0 88 88",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[n.jsx("rect",{x:".5",y:".5",width:"87",height:"87",rx:"43.5",fill:"#F0F0FF"}),n.jsx("path",{d:"M34 39.3h-4c-2.6 0-4.7 1-6.6 2.8a9 9 0 0 0-2.7 6.6 9 9 0 0 0 2.7 6.6A9 9 0 0 0 30 58h22.8L34 39.3Zm-11-11 3-3 39 39-3 3-4.7-4.6H30a13.8 13.8 0 0 1-14-14c0-3.8 1.3-7 4-9.7 2.6-2.7 5.7-4.2 9.5-4.3L23 28.2Zm38.2 11.1c3 .2 5.5 1.5 7.6 3.7A11 11 0 0 1 72 51c0 4-1.6 7.2-5 9.5l-3.3-3.4a6.5 6.5 0 0 0 3.6-6.1c0-1.9-.7-3.5-2-5-1.5-1.3-3.1-2-5-2h-3.5v-1.2c0-3.6-1.2-6.6-3.7-9a13 13 0 0 0-15-2.3L34.6 28a17 17 0 0 1 20.3 1.5c3.5 2.7 5.5 6 6.3 10Z",fill:"#4945FF"}),n.jsx("rect",{x:".5",y:".5",width:"87",height:"87",rx:"43.5",stroke:"#D9D8FF"})]})]})]})})},$t={"name:asc":{selected:{id:"admin.pages.MarketPlacePage.sort.alphabetical.selected",defaultMessage:"Sort by alphabetical order"},option:{id:"admin.pages.MarketPlacePage.sort.alphabetical",defaultMessage:"Alphabetical order"}},"submissionDate:desc":{selected:{id:"admin.pages.MarketPlacePage.sort.newest.selected",defaultMessage:"Sort by newest"},option:{id:"admin.pages.MarketPlacePage.sort.newest",defaultMessage:"Newest"}},"githubStars:desc":{selected:{id:"admin.pages.MarketPlacePage.sort.githubStars.selected",defaultMessage:"Sort by GitHub stars"},option:{id:"admin.pages.MarketPlacePage.sort.githubStars",defaultMessage:"Number of GitHub stars"}},"npmDownloads:desc":{selected:{id:"admin.pages.MarketPlacePage.sort.npmDownloads.selected",defaultMessage:"Sort by npm downloads"},option:{id:"admin.pages.MarketPlacePage.sort.npmDownloads",defaultMessage:"Number of downloads"}}},qa=({sortQuery:t,handleSelectChange:e})=>{const{formatMessage:s}=_();return n.jsx(Wa,{children:n.jsx(qt,{value:t,customizeContent:()=>s($t[t].selected),onChange:r=>{e({sort:r})},"aria-label":s({id:"admin.pages.MarketPlacePage.sort.label",defaultMessage:"Sort by"}),size:"S",children:Object.entries($t).map(([r,a])=>n.jsx(Wt,{value:r,children:s(a.option)},r))})})},Wa=xe(M)`
  font-weight: ${({theme:t})=>t.fontWeights.semiBold};

  span {
    font-size: ${({theme:t})=>t.fontSizes[1]};
  }
`,xt="https://market-api.strapi.io";function Ua({npmPackageType:t,debouncedSearch:e,query:s,tabQuery:r,strapiVersion:a}){const{notifyStatus:i}=Ut(),{formatMessage:o}=_(),{toggleNotification:h}=De(),u=o({id:"global.marketplace",defaultMessage:"Marketplace"}),S=()=>{i(o({id:"app.utils.notify.data-loaded",defaultMessage:"The {target} has loaded"},{target:u}))},l={page:s?.page||1,pageSize:s?.pageSize||24},d={...r.plugin,pagination:l,search:e,version:a},{data:y,status:p}=Qe(["marketplace","plugins",d],async()=>{try{const Y=Ze.stringify(d),V=await fetch(`${xt}/plugins?${Y}`);if(!V.ok)throw new Error("Failed to fetch marketplace plugins.");return await V.json()}catch{}return null},{onSuccess(){S()},onError(){h({type:"danger",message:o({id:"notification.error",defaultMessage:"An error occured"})})}}),O={...r.provider,pagination:l,search:e,version:a},{data:H,status:Q}=Qe(["marketplace","providers",O],async()=>{const Y=Ze.stringify(O),V=await fetch(`${xt}/providers?${Y}`);if(!V.ok)throw new Error("Failed to fetch marketplace providers.");return await V.json()},{onSuccess(){S()},onError(){h({type:"danger",message:o({id:"notification.error",defaultMessage:"An error occured"})})}}),Z=t==="plugin"?y:H,E=Z?.meta.collections??{},se=y?.meta.categories??{},{pagination:re}=Z?.meta??{};return{pluginsResponse:y,providersResponse:H,pluginsStatus:p,providersStatus:Q,possibleCollections:E,possibleCategories:se,pagination:re}}const Xa=()=>{const t=typeof navigator<"u"&&typeof navigator.onLine=="boolean"?navigator.onLine:!0,[e,s]=U.useState(t),r=()=>s(!0),a=()=>s(!1);return U.useEffect(()=>(window.addEventListener("online",r),window.addEventListener("offline",a),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",a)}),[]),e},ee="plugin",ge="provider",Qa=()=>{const{formatMessage:t}=_(),{trackUsage:e}=$e(),{toggleNotification:s}=De(),[{query:r},a]=Qt(),i=rs(r?.search,500)||"",{autoReload:o,dependencies:h,useYarn:u,strapiVersion:S}=Zt("MarketplacePage",T=>T),l=Xa(),d=r?.npmPackageType||ee,[y,p]=U.useState({plugin:d===ee?{...r}:{},provider:d===ge?{...r}:{}});U.useEffect(()=>{e("didGoToMarketplace")},[e]),U.useEffect(()=>{o||s({type:"info",message:t({id:"admin.pages.MarketPlacePage.production",defaultMessage:"Manage plugins from the development environment"})})},[s,o,t]);const{pluginsResponse:O,providersResponse:H,pluginsStatus:Q,providersStatus:Z,possibleCollections:E,possibleCategories:se,pagination:re}=Ua({npmPackageType:d,debouncedSearch:i,query:r,tabQuery:y,strapiVersion:S});if(!l)return n.jsx(za,{});const Y=T=>{const L=T===ee||T===ge?T:ee,be=y[L]&&Object.keys(y[L]).length;a(be?{...y[L],search:r?.search||"",npmPackageType:L,page:1}:{npmPackageType:L,collections:[],categories:[],sort:"name:asc",page:1,search:r?.search||""})},V=T=>{a({...T,page:1}),p(L=>({...L,[d]:{...L[d],...T}}))},ae=T=>{a({[T]:[],page:void 0},"remove"),p(L=>({...L,[d]:{}}))},we=({sort:T})=>V({sort:T}),de=Object.keys(h??{});return n.jsx(ue.Root,{children:n.jsxs(ce.Main,{children:[n.jsx(ce.Title,{children:t({id:"admin.pages.MarketPlacePage.head",defaultMessage:"Marketplace - Plugins"})}),n.jsx(Mt,{isOnline:l,npmPackageType:d}),n.jsx(ue.Content,{children:n.jsxs(K.Root,{variant:"simple",onValueChange:Y,value:d,children:[n.jsxs(N,{justifyContent:"space-between",paddingBottom:4,children:[n.jsxs(K.List,{"aria-label":t({id:"admin.pages.MarketPlacePage.tab-group.label",defaultMessage:"Plugins and Providers for Strapi"}),children:[n.jsxs(K.Trigger,{value:ee,children:[t({id:"admin.pages.MarketPlacePage.plugins",defaultMessage:"Plugins"})," ",O?`(${O.meta.pagination.total})`:"..."]}),n.jsxs(K.Trigger,{value:ge,children:[t({id:"admin.pages.MarketPlacePage.providers",defaultMessage:"Providers"})," ",H?`(${H.meta.pagination.total})`:"..."]})]}),n.jsx(M,{width:"25%",children:n.jsx(Kt,{name:"searchbar",onClear:()=>a({search:"",page:1}),value:r?.search,onChange:T=>a({search:T.target.value,page:1}),clearLabel:t({id:"admin.pages.MarketPlacePage.search.clear",defaultMessage:"Clear the search"}),placeholder:t({id:"admin.pages.MarketPlacePage.search.placeholder",defaultMessage:"Search"}),children:t({id:"admin.pages.MarketPlacePage.search.placeholder",defaultMessage:"Search"})})})]}),n.jsxs(N,{paddingBottom:4,gap:2,children:[n.jsx(qa,{sortQuery:r?.sort||"name:asc",handleSelectChange:we}),n.jsx(as,{npmPackageType:d,possibleCollections:E,possibleCategories:se,query:r||{},handleSelectChange:V,handleSelectClear:ae})]}),n.jsx(K.Content,{value:ee,children:n.jsx(vt,{npmPackages:O?.data,status:Q,installedPackageNames:de,useYarn:u,isInDevelopmentMode:o,npmPackageType:"plugin",strapiAppVersion:S,debouncedSearch:i})}),n.jsx(K.Content,{value:ge,children:n.jsx(vt,{npmPackages:H?.data,status:Z,installedPackageNames:de,useYarn:u,isInDevelopmentMode:o,npmPackageType:"provider",debouncedSearch:i})}),n.jsxs(Ee.Root,{...re,defaultPageSize:24,children:[n.jsx(Ee.PageSize,{options:["12","24","50","100"]}),n.jsx(Ee.Links,{})]}),n.jsx(M,{paddingTop:8,children:n.jsx("a",{href:"https://strapi.canny.io/plugin-requests",target:"_blank",rel:"noopener noreferrer nofollow",style:{textDecoration:"none"},onClick:()=>e("didMissMarketplacePlugin"),children:n.jsx(Jt,{title:t({id:"admin.pages.MarketPlacePage.missingPlugin.title",defaultMessage:"Documentation"}),subtitle:t({id:"admin.pages.MarketPlacePage.missingPlugin.description",defaultMessage:"Tell us what plugin you are looking for and we'll let our community plugin developers know in case they are in search for inspiration!"}),icon:n.jsx(es,{}),iconBackground:"alternative100",endAction:n.jsx(Pt,{fill:"neutral600",width:"1.2rem",height:"1.2rem",style:{marginLeft:"0.8rem"}})})})})]})})]})})},en=()=>{const t=Xt(e=>e.admin_app.permissions.marketplace?.main);return n.jsx(ce.Protect,{permissions:t,children:n.jsx(Qa,{})})};export{Qa as MarketplacePage,en as ProtectedMarketplacePage};
