import{j as a,e as w,d0 as M,T as S,a as x,P as k,B as j,$ as h,w as A,v as P,d1 as O,r as u,d2 as W,d3 as I,d4 as _}from"./strapi-z7ApxZZq.js";import{u as v}from"./useDragLayer-DybN2c0t.js";const F={STAGE:"stage"},G=({name:n})=>a.jsxs(w,{background:"primary100",borderStyle:"dashed",borderColor:"primary600",borderWidth:"1px",gap:3,hasRadius:!0,padding:3,shadow:"tableShadow",width:"30rem",children:[a.jsx(w,{alignItems:"center",background:"neutral200",borderRadius:"50%",height:6,justifyContent:"center",width:6,children:a.jsx(M,{width:"0.8rem",fill:"neutral600"})}),a.jsx(S,{fontWeight:"bold",children:n})]});function L(n,e,t){if(!n||!e||!t)return{display:"none"};const{x:r,y:i}=t;return{transform:`translate(${r}px, ${i}px)`}}const $=()=>{const{itemType:n,isDragging:e,item:t,initialOffset:r,currentOffset:i,mouseOffset:g}=v(o=>({item:o.getItem(),itemType:o.getItemType(),initialOffset:o.getInitialSourceClientOffset(),currentOffset:o.getSourceClientOffset(),isDragging:o.isDragging(),mouseOffset:o.getClientOffset()}));return!e||n!==F.STAGE?null:a.jsx(h,{height:"100%",left:0,position:"fixed",pointerEvents:"none",top:0,zIndex:100,width:"100%",children:a.jsxs(h,{style:L(r,i,g),children:[a.jsx(G,{name:typeof t.item=="string"?t.item:null}),";"]})})},B=({children:n})=>a.jsx(k.Main,{children:a.jsx(j.Content,{children:n})}),N=({title:n,subtitle:e,navigationAction:t,primaryAction:r})=>{const{formatMessage:i}=x();return a.jsxs(a.Fragment,{children:[a.jsx(k.Title,{children:i({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:n})}),a.jsx(j.BaseHeader,{navigationAction:t,primaryAction:r,title:n,subtitle:e})]})},f={id:"notification.error",defaultMessage:"An error occurred, please try again"},q=(n={})=>{const{toggleNotification:e}=A(),{formatMessage:t}=x(),{_unstableFormatAPIError:r}=P(),{skip:i=!1,...g}=n,{data:o,isLoading:E,error:l}=O({...g},{skip:i});u.useEffect(()=>{l&&e({type:"danger",message:r(l)})},[l,r,e]);const[p]=W(),b=u.useCallback(async c=>{try{const s=await p({data:c});return"error"in s?(e({type:"danger",message:r(s.error)}),s):(e({type:"success",message:t({id:"actions.created",defaultMessage:"Created workflow"})}),s)}catch(s){throw e({type:"danger",message:t(f)}),s}},[p,r,t,e]),[y]=I(),D=u.useCallback(async(c,s)=>{try{const d=await y({id:c,data:s});return"error"in d?(e({type:"danger",message:r(d.error)}),d):(e({type:"success",message:t({id:"actions.updated",defaultMessage:"Updated workflow"})}),d)}catch(d){throw e({type:"danger",message:t(f)}),d}},[r,t,e,y]),[m]=_(),R=u.useCallback(async c=>{try{const s=await m({id:c});if("error"in s){e({type:"danger",message:r(s.error)});return}return e({type:"success",message:t({id:"actions.deleted",defaultMessage:"Deleted workflow"})}),s.data}catch(s){throw e({type:"danger",message:t(f)}),s}},[m,r,t,e]),{workflows:T=[],meta:C}=o??{};return{meta:C,workflows:T,isLoading:E,error:l,create:b,delete:R,update:D}};export{F as D,N as H,B as R,$ as a,q as u};
