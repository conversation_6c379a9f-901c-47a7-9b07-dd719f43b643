{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/hu.json.mjs"], "sourcesContent": ["var Analytics = \"Analitika\";\nvar Documentation = \"Dokumentáció\";\nvar Email = \"Email\";\nvar Password = \"Je<PERSON>z<PERSON>\";\nvar Provider = \"Szolgáltató\";\nvar ResetPasswordToken = \"Token visszaállítása\";\nvar Role = \"Szerepkör\";\nvar light = \"Világos\";\nvar dark = \"Sötét\";\nvar Username = \"Felhasználónév\";\nvar Users = \"Felhasználók\";\nvar anErrorOccurred = \"Hoppá! Valami elromlott. Kérlek próbáld újra.\";\nvar clearLabel = \"Kiürít\";\nvar or = \"Vagy\";\nvar skipToContent = \"Kihagyás\";\nvar submit = \"Küldés\";\nvar hu = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"A fiókodat felfüggesztettük\",\n    \"Auth.components.Oops.text.admin\": \"Amennyiben ez hiba, kérjük vegye fel a kapcsolatot az adminisztrátorokkal!\",\n    \"Auth.components.Oops.title\": \"Oops...\",\n    \"Auth.form.active.label\": \"Aktív\",\n    \"Auth.form.button.forgot-password\": \"Email küldése\",\n    \"Auth.form.button.go-home\": \"Vissza a kezdőlapra\",\n    \"Auth.form.button.login\": \"Bejelentkezés\",\n    \"Auth.form.button.login.providers.error\": \"Nem sikerült kapcsolódni a szolgáltatón keresztül\",\n    \"Auth.form.button.login.strapi\": \"Bejelentkezés Strapi-val\",\n    \"Auth.form.button.password-recovery\": \"Jelszó visszaállítása\",\n    \"Auth.form.button.register\": \"Kezdjük\",\n    \"Auth.form.confirmPassword.label\": \"Jelszó megerősítése\",\n    \"Auth.form.currentPassword.label\": \"Jelenlegi jelszó\",\n    \"Auth.form.email.label\": \"Email\",\n    \"Auth.form.email.placeholder\": \"e.g. <EMAIL>\",\n    \"Auth.form.error.blocked\": \"A fiókodat az adminisztrátor blokkolta\",\n    \"Auth.form.error.code.provide\": \"Hibás a megadott kód\",\n    \"Auth.form.error.confirmed\": \"Az email cím nincs megerősítve\",\n    \"Auth.form.error.email.invalid\": \"Hibás email.\",\n    \"Auth.form.error.email.provide\": \"Kérjük adja meg felhasználónevét és jelszavát.\",\n    \"Auth.form.error.email.taken\": \"Ez az email cím már foglalt.\",\n    \"Auth.form.error.invalid\": \"Felhasználónév vagy jelszó hibás.\",\n    \"Auth.form.error.params.provide\": \"Hibás a megadott adat.\",\n    \"Auth.form.error.password.format\": \"A jelszó nem tartalmazhatja a `$` szimbólumot többször, mint három.\",\n    \"Auth.form.error.password.local\": \"Ez a felhasználó nem állított be jelszót, kérjük jelentkezzen be szolgáltatón keresztül.\",\n    \"Auth.form.error.password.matching\": \"A jelszavak nem egeyznek.\",\n    \"Auth.form.error.password.provide\": \"Kérjük adja meg a jelszavát.\",\n    \"Auth.form.error.ratelimit\": \"Túl sok próbálkozás, kérjük próbálkozzon újra egy perc múlva.\",\n    \"Auth.form.error.user.not-exist\": \"Ez az email nem létezik.\",\n    \"Auth.form.error.username.taken\": \"A felhasználónév foglalt.\",\n    \"Auth.form.firstname.label\": \"Keresztnév\",\n    \"Auth.form.firstname.placeholder\": \"pl. Elek\",\n    \"Auth.form.forgot-password.email.label\": \"Adja meg az email címét\",\n    \"Auth.form.forgot-password.email.label.success\": \"Az email-t sikeresen kiküldtük\",\n    \"Auth.form.lastname.label\": \"Vezetéknév\",\n    \"Auth.form.lastname.placeholder\": \"pl. Teszt\",\n    \"Auth.form.password.hide-password\": \"Jelszó elrejtése\",\n    \"Auth.form.password.hint\": \"A jelszónak legalább 8 karaktert, 1 nagybetűt, 1 kisbetűt és 1 számot kell tartalmaznia.\",\n    \"Auth.form.password.show-password\": \"Jelszó megjelenítése\",\n    \"Auth.form.register.news.label\": \"Értesítést kérek az új funkciókról és javításokról (ezzel elfogadja a {terms} és a {policy}).\",\n    \"Auth.form.register.subtitle\": \"Ez csak az admin oldalra való bejelentkezésre ad lehetőséget. Minden elmentett adat a saját adatbázisaba kerül mentésre.\",\n    \"Auth.form.rememberMe.label\": \"Emlékezz rám\",\n    \"Auth.form.username.label\": \"Felhasználónév\",\n    \"Auth.form.username.placeholder\": \"e.g. Kai_Doe\",\n    \"Auth.form.welcome.subtitle\": \"Bejelentkezés a Strapi fiókjába\",\n    \"Auth.form.welcome.title\": \"Üdvözöljük!\",\n    \"Auth.link.forgot-password\": \"Elfelejtette a jelszavát?\",\n    \"Auth.link.ready\": \"Készen áll a bejelentkezésre?\",\n    \"Auth.link.signin\": \"Bejelentkezés\",\n    \"Auth.link.signin.account\": \"Már van felhasználói fiókja?\",\n    \"Auth.login.sso.divider\": \"Vagy bejelentkezés ezzel:\",\n    \"Auth.login.sso.loading\": \"Szolgáltatók betöltése...\",\n    \"Auth.login.sso.subtitle\": \"Bejelentkezés a fiókjába SSO-val\",\n    \"Auth.privacy-policy-agreement.policy\": \"adatvédelmi nyilatkozat\",\n    \"Auth.privacy-policy-agreement.terms\": \"felhasználási feltételek\",\n    \"Auth.reset-password.title\": \"Jelszó visszaállítása\",\n    \"Content Manager\": \"Tartalom Menedzser\",\n    \"Content Type Builder\": \"Tartalomtípus építő\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Fájl feltöltés\",\n    \"HomePage.head.title\": \"Kezdőlap\",\n    \"HomePage.roadmap\": \"Nézze meg a terveinket\",\n    \"HomePage.welcome.congrats\": \"Gratulálunk!\",\n    \"HomePage.welcome.congrats.content\": \"Első adminisztrátorként jelentkezett be. Ahhoz, hogy felfedezhesse a Strapi funkcióit,\",\n    \"HomePage.welcome.congrats.content.bold\": \"azt ajánljuk, hogy hozza létre az első tartalomtípust.\",\n    \"Media Library\": \"Média Könyvtár\",\n    \"New entry\": \"Új elem\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Szerepkörök & Engedélyek\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Egyes szerepkörök nem törölhetők, mivel felhasználókhoz vannak társítva\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"A felhasználókhoz társított szerepkör nem törölhető\",\n    \"Roles.RoleRow.select-all\": \"{name} kiválasztása tömeges műveletekhez\",\n    \"Roles.RoleRow.user-count\": \"{number, plural, =0 {# felhasználó} one {# felhasználó} other {# felhasználók}}\",\n    \"Roles.components.List.empty.withSearch\": \"Nincs a keresésnek megfelelő szerepkör ({search})...\",\n    \"Settings.PageTitle\": \"Beállítások - {name}\",\n    \"Settings.apiTokens.addFirstToken\": \"Első API Token hozzáadása\",\n    \"Settings.apiTokens.addNewToken\": \"Új API Token hozzáadása\",\n    \"Settings.tokens.copy.editMessage\": \"Biztonsági okokból csak egyszer láthatja a tokent.\",\n    \"Settings.tokens.copy.editTitle\": \"Ez a token már nem elérhető.\",\n    \"Settings.tokens.copy.lastWarning\": \"Másolja le a tokent, mert később már nem lesz látható!\",\n    \"Settings.apiTokens.create\": \"Új hozzáadása\",\n    \"Settings.apiTokens.description\": \"Az API felhasználásához generált tokenek listája\",\n    \"Settings.apiTokens.emptyStateLayout\": \"Még nincs tartalom hozzáadva...\",\n    \"Settings.apiTokens.ListView.headers.name\": \"Név\",\n    \"Settings.apiTokens.ListView.headers.description\": \"Leírás\",\n    \"Settings.apiTokens.ListView.headers.type\": \"Token típusa\",\n    \"Settings.apiTokens.ListView.headers.createdAt\": \"Létrehozva\",\n    \"Settings.apiTokens.ListView.headers.lastUsedAt\": \"Utoljára használva\",\n    \"Settings.tokens.notification.copied\": \"Token a vágólapra másolva.\",\n    \"Settings.apiTokens.title\": \"API Token-ek\",\n    \"Settings.tokens.types.full-access\": \"Teljes hozzáférés\",\n    \"Settings.tokens.types.read-only\": \"Csak olvasható\",\n    \"Settings.tokens.duration.7-days\": \"7 nap\",\n    \"Settings.tokens.duration.30-days\": \"30 nap\",\n    \"Settings.tokens.duration.90-days\": \"90 nap\",\n    \"Settings.tokens.duration.unlimited\": \"Korlátlan\",\n    \"Settings.tokens.form.duration\": \"Token időtartama\",\n    \"Settings.tokens.form.type\": \"Token típusa\",\n    \"Settings.tokens.duration.expiration-date\": \"Lejárati dátum\",\n    \"Settings.apiTokens.createPage.permissions.title\": \"Engedélyek\",\n    \"Settings.apiTokens.createPage.permissions.description\": \"Csak az útvonalakhoz kötött műveletek szerepelnek az alábbiakban.\",\n    \"Settings.tokens.RegenerateDialog.title\": \"Token újragenerálása\",\n    \"Settings.tokens.popUpWarning.message\": \"Biztosan újragenerálod ezt a token-t?\",\n    \"Settings.tokens.Button.cancel\": \"Mégse\",\n    \"Settings.tokens.Button.regenerate\": \"Újragenerálás\",\n    \"Settings.application.description\": \"Az adminisztrációs panel globális információi\",\n    \"Settings.application.edition-title\": \"Aktuális kiadás\",\n    \"Settings.application.get-help\": \"Kérje segítségünket\",\n    \"Settings.application.link-pricing\": \"Tekintse meg az összes csomagot\",\n    \"Settings.application.link-upgrade\": \"Frissítse az adminisztrációs panelt\",\n    \"Settings.application.node-version\": \"node verzió\",\n    \"Settings.application.strapi-version\": \"strapi verzió\",\n    \"Settings.application.strapiVersion\": \"strapi verzió\",\n    \"Settings.application.title\": \"Áttekintés\",\n    \"Settings.application.customization\": \"Testreszabás\",\n    \"Settings.application.customization.carousel.title\": \"Logó\",\n    \"Settings.application.customization.carousel.change-action\": \"Logó módosítása\",\n    \"Settings.application.customization.carousel.reset-action\": \"Logó visszaállítása\",\n    \"Settings.application.customization.carousel-slide.label\": \"Logó diasor\",\n    \"Settings.application.customization.carousel-hint\": \"Változtasd meg az admin panel logóját (Max méret: {dimension}x{dimension}, Max fájlméret: {size}KB)\",\n    \"Settings.application.customization.modal.cancel\": \"Mégse\",\n    \"Settings.application.customization.modal.upload\": \"Logó feltöltése\",\n    \"Settings.application.customization.modal.tab.label\": \"Hogyan szeretnéd feltölteni az állományaidat?\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"Számítógépről\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"Max méret: {dimension}x{dimension}, Max méret: {size}KB\",\n    \"Settings.application.customization.modal.upload.error-format\": \"Rossz formátumot töltöttél fel (csak a következő formátumokat fogadja el: jpeg, jpg, png, svg).\",\n    \"Settings.application.customization.modal.upload.error-size\": \"A feltöltött fájl túl nagy (max méret: {dimension}x{dimension}, max fájlméret: {size}KB)\",\n    \"Settings.application.customization.modal.upload.error-network\": \"Hálózati hiba\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"Fájlok tallózása\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"Húzz és ejtsd ide vagy\",\n    \"Settings.application.customization.modal.upload.from-url\": \"URL-ről\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n    \"Settings.application.customization.modal.upload.next\": \"Következő\",\n    \"Settings.application.customization.modal.pending\": \"Függőben lévő logó\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"Válassz másik logót\",\n    \"Settings.application.customization.modal.pending.title\": \"Logó készen áll a feltöltésre\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"Kezeljed a kiválasztott logót a feltöltés előtt\",\n    \"Settings.application.customization.modal.pending.upload\": \"Logó feltöltése\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"kép\",\n    \"Settings.error\": \"Hiba\",\n    \"Settings.global\": \"Globális Beállítások\",\n    \"Settings.permissions\": \"Adminisztrációs panel\",\n    \"Settings.permissions.category\": \"{category} engedélyeinek beállításai\",\n    \"Settings.permissions.category.plugins\": \"{category} plugin engedélyeinek beállításai\",\n    \"Settings.permissions.conditions.anytime\": \"Bármikor\",\n    \"Settings.permissions.conditions.apply\": \"Alkalmaz\",\n    \"Settings.permissions.conditions.can\": \"Tudja\",\n    \"Settings.permissions.conditions.conditions\": \"Határozza meg a feltételeket\",\n    \"Settings.permissions.conditions.links\": \"Linkek\",\n    \"Settings.permissions.conditions.no-actions\": \"Először választania kell egy műveletet (create, read, update, ...) mielőtt megadja a feltételeket.\",\n    \"Settings.permissions.conditions.none-selected\": \"Bármikor\",\n    \"Settings.permissions.conditions.or\": \"VAGY\",\n    \"Settings.permissions.conditions.when\": \"Mikor\",\n    \"Settings.permissions.select-all-by-permission\": \"Minden {label} hozzáféres kiválasztása\",\n    \"Settings.permissions.select-by-permission\": \"{label} hozzáféres kiválasztása\",\n    \"Settings.permissions.users.create\": \"Új felhasználó meghívása\",\n    \"Settings.permissions.users.email\": \"Email\",\n    \"Settings.permissions.users.firstname\": \"Keresztnév\",\n    \"Settings.permissions.users.lastname\": \"Vezetéknév\",\n    \"Settings.permissions.users.user-status\": \"Felhasználói állapot\",\n    \"Settings.permissions.users.roles\": \"Szerepek\",\n    \"Settings.permissions.users.username\": \"Felhasználónév\",\n    \"Settings.permissions.users.active\": \"Aktív\",\n    \"Settings.permissions.users.inactive\": \"Inaktív\",\n    \"Settings.permissions.users.form.sso\": \"Csatlakozas SSO-val\",\n    \"Settings.permissions.users.form.sso.description\": \"Ha engedélyezve van (ON), a felhasználók bejelentkezhetnek SSO-n keresztül\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Minden felhasználó, aki hozzáfér a Strapi adminisztrációs panelhez\",\n    \"Settings.permissions.users.tabs.label\": \"Hozzáférések Tab\",\n    \"Settings.permissions.users.strapi-super-admin\": \"Super Adminisztrátor\",\n    \"Settings.permissions.users.strapi-editor\": \"Szerkesztő\",\n    \"Settings.permissions.users.strapi-author\": \"Szerző\",\n    \"Settings.profile.form.notify.data.loaded\": \"Profiladatok betöltve\",\n    \"Settings.profile.form.section.experience.clear.select\": \"A kiválasztott felület nyelvének törlése\",\n    \"Settings.profile.form.section.experience.here\": \"itt\",\n    \"Settings.profile.form.section.experience.documentation\": \"dokumentáció\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"A felület nyelve\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Ez csak a saját felületét jeleníti meg a kiválasztott nyelven.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"A kiválasztás csak az Ön számára módosítja a felület nyelvét. Kérjük, olvassa el ezt a {document}, hogy más nyelveket a csapata számára is elérhetővé tehesse.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Felület mód\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Megjeleníti a felhasználói felületedet a kiválasztott módban.\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name} mód\",\n    light: light,\n    dark: dark,\n    \"Settings.profile.form.section.experience.title\": \"Tapasztalat\",\n    \"Settings.profile.form.section.head.title\": \"Felhasználói profil\",\n    \"Settings.profile.form.section.profile.page.title\": \"Profil oldal\",\n    \"Settings.roles.create.description\": \"Határozza meg a szerephezkörhöz biztosított jogokat\",\n    \"Settings.roles.create.title\": \"Szerepkör létrehozása\",\n    \"Settings.roles.created\": \"A szerepkör létrejött\",\n    \"Settings.roles.edit.title\": \"Szerepkör módosítása\",\n    \"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# felhasználó} one {# felhasználó} other {# felhasználók}} ezzel a szereppel\",\n    \"Settings.roles.form.created\": \"Létrehozva\",\n    \"Settings.roles.form.description\": \"A szerepkör neve és leírása\",\n    \"Settings.roles.form.permission.property-label\": \"{label} hozzáfére's\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Mezők hozzáférései\",\n    \"Settings.roles.form.permissions.create\": \"Létrehoz\",\n    \"Settings.roles.form.permissions.delete\": \"Töröl\",\n    \"Settings.roles.form.permissions.publish\": \"Közzétesz\",\n    \"Settings.roles.form.permissions.read\": \"Olvasás\",\n    \"Settings.roles.form.permissions.update\": \"Frissítés\",\n    \"Settings.roles.list.button.add\": \"Új szerepkör hozzáadása\",\n    \"Settings.roles.list.description\": \"Szerepkörök listája\",\n    \"Settings.roles.title.singular\": \"Szerepkör\",\n    \"Settings.sso.description\": \"Konfigurálja az egyszeri bejelentkezés funkció beállításait.\",\n    \"Settings.sso.form.defaultRole.description\": \"Az új hitelesített felhasználót a kiválasztott szerepkörhöz csatolja\",\n    \"Settings.sso.form.defaultRole.description-not-allowed\": \"Nincs megfelelő engedélye az adminisztrátori szerepkörök olvasásához\",\n    \"Settings.sso.form.defaultRole.label\": \"Alapértelmezett szerepkör\",\n    \"Settings.sso.form.registration.description\": \"Egyszeri bejelentkezéskor, ha nincs fiók, hozzon létre új felhasználót\",\n    \"Settings.sso.form.registration.label\": \"Automatikus regisztráció\",\n    \"Settings.sso.title\": \"Egyszeri bejelentkezés\",\n    \"Settings.webhooks.create\": \"Webhook létrehozása\",\n    \"Settings.webhooks.create.header\": \"Új fejléc létrehozása\",\n    \"Settings.webhooks.created\": \"Webhook létrehozva\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Ez az esemény csak olyan tartalmak esetében létezik, amelyeknél engedélyezve van a Piszkozat/Közzététel rendszer\",\n    \"Settings.webhooks.events.create\": \"Létrehoz\",\n    \"Settings.webhooks.events.update\": \"Frissít\",\n    \"Settings.webhooks.form.events\": \"Esemnények\",\n    \"Settings.webhooks.form.headers\": \"Fejléc\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"Fejlésor eltávolítása {number}\",\n    \"Settings.webhooks.key\": \"Kulcs\",\n    \"Settings.webhooks.list.button.add\": \"Új webhook létrehozása\",\n    \"Settings.webhooks.list.description\": \"Értesítések a POST módosításairól\",\n    \"Settings.webhooks.list.empty.description\": \"Nem található webhook\",\n    \"Settings.webhooks.list.empty.link\": \"Tekintse meg dokumentációnkat\",\n    \"Settings.webhooks.list.empty.title\": \"Még nincsenek webhookok\",\n    \"Settings.webhooks.list.th.actions\": \"Műveletek\",\n    \"Settings.webhooks.list.th.status\": \"Státusz\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhook-ok\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# elem} other {# elemek}} kiválasztva\",\n    \"Settings.webhooks.trigger\": \"Kapcsoló\",\n    \"Settings.webhooks.trigger.cancel\": \"Kapcsoló törlése\",\n    \"Settings.webhooks.trigger.pending\": \"Folyamatban…\",\n    \"Settings.webhooks.trigger.save\": \"Kérjük mentse\",\n    \"Settings.webhooks.trigger.success\": \"Sikerült!\",\n    \"Settings.webhooks.trigger.success.label\": \"A kapcsoló sikerült\",\n    \"Settings.webhooks.trigger.test\": \"Teszt-kapcsoló\",\n    \"Settings.webhooks.trigger.title\": \"Először mentsen\",\n    \"Settings.webhooks.value\": \"Érték\",\n    \"Usecase.back-end\": \"Back-end fejlesztő\",\n    \"Usecase.button.skip\": \"Kérdezés kihagyása\",\n    \"Usecase.content-creator\": \"Tartalomkészítő\",\n    \"Usecase.front-end\": \"Front-end fejlesztő\",\n    \"Usecase.full-stack\": \"Teljeskörű fejlesztő\",\n    \"Usecase.input.work-type\": \"Milyen típusú munkát végzel?\",\n    \"Usecase.notification.success.project-created\": \"A projekt sikeresen létrehozva\",\n    \"Usecase.other\": \"Egyéb\",\n    \"Usecase.title\": \"Mesélj egy kicsit magadról\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Felhasználók & Engedélyek\",\n    \"Users.components.List.empty\": \"Nincsenek felhasználók...\",\n    \"Users.components.List.empty.withFilters\": \"Nincs a beállított szűrőknek megfelelő felhasználó..\",\n    \"Users.components.List.empty.withSearch\": \"Nincs a keresének megfelelő felhasználó ({search})...\",\n    \"admin.pages.MarketPlacePage.head\": \"Piactér - Plugin-ok\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"Ön offline állapotban van\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"Csatlakoznia kell az internethez a Strapi Market eléréséhez.\",\n    \"admin.pages.MarketPlacePage.plugins\": \"Bővítmények\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"Telepítési parancs másolása\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"A telepítési parancs készen áll a terminálba való bemásolásra\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"További információk\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"{pluginName} bővítmény további információi\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"További információk\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"Telepítve\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Készítette: Strapi\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Bővítmény hitelesítve a Strapi által\",\n    \"admin.pages.MarketPlacePage.plugin.version\": \"Frissítsd a Strapi verziód: \\\"{strapiAppVersion}\\\" erre: \\\"{versionRange}\\\"\",\n    \"admin.pages.MarketPlacePage.plugin.version.null\": \"Nem sikerült ellenőrizni a kompatibilitást a Strapi verzióddal: \\\"{strapiAppVersion}\\\"\",\n    \"admin.pages.MarketPlacePage.plugin.githubStars\": \"Ezt a plugint {starsCount} csillagra jelölték a GitHub-on\",\n    \"admin.pages.MarketPlacePage.plugin.downloads\": \"Ezt a plugint hetente {downloadsCount} alkalommal töltik le\",\n    \"admin.pages.MarketPlacePage.providers\": \"Szolgáltatók\",\n    \"admin.pages.MarketPlacePage.provider.githubStars\": \"Ezt a szolgáltatót {starsCount} csillagra jelölték a GitHub-on\",\n    \"admin.pages.MarketPlacePage.provider.downloads\": \"Ezt a szolgáltatót hetente {downloadsCount} alkalommal töltik le\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"Keresés törlése\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"Nincs találat erre: \\\"{target}\\\"\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"Keresés\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Plugin küldése\",\n    \"admin.pages.MarketPlacePage.submit.provider.link\": \"Provider beküldése\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Hozzon ki többet a Strapi-ból\",\n    \"admin.pages.MarketPlacePage.tab-group.label\": \"Strapi pluginek és szolgáltatók\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"Hiányzik egy plugin?\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"Mondd el, milyen pluginra van szükséged, és tájékoztatjuk a közösségi plugin fejlesztőinket, hogy esetleg ötletet meríthessenek belőle!\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical\": \"Betűrendes rendezés\",\n    \"admin.pages.MarketPlacePage.sort.newest\": \"Legújabb\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Rendezés betűrend szerint\",\n    \"admin.pages.MarketPlacePage.sort.newest.selected\": \"Rendezés legújabbak szerint\",\n    \"admin.pages.MarketPlacePage.sort.githubStars\": \"GitHub csillagok száma\",\n    \"admin.pages.MarketPlacePage.sort.githubStars.selected\": \"Rendezés GitHub csillagok szerint\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads\": \"Letöltések száma\",\n    \"admin.pages.MarketPlacePage.sort.npmDownloads.selected\": \"Rendezés npm letöltések szerint\",\n    \"admin.pages.MarketPlacePage.filters.collections\": \"Gyűjtemények\",\n    \"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"{count, plural, =0 {Nincsenek gyűjtemények} one {# gyűjtemény} other {# gyűjtemények}} kiválasztva\",\n    \"admin.pages.MarketPlacePage.filters.categories\": \"Kategóriák\",\n    \"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"{count, plural, =0 {Nincsenek kategóriák} one {# kategória} other {# kategóriák}} kiválasztva\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Másolás a vágólapra\",\n    \"app.component.search.label\": \"{target} keresése\",\n    \"app.component.table.duplicate\": \"{target} duplikálása\",\n    \"app.component.table.edit\": \"{target} szerkesztése\",\n    \"app.component.table.select.one-entry\": \"{target} kiválasztása\",\n    \"app.components.BlockLink.blog\": \"Blog\",\n    \"app.components.BlockLink.blog.content\": \"Olvassa el a legfrissebb híreket a Strapiról és az ökoszisztémáról.\",\n    \"app.components.BlockLink.code\": \"Kód példák\",\n    \"app.components.BlockLink.code.content\": \"Tanuljon a közösség által fejlesztett valós projektek segítségével.\",\n    \"app.components.BlockLink.documentation.content\": \"Fedezze fel az alapvető fogalmakat, útmutatókat és utasításokat.\",\n    \"app.components.BlockLink.tutorial\": \"Oktatóanyagok\",\n    \"app.components.BlockLink.tutorial.content\": \"Kövesse az utasításokat a Strapi használatához és testreszabásához.\",\n    \"app.components.Button.cancel\": \"Mégsem\",\n    \"app.components.Button.confirm\": \"Megerősítés\",\n    \"app.components.Button.reset\": \"Visszaállítás\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Hamarosan\",\n    \"app.components.ConfirmDialog.title\": \"Megerősítés\",\n    \"app.components.DownloadInfo.download\": \"Letöltés folyamatban...\",\n    \"app.components.DownloadInfo.text\": \"Ez eltarthat egy percig. Köszönjük a türelmét.\",\n    \"app.components.EmptyAttributes.title\": \"Még nincsenek mezők\",\n    \"app.components.EmptyStateLayout.content-document\": \"Nem található tartalom\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"Nincs megfelelő jogosultsága a tartalomhozhoz\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>Hozz létre és kezelj minden tartalmat itt a Tartalomkezelőben.</p><p>Például: A Blog weboldal példáját folytatva, írhatsz egy Cikket, mentheted és publikálhatod úgy, ahogy szeretnéd.</p><p>💡 Gyors tipp - Ne felejtsd el publikálni a létrehozott tartalmat.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ Tartalom létrehozása\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>Szuper, még egy lépés van hátra!</p><b>🚀 Lásd a tartalmat működés közben</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"API tesztelése\",\n    \"app.components.GuidedTour.CM.success.title\": \"2. lépés: Kész ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>A Gyűjtemény típusok segítségével több bejegyzést tudsz kezelni, míg az Egy típusok a csak egy bejegyzés kezelésére alkalmasak.</p> <p>Például: Egy Blog weboldalnál a Cikkek lennek egy Gyűjtemény típus, míg a Honlap lenne egy Egy típus.</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"Hozz létre egy Gyűjtemény típust\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 Hozz létre első Gyűjtemény típust\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>Jól haladsz!</p><b>⚡️ Mit szeretnél megosztani a világgal?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"1. lépés: Kész ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>Hozz létre itt egy hitelesítési token-t, és töltsd le az általad létrehozott tartalmat.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"API Token generálása\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Lásd a tartalmat működés közben\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>Lásd a tartalmat működés közben az HTTP kéréssel:</p><ul><li><p>Erre a URL-re: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Ezzel a fejléccel: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>További lehetőségek a tartalommal való interakcióhoz, lásd a <documentationLink>dokumentációt</documentationLink>.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"Menj vissza a főoldalra\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"3. lépés: befejezve ✅\",\n    \"app.components.GuidedTour.create-content\": \"Tartalom létrehozása\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ Mire szeretnéd megosztani a világgal?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"Menj a Content type Builder-be\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 Építsd fel a tartalom struktúráját\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"API tesztelése\",\n    \"app.components.GuidedTour.skip\": \"A túra átugrása\",\n    \"app.components.GuidedTour.title\": \"3 lépés a kezdéshez\",\n    \"app.components.HomePage.button.blog\": \"Bővebben a blogon\",\n    \"app.components.HomePage.community\": \"Csatlakozz a közösséghez\",\n    \"app.components.HomePage.community.content\": \"Beszélgessen a csapattagokkal, a közreműködőkkel és a fejlesztőkkel különböző csatornákon.\",\n    \"app.components.HomePage.create\": \"Hozza létre az első tartalomtípust\",\n    \"app.components.HomePage.roadmap\": \"Tekintse meg terveinket\",\n    \"app.components.HomePage.welcome\": \"Üdvözöljük a fedélzeten 👋\",\n    \"app.components.HomePage.welcome.again\": \"Üdvözöljük 👋\",\n    \"app.components.HomePage.welcomeBlock.content\": \"Gratulálunk! Első rendszergazdaként jelentkezett be. A Strapi által nyújtott funkciók felfedezéséhez javasoljuk, hogy hozza létre első tartalomtípusát!\",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Reméljük, hogy jól halad a projektje! Olvassa el a Strapi legfrissebb híreit. Visszajelzései alapján mindent megteszünk, hogy javítsuk a terméket.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"problémák.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" vagy írjon \",\n    \"app.components.ImgPreview.hint\": \"Húzza a fájlt erre a területre, vagy {browse} a feltöltendő fájlért\",\n    \"app.components.ImgPreview.hint.browse\": \"tallózás\",\n    \"app.components.InputFile.newFile\": \"Új fájl hozzáadása\",\n    \"app.components.InputFileDetails.open\": \"Megnyitás új lapon\",\n    \"app.components.InputFileDetails.originalName\": \"Eredeti név:\",\n    \"app.components.InputFileDetails.remove\": \"Fájl eltávolítása\",\n    \"app.components.InputFileDetails.size\": \"Méret:\",\n    \"app.components.InstallPluginPage.Download.description\": \"A bővítmény letöltése és telepítése eltarthat néhány másodpercig.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Letöltés...\",\n    \"app.components.InstallPluginPage.description\": \"Bővítse alkalmazását erőfeszítés nélkül.\",\n    \"app.components.LeftMenu.collapse\": \"A navigációs sáv összecsukása\",\n    \"app.components.LeftMenu.expand\": \"A navigációs sáv kinyitása\",\n    \"app.components.LeftMenu.general\": \"Általános\",\n    \"app.components.LeftMenu.logout\": \"Kijelentkezés\",\n    \"app.components.LeftMenu.logo.alt\": \"Alkalmazás logó\",\n    \"app.components.LeftMenu.plugins\": \"Bővítmények\",\n    \"app.components.LeftMenu.trialCountdown\": \"A próbaidőszak végére {date}.\",\n    \"app.components.LeftMenu.navbrand.title\": \"Strapi Műszerfal\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"Munkaterület\",\n    \"app.components.LeftMenuFooter.help\": \"Segítség\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Powered by \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Gyűjtemény típusai\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Beállítások\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Általános\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Nincs bővítmény telepítve\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Bővítmények\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Egyedülálló típusok\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"A bővítmény eltávolítása eltarthat néhány másodpercig.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Eltávolítás\",\n    \"app.components.ListPluginsPage.description\": \"A telepített bővítmények listája.\",\n    \"app.components.ListPluginsPage.head.title\": \"A bővítmények listája\",\n    \"app.components.Logout.logout\": \"Kijelentkezés\",\n    \"app.components.Logout.profile\": \"Profil\",\n    \"app.components.MarketplaceBanner\": \"Fedezze fel a közösség által épített modulokat, és még sok más fantasztikus dolgot, amik segítenek a projekt elindításában.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"a strapi rocket logo\",\n    \"app.components.MarketplaceBanner.link\": \"Nézze meg most\",\n    \"app.components.NotFoundPage.back\": \"Vissza a kezdőoldalra\",\n    \"app.components.NotFoundPage.description\": \"Nem található\",\n    \"app.components.Official\": \"Hivatalos\",\n    \"app.components.Onboarding.help.button\": \"Súgó gomb\",\n    \"app.components.Onboarding.label.completed\": \"% elkészült\",\n    \"app.components.Onboarding.title\": \"Bemutató videók\",\n    \"app.components.PluginCard.Button.label.download\": \"Letöltés\",\n    \"app.components.PluginCard.Button.label.install\": \"Már telepítve van\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Az automatikus újratöltés funkciót engedélyezni kell. Kérjük, indítsa el az alkalmazást ezzel a paranccsal: `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Megértettem!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Biztonsági okokból egy plugin csak fejlesztői környezetben tölthető le.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"A letöltés nem lehetséges\",\n    \"app.components.PluginCard.compatible\": \"Kompatibilis az alkalmazásoddal\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Kompatibilis a közösséggel\",\n    \"app.components.PluginCard.more-details\": \"További részletek\",\n    \"app.components.ToggleCheckbox.off-label\": \"Kikapcsol\",\n    \"app.components.ToggleCheckbox.on-label\": \"Bekapcsol\",\n    \"app.components.Users.MagicLink.connect\": \"Másolja ki és ossza meg ezt a linket, hogy hozzáférést biztosítson ehhez a felhasználóhoz\",\n    \"app.components.Users.MagicLink.connect.sso\": \"Küldje el ezt a linket a felhasználónak. Az első bejelentkezés történhet SSO szolgáltatón keresztül\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Felhasználói adatok\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"A felhasználó szerepkörei\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Egy felhasználónak lehet egy, illetve több szerepköre is\",\n    \"app.components.Users.SortPicker.button-label\": \"Rendezés\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"Email (A - Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"Email (Z - A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Keresztnév (A - Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Keresztnév (Z - A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Vezetéknév (A - Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Vezetéknév (Z - A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Felhasználónév (A - Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Felhasználónév (Z - A)\",\n    \"app.components.listPlugins.button\": \"Új bővítmény hozzáadása\",\n    \"app.components.listPlugins.title.none\": \"Nincs telepítve bővítmény\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Hiba történt a bővítmény eltávolítása közben\",\n    \"app.containers.App.notification.error.init\": \"Hiba történt az API kérése közben\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Ha nem kapja meg ezt a linket, forduljon az adminisztrátorhoz.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Eltarthat néhány percig, amíg megkapja a jelszó-helyreállítási linket.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email elküdlve\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Aktív\",\n    \"app.containers.Users.EditPage.header.label\": \"{name} módosítása\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Felhasználó szerkesztése\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"A hozzárendelt szerepkörök\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Felhasználó meghívása\",\n    \"app.links.configure-view\": \"A nézet testreszabása\",\n    \"app.page.not.found\": \"Hoppá! Úgy tűnik, nem találjuk a keresett oldalt...\",\n    \"app.static.links.cheatsheet\": \"Puska\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Szűrő hozzáadása\",\n    \"app.utils.close-label\": \"Bezárás\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.duplicate\": \"Duplikálás\",\n    \"app.utils.edit\": \"Szerkesztés\",\n    \"app.utils.errors.file-too-big.message\": \"A fájl mérete túl nagy\",\n    \"app.utils.filter-value\": \"Szűrési érték\",\n    \"app.utils.filters\": \"Szűrők\",\n    \"app.utils.notify.data-loaded\": \"A {target} betöltődött\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Közzétesz\",\n    \"app.utils.select-all\": \"Minden kiválasztása\",\n    \"app.utils.select-field\": \"Mező kiválasztása\",\n    \"app.utils.select-filter\": \"Szűrő kiválasztása\",\n    \"app.utils.unpublish\": \"Közzététel visszavonása\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Ez a tartalom jelenleg fejlesztés alatt áll, és néhány héten belül újra elérhető lesz!\",\n    \"component.Input.error.validation.integer\": \"Az értéknek egész számnak kell lennie\",\n    \"components.AutoReloadBlocker.description\": \"Futtassa a Strapit a következő parancsok egyikével:\",\n    \"components.AutoReloadBlocker.header\": \"Ehhez a bővítményhez töltse be újra a funkciót.\",\n    \"components.ErrorBoundary.title\": \"Valami elromlott...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"tartalmazza\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"tartalmazza (nem nagybetű érzékeny)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"erre végződik\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"erre végződik (nem nagybetű érzékeny)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"egyenlő\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"egyenlő (nem nagybetű érzékeny)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"nagyobb, mint\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"nagyobb, vagy egyenlő, mint\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"kisebb, mint\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"kisebb, vagy egyenlő, mint\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"nem egyenlő\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"nem egyenlő (nem nagybetű érzékeny)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"nem tartalmazza\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"nem tartalmazza (nem nagybetű érzékeny)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"nem null\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"null\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"ezzel kezdődik\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"ezzel kezdődik (nem nagybetű érzékeny)\",\n    \"components.Input.error.attribute.key.taken\": \"Ez az érték már létezik\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Nem lehet egyenlő\",\n    \"components.Input.error.attribute.taken\": \"Ez a mezőnév már létezik\",\n    \"components.Input.error.contain.lowercase\": \"A jelszónak tartalmaznia kell legalább egy kisbetűt\",\n    \"components.Input.error.contain.number\": \"A jelszónak tartalmaznia kell legalább egy számot\",\n    \"components.Input.error.contain.uppercase\": \"A jelszónak tartalmaznia kell legalább egy nagybetűt\",\n    \"components.Input.error.contentTypeName.taken\": \"Ez a név már létezik\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"A jelszavak nem egyeznek\",\n    \"components.Input.error.validation.email\": \"Érvénytelen e-mail\",\n    \"components.Input.error.validation.json\": \"Hibás JSON formátum\",\n    \"components.Input.error.validation.lowercase\": \"Az értéknek kisbetűs karakterláncnak kell lennie\",\n    \"components.Input.error.validation.max\": \"A megadott érték túl nagy {max}.\",\n    \"components.Input.error.validation.maxLength\": \"A megadott érték túl hosszú {max}.\",\n    \"components.Input.error.validation.min\": \"A megadott érték túl alacsony {min}.\",\n    \"components.Input.error.validation.minLength\": \"A megadott érték túl rövid {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Nem lehet felsőbbrendű\",\n    \"components.Input.error.validation.regex\": \"A megadott érték formátuma nem megfelelő.\",\n    \"components.Input.error.validation.required\": \"Ez az érték kötelező.\",\n    \"components.Input.error.validation.unique\": \"Ez az érték már használatban van.\",\n    \"components.InputSelect.option.placeholder\": \"Válasszon itt\",\n    \"components.ListRow.empty\": \"Nincsenek megjelenítendő adatok.\",\n    \"components.NotAllowedInput.text\": \"Nincs jogosultsága a mező megtekintéséhez\",\n    \"components.OverlayBlocker.description\": \"Olyan funkciót használ, amelynek újra kell indítania a szervert. Kérjük, várja meg, amíg a szerver feláll.\",\n    \"components.OverlayBlocker.description.serverError\": \"A szervernek újra kellett volna indulnia, kérjük, ellenőrizze a logokat a terminálban.\",\n    \"components.OverlayBlocker.title\": \"Újraindításra vár...\",\n    \"components.OverlayBlocker.title.serverError\": \"Az újraindítás a vártnál tovább tart\",\n    \"components.PageFooter.select\": \"Bejegyzések oldalanként\",\n    \"components.ProductionBlocker.description\": \"Biztonsági okokból le kell tiltanunk ezt a bővítményt más környezetekben.\",\n    \"components.ProductionBlocker.header\": \"Ez a bővítmény csak fejlesztői környezetben érhető el.\",\n    \"components.Search.placeholder\": \"Keresés...\",\n    \"components.TableHeader.sort\": \"Rendezés {label} szerint\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown mód\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Előnézet mód\",\n    \"components.Wysiwyg.collapse\": \"Összecsuk\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Cím H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Cím H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Cím H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Cím H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Cím H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Cím H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Cím hozzádása\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"karakterek\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Kinyit\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Fájlok behúzása, beillesztése a vágólapról vagy {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"Válassza ki őket\",\n    \"components.pagination.go-to\": \"Ugrás a(z) {page} oldalra\",\n    \"components.pagination.go-to-next\": \"Ugrás a következő oldalra\",\n    \"components.pagination.go-to-previous\": \"Ugrás az előző oldalra\",\n    \"components.pagination.remaining-links\": \"És {number} további link\",\n    \"components.popUpWarning.button.cancel\": \"Mégsem\",\n    \"components.popUpWarning.button.confirm\": \"Megerősítés\",\n    \"components.popUpWarning.message\": \"Biztosan törölni szeretné?\",\n    \"components.popUpWarning.title\": \"Erősítse meg\",\n    \"form.button.continue\": \"Folytatás\",\n    \"form.button.done\": \"Kész\",\n    \"global.search\": \"Keresés\",\n    \"global.actions\": \"Műveletek\",\n    \"global.back\": \"Vissza\",\n    \"global.cancel\": \"Mégsem\",\n    \"global.change-password\": \"Jelszó megváltoztatása\",\n    \"global.content-manager\": \"Tartalomkezelő\",\n    \"global.continue\": \"Folytatás\",\n    \"global.delete\": \"Törlés\",\n    \"global.delete-target\": \"{target} törlése\",\n    \"global.description\": \"Leírás\",\n    \"global.details\": \"Részletek\",\n    \"global.disabled\": \"Letiltva\",\n    \"global.documentation\": \"Dokumentáció\",\n    \"global.enabled\": \"Engedélyezve\",\n    \"global.finish\": \"Befejezés\",\n    \"global.marketplace\": \"Piactér\",\n    \"global.name\": \"Név\",\n    \"global.none\": \"Nincs\",\n    \"global.password\": \"Jelszó\",\n    \"global.plugins\": \"Bővítmények\",\n    \"global.plugins.content-manager\": \"Tartalomkezelő\",\n    \"global.plugins.content-manager.description\": \"Gyors módja annak, hogy megtekintse, szerkesztse és törölje az adatokat az adatbázisában.\",\n    \"global.plugins.content-type-builder\": \"Tartalomtípus-építő\",\n    \"global.plugins.content-type-builder.description\": \"Modellezze az API adatszerkezetét. Hozzon létre új mezőket és relationokat csak egy perc alatt. A fájlok automatikusan létrehozódnak és frissülnek a projektjében.\",\n    \"global.plugins.email\": \"E-mail\",\n    \"global.plugins.email.description\": \"Állítsa be az alkalmazást, hogy e-maileket küldjön.\",\n    \"global.plugins.upload\": \"Médiatár\",\n    \"global.plugins.upload.description\": \"Médiafájlok kezelése.\",\n    \"global.plugins.graphql\": \"GraphQL\",\n    \"global.plugins.graphql.description\": \"GraphQL végpont hozzáadása alapértelmezett API metódusokkal.\",\n    \"global.plugins.documentation\": \"Dokumentáció\",\n    \"global.plugins.documentation.description\": \"OpenAPI Dokumentum létrehozása és API megjelenítése SWAGGER UI-val.\",\n    \"global.plugins.i18n\": \"Nemzetköziítés\",\n    \"global.plugins.i18n.description\": \"Ez a plugin lehetővé teszi különböző nyelveken történő tartalom létrehozását, olvasását és frissítését, tanto az Admin Panelból, mint az API-ból.\",\n    \"global.plugins.sentry\": \"Sentry\",\n    \"global.plugins.sentry.description\": \"Strapi hibaesemények küldése a Sentry-be.\",\n    \"global.plugins.users-permissions\": \"Szerepek & Engedélyek\",\n    \"global.plugins.users-permissions.description\": \"API védelme teljes hitelesítési folyamattal JWT alapján. Ez a plugin egyúttal olyan ACL stratégiát is tartalmaz, amely lehetővé teszi a felhasználói csoportok közötti engedélyek kezelését.\",\n    \"global.profile\": \"Profil\",\n    \"global.prompt.unsaved\": \"Biztos, hogy elhagyja ezt az oldalt? Az összes módosítása elveszik\",\n    \"global.reset-password\": \"Jelszó visszaállítása\",\n    \"global.roles\": \"Szerepek\",\n    \"global.save\": \"Mentés\",\n    \"global.see-more\": \"Továbbiak megtekintése\",\n    \"global.select\": \"Kiválasztás\",\n    \"global.select-all-entries\": \"Az összes bejegyzés kiválasztása\",\n    \"global.settings\": \"Beállítások\",\n    \"global.type\": \"Típus\",\n    \"global.users\": \"Felhasználók\",\n    \"notification.contentType.relations.conflict\": \"A tartalomtípusnak ellenkező kapcsolatai vannak\",\n    \"notification.default.title\": \"Információ:\",\n    \"notification.error\": \"Hiba lépett fel\",\n    \"notification.error.layout\": \"Nem sikerült lekérni az elrendezést\",\n    \"notification.form.error.fields\": \"Az űrlap kitöltése hibás\",\n    \"notification.form.success.fields\": \"Változtatások elmentve\",\n    \"notification.link-copied\": \"A link a vágólapra másolva\",\n    \"notification.permission.not-allowed-read\": \"Ezt a dokumentumot nem tekintheti meg\",\n    \"notification.success.delete\": \"Az elemet törölték\",\n    \"notification.success.saved\": \"Mentve\",\n    \"notification.success.title\": \"Sikeres:\",\n    \"notification.success.apitokencreated\": \"API Token sikeresen létrehozva\",\n    \"notification.success.apitokenedited\": \"API Token sikeresen szerkesztve\",\n    \"notification.error.tokennamenotunique\": \"Név már hozzárendelve egy másik tokenhez\",\n    \"notification.version.update.message\": \"Megjelent a Strapi új verziója!\",\n    \"notification.warning.title\": \"Figyelmeztetés:\",\n    \"notification.warning.404\": \"404 - Nem található\",\n    or: or,\n    \"request.error.model.unknown\": \"Ez a modell nem létezik\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, hu as default, light, or, skipToContent, submit };\n//# sourceMappingURL=hu.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,kDAAkD;AAAA,EAClD,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,sCAAsC;AAAA,EACtC,qDAAqD;AAAA,EACrD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,2DAA2D;AAAA,EAC3D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,8DAA8D;AAAA,EAC9D,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,mEAAmE;AAAA,EACnE,0DAA0D;AAAA,EAC1D,6DAA6D;AAAA,EAC7D,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,gDAAgD;AAAA,EAChD,yCAAyC;AAAA,EACzC,oDAAoD;AAAA,EACpD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,2CAA2C;AAAA,EAC3C,0DAA0D;AAAA,EAC1D,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}