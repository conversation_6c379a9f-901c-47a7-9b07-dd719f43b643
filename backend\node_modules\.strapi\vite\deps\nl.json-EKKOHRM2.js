import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/translations/nl.json.mjs
var Analytics = "Analytics";
var Documentation = "Documentatie";
var Email = "E-mail";
var Password = "Wachtwoord";
var Provider = "Provider";
var ResetPasswordToken = "Wachtwoord Reset Token";
var Role = "Rol";
var light = "Licht";
var dark = "Donker";
var Username = "Gebruikersnaam";
var Users = "Gebruikers";
var anErrorOccurred = "Oeps! Er ging iets mis. Probeer het a.u.b. opnieuw.";
var clearLabel = "Wis";
var or = "OF";
var skipToContent = "Skip naar inhoud";
var submit = "Verzend";
var nl = {
  Analytics,
  "Auth.components.Oops.text": "Uw account is geblokkeerd.",
  "Auth.components.Oops.text.admin": "Als dit een fout is, neem dan contact op met uw beheerder.",
  "Auth.components.Oops.title": "Oeps...",
  "Auth.form.active.label": "Actief",
  "Auth.form.button.forgot-password": "E-mail versturen",
  "Auth.form.button.go-home": "TERUG NAAR HOME",
  "Auth.form.button.login": "Inloggen",
  "Auth.form.button.login.providers.error": "We kunnen je niet verbinden via de geselecteerde provider.",
  "Auth.form.button.login.strapi": "Log in via Strapi",
  "Auth.form.button.password-recovery": "Wachtwoordherstel",
  "Auth.form.button.register": "Beginnen",
  "Auth.form.confirmPassword.label": "Bevestig wachtwoord",
  "Auth.form.currentPassword.label": "Huidige wachtwoord",
  "Auth.form.email.label": "E-mail",
  "Auth.form.email.placeholder": "Bijv. <EMAIL>",
  "Auth.form.error.blocked": "Uw account werd geblokkeerd door de beheerder.",
  "Auth.form.error.code.provide": "Incorrecte code ingevoerd.",
  "Auth.form.error.confirmed": "Het e-mailadres voor uw account is nog niet bevestigd.",
  "Auth.form.error.email.invalid": "Dit e-mailadres is onjuist.",
  "Auth.form.error.email.provide": "Voer a.u.b. je gebruikersnaam of je e-mail in.",
  "Auth.form.error.email.taken": "E-mailadres is al in gebruik.",
  "Auth.form.error.invalid": "Gebruikersnaam of wachtwoord onjuist.",
  "Auth.form.error.params.provide": "Incorrecte parameters ingevoerd.",
  "Auth.form.error.password.format": "Het wachtwoord mag het `$` symbool niet meer dan drie keer bevatten.",
  "Auth.form.error.password.local": "Deze gebruiker heeft nooit een lokaal wachtwoord ingesteld, gebruik de provider welke gebruikt is tijdens het maken van het account om in te loggen.",
  "Auth.form.error.password.matching": "Wachtwoorden komen niet overeen.",
  "Auth.form.error.password.provide": "Voer a.u.b. je wachtwoord in.",
  "Auth.form.error.ratelimit": "Teveel pogingen, gelieve opnieuw te proberen binnen een minuut.",
  "Auth.form.error.user.not-exist": "Dit e-mailadres bestaat niet.",
  "Auth.form.error.username.taken": "Gebruikersnaam is al in gebruik.",
  "Auth.form.firstname.label": "Voornaam",
  "Auth.form.firstname.placeholder": "Bijv. Kai",
  "Auth.form.forgot-password.email.label": "Voer je e-mail in",
  "Auth.form.forgot-password.email.label.success": "E-mail succesvol verstuurd naar",
  "Auth.form.lastname.label": "Achternaam",
  "Auth.form.lastname.placeholder": "Bijv. Doe",
  "Auth.form.password.hide-password": "Wachtwoord verbergen",
  "Auth.form.password.hint": "Wachtwoord moet ten minste 8 karakters, 1 hoofdletter, 1 kleine letter en 1 nummer bevatten",
  "Auth.form.password.show-password": "Wachtwoord tonen",
  "Auth.form.register.news.label": "Houd me op de hoogte van de nieuwe functionaliteiten en aankomende verbeteringen (door dit te doen accepteer je de {terms} en het {policy}).",
  "Auth.form.register.subtitle": "Je inloggegevens worden alleen gebruikt om jezelf te authenticeren op het beheerders dashboard. Alle gegevens worden opgeslagen in je eigen database.",
  "Auth.form.rememberMe.label": "Onthoud mij",
  "Auth.form.username.label": "Gebruikersnaam",
  "Auth.form.username.placeholder": "Bijv. Kai_Doe",
  "Auth.form.welcome.subtitle": "Inloggen op je Strapi account",
  "Auth.form.welcome.title": "Welkom!",
  "Auth.link.forgot-password": "Wachtwoord vergeten?",
  "Auth.link.ready": "Klaar om in te loggen?",
  "Auth.link.signin": "Inloggen",
  "Auth.link.signin.account": "Heb je al een account?",
  "Auth.login.sso.divider": "Of log in met",
  "Auth.login.sso.loading": "Providers laden...",
  "Auth.login.sso.subtitle": "Log in met SSO",
  "Auth.privacy-policy-agreement.policy": "privacybeleid",
  "Auth.privacy-policy-agreement.terms": "voorwaarden",
  "Auth.reset-password.title": "Herstel wachtwoord",
  "Content Manager": "Content Manager",
  "Content Type Builder": "Content-Type Bouwer",
  Documentation,
  Email,
  "Files Upload": "Upload Bestanden",
  "HomePage.head.title": "Startpagina",
  "HomePage.roadmap": "Bekijk onze roadmap",
  "HomePage.welcome.congrats": "Gefeliciteerd!",
  "HomePage.welcome.congrats.content": "Je bent aangemeld als de eerste beheerder. Om de krachtige functies van Strapi te ontdekken,",
  "HomePage.welcome.congrats.content.bold": "raden we je aan om je eerste Collectie-Type aan te maken.",
  "Media Library": "Media Bibliotheek",
  "New entry": "Nieuwe invoer",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "Rollen & Rechten",
  "Roles.ListPage.notification.delete-all-not-allowed": "Sommige rollen konden niet verwijderd worden omdat ze aan gebruikers zijn gekoppeld",
  "Roles.ListPage.notification.delete-not-allowed": "Een rol kan niet verwijderd worden als deze is gekoppeld aan gebruikers",
  "Roles.RoleRow.select-all": "Selecteer {name} voor bulkacties",
  "Roles.RoleRow.user-count": "{number, plural, =0 {# gebruiker} one {# gebruiker} other {# gebruikers}}",
  "Roles.components.List.empty.withSearch": "Er is geen rol die overeenkomt met de zoekopdracht ({search})...",
  "Settings.PageTitle": "Instellingen - {name}",
  "Settings.apiTokens.addFirstToken": "Voeg je eerste API Token toe",
  "Settings.apiTokens.addNewToken": "Voeg API Token toe",
  "Settings.tokens.copy.editMessage": "Voor veiligheidsredenen kun je je API Token maar één keer zien.",
  "Settings.tokens.copy.editTitle": "Deze token is niet meer toegankelijk.",
  "Settings.tokens.copy.lastWarning": "Maak een kopie van deze token, we laten deze niet nog eens zien!",
  "Settings.apiTokens.create": "Token toevoegen",
  "Settings.apiTokens.description": "Lijst van gegenereerde tokens met API-toegang",
  "Settings.apiTokens.emptyStateLayout": "Je hebt nog geen content...",
  "Settings.apiTokens.ListView.headers.name": "Naam",
  "Settings.apiTokens.ListView.headers.description": "Omschrijving",
  "Settings.apiTokens.ListView.headers.type": "Token type",
  "Settings.apiTokens.ListView.headers.createdAt": "Aangemaakt op",
  "Settings.apiTokens.ListView.headers.lastUsedAt": "Laatst gebruikt op",
  "Settings.tokens.notification.copied": "Token gekopieerd naar klembord.",
  "Settings.apiTokens.title": "API Tokens",
  "Settings.tokens.types.full-access": "Volledige toegang",
  "Settings.tokens.types.read-only": "Alleen lezen",
  "Settings.tokens.duration.7-days": "7 dagen",
  "Settings.tokens.duration.30-days": "30 dagen",
  "Settings.tokens.duration.90-days": "90 dagen",
  "Settings.tokens.duration.unlimited": "Unlimited",
  "Settings.tokens.form.duration": "Token duur",
  "Settings.tokens.form.type": "Token type",
  "Settings.tokens.duration.expiration-date": "Vervaldatum",
  "Settings.apiTokens.createPage.permissions.title": "Rechten",
  "Settings.apiTokens.createPage.permissions.description": "Hieronder staan alleen acties die aan een route zijn gebonden.",
  "Settings.tokens.RegenerateDialog.title": "Token opnieuw genereren",
  "Settings.tokens.popUpWarning.message": "Weet u zeker dat u dit token opnieuw wilt genereren?",
  "Settings.tokens.Button.cancel": "Annuleren",
  "Settings.tokens.Button.regenerate": "Regenereren",
  "Settings.application.description": "Beheerders dashboard's globale informatie",
  "Settings.application.edition-title": "huidige editie",
  "Settings.application.get-help": "Hulp krijgen",
  "Settings.application.link-pricing": "Bekijk alle abonnementen",
  "Settings.application.link-upgrade": "Upgrade je beheerders dashboard",
  "Settings.application.node-version": "node versie",
  "Settings.application.strapi-version": "strapi versie",
  "Settings.application.strapiVersion": "strapi versie",
  "Settings.application.title": "Overzicht",
  "Settings.application.customization": "Maatwerk",
  "Settings.application.customization.carousel.title": "Logo",
  "Settings.application.customization.carousel.change-action": "Pas logo aan",
  "Settings.application.customization.carousel.reset-action": "Herstel logo",
  "Settings.application.customization.carousel-slide.label": "Logo schuif",
  "Settings.application.customization.carousel-hint": "Pas Strapi dashboard logo aan (maximaal formaat: {dimension}x{dimension}, maximale bestandsgrootte: {size}KB)",
  "Settings.application.customization.modal.cancel": "Annuleren",
  "Settings.application.customization.modal.upload": "Upload logo",
  "Settings.application.customization.modal.tab.label": "Hoe wil je de bestanden uploaden?",
  "Settings.application.customization.modal.upload.from-computer": "Vanaf computer",
  "Settings.application.customization.modal.upload.file-validation": "maximaal formaat: {dimension}x{dimension}, maximale bestandsgrootte: {size}KB",
  "Settings.application.customization.modal.upload.error-format": "Verkeerd formaat ge-upload (alleen jpeg, jpg, png en svg worden geaccepteerd).",
  "Settings.application.customization.modal.upload.error-size": "Te groot bestand ge-upload (maximaal formaat: {dimension}x{dimension}, maximale bestandsgrootte: {size}KB)",
  "Settings.application.customization.modal.upload.error-network": "Netwerk fout",
  "Settings.application.customization.modal.upload.cta.browse": "Blader bestanden",
  "Settings.application.customization.modal.upload.drag-drop": "Hier naar toe slepen",
  "Settings.application.customization.modal.upload.from-url": "Van url",
  "Settings.application.customization.modal.upload.from-url.input-label": "URL",
  "Settings.application.customization.modal.upload.next": "Volgende",
  "Settings.application.customization.modal.pending": "Pas logo aan",
  "Settings.application.customization.modal.pending.choose-another": "Kies een ander logo",
  "Settings.application.customization.modal.pending.title": "Logo klaar om te uploaden",
  "Settings.application.customization.modal.pending.subtitle": "Manage the chosen logo before uploading it",
  "Settings.application.customization.modal.pending.upload": "Upload logo",
  "Settings.application.customization.modal.pending.card-badge": "image",
  "Settings.error": "Fout",
  "Settings.global": "Globale Instellingen",
  "Settings.permissions": "Administratiepaneel",
  "Settings.permissions.category": "Rechten instellen voor {category}",
  "Settings.permissions.category.plugins": "Rechten instellen voor de {category} plugin",
  "Settings.permissions.conditions.anytime": "Altijd",
  "Settings.permissions.conditions.apply": "Pas toe",
  "Settings.permissions.conditions.can": "Kan",
  "Settings.permissions.conditions.define-conditions": "Definieer voorwaarden",
  "Settings.permissions.conditions.links": "Links",
  "Settings.permissions.conditions.no-actions": "Selecteer eerst acties (creëer, lees, update, ...) voordat je voorwaarden definieert.",
  "Settings.permissions.conditions.none-selected": "Altijd",
  "Settings.permissions.conditions.or": "OF",
  "Settings.permissions.conditions.when": "Wanneer",
  "Settings.permissions.select-all-by-permission": "Selecteer alle {label} rechten",
  "Settings.permissions.select-by-permission": "Selecteer {label} recht",
  "Settings.permissions.users.create": "Nodig nieuwe gebruiker uit",
  "Settings.permissions.users.email": "E-mail",
  "Settings.permissions.users.firstname": "Voornaam",
  "Settings.permissions.users.lastname": "Achternaam",
  "Settings.permissions.users.user-status": "User status",
  "Settings.permissions.users.roles": "Roles",
  "Settings.permissions.users.username": "Username",
  "Settings.permissions.users.active": "Active",
  "Settings.permissions.users.inactive": "Inactive",
  "Settings.permissions.users.form.sso": "Verbind met SSO",
  "Settings.permissions.users.form.sso.description": "Wanneer dit aan staat (ON), kunnen gebruikers inloggen met SSO",
  "Settings.permissions.users.listview.header.subtitle": "Alle gebruikers die toegang hebben tot het Strapi beheerders dashboard",
  "Settings.permissions.users.tabs.label": "Tabs Rechten",
  "Settings.permissions.users.strapi-super-admin": "Super Admin",
  "Settings.permissions.users.strapi-editor": "Editor",
  "Settings.permissions.users.strapi-author": "Auteur",
  "Settings.profile.form.notify.data.loaded": "Je profielgegevens zijn geladen",
  "Settings.profile.form.section.experience.clear.select": "Wis de geselecteerde interfacetaal",
  "Settings.profile.form.section.experience.here": "documentatie",
  "Settings.profile.form.section.experience.interfaceLanguage": "Interfacetaal",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "Hierdoor wordt alleen je eigen interface in de gekozen taal weergegeven.",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "Selectie zal de interfacetaal alleen voor jou veranderen. Raadpleeg deze {here} om andere talen beschikbaar te maken voor uw team.",
  "Settings.profile.form.section.experience.mode.label": "Interface modus",
  "Settings.profile.form.section.experience.mode.hint": "Toont uw interface in de gekozen modus.",
  "Settings.profile.form.section.experience.mode.option-label": "{name} modus",
  light,
  dark,
  "Settings.profile.form.section.experience.title": "Ervaring",
  "Settings.profile.form.section.head.title": "Gebruikersprofiel",
  "Settings.profile.form.section.profile.page.title": "Profiel pagina",
  "Settings.roles.create.description": "Definieer de rechten om aan de rol te geven",
  "Settings.roles.create.title": "Creëer een rol",
  "Settings.roles.created": "Rol gecreëerd",
  "Settings.roles.edit.title": "Wijzig een rol",
  "Settings.roles.form.button.users-with-role": "{number, plural, =0 {# gebruikers} one {# gebruiker} other {# gebruikers}} met deze rol",
  "Settings.roles.form.created": "Gecreëerd",
  "Settings.roles.form.description": "Naam en beschrijving van de rol",
  "Settings.roles.form.permission.property-label": "{label} rechten",
  "Settings.roles.form.permissions.attributesPermissions": "Velden rechten",
  "Settings.roles.form.permissions.create": "Creëer",
  "Settings.roles.form.permissions.delete": "Verwijder",
  "Settings.roles.form.permissions.publish": "Publiceer",
  "Settings.roles.form.permissions.read": "Lees",
  "Settings.roles.form.permissions.update": "Update",
  "Settings.roles.list.button.add": "Nieuwe rol",
  "Settings.roles.list.description": "Lijst van rollen",
  "Settings.roles.title.singular": "rol",
  "Settings.sso.description": "Configureer de instellingen voor SSO functie.",
  "Settings.sso.form.defaultRole.description": "Dit zal de nieuwe geverifieerde gebruiker aan de geselecteerde rol koppelen",
  "Settings.sso.form.defaultRole.description-not-allowed": "Je hebt niet de juiste rechten om de beheerdersrollen te bekijken",
  "Settings.sso.form.defaultRole.label": "Standaard rol",
  "Settings.sso.form.registration.description": "Creëer nieuwe gebruiker met SSO login als er nog geen account bestaat",
  "Settings.sso.form.registration.label": "Automatische registratie",
  "Settings.sso.title": "Single Sign-On",
  "Settings.webhooks.create": "Maak een webhook",
  "Settings.webhooks.create.header": "Voeg een nieuwe header toe",
  "Settings.webhooks.created": "Webhook aangemaakt",
  "Settings.webhooks.event.publish-tooltip": "Dit event bestaat alleen voor content waar het Concept/Publiceer systeem is ingeschakeld",
  "Settings.webhooks.events.create": "Creëer",
  "Settings.webhooks.events.update": "Update",
  "Settings.webhooks.form.events": "Events",
  "Settings.webhooks.form.headers": "Headers",
  "Settings.webhooks.form.url": "URL",
  "Settings.webhooks.headers.remove": "Verwijder header rij {number}",
  "Settings.webhooks.key": "Sleutel",
  "Settings.webhooks.list.button.add": "Maak een nieuwe webhook",
  "Settings.webhooks.list.description": "Ontvang POST veranderingen als notificatie.",
  "Settings.webhooks.list.empty.description": "Voeg de eerste toe aan deze lijst.",
  "Settings.webhooks.list.empty.link": "Bekijk onze documentatie",
  "Settings.webhooks.list.empty.title": "Er zijn nog geen webhooks",
  "Settings.webhooks.list.th.actions": "acties",
  "Settings.webhooks.list.th.status": "status",
  "Settings.webhooks.singular": "webhook",
  "Settings.webhooks.title": "Webhooks",
  "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# item} other {# items}} geselecteerd",
  "Settings.webhooks.trigger": "Trigger",
  "Settings.webhooks.trigger.cancel": "Trigger annuleren",
  "Settings.webhooks.trigger.pending": "Bezig...",
  "Settings.webhooks.trigger.save": "Sla op om te triggeren",
  "Settings.webhooks.trigger.success": "Success vol!",
  "Settings.webhooks.trigger.success.label": "Trigger succesvol",
  "Settings.webhooks.trigger.test": "Test-trigger",
  "Settings.webhooks.trigger.title": "Opslaan vóór het triggeren",
  "Settings.webhooks.value": "Waarde",
  "Usecase.back-end": "Back-end developer",
  "Usecase.button.skip": "Deze vraag overslaan",
  "Usecase.content-creator": "Content maker",
  "Usecase.front-end": "Front-end developer",
  "Usecase.full-stack": "Full-stack developer",
  "Usecase.input.work-type": "Wat voor werk doe je?",
  "Usecase.notification.success.project-created": "Project is succesvol aangemaakt",
  "Usecase.other": "Anders",
  "Usecase.title": "Vertel eens wat meer over jezelf",
  Username,
  Users,
  "Users & Permissions": "Gebruikers & Rechten",
  "Users.components.List.empty": "Er zijn geen gebruiker...",
  "Users.components.List.empty.withFilters": "Er zijn geen gebruikers met de geselecteerde filters...",
  "Users.components.List.empty.withSearch": "Er zijn geen gebruikers die overeenkomen met de zoekopdracht ({search})...",
  "admin.pages.MarketPlacePage.head": "Marktplaats - Plugins",
  "admin.pages.MarketPlacePage.offline.title": "Je bent offline",
  "admin.pages.MarketPlacePage.offline.subtitle": "U moet verbonden zijn met internet om toegang te krijgen tot Strapi Market.",
  "admin.pages.MarketPlacePage.plugins": "Plugins",
  "admin.pages.MarketPlacePage.plugin.copy": "Kopieer de installatieopdracht",
  "admin.pages.MarketPlacePage.plugin.copy.success": "Installatieopdracht klaar om in uw terminal te worden geplakt",
  "admin.pages.MarketPlacePage.plugin.info": "Learn more",
  "admin.pages.MarketPlacePage.plugin.info.label": "Meer informatie over {pluginName}",
  "admin.pages.MarketPlacePage.plugin.info.text": "Learn more",
  "admin.pages.MarketPlacePage.plugin.installed": "Geïnstalleerd",
  "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "Gemaakt door Strapi",
  "admin.pages.MarketPlacePage.plugin.tooltip.verified": "Plug-in geverifieerd door Strapi",
  "admin.pages.MarketPlacePage.plugin.version": 'Werk uw Strapi-versie bij: "{strapiAppVersion}" naar: "{versionRange}"',
  "admin.pages.MarketPlacePage.plugin.version.null": 'Kan compatibiliteit met uw Strapi-versie niet verifiëren: "{strapiAppVersion}"',
  "admin.pages.MarketPlacePage.plugin.githubStars": "Deze plug-in heeft {starsCount} sterren op GitHub",
  "admin.pages.MarketPlacePage.plugin.downloads": "Deze plug-in heeft {downloadsCount} wekelijkse downloads",
  "admin.pages.MarketPlacePage.providers": "Aanbieders",
  "admin.pages.MarketPlacePage.provider.githubStars": "Deze provider heeft {starsCount} sterren op GitHub",
  "admin.pages.MarketPlacePage.provider.downloads": "Deze provider heeft {downloadsCount} wekelijkse downloads",
  "admin.pages.MarketPlacePage.search.clear": "Wis de zoekopdracht",
  "admin.pages.MarketPlacePage.search.empty": 'Geen resultaat voor "{target}"',
  "admin.pages.MarketPlacePage.search.placeholder": "Zoek",
  "admin.pages.MarketPlacePage.submit.plugin.link": "Stuur je plugin in",
  "admin.pages.MarketPlacePage.submit.provider.link": "Submit provider",
  "admin.pages.MarketPlacePage.subtitle": "Haal meer uit Strapi",
  "admin.pages.MarketPlacePage.tab-group.label": "Plug-ins en providers voor Strapi",
  "admin.pages.MarketPlacePage.missingPlugin.title": "Mis je een plug-in?",
  "admin.pages.MarketPlacePage.missingPlugin.description": "Vertel ons naar welke plug-in je op zoek bent en we laten het onze ontwikkelaars van plug-ins weten als ze op zoek zijn naar inspiratie!",
  "admin.pages.MarketPlacePage.sort.alphabetical": "Alfabetische volgorde",
  "admin.pages.MarketPlacePage.sort.newest": "Nieuwste",
  "admin.pages.MarketPlacePage.sort.alphabetical.selected": "Sorteer op alfabetische volgorde",
  "admin.pages.MarketPlacePage.sort.newest.selected": "Sorteer op nieuwste",
  "admin.pages.MarketPlacePage.filters.collections": "Collecties",
  "admin.pages.MarketPlacePage.filters.collectionsSelected": "{count, plural, =0 {Geen collecties} one {# collectie} other {# collecties}} geselecteerd",
  "admin.pages.MarketPlacePage.filters.categories": "Categories",
  "admin.pages.MarketPlacePage.filters.categoriesSelected": "{count, plural, =0 {Geen categorieën} one {# categorie} other {# categorieën}} geselecteerd",
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "Kopieer naar klembord",
  "app.component.search.label": "Zoek naar {target}",
  "app.component.table.duplicate": "Dupliceer {target}",
  "app.component.table.edit": "Pas {target} aan",
  "app.component.table.select.one-entry": "Selecteer {target}",
  "app.components.BlockLink.blog": "Blog",
  "app.components.BlockLink.blog.content": "Lees het laatste nieuws over Strapi en het ecosysteem.",
  "app.components.BlockLink.code": "Code voorbeelden",
  "app.components.BlockLink.code.content": "Leer door het testen van echte projecten die zijn ontwikkeld door de community.",
  "app.components.BlockLink.documentation.content": "Ontdek de essentiële concepten, handleidingen en instructies.",
  "app.components.BlockLink.tutorial": "Tutorials",
  "app.components.BlockLink.tutorial.content": "Volg stapsgewijze instructies om Strapi te gebruiken en aan te passen.",
  "app.components.Button.cancel": "Annuleer",
  "app.components.Button.confirm": "Bevestig",
  "app.components.Button.reset": "Resetten",
  "app.components.ComingSoonPage.comingSoon": "Binnenkort beschikbaar",
  "app.components.ConfirmDialog.title": "Bevestiging",
  "app.components.DownloadInfo.download": "Download bezig...",
  "app.components.DownloadInfo.text": "Dit kan even duren. Bedankt voor je geduld.",
  "app.components.EmptyAttributes.title": "Er zijn nog geen velden",
  "app.components.EmptyStateLayout.content-document": "Geen content gevonden",
  "app.components.EmptyStateLayout.content-permissions": "Je hebt niet de juiste rechten om die content te bekijken",
  "app.components.GuidedTour.CM.create.content": "<p>Maak en beheer alle inhoud hier in de content manager.</p><p>Bijvoorbeeld: als we verder gaan met het blog-websitevoorbeeld, kan men een artikel schrijven, opslaan en publiceren zoals ze willen.</p>< p>💡 Snelle tip - Vergeet niet op publiceren te klikken bij de inhoud die u maakt.</p>",
  "app.components.GuidedTour.CM.create.title": "⚡️ Maak content",
  "app.components.GuidedTour.CM.success.content": "<p>Geweldig, nog een laatste stap te gaan!</p><b>🚀  Zie inhoud in actie</b>",
  "app.components.GuidedTour.CM.success.cta.title": "Test de API",
  "app.components.GuidedTour.CM.success.title": "Stap 2: Voltooid ✅",
  "app.components.GuidedTour.CTB.create.content": "<p>Verzamelingstypen helpen u bij het beheren van meerdere inzendingen. Enkele typen zijn geschikt om slechts één inzending te beheren.</p> <p>Bijvoorbeeld: voor een blogwebsite zijn Artikelen een verzamelingstype, terwijl een startpagina een enkelvoudig type is. </p>",
  "app.components.GuidedTour.CTB.create.cta.title": "Een verzamelingstype bouwen",
  "app.components.GuidedTour.CTB.create.title": "🧠 Een eerste collectietype maken",
  "app.components.GuidedTour.CTB.success.content": "<p>Gaat goed!</p><b>⚡️ Wat zou je met de wereld willen delen?</b>",
  "app.components.GuidedTour.CTB.success.title": "Stap 1: Voltooid ✅",
  "app.components.GuidedTour.apiTokens.create.content": "<p>Genereer hier een authenticatietoken en haal de inhoud op die u zojuist hebt gemaakt.</p>",
  "app.components.GuidedTour.apiTokens.create.cta.title": "Genereer een API-token",
  "app.components.GuidedTour.apiTokens.create.title": "🚀 Zie inhoud in actie",
  "app.components.GuidedTour.apiTokens.success.content": "<p>Zie inhoud in actie door een HTTP-verzoek te doen:</p><ul><li><p>Naar deze URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Met de kop: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Voor meer manieren om met inhoud om te gaan, zie de <documentationLink>documentatie</documentationLink>.</p>",
  "app.components.GuidedTour.apiTokens.success.cta.title": "Ga terug naar de startpagina",
  "app.components.GuidedTour.apiTokens.success.title": "Stap 3: Voltooid ✅",
  "app.components.GuidedTour.create-content": "Inhoud maken",
  "app.components.GuidedTour.home.CM.title": "⚡️Wat zou je met de wereld willen delen?",
  "app.components.GuidedTour.home.CTB.cta.title": "Ga naar de Content-Type Bouwer",
  "app.components.GuidedTour.home.CTB.title": "🧠 Bouw de inhoudsstructuur",
  "app.components.GuidedTour.home.apiTokens.cta.title": "Test de API",
  "app.components.GuidedTour.skip": "Sla de rondleiding over",
  "app.components.GuidedTour.title": "3 stappen om te beginnen",
  "app.components.HomePage.button.blog": "Lees meer op de blog",
  "app.components.HomePage.community": "Word lid van de community",
  "app.components.HomePage.community.content": "Bespreek met teamleden, bijdragers en ontwikkelaars op verschillende kanalen.",
  "app.components.HomePage.create": "Creëer je eerste Content-Type",
  "app.components.HomePage.roadmap": "Bekijk onze roadmap",
  "app.components.HomePage.welcome": "Welkom aan boord 👋",
  "app.components.HomePage.welcome.again": "Welkom 👋",
  "app.components.HomePage.welcomeBlock.content": "Gefeliciteerd! Je bent ingelogd als de eerste beheerder. Om de krachtige functies van Strapi te ontdekken, raden we je aan om je eerste Collectie-Type te maken.",
  "app.components.HomePage.welcomeBlock.content.again": "We hopen dat je vooruitgang boekt met je project! Lees gerust het laatste nieuws over Strapi. We doen ons best om het product te verbeteren op basis van jouw feedback.",
  "app.components.HomePage.welcomeBlock.content.issues": "problemen.",
  "app.components.HomePage.welcomeBlock.content.raise": " of upgrade ",
  "app.components.ImgPreview.hint": "Sleep je bestand naar dit vak of {browse} naar een bestand om te uploaden",
  "app.components.ImgPreview.hint.browse": "zoek",
  "app.components.InputFile.newFile": "Nieuw bestand",
  "app.components.InputFileDetails.open": "Open in nieuw tabblad",
  "app.components.InputFileDetails.originalName": "Originele naam:",
  "app.components.InputFileDetails.remove": "Verwijder dit bestand",
  "app.components.InputFileDetails.size": "Grootte:",
  "app.components.InstallPluginPage.Download.description": "Het kan enkele seconden duren om de plugin te downloaden en te installeren.",
  "app.components.InstallPluginPage.Download.title": "Download bezig...",
  "app.components.InstallPluginPage.description": "Breid je app zonder moeite uit.",
  "app.components.LeftMenu.collapse": "Vouw de navigatiebalk samen",
  "app.components.LeftMenu.expand": "Vouw de navigatiebalk uit",
  "app.components.LeftMenu.general": "Algemeen",
  "app.components.LeftMenu.logout": "Uitloggen",
  "app.components.LeftMenu.logo.alt": "Application logo",
  "app.components.LeftMenu.plugins": "Plugins",
  "app.components.LeftMenu.trialCountdown": "Je proefperiode eindigt op {date}.",
  "app.components.LeftMenu.navbrand.title": "Strapi Dashboard",
  "app.components.LeftMenu.navbrand.workplace": "Werkplek",
  "app.components.LeftMenuFooter.help": "Help",
  "app.components.LeftMenuFooter.poweredBy": "Powered by ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "Collectie Types",
  "app.components.LeftMenuLinkContainer.configuration": "Configuraties",
  "app.components.LeftMenuLinkContainer.general": "Algemeen",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "Nog geen plugins geïnstalleerd",
  "app.components.LeftMenuLinkContainer.plugins": "Plugins",
  "app.components.LeftMenuLinkContainer.singleTypes": "Enkele Types",
  "app.components.ListPluginsPage.deletePlugin.description": "Het kan enkele seconden duren om de plugin te deinstalleren.",
  "app.components.ListPluginsPage.deletePlugin.title": "Deinstalleren",
  "app.components.ListPluginsPage.description": "Lijst van alle plugins voor dit project",
  "app.components.ListPluginsPage.head.title": "Lijst plugins",
  "app.components.Logout.logout": "Logout",
  "app.components.Logout.profile": "Profile",
  "app.components.MarketplaceBanner": "Ontdek plugins die zijn gebouwd door de community en nog veel meer geweldige dingen om je project een kickstart te geven op Strapi Awesome.",
  "app.components.MarketplaceBanner.image.alt": "een strapi rocket logo",
  "app.components.MarketplaceBanner.link": "Bekijk het nu",
  "app.components.NotFoundPage.back": "Terug naar thuispagina",
  "app.components.NotFoundPage.description": "Niet Gevonden",
  "app.components.Official": "Officieel",
  "app.components.Onboarding.help.button": "Help knop",
  "app.components.Onboarding.label.completed": "% voltooid",
  "app.components.Onboarding.title": "Aan de slag-video's",
  "app.components.PluginCard.Button.label.download": "Download",
  "app.components.PluginCard.Button.label.install": "Al geïnstalleerd",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "De autoReload-functie is vereist. Herstart de server met `strapi develop`",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "Ik begrijp het!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "Om veiligheidsredenen kan een plugin alleen worden gedownload in een ontwikkelomgeving.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "Downloaden is niet mogelijk",
  "app.components.PluginCard.compatible": "Geschikt voor jouw app",
  "app.components.PluginCard.compatibleCommunity": "Geschikt voor de community",
  "app.components.PluginCard.more-details": "Meer details",
  "app.components.ToggleCheckbox.off-label": "Uit",
  "app.components.ToggleCheckbox.on-label": "Aan",
  "app.components.Users.MagicLink.connect": "Kopieer en deel deze link om toegang te geven aan deze gebruiker",
  "app.components.Users.MagicLink.connect.sso": "Stuur deze link naar de gebruiker, de eerste login kan gedaan worden via een SSO provider",
  "app.components.Users.ModalCreateBody.block-title.details": "Gebruiker details",
  "app.components.Users.ModalCreateBody.block-title.roles": "Gebruiker's rollen",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "Een gebruiker kan één of meerdere rollen hebben",
  "app.components.Users.SortPicker.button-label": "Sorteer op",
  "app.components.Users.SortPicker.sortby.email_asc": "E-mail (A tot Z)",
  "app.components.Users.SortPicker.sortby.email_desc": "E-mail (Z tot A)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "Voornaam (A tot Z)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "Voornaam (Z tot A)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "Achternaam (A tot Z)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "Achternaam (Z tot A)",
  "app.components.Users.SortPicker.sortby.username_asc": "Gebruikersnaam (A tot Z)",
  "app.components.Users.SortPicker.sortby.username_desc": "Gebruikersnaam (Z tot A)",
  "app.components.listPlugins.button": "Nieuwe plugin toevoegen",
  "app.components.listPlugins.title.none": "Geen plugins geïnstalleerd",
  "app.components.listPluginsPage.deletePlugin.error": "Er is een fout opgetreden tijdens het verwijderen van de plugin",
  "app.containers.App.notification.error.init": "Er is een fout opgetreden bij het aanvragen van de API",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "Als je deze link niet ontvangt, neem dan contact op met je beheerder.",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "Het kan een paar minuten duren voordat je de e-mail met een link om je wachtwoord te herstellen ontvangt.",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "E-mail verzonden",
  "app.containers.Users.EditPage.form.active.label": "Actief",
  "app.containers.Users.EditPage.header.label": "Pas {name} aan",
  "app.containers.Users.EditPage.header.label-loading": "Pas gebruiker aan",
  "app.containers.Users.EditPage.roles-bloc-title": "Toegekende rollen",
  "app.containers.Users.ModalForm.footer.button-success": "Nodig gebruiker uit",
  "app.links.configure-view": "Configureer de weergave",
  "app.page.not.found": "Oeps! We kunnen de pagina die u zoekt niet vinden...",
  "app.static.links.cheatsheet": "Spiekbriefje",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "Voeg filter toe",
  "app.utils.close-label": "Sluit",
  "app.utils.defaultMessage": " ",
  "app.utils.duplicate": "Dupliceer",
  "app.utils.edit": "Pas aan",
  "app.utils.delete": "Verwijderen",
  "app.utils.errors.file-too-big.message": "Het bestand is te groot",
  "app.utils.filter-value": "Filter waarde",
  "app.utils.filters": "Filters",
  "app.utils.notify.data-loaded": "Het {target} is geladen",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "Publiceer",
  "app.utils.select-all": "Selecteer alles",
  "app.utils.select-field": "Selecteer veld",
  "app.utils.select-filter": "Selecteer filter",
  "app.utils.unpublish": "Depubliceer",
  clearLabel,
  "coming.soon": "Deze content is momenteel onder constructie en komt over een paar weken terug!",
  "component.Input.error.validation.integer": "De waarde moet een geheel getal zijn",
  "components.AutoReloadBlocker.description": "Start Strapi met een van de volgende commands:",
  "components.AutoReloadBlocker.header": "De herlaad feature is nodig voor deze extensie",
  "components.ErrorBoundary.title": "Er is iets fout gegaan...",
  "components.FilterOptions.FILTER_TYPES.$contains": "bevat",
  "components.FilterOptions.FILTER_TYPES.$containsi": "bevat (niet hoofdlettergevoelig)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "eindigt op",
  "components.FilterOptions.FILTER_TYPES.$endsWithi": "eindigt op (niet hoofdlettergevoelig)",
  "components.FilterOptions.FILTER_TYPES.$eq": "is",
  "components.FilterOptions.FILTER_TYPES.$eqi": "is (niet hoofdlettergevoelig)",
  "components.FilterOptions.FILTER_TYPES.$gt": "is groter dan",
  "components.FilterOptions.FILTER_TYPES.$gte": "is groter dan of gelijk aan",
  "components.FilterOptions.FILTER_TYPES.$lt": "is kleiner dan",
  "components.FilterOptions.FILTER_TYPES.$lte": "is kleiner dan of gelijk aan",
  "components.FilterOptions.FILTER_TYPES.$ne": "is niet",
  "components.FilterOptions.FILTER_TYPES.$nei": "is niet (niet hoofdlettergevoelig)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "bevat niet",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "bevat niet (niet hoofdlettergevoelig)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "is niet null",
  "components.FilterOptions.FILTER_TYPES.$null": "is null",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "start met",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "start met (niet hoofdlettergevoelig)",
  "components.Input.error.attribute.key.taken": "Deze waarde bestaat al",
  "components.Input.error.attribute.sameKeyAndName": "Mag niet gelijk zijn",
  "components.Input.error.attribute.taken": "Deze veld naam bestaat al",
  "components.Input.error.contain.lowercase": "Wachtwoord moet op zijn minst één kleine letter bevatten",
  "components.Input.error.contain.number": "Wachtwoord moet op zijn minst één nummer bevatten",
  "components.Input.error.contain.uppercase": "Wachtwoord moet op zijn minst één hoofdletter bevatten",
  "components.Input.error.contentTypeName.taken": "Deze naam bestaat al",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "Wachtwoorden komen niet overeen",
  "components.Input.error.validation.email": "Dit is geen e-mailadres",
  "components.Input.error.validation.json": "Dit komt niet overeen met het JSON-formaat",
  "components.Input.error.validation.lowercase": "De waarde moet in kleine letters zijn",
  "components.Input.error.validation.max": "De waarde is te hoog {max}.",
  "components.Input.error.validation.maxLength": "De waarde is te lang {max}.",
  "components.Input.error.validation.min": "De waarde is te laag {min}.",
  "components.Input.error.validation.minLength": "De waarde is te kort {min}.",
  "components.Input.error.validation.minSupMax": "Mag niet superieur zijn.",
  "components.Input.error.validation.regex": "De ingevoerde waarde komt niet overeen met de regex.",
  "components.Input.error.validation.required": "Deze waarde is verplicht.",
  "components.Input.error.validation.unique": "Deze waarde is al gebruik.",
  "components.InputSelect.option.placeholder": "Kies hier",
  "components.ListRow.empty": "Er is geen data beschikbaar.",
  "components.NotAllowedInput.text": "Geen rechten om dit veld te bekijken",
  "components.OverlayBlocker.description": "Je gebruikt een feature waardoor de server opnieuw op moet starten. Een moment geduld terwijl de server opnieuw opstart.",
  "components.OverlayBlocker.description.serverError": "De server zou opnieuw opgestart moeten zijn, controleer de logs in de terminal.",
  "components.OverlayBlocker.title": "Wachten op herstart...",
  "components.OverlayBlocker.title.serverError": "Het herstarten duurt langer dan verwacht",
  "components.PageFooter.select": "items per pagina",
  "components.ProductionBlocker.description": "Om veiligheidsredenen schakelen we deze plugin uit in andere omgevingen.",
  "components.ProductionBlocker.header": "Deze extensie is alleen beschikbaar in ontwikkelaarsmodus",
  "components.Search.placeholder": "Zoeken...",
  "components.TableHeader.sort": "Sorteer op {label}",
  "components.Wysiwyg.ToggleMode.markdown-mode": "Markdown modus",
  "components.Wysiwyg.ToggleMode.preview-mode": "Voorbeeld modus",
  "components.Wysiwyg.collapse": "Samenvouwen",
  "components.Wysiwyg.selectOptions.H1": "H1 titel",
  "components.Wysiwyg.selectOptions.H2": "H2 titel",
  "components.Wysiwyg.selectOptions.H3": "H3 titel",
  "components.Wysiwyg.selectOptions.H4": "H4 titel",
  "components.Wysiwyg.selectOptions.H5": "H5 titel",
  "components.Wysiwyg.selectOptions.H6": "H6 titel",
  "components.Wysiwyg.selectOptions.title": "Voeg een titel toe",
  "components.WysiwygBottomControls.charactersIndicators": "karakters",
  "components.WysiwygBottomControls.fullscreen": "Uitklappen",
  "components.WysiwygBottomControls.uploadFiles": "Sleep bestanden naar dit vak, plak ze van het klembord of {browse}",
  "components.WysiwygBottomControls.uploadFiles.browse": "selecteer ze",
  "components.pagination.go-to": "Ga naar pagina {page}",
  "components.pagination.go-to-next": "Ga naar volgende pagina",
  "components.pagination.go-to-previous": "Ga naar vorige pagina",
  "components.pagination.remaining-links": "En {number} andere links",
  "components.popUpWarning.button.cancel": "Nee, annuleren",
  "components.popUpWarning.button.confirm": "Ja, bevestigen",
  "components.popUpWarning.message": "Weet je zeker dat je dit wilt verwijderen?",
  "components.popUpWarning.title": "Bevestig a.u.b.",
  "form.button.continue": "Doorgaan",
  "form.button.done": "Klaar",
  "global.search": "Zoeken",
  "global.actions": "Acties",
  "global.back": "Terug",
  "global.cancel": "Annuleren",
  "global.change-password": "Verander wachtwoord",
  "global.content-manager": "Content beheer",
  "global.continue": "Doorgaan",
  "global.delete": "Verwijderen",
  "global.delete-target": "Verwijder {target}",
  "global.description": "Omschrijving",
  "global.details": "Details",
  "global.disabled": "Uitgeschakeld",
  "global.documentation": "Documentatie",
  "global.enabled": "Ingeschakeld",
  "global.finish": "Einde",
  "global.marketplace": "Marktplaats",
  "global.name": "Naam",
  "global.none": "Geen",
  "global.password": "Wachtwoord",
  "global.plugins": "Plug-ins",
  "global.plugins.content-manager": "Content beheer",
  "global.plugins.content-manager.description": "Snelle manier om de gegevens in uw database te bekijken, bewerken en verwijderen.",
  "global.plugins.content-type-builder": "Content-Type Bouwer",
  "global.plugins.content-type-builder.description": "Modelleer de datastructuur van uw API. Creëer nieuwe velden en relaties in slechts een minuut. De bestanden worden automatisch aangemaakt en bijgewerkt in uw project.",
  "global.plugins.email": "E-mail",
  "global.plugins.email.description": "Configureer uw applicatie om e-mails te verzenden.",
  "global.plugins.upload": "Mediatheek",
  "global.plugins.upload.description": "Beheer van mediabestanden.",
  "global.plugins.graphql": "GraphQL",
  "global.plugins.graphql.description": "Voegt GraphQL-eindpunt toe met standaard API-methoden.",
  "global.plugins.documentation": "Documentatie",
  "global.plugins.documentation.description": "Maak een OpenAPI-document en visualiseer uw API met SWAGGER UI.",
  "global.plugins.i18n": "Internationalisering",
  "global.plugins.i18n.description": "Deze plug-in maakt het mogelijk om inhoud in verschillende talen te creëren, te lezen en bij te werken, zowel vanuit het beheerdersdashboard als vanuit de API.",
  "global.plugins.sentry": "Sentry",
  "global.plugins.sentry.description": "Stuur Strapi-foutgebeurtenissen naar Sentry.",
  "global.plugins.users-permissions": "Rollen & Machtigingen",
  "global.plugins.users-permissions.description": "Bescherm uw API met een volledig authenticatieproces op basis van JWT. Deze plug-in wordt ook geleverd met een ACL-strategie waarmee u de machtigingen tussen de groepen gebruikers kunt beheren.",
  "global.profile": "Profiel",
  "global.prompt.unsaved": "Weet je zeker dat je deze pagina wilt verlaten? Al de wijzigingen gaan verloren.",
  "global.reset-password": "Herstel wachtwoord",
  "global.roles": "Rollen",
  "global.save": "Bewaar",
  "global.see-more": "Zie meer",
  "global.select": "Kies",
  "global.select-all-entries": "Selecteer alle vermeldingen",
  "global.settings": "Instellingen",
  "global.type": "Type",
  "global.users": "Gebruikers",
  "notification.contentType.relations.conflict": "Content-Type heeft conflicterende relaties",
  "notification.default.title": "Informatie:",
  "notification.error": "Er is een fout opgetreden",
  "notification.error.layout": "Kon de lay-out niet laden",
  "notification.form.error.fields": "Het formulier bevat enkele fouten",
  "notification.form.success.fields": "Wijzigingen opgeslagen",
  "notification.link-copied": "Link gekopieerd naar het klembord",
  "notification.permission.not-allowed-read": "Je hebt niet de rechten om dit document te zien",
  "notification.success.delete": "Het item is verwijderd",
  "notification.success.saved": "Opgeslagen",
  "notification.success.title": "Succes:",
  "notification.success.apitokencreated": "API-token is gemaakt",
  "notification.success.apitokenedited": "API-token succesvol bewerkt",
  "notification.error.tokennamenotunique": "Naam al toegewezen aan een ander token",
  "notification.version.update.message": "Een nieuwe versie van Strapi is beschikbaar!",
  "notification.warning.title": "Waarschuwing:",
  "notification.warning.404": "404 - Niet gevonden",
  or,
  "request.error.model.unknown": "Dit model bestaat niet",
  skipToContent,
  submit
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  anErrorOccurred,
  clearLabel,
  dark,
  nl as default,
  light,
  or,
  skipToContent,
  submit
};
//# sourceMappingURL=nl.json-EKKOHRM2.js.map
