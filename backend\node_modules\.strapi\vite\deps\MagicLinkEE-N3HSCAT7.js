import {
  Magic<PERSON>inkWrapper
} from "./chunk-AEVEKNWA.js";
import {
  getBasename
} from "./chunk-QIJGNK42.js";
import "./chunk-67LA7IEH.js";
import "./chunk-PW6GS6S3.js";
import {
  useIntl
} from "./chunk-Y6YT4U2T.js";
import "./chunk-FOD4ENRR.js";
import "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-3CQBCJ3G.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/SettingsPage/pages/Users/<USER>/MagicLinkEE.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var MagicLinkEE = ({ registrationToken }) => {
  const { formatMessage } = useIntl();
  if (registrationToken) {
    return (0, import_jsx_runtime.jsx)(MagicLinkWrapper, {
      target: `${window.location.origin}${getBasename()}/auth/register?registrationToken=${registrationToken}`,
      children: formatMessage({
        id: "app.components.Users.MagicLink.connect",
        defaultMessage: "Copy and share this link to give access to this user"
      })
    });
  }
  return (0, import_jsx_runtime.jsx)(MagicLinkWrapper, {
    target: `${window.location.origin}${getBasename()}/auth/login`,
    children: formatMessage({
      id: "app.components.Users.MagicLink.connect.sso",
      defaultMessage: "Send this link to the user, the first login can be made via a SSO provider."
    })
  });
};
export {
  MagicLinkEE
};
//# sourceMappingURL=MagicLinkEE-N3HSCAT7.js.map
