{"version": 3, "sources": ["../../../lodash/_baseDifference.js", "../../../lodash/without.js", "../../../lodash/take.js", "../../../@strapi/plugin-users-permissions/admin/src/contexts/UsersPermissionsContext/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/utils/formatPluginName.js", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/init.js", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/PermissionRow/CheckboxWrapper.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/PermissionRow/SubCategory.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/PermissionRow/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/reducer.js", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/BoundRoute/getMethodColor.js", "../../../@strapi/plugin-users-permissions/admin/src/components/BoundRoute/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/Policies/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/UsersPermissions/init.js", "../../../@strapi/plugin-users-permissions/admin/src/components/UsersPermissions/reducer.js", "../../../@strapi/plugin-users-permissions/admin/src/components/UsersPermissions/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/constants.js", "../../../@strapi/plugin-users-permissions/admin/src/utils/cleanPermissions.js", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/hooks/usePlugins.js", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/pages/CreatePage.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/pages/EditPage.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/pages/ListPage/components/TableBody.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/pages/ListPage/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/index.jsx"], "sourcesContent": ["var SetCache = require('./_SetCache'),\n    arrayIncludes = require('./_arrayIncludes'),\n    arrayIncludesWith = require('./_arrayIncludesWith'),\n    arrayMap = require('./_arrayMap'),\n    baseUnary = require('./_baseUnary'),\n    cacheHas = require('./_cacheHas');\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * The base implementation of methods like `_.difference` without support\n * for excluding multiple arrays or iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Array} values The values to exclude.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n */\nfunction baseDifference(array, values, iteratee, comparator) {\n  var index = -1,\n      includes = arrayIncludes,\n      isCommon = true,\n      length = array.length,\n      result = [],\n      valuesLength = values.length;\n\n  if (!length) {\n    return result;\n  }\n  if (iteratee) {\n    values = arrayMap(values, baseUnary(iteratee));\n  }\n  if (comparator) {\n    includes = arrayIncludesWith;\n    isCommon = false;\n  }\n  else if (values.length >= LARGE_ARRAY_SIZE) {\n    includes = cacheHas;\n    isCommon = false;\n    values = new SetCache(values);\n  }\n  outer:\n  while (++index < length) {\n    var value = array[index],\n        computed = iteratee == null ? value : iteratee(value);\n\n    value = (comparator || value !== 0) ? value : 0;\n    if (isCommon && computed === computed) {\n      var valuesIndex = valuesLength;\n      while (valuesIndex--) {\n        if (values[valuesIndex] === computed) {\n          continue outer;\n        }\n      }\n      result.push(value);\n    }\n    else if (!includes(values, computed, comparator)) {\n      result.push(value);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseDifference;\n", "var baseDifference = require('./_baseDifference'),\n    baseRest = require('./_baseRest'),\n    isArrayLikeObject = require('./isArrayLikeObject');\n\n/**\n * Creates an array excluding all given values using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * **Note:** Unlike `_.pull`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {...*} [values] The values to exclude.\n * @returns {Array} Returns the new array of filtered values.\n * @see _.difference, _.xor\n * @example\n *\n * _.without([2, 1, 2, 3], 1, 2);\n * // => [3]\n */\nvar without = baseRest(function(array, values) {\n  return isArrayLikeObject(array)\n    ? baseDifference(array, values)\n    : [];\n});\n\nmodule.exports = without;\n", "var baseSlice = require('./_baseSlice'),\n    toInteger = require('./toInteger');\n\n/**\n * Creates a slice of `array` with `n` elements taken from the beginning.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=1] The number of elements to take.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.take([1, 2, 3]);\n * // => [1]\n *\n * _.take([1, 2, 3], 2);\n * // => [1, 2]\n *\n * _.take([1, 2, 3], 5);\n * // => [1, 2, 3]\n *\n * _.take([1, 2, 3], 0);\n * // => []\n */\nfunction take(array, n, guard) {\n  if (!(array && array.length)) {\n    return [];\n  }\n  n = (guard || n === undefined) ? 1 : toInteger(n);\n  return baseSlice(array, 0, n < 0 ? 0 : n);\n}\n\nmodule.exports = take;\n", "import React, { createContext, useContext } from 'react';\n\nimport PropTypes from 'prop-types';\n\nconst UsersPermissions = createContext({});\n\nconst UsersPermissionsProvider = ({ children, value }) => {\n  return <UsersPermissions.Provider value={value}>{children}</UsersPermissions.Provider>;\n};\n\nconst useUsersPermissions = () => useContext(UsersPermissions);\n\nUsersPermissionsProvider.propTypes = {\n  children: PropTypes.node.isRequired,\n  value: PropTypes.object.isRequired,\n};\n\nexport { UsersPermissions, UsersPermissionsProvider, useUsersPermissions };\n", "import upperFirst from 'lodash/upperFirst';\n\nfunction formatPluginName(pluginSlug) {\n  switch (pluginSlug) {\n    case 'application':\n      return 'Application';\n    case 'plugin::content-manager':\n      return 'Content manager';\n    case 'plugin::content-type-builder':\n      return 'Content types builder';\n    case 'plugin::documentation':\n      return 'Documentation';\n    case 'plugin::email':\n      return 'Email';\n    case 'plugin::i18n':\n      return 'i18n';\n    case 'plugin::upload':\n      return 'Media Library';\n    case 'plugin::users-permissions':\n      return 'Users-permissions';\n    default:\n      return upperFirst(pluginSlug.replace('api::', '').replace('plugin::', ''));\n  }\n}\n\nexport default formatPluginName;\n", "const init = (initialState, permissions) => {\n  const collapses = Object.keys(permissions)\n    .sort()\n    .map((name) => ({ name, isOpen: false }));\n\n  return { ...initialState, collapses };\n};\n\nexport default init;\n", "import { Box } from '@strapi/design-system';\nimport { styled, css } from 'styled-components';\n\nconst activeCheckboxWrapperStyles = css`\n  background: ${(props) => props.theme.colors.primary100};\n\n  #cog {\n    opacity: 1;\n  }\n`;\n\nconst CheckboxWrapper = styled(Box)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  #cog {\n    opacity: 0;\n    path {\n      fill: ${(props) => props.theme.colors.primary600};\n    }\n  }\n\n  /* Show active style both on hover and when the action is selected */\n  ${(props) => props.isActive && activeCheckboxWrapperStyles}\n  &:hover {\n    ${activeCheckboxWrapperStyles}\n  }\n`;\n\nexport default CheckboxWrapper;\n", "import React, { useCallback, useMemo } from 'react';\n\nimport { Box, Checkbox, Flex, Typography, Grid, VisuallyHidden } from '@strapi/design-system';\nimport { Cog } from '@strapi/icons';\nimport get from 'lodash/get';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useUsersPermissions } from '../../../contexts/UsersPermissionsContext';\n\nimport CheckboxWrapper from './CheckboxWrapper';\n\nconst Border = styled.div`\n  flex: 1;\n  align-self: center;\n  border-top: 1px solid ${({ theme }) => theme.colors.neutral150};\n`;\n\nconst SubCategory = ({ subCategory }) => {\n  const { formatMessage } = useIntl();\n  const { onChange, onChangeSelectAll, onSelectedAction, selectedAction, modifiedData } =\n    useUsersPermissions();\n\n  const currentScopedModifiedData = useMemo(() => {\n    return get(modifiedData, subCategory.name, {});\n  }, [modifiedData, subCategory]);\n\n  const hasAllActionsSelected = useMemo(() => {\n    return Object.values(currentScopedModifiedData).every((action) => action.enabled === true);\n  }, [currentScopedModifiedData]);\n\n  const hasSomeActionsSelected = useMemo(() => {\n    return (\n      Object.values(currentScopedModifiedData).some((action) => action.enabled === true) &&\n      !hasAllActionsSelected\n    );\n  }, [currentScopedModifiedData, hasAllActionsSelected]);\n\n  const handleChangeSelectAll = useCallback(\n    ({ target: { name } }) => {\n      onChangeSelectAll({ target: { name, value: !hasAllActionsSelected } });\n    },\n    [hasAllActionsSelected, onChangeSelectAll]\n  );\n\n  const isActionSelected = useCallback(\n    (actionName) => {\n      return selectedAction === actionName;\n    },\n    [selectedAction]\n  );\n\n  return (\n    <Box>\n      <Flex justifyContent=\"space-between\" alignItems=\"center\">\n        <Box paddingRight={4}>\n          <Typography variant=\"sigma\" textColor=\"neutral600\">\n            {subCategory.label}\n          </Typography>\n        </Box>\n        <Border />\n        <Box paddingLeft={4}>\n          <Checkbox\n            name={subCategory.name}\n            checked={hasSomeActionsSelected ? 'indeterminate' : hasAllActionsSelected}\n            onCheckedChange={(value) =>\n              handleChangeSelectAll({ target: { name: subCategory.name, value } })\n            }\n          >\n            {formatMessage({ id: 'app.utils.select-all', defaultMessage: 'Select all' })}\n          </Checkbox>\n        </Box>\n      </Flex>\n      <Flex paddingTop={6} paddingBottom={6}>\n        <Grid.Root gap={2} style={{ flex: 1 }}>\n          {subCategory.actions.map((action) => {\n            const name = `${action.name}.enabled`;\n\n            return (\n              <Grid.Item col={6} key={action.name} direction=\"column\" alignItems=\"stretch\">\n                <CheckboxWrapper isActive={isActionSelected(action.name)} padding={2} hasRadius>\n                  <Checkbox\n                    checked={get(modifiedData, name, false)}\n                    name={name}\n                    onCheckedChange={(value) => onChange({ target: { name, value } })}\n                  >\n                    {action.label}\n                  </Checkbox>\n                  <button\n                    type=\"button\"\n                    onClick={() => onSelectedAction(action.name)}\n                    style={{ display: 'inline-flex', alignItems: 'center' }}\n                  >\n                    <VisuallyHidden tag=\"span\">\n                      {formatMessage(\n                        {\n                          id: 'app.utils.show-bound-route',\n                          defaultMessage: 'Show bound route for {route}',\n                        },\n                        {\n                          route: action.name,\n                        }\n                      )}\n                    </VisuallyHidden>\n                    <Cog id=\"cog\" cursor=\"pointer\" />\n                  </button>\n                </CheckboxWrapper>\n              </Grid.Item>\n            );\n          })}\n        </Grid.Root>\n      </Flex>\n    </Box>\n  );\n};\n\nSubCategory.propTypes = {\n  subCategory: PropTypes.object.isRequired,\n};\n\nexport default SubCategory;\n", "import React, { useMemo } from 'react';\n\nimport { Box } from '@strapi/design-system';\nimport sortBy from 'lodash/sortBy';\nimport PropTypes from 'prop-types';\n\nimport SubCategory from './SubCategory';\n\nconst PermissionRow = ({ name, permissions }) => {\n  const subCategories = useMemo(() => {\n    return sortBy(\n      Object.values(permissions.controllers).reduce((acc, curr, index) => {\n        const currentName = `${name}.controllers.${Object.keys(permissions.controllers)[index]}`;\n        const actions = sortBy(\n          Object.keys(curr).reduce((acc, current) => {\n            return [\n              ...acc,\n              {\n                ...curr[current],\n                label: current,\n                name: `${currentName}.${current}`,\n              },\n            ];\n          }, []),\n          'label'\n        );\n\n        return [\n          ...acc,\n          {\n            actions,\n            label: Object.keys(permissions.controllers)[index],\n            name: currentName,\n          },\n        ];\n      }, []),\n      'label'\n    );\n  }, [name, permissions]);\n\n  return (\n    <Box padding={6}>\n      {subCategories.map((subCategory) => (\n        <SubCategory key={subCategory.name} subCategory={subCategory} />\n      ))}\n    </Box>\n  );\n};\n\nPermissionRow.propTypes = {\n  name: PropTypes.string.isRequired,\n  permissions: PropTypes.object.isRequired,\n};\n\nexport default PermissionRow;\n", "import { produce } from 'immer';\n\nconst initialState = {\n  collapses: [],\n};\n\nconst reducer = (state, action) =>\n  // eslint-disable-next-line consistent-return\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case 'TOGGLE_COLLAPSE': {\n        draftState.collapses = state.collapses.map((collapse, index) => {\n          if (index === action.index) {\n            return { ...collapse, isOpen: !collapse.isOpen };\n          }\n\n          return { ...collapse, isOpen: false };\n        });\n\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n\nexport { initialState, reducer };\n", "import React, { useReducer } from 'react';\n\nimport { Accordion, Flex } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { useUsersPermissions } from '../../contexts/UsersPermissionsContext';\nimport formatPluginName from '../../utils/formatPluginName';\n\nimport init from './init';\nimport PermissionRow from './PermissionRow';\nimport { initialState, reducer } from './reducer';\n\nconst Permissions = () => {\n  const { modifiedData } = useUsersPermissions();\n  const { formatMessage } = useIntl();\n  const [{ collapses }] = useReducer(reducer, initialState, (state) => init(state, modifiedData));\n\n  return (\n    <Accordion.Root size=\"M\">\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n        {collapses.map((collapse, index) => (\n          <Accordion.Item key={collapse.name} value={collapse.name}>\n            <Accordion.Header variant={index % 2 === 0 ? 'secondary' : undefined}>\n              <Accordion.Trigger\n                caretPosition=\"right\"\n                description={formatMessage(\n                  {\n                    id: 'users-permissions.Plugin.permissions.plugins.description',\n                    defaultMessage: 'Define all allowed actions for the {name} plugin.',\n                  },\n                  { name: collapse.name }\n                )}\n              >\n                {formatPluginName(collapse.name)}\n              </Accordion.Trigger>\n            </Accordion.Header>\n            <Accordion.Content>\n              <PermissionRow permissions={modifiedData[collapse.name]} name={collapse.name} />\n            </Accordion.Content>\n          </Accordion.Item>\n        ))}\n      </Flex>\n    </Accordion.Root>\n  );\n};\n\nexport default Permissions;\n", "const getMethodColor = (verb) => {\n  switch (verb) {\n    case 'POST': {\n      return {\n        text: 'success600',\n        border: 'success200',\n        background: 'success100',\n      };\n    }\n    case 'GET': {\n      return {\n        text: 'secondary600',\n        border: 'secondary200',\n        background: 'secondary100',\n      };\n    }\n    case 'PUT': {\n      return {\n        text: 'warning600',\n        border: 'warning200',\n        background: 'warning100',\n      };\n    }\n    case 'DELETE': {\n      return {\n        text: 'danger600',\n        border: 'danger200',\n        background: 'danger100',\n      };\n    }\n    default: {\n      return {\n        text: 'neutral600',\n        border: 'neutral200',\n        background: 'neutral100',\n      };\n    }\n  }\n};\n\nexport default getMethodColor;\n", "import * as React from 'react';\n\nimport { Box, Flex, Typography } from '@strapi/design-system';\nimport map from 'lodash/map';\nimport tail from 'lodash/tail';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport getMethodColor from './getMethodColor';\n\nconst MethodBox = styled(Box)`\n  margin: -1px;\n  border-radius: ${({ theme }) => theme.spaces[1]} 0 0 ${({ theme }) => theme.spaces[1]};\n`;\n\nfunction BoundRoute({ route }) {\n  const { formatMessage } = useIntl();\n\n  const { method, handler: title, path } = route;\n  const formattedRoute = path ? tail(path.split('/')) : [];\n  const [controller = '', action = ''] = title ? title.split('.') : [];\n  const colors = getMethodColor(route.method);\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n      <Typography variant=\"delta\" tag=\"h3\">\n        {formatMessage({\n          id: 'users-permissions.BoundRoute.title',\n          defaultMessage: 'Bound route to',\n        })}\n        &nbsp;\n        <span>{controller}</span>\n        <Typography variant=\"delta\" textColor=\"primary600\">\n          .{action}\n        </Typography>\n      </Typography>\n      <Flex hasRadius background=\"neutral0\" borderColor=\"neutral200\" gap={0}>\n        <MethodBox background={colors.background} borderColor={colors.border} padding={2}>\n          <Typography fontWeight=\"bold\" textColor={colors.text}>\n            {method}\n          </Typography>\n        </MethodBox>\n        <Box paddingLeft={2} paddingRight={2}>\n          {map(formattedRoute, (value) => (\n            <Typography key={value} textColor={value.includes(':') ? 'neutral600' : 'neutral900'}>\n              /{value}\n            </Typography>\n          ))}\n        </Box>\n      </Flex>\n    </Flex>\n  );\n}\n\nBoundRoute.defaultProps = {\n  route: {\n    handler: 'Nocontroller.error',\n    method: 'GET',\n    path: '/there-is-no-path',\n  },\n};\n\nBoundRoute.propTypes = {\n  route: PropTypes.shape({\n    handler: PropTypes.string,\n    method: PropTypes.string,\n    path: PropTypes.string,\n  }),\n};\n\nexport default BoundRoute;\n", "import * as React from 'react';\n\nimport { Flex, Grid, Typography } from '@strapi/design-system';\nimport get from 'lodash/get';\nimport isEmpty from 'lodash/isEmpty';\nimport without from 'lodash/without';\nimport { useIntl } from 'react-intl';\n\nimport { useUsersPermissions } from '../../contexts/UsersPermissionsContext';\nimport BoundRoute from '../BoundRoute';\n\nconst Policies = () => {\n  const { formatMessage } = useIntl();\n  const { selectedAction, routes } = useUsersPermissions();\n\n  const path = without(selectedAction.split('.'), 'controllers');\n  const controllerRoutes = get(routes, path[0]);\n  const pathResolved = path.slice(1).join('.');\n\n  const displayedRoutes = isEmpty(controllerRoutes)\n    ? []\n    : controllerRoutes.filter((o) => o.handler.endsWith(pathResolved));\n\n  return (\n    <Grid.Item\n      col={5}\n      background=\"neutral150\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n      style={{ minHeight: '100%' }}\n      direction=\"column\"\n      alignItems=\"stretch\"\n    >\n      {selectedAction ? (\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          {displayedRoutes.map((route, key) => (\n            // eslint-disable-next-line react/no-array-index-key\n            <BoundRoute key={key} route={route} />\n          ))}\n        </Flex>\n      ) : (\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          <Typography variant=\"delta\" tag=\"h3\">\n            {formatMessage({\n              id: 'users-permissions.Policies.header.title',\n              defaultMessage: 'Advanced settings',\n            })}\n          </Typography>\n          <Typography tag=\"p\" textColor=\"neutral600\">\n            {formatMessage({\n              id: 'users-permissions.Policies.header.hint',\n              defaultMessage:\n                \"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route\",\n            })}\n          </Typography>\n        </Flex>\n      )}\n    </Grid.Item>\n  );\n};\n\nexport default Policies;\n", "const init = (state, permissions, routes) => {\n  return {\n    ...state,\n    initialData: permissions,\n    modifiedData: permissions,\n    routes,\n  };\n};\n\nexport default init;\n", "/* eslint-disable consistent-return */\nimport { produce } from 'immer';\nimport get from 'lodash/get';\nimport set from 'lodash/set';\nimport take from 'lodash/take';\n\nexport const initialState = {\n  initialData: {},\n  modifiedData: {},\n  routes: {},\n  selectedAction: '',\n  policies: [],\n};\n\nconst reducer = (state, action) =>\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case 'ON_CHANGE': {\n        const keysLength = action.keys.length;\n        const isChangingCheckbox = action.keys[keysLength - 1] === 'enabled';\n\n        if (action.value && isChangingCheckbox) {\n          const selectedAction = take(action.keys, keysLength - 1).join('.');\n          draftState.selectedAction = selectedAction;\n        }\n\n        set(draftState, ['modifiedData', ...action.keys], action.value);\n        break;\n      }\n      case 'ON_CHANGE_SELECT_ALL': {\n        const pathToValue = ['modifiedData', ...action.keys];\n        const oldValues = get(state, pathToValue, {});\n        const updatedValues = Object.keys(oldValues).reduce((acc, current) => {\n          acc[current] = { ...oldValues[current], enabled: action.value };\n\n          return acc;\n        }, {});\n\n        set(draftState, pathToValue, updatedValues);\n\n        break;\n      }\n      case 'ON_RESET': {\n        draftState.modifiedData = state.initialData;\n        break;\n      }\n      case 'ON_SUBMIT_SUCCEEDED': {\n        draftState.initialData = state.modifiedData;\n        break;\n      }\n\n      case 'SELECT_ACTION': {\n        const { actionToSelect } = action;\n        draftState.selectedAction = actionToSelect === state.selectedAction ? '' : actionToSelect;\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n\nexport default reducer;\n", "import React, { forwardRef, memo, useImperativeHandle, useReducer } from 'react';\n\nimport { Flex, Grid, Typography } from '@strapi/design-system';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport { UsersPermissionsProvider } from '../../contexts/UsersPermissionsContext';\nimport getTrad from '../../utils/getTrad';\nimport Permissions from '../Permissions';\nimport Policies from '../Policies';\n\nimport init from './init';\nimport reducer, { initialState } from './reducer';\n\nconst UsersPermissions = forwardRef(({ permissions, routes }, ref) => {\n  const { formatMessage } = useIntl();\n  const [state, dispatch] = useReducer(reducer, initialState, (state) =>\n    init(state, permissions, routes)\n  );\n\n  useImperativeHandle(ref, () => ({\n    getPermissions() {\n      return {\n        permissions: state.modifiedData,\n      };\n    },\n    resetForm() {\n      dispatch({ type: 'ON_RESET' });\n    },\n    setFormAfterSubmit() {\n      dispatch({ type: 'ON_SUBMIT_SUCCEEDED' });\n    },\n  }));\n\n  const handleChange = ({ target: { name, value } }) =>\n    dispatch({\n      type: 'ON_CHANGE',\n      keys: name.split('.'),\n      value: value === 'empty__string_value' ? '' : value,\n    });\n\n  const handleChangeSelectAll = ({ target: { name, value } }) =>\n    dispatch({\n      type: 'ON_CHANGE_SELECT_ALL',\n      keys: name.split('.'),\n      value,\n    });\n\n  const handleSelectedAction = (actionToSelect) =>\n    dispatch({\n      type: 'SELECT_ACTION',\n      actionToSelect,\n    });\n\n  const providerValue = {\n    ...state,\n    onChange: handleChange,\n    onChangeSelectAll: handleChangeSelectAll,\n    onSelectedAction: handleSelectedAction,\n  };\n\n  return (\n    <UsersPermissionsProvider value={providerValue}>\n      <Grid.Root gap={0} shadow=\"filterShadow\" hasRadius background=\"neutral0\">\n        <Grid.Item\n          col={7}\n          paddingTop={6}\n          paddingBottom={6}\n          paddingLeft={7}\n          paddingRight={7}\n          direction=\"column\"\n          alignItems=\"stretch\"\n        >\n          <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n              <Typography variant=\"delta\" tag=\"h2\">\n                {formatMessage({\n                  id: getTrad('Plugins.header.title'),\n                  defaultMessage: 'Permissions',\n                })}\n              </Typography>\n              <Typography tag=\"p\" textColor=\"neutral600\">\n                {formatMessage({\n                  id: getTrad('Plugins.header.description'),\n                  defaultMessage: 'Only actions bound by a route are listed below.',\n                })}\n              </Typography>\n            </Flex>\n            <Permissions />\n          </Flex>\n        </Grid.Item>\n        <Policies />\n      </Grid.Root>\n    </UsersPermissionsProvider>\n  );\n});\n\nUsersPermissions.propTypes = {\n  permissions: PropTypes.object.isRequired,\n  routes: PropTypes.object.isRequired,\n};\n\nexport default memo(UsersPermissions);\n", "import { translatedErrors } from '@strapi/strapi/admin';\nimport * as yup from 'yup';\n\nexport const createRoleSchema = yup.object().shape({\n  name: yup.string().required(translatedErrors.required.id),\n  description: yup.string().required(translatedErrors.required.id),\n});\n", "import isEmpty from 'lodash/isEmpty';\n\nconst cleanPermissions = (permissions) =>\n  Object.keys(permissions).reduce((acc, current) => {\n    const currentPermission = permissions[current].controllers;\n    const cleanedControllers = Object.keys(currentPermission).reduce((acc2, curr) => {\n      if (isEmpty(currentPermission[curr])) {\n        return acc2;\n      }\n\n      acc2[curr] = currentPermission[curr];\n\n      return acc2;\n    }, {});\n\n    if (isEmpty(cleanedControllers)) {\n      return acc;\n    }\n\n    acc[current] = { controllers: cleanedControllers };\n\n    return acc;\n  }, {});\n\nexport default cleanPermissions;\n", "import { useEffect } from 'react';\n\nimport { useAPIErrorHandler, useNotification, useFetchClient } from '@strapi/strapi/admin';\nimport { useQueries } from 'react-query';\n\nimport { cleanPermissions, getTrad } from '../../../utils';\n\nexport const usePlugins = () => {\n  const { toggleNotification } = useNotification();\n  const { get } = useFetchClient();\n  const { formatAPIError } = useAPIErrorHandler(getTrad);\n\n  const [\n    {\n      data: permissions,\n      isLoading: isLoadingPermissions,\n      error: permissionsError,\n      refetch: refetchPermissions,\n    },\n    { data: routes, isLoading: isLoadingRoutes, error: routesError, refetch: refetchRoutes },\n  ] = useQueries([\n    {\n      queryKey: ['users-permissions', 'permissions'],\n      async queryFn() {\n        const {\n          data: { permissions },\n        } = await get(`/users-permissions/permissions`);\n\n        return permissions;\n      },\n    },\n    {\n      queryKey: ['users-permissions', 'routes'],\n      async queryFn() {\n        const {\n          data: { routes },\n        } = await get(`/users-permissions/routes`);\n\n        return routes;\n      },\n    },\n  ]);\n\n  const refetchQueries = async () => {\n    await Promise.all([refetchPermissions(), refetchRoutes()]);\n  };\n\n  useEffect(() => {\n    if (permissionsError) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(permissionsError),\n      });\n    }\n  }, [toggleNotification, permissionsError, formatAPIError]);\n\n  useEffect(() => {\n    if (routesError) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(routesError),\n      });\n    }\n  }, [toggleNotification, routesError, formatAPIError]);\n\n  const isLoading = isLoadingPermissions || isLoadingRoutes;\n\n  return {\n    // TODO: these return values need to be memoized, otherwise\n    // they will create infinite rendering loops when used as\n    // effect dependencies\n    permissions: permissions ? cleanPermissions(permissions) : {},\n    routes: routes ?? {},\n\n    getData: refetchQueries,\n    isLoading,\n  };\n};\n", "import * as React from 'react';\n\nimport {\n  Button,\n  Flex,\n  Grid,\n  Main,\n  Textarea,\n  TextInput,\n  Typography,\n  Field,\n} from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport { Page, useTracking, useNotification, useFetchClient, Layouts } from '@strapi/strapi/admin';\nimport { Formik, Form } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { useMutation } from 'react-query';\nimport { useNavigate } from 'react-router-dom';\n\nimport UsersPermissions from '../../../components/UsersPermissions';\nimport { PERMISSIONS } from '../../../constants';\nimport getTrad from '../../../utils/getTrad';\nimport { createRoleSchema } from '../constants';\nimport { usePlugins } from '../hooks/usePlugins';\n\nexport const CreatePage = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const navigate = useNavigate();\n  const { isLoading: isLoadingPlugins, permissions, routes } = usePlugins();\n  const { trackUsage } = useTracking();\n  const permissionsRef = React.useRef();\n  const { post } = useFetchClient();\n  const mutation = useMutation((body) => post(`/users-permissions/roles`, body), {\n    onError() {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    },\n\n    onSuccess() {\n      trackUsage('didCreateRole');\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTrad('Settings.roles.created'),\n          defaultMessage: 'Role created',\n        }),\n      });\n\n      // Forcing redirecting since we don't have the id in the response\n      navigate(-1);\n    },\n  });\n\n  const handleCreateRoleSubmit = async (data) => {\n    // TODO: refactor. Child -> parent component communication is evil;\n    // We should either move the provider one level up or move the state\n    // straight into redux.\n    const permissions = permissionsRef.current.getPermissions();\n\n    await mutation.mutate({ ...data, ...permissions, users: [] });\n  };\n\n  return (\n    <Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          { name: 'Roles' }\n        )}\n      </Page.Title>\n      <Formik\n        enableReinitialize\n        initialValues={{ name: '', description: '' }}\n        onSubmit={handleCreateRoleSubmit}\n        validationSchema={createRoleSchema}\n      >\n        {({ handleSubmit, values, handleChange, errors }) => (\n          <Form noValidate onSubmit={handleSubmit}>\n            <Layouts.Header\n              primaryAction={\n                !isLoadingPlugins && (\n                  <Button type=\"submit\" loading={mutation.isLoading} startIcon={<Check />}>\n                    {formatMessage({\n                      id: 'global.save',\n                      defaultMessage: 'Save',\n                    })}\n                  </Button>\n                )\n              }\n              title={formatMessage({\n                id: 'Settings.roles.create.title',\n                defaultMessage: 'Create a role',\n              })}\n              subtitle={formatMessage({\n                id: 'Settings.roles.create.description',\n                defaultMessage: 'Define the rights given to the role',\n              })}\n            />\n            <Layouts.Content>\n              <Flex\n                background=\"neutral0\"\n                direction=\"column\"\n                alignItems=\"stretch\"\n                gap={7}\n                hasRadius\n                paddingTop={6}\n                paddingBottom={6}\n                paddingLeft={7}\n                paddingRight={7}\n                shadow=\"filterShadow\"\n              >\n                <Flex direction=\"column\" alignItems=\"stretch\">\n                  <Typography variant=\"delta\" tag=\"h2\">\n                    {formatMessage({\n                      id: getTrad('EditPage.form.roles'),\n                      defaultMessage: 'Role details',\n                    })}\n                  </Typography>\n\n                  <Grid.Root gap={4}>\n                    <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        name=\"name\"\n                        error={\n                          errors?.name\n                            ? formatMessage({ id: errors.name, defaultMessage: 'Name is required' })\n                            : false\n                        }\n                        required\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'global.name',\n                            defaultMessage: 'Name',\n                          })}\n                        </Field.Label>\n                        <TextInput value={values.name || ''} onChange={handleChange} />\n                        <Field.Error />\n                      </Field.Root>\n                    </Grid.Item>\n                    <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        name=\"description\"\n                        error={\n                          errors?.description\n                            ? formatMessage({\n                                id: errors.description,\n                                defaultMessage: 'Description is required',\n                              })\n                            : false\n                        }\n                        required\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'global.description',\n                            defaultMessage: 'Description',\n                          })}\n                        </Field.Label>\n                        <Textarea value={values.description || ''} onChange={handleChange} />\n                        <Field.Error />\n                      </Field.Root>\n                    </Grid.Item>\n                  </Grid.Root>\n                </Flex>\n\n                {!isLoadingPlugins && (\n                  <UsersPermissions\n                    ref={permissionsRef}\n                    permissions={permissions}\n                    routes={routes}\n                  />\n                )}\n              </Flex>\n            </Layouts.Content>\n          </Form>\n        )}\n      </Formik>\n    </Main>\n  );\n};\n\nexport const ProtectedRolesCreatePage = () => (\n  <Page.Protect permissions={PERMISSIONS.createRole}>\n    <CreatePage />\n  </Page.Protect>\n);\n", "import * as React from 'react';\n\nimport {\n  Main,\n  Button,\n  Flex,\n  TextInput,\n  Textarea,\n  Typography,\n  Grid,\n  Field,\n} from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport {\n  <PERSON>,\n  BackButton,\n  useAPIError<PERSON>and<PERSON>,\n  useNotification,\n  useFetchClient,\n  Layouts,\n} from '@strapi/strapi/admin';\nimport { Formik, Form } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { useQuery, useMutation } from 'react-query';\nimport { useMatch } from 'react-router-dom';\n\nimport UsersPermissions from '../../../components/UsersPermissions';\nimport { PERMISSIONS } from '../../../constants';\nimport getTrad from '../../../utils/getTrad';\nimport { createRoleSchema } from '../constants';\nimport { usePlugins } from '../hooks/usePlugins';\n\nexport const EditPage = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const {\n    params: { id },\n  } = useMatch(`/settings/users-permissions/roles/:id`);\n  const { get } = useFetchClient();\n  const { isLoading: isLoadingPlugins, routes } = usePlugins();\n  const {\n    data: role,\n    isLoading: isLoadingRole,\n    refetch: refetchRole,\n  } = useQuery(['users-permissions', 'role', id], async () => {\n    // TODO: why doesn't this endpoint follow the admin API conventions?\n    const {\n      data: { role },\n    } = await get(`/users-permissions/roles/${id}`);\n\n    return role;\n  });\n\n  const permissionsRef = React.useRef();\n  const { put } = useFetchClient();\n  const { formatAPIError } = useAPIErrorHandler();\n  const mutation = useMutation((body) => put(`/users-permissions/roles/${id}`, body), {\n    onError(error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    },\n\n    async onSuccess() {\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTrad('Settings.roles.created'),\n          defaultMessage: 'Role edited',\n        }),\n      });\n\n      await refetchRole();\n    },\n  });\n\n  const handleEditRoleSubmit = async (data) => {\n    const permissions = permissionsRef.current.getPermissions();\n\n    await mutation.mutate({ ...data, ...permissions, users: [] });\n  };\n\n  if (isLoadingRole) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          { name: 'Roles' }\n        )}\n      </Page.Title>\n      <Formik\n        enableReinitialize\n        initialValues={{ name: role.name, description: role.description }}\n        onSubmit={handleEditRoleSubmit}\n        validationSchema={createRoleSchema}\n      >\n        {({ handleSubmit, values, handleChange, errors }) => (\n          <Form noValidate onSubmit={handleSubmit}>\n            <Layouts.Header\n              primaryAction={\n                !isLoadingPlugins ? (\n                  <Button\n                    disabled={role.code === 'strapi-super-admin'}\n                    type=\"submit\"\n                    loading={mutation.isLoading}\n                    startIcon={<Check />}\n                  >\n                    {formatMessage({\n                      id: 'global.save',\n                      defaultMessage: 'Save',\n                    })}\n                  </Button>\n                ) : null\n              }\n              title={role.name}\n              subtitle={role.description}\n              navigationAction={<BackButton fallback=\"..\" />}\n            />\n            <Layouts.Content>\n              <Flex\n                background=\"neutral0\"\n                direction=\"column\"\n                alignItems=\"stretch\"\n                gap={7}\n                hasRadius\n                paddingTop={6}\n                paddingBottom={6}\n                paddingLeft={7}\n                paddingRight={7}\n                shadow=\"filterShadow\"\n              >\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                  <Typography variant=\"delta\" tag=\"h2\">\n                    {formatMessage({\n                      id: getTrad('EditPage.form.roles'),\n                      defaultMessage: 'Role details',\n                    })}\n                  </Typography>\n\n                  <Grid.Root gap={4}>\n                    <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        name=\"name\"\n                        error={\n                          errors?.name\n                            ? formatMessage({\n                                id: errors.name,\n                                defaultMessage: 'Name is required',\n                              })\n                            : false\n                        }\n                        required\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'global.name',\n                            defaultMessage: 'Name',\n                          })}\n                        </Field.Label>\n                        <TextInput value={values.name || ''} onChange={handleChange} />\n                        <Field.Error />\n                      </Field.Root>\n                    </Grid.Item>\n                    <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        name=\"description\"\n                        error={\n                          errors?.description\n                            ? formatMessage({\n                                id: errors.description,\n                                defaultMessage: 'Description is required',\n                              })\n                            : false\n                        }\n                        required\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'global.description',\n                            defaultMessage: 'Description',\n                          })}\n                        </Field.Label>\n                        <Textarea value={values.description || ''} onChange={handleChange} />\n                        <Field.Error />\n                      </Field.Root>\n                    </Grid.Item>\n                  </Grid.Root>\n                </Flex>\n\n                {!isLoadingPlugins && (\n                  <UsersPermissions\n                    ref={permissionsRef}\n                    permissions={role.permissions}\n                    routes={routes}\n                  />\n                )}\n              </Flex>\n            </Layouts.Content>\n          </Form>\n        )}\n      </Formik>\n    </Main>\n  );\n};\n\nexport const ProtectedRolesEditPage = () => (\n  <Page.Protect permissions={PERMISSIONS.updateRole}>\n    <EditPage />\n  </Page.Protect>\n);\n", "import * as React from 'react';\n\nimport { Flex, Icon<PERSON>utton, Link, Tbody, Td, Tr, Typography } from '@strapi/design-system';\nimport { Pencil, Trash } from '@strapi/icons';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\nimport { useNavigate, NavLink } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nconst EditLink = styled(Link)`\n  align-items: center;\n  height: 3.2rem;\n  width: 3.2rem;\n  display: flex;\n  justify-content: center;\n  padding: ${({ theme }) => `${theme.spaces[2]}`};\n\n  svg {\n    height: 1.6rem;\n    width: 1.6rem;\n\n    path {\n      fill: ${({ theme }) => theme.colors.neutral500};\n    }\n  }\n\n  &:hover,\n  &:focus {\n    svg {\n      path {\n        fill: ${({ theme }) => theme.colors.neutral800};\n      }\n    }\n  }\n`;\n\nconst TableBody = ({ sortedRoles, canDelete, canUpdate, setRoleToDelete, onDelete }) => {\n  const { formatMessage } = useIntl();\n  const navigate = useNavigate();\n  const [showConfirmDelete, setShowConfirmDelete] = onDelete;\n\n  const checkCanDeleteRole = (role) =>\n    canDelete && !['public', 'authenticated'].includes(role.type);\n\n  const handleClickDelete = (id) => {\n    setRoleToDelete(id);\n    setShowConfirmDelete(!showConfirmDelete);\n  };\n\n  return (\n    <Tbody>\n      {sortedRoles?.map((role) => (\n        <Tr cursor=\"pointer\" key={role.name} onClick={() => navigate(role.id.toString())}>\n          <Td width=\"20%\">\n            <Typography>{role.name}</Typography>\n          </Td>\n          <Td width=\"50%\">\n            <Typography>{role.description}</Typography>\n          </Td>\n          <Td width=\"30%\">\n            <Typography>\n              {formatMessage(\n                {\n                  id: 'Roles.RoleRow.user-count',\n                  defaultMessage: '{number, plural, =0 {# user} one {# user} other {# users}}',\n                },\n                { number: role.nb_users }\n              )}\n            </Typography>\n          </Td>\n          <Td>\n            <Flex justifyContent=\"end\" onClick={(e) => e.stopPropagation()}>\n              {canUpdate ? (\n                <EditLink\n                  tag={NavLink}\n                  to={role.id.toString()}\n                  aria-label={formatMessage(\n                    { id: 'app.component.table.edit', defaultMessage: 'Edit {target}' },\n                    { target: `${role.name}` }\n                  )}\n                >\n                  <Pencil />\n                </EditLink>\n              ) : null}\n\n              {checkCanDeleteRole(role) && (\n                <IconButton\n                  onClick={() => handleClickDelete(role.id.toString())}\n                  variant=\"ghost\"\n                  label={formatMessage(\n                    { id: 'global.delete-target', defaultMessage: 'Delete {target}' },\n                    { target: `${role.name}` }\n                  )}\n                >\n                  <Trash />\n                </IconButton>\n              )}\n            </Flex>\n          </Td>\n        </Tr>\n      ))}\n    </Tbody>\n  );\n};\n\nexport default TableBody;\n\nTableBody.defaultProps = {\n  canDelete: false,\n  canUpdate: false,\n};\n\nTableBody.propTypes = {\n  onDelete: PropTypes.array.isRequired,\n  setRoleToDelete: PropTypes.func.isRequired,\n  sortedRoles: PropTypes.array.isRequired,\n  canDelete: PropTypes.bool,\n  canUpdate: PropTypes.bool,\n};\n", "import React, { useState } from 'react';\n\nimport {\n  Table,\n  Th,\n  Thead,\n  Tr,\n  Typography,\n  useNotifyAT,\n  VisuallyHidden,\n  EmptyStateLayout,\n  useCollator,\n  useFilter,\n  LinkButton,\n  Dialog,\n} from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport {\n  ConfirmDialog,\n  useTracking,\n  Page,\n  SearchInput,\n  useNotification,\n  useQueryParams,\n  useFetchClient,\n  useRBAC,\n  Layouts,\n} from '@strapi/strapi/admin';\nimport { useIntl } from 'react-intl';\nimport { useMutation, useQuery } from 'react-query';\nimport { NavLink } from 'react-router-dom';\n\nimport { PERMISSIONS } from '../../../../constants';\nimport { getTrad } from '../../../../utils';\n\nimport TableBody from './components/TableBody';\n\nexport const RolesListPage = () => {\n  const { trackUsage } = useTracking();\n  const { formatMessage, locale } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { notifyStatus } = useNotifyAT();\n  const [{ query }] = useQueryParams();\n  const _q = query?._q || '';\n  const [showConfirmDelete, setShowConfirmDelete] = useState(false);\n  const [roleToDelete, setRoleToDelete] = useState();\n  const { del, get } = useFetchClient();\n\n  const {\n    isLoading: isLoadingForPermissions,\n    allowedActions: { canRead, canDelete, canCreate, canUpdate },\n  } = useRBAC({\n    create: PERMISSIONS.createRole,\n    read: PERMISSIONS.readRoles,\n    update: PERMISSIONS.updateRole,\n    delete: PERMISSIONS.deleteRole,\n  });\n\n  const {\n    isLoading: isLoadingForData,\n    data: { roles },\n    isFetching,\n    refetch,\n  } = useQuery('get-roles', () => fetchData(toggleNotification, formatMessage, notifyStatus), {\n    initialData: {},\n    enabled: canRead,\n  });\n\n  const { contains } = useFilter(locale, {\n    sensitivity: 'base',\n  });\n\n  /**\n   * @type {Intl.Collator}\n   */\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const isLoading = isLoadingForData || isFetching || isLoadingForPermissions;\n\n  const handleShowConfirmDelete = () => {\n    setShowConfirmDelete(!showConfirmDelete);\n  };\n\n  const deleteData = async (id, formatMessage, toggleNotification) => {\n    try {\n      await del(`/users-permissions/roles/${id}`);\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n      });\n    }\n  };\n\n  const fetchData = async (toggleNotification, formatMessage, notifyStatus) => {\n    try {\n      const { data } = await get('/users-permissions/roles');\n      notifyStatus('The roles have loaded successfully');\n\n      return data;\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n\n      throw new Error(err);\n    }\n  };\n\n  const emptyLayout = {\n    roles: {\n      id: getTrad('Roles.empty'),\n      defaultMessage: \"You don't have any roles yet.\",\n    },\n    search: {\n      id: getTrad('Roles.empty.search'),\n      defaultMessage: 'No roles match the search.',\n    },\n  };\n\n  const pageTitle = formatMessage({\n    id: 'global.roles',\n    defaultMessage: 'Roles',\n  });\n\n  const deleteMutation = useMutation((id) => deleteData(id, formatMessage, toggleNotification), {\n    async onSuccess() {\n      await refetch();\n    },\n  });\n\n  const handleConfirmDelete = async () => {\n    await deleteMutation.mutateAsync(roleToDelete);\n    setShowConfirmDelete(!showConfirmDelete);\n  };\n\n  const sortedRoles = (roles || [])\n    .filter((role) => contains(role.name, _q) || contains(role.description, _q))\n    .sort(\n      (a, b) => formatter.compare(a.name, b.name) || formatter.compare(a.description, b.description)\n    );\n\n  const emptyContent = _q && !sortedRoles.length ? 'search' : 'roles';\n\n  const colCount = 4;\n  const rowCount = (roles?.length || 0) + 1;\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          { name: pageTitle }\n        )}\n      </Page.Title>\n      <Page.Main>\n        <Layouts.Header\n          title={formatMessage({\n            id: 'global.roles',\n            defaultMessage: 'Roles',\n          })}\n          subtitle={formatMessage({\n            id: 'Settings.roles.list.description',\n            defaultMessage: 'List of roles',\n          })}\n          primaryAction={\n            canCreate ? (\n              <LinkButton\n                to=\"new\"\n                tag={NavLink}\n                onClick={() => trackUsage('willCreateRole')}\n                startIcon={<Plus />}\n                size=\"S\"\n              >\n                {formatMessage({\n                  id: getTrad('List.button.roles'),\n                  defaultMessage: 'Add new role',\n                })}\n              </LinkButton>\n            ) : null\n          }\n        />\n\n        <Layouts.Action\n          startActions={\n            <SearchInput\n              label={formatMessage({\n                id: 'app.component.search.label',\n                defaultMessage: 'Search',\n              })}\n            />\n          }\n        />\n\n        <Layouts.Content>\n          {!canRead && <Page.NoPermissions />}\n          {canRead && sortedRoles && sortedRoles?.length ? (\n            <Table colCount={colCount} rowCount={rowCount}>\n              <Thead>\n                <Tr>\n                  <Th>\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({ id: 'global.name', defaultMessage: 'Name' })}\n                    </Typography>\n                  </Th>\n                  <Th>\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'global.description',\n                        defaultMessage: 'Description',\n                      })}\n                    </Typography>\n                  </Th>\n                  <Th>\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'global.users',\n                        defaultMessage: 'Users',\n                      })}\n                    </Typography>\n                  </Th>\n                  <Th>\n                    <VisuallyHidden>\n                      {formatMessage({\n                        id: 'global.actions',\n                        defaultMessage: 'Actions',\n                      })}\n                    </VisuallyHidden>\n                  </Th>\n                </Tr>\n              </Thead>\n              <TableBody\n                sortedRoles={sortedRoles}\n                canDelete={canDelete}\n                canUpdate={canUpdate}\n                permissions={PERMISSIONS}\n                setRoleToDelete={setRoleToDelete}\n                onDelete={[showConfirmDelete, setShowConfirmDelete]}\n              />\n            </Table>\n          ) : (\n            <EmptyStateLayout content={formatMessage(emptyLayout[emptyContent])} />\n          )}\n        </Layouts.Content>\n        <Dialog.Root open={showConfirmDelete} onOpenChange={handleShowConfirmDelete}>\n          <ConfirmDialog onConfirm={handleConfirmDelete} />\n        </Dialog.Root>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\nexport const ProtectedRolesListPage = () => {\n  return (\n    <Page.Protect permissions={PERMISSIONS.accessRoles}>\n      <RolesListPage />\n    </Page.Protect>\n  );\n};\n", "import * as React from 'react';\n\nimport { Page } from '@strapi/strapi/admin';\nimport { Route, Routes } from 'react-router-dom';\n\nimport { PERMISSIONS } from '../../constants';\n\nimport { ProtectedRolesCreatePage } from './pages/CreatePage';\nimport { ProtectedRolesEditPage } from './pages/EditPage';\nimport { ProtectedRolesListPage } from './pages/ListPage';\n\nconst Roles = () => {\n  return (\n    <Page.Protect permissions={PERMISSIONS.accessRoles}>\n      <Routes>\n        <Route index element={<ProtectedRolesListPage />} />\n        <Route path=\"new\" element={<ProtectedRolesCreatePage />} />\n        <Route path=\":id\" element={<ProtectedRolesEditPage />} />\n      </Routes>\n    </Page.Protect>\n  );\n};\n\nexport default Roles;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,gBAAgB;AADpB,QAEI,oBAAoB;AAFxB,QAGI,WAAW;AAHf,QAII,YAAY;AAJhB,QAKI,WAAW;AAGf,QAAI,mBAAmB;AAavB,aAAS,eAAe,OAAO,QAAQ,UAAU,YAAY;AAC3D,UAAI,QAAQ,IACR,WAAW,eACX,WAAW,MACX,SAAS,MAAM,QACf,SAAS,CAAC,GACV,eAAe,OAAO;AAE1B,UAAI,CAAC,QAAQ;AACX,eAAO;AAAA,MACT;AACA,UAAI,UAAU;AACZ,iBAAS,SAAS,QAAQ,UAAU,QAAQ,CAAC;AAAA,MAC/C;AACA,UAAI,YAAY;AACd,mBAAW;AACX,mBAAW;AAAA,MACb,WACS,OAAO,UAAU,kBAAkB;AAC1C,mBAAW;AACX,mBAAW;AACX,iBAAS,IAAI,SAAS,MAAM;AAAA,MAC9B;AACA;AACA,eAAO,EAAE,QAAQ,QAAQ;AACvB,cAAI,QAAQ,MAAM,KAAK,GACnB,WAAW,YAAY,OAAO,QAAQ,SAAS,KAAK;AAExD,kBAAS,cAAc,UAAU,IAAK,QAAQ;AAC9C,cAAI,YAAY,aAAa,UAAU;AACrC,gBAAI,cAAc;AAClB,mBAAO,eAAe;AACpB,kBAAI,OAAO,WAAW,MAAM,UAAU;AACpC,yBAAS;AAAA,cACX;AAAA,YACF;AACA,mBAAO,KAAK,KAAK;AAAA,UACnB,WACS,CAAC,SAAS,QAAQ,UAAU,UAAU,GAAG;AAChD,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClEjB;AAAA;AAAA,QAAI,iBAAiB;AAArB,QACI,WAAW;AADf,QAEI,oBAAoB;AAsBxB,QAAIA,WAAU,SAAS,SAAS,OAAO,QAAQ;AAC7C,aAAO,kBAAkB,KAAK,IAC1B,eAAe,OAAO,MAAM,IAC5B,CAAC;AAAA,IACP,CAAC;AAED,WAAO,UAAUA;AAAA;AAAA;;;AC9BjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,YAAY;AA2BhB,aAASC,MAAK,OAAO,GAAG,OAAO;AAC7B,UAAI,EAAE,SAAS,MAAM,SAAS;AAC5B,eAAO,CAAC;AAAA,MACV;AACA,UAAK,SAAS,MAAM,SAAa,IAAI,UAAU,CAAC;AAChD,aAAO,UAAU,OAAO,GAAG,IAAI,IAAI,IAAI,CAAC;AAAA,IAC1C;AAEA,WAAO,UAAUA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;AChCXC,IAAAA,uBAAmBC,4BAAc,CAAA,CAAC;AAExC,IAAMC,2BAA2B,CAAC,EAAEC,UAAUC,MAAK,MAAE;AACnD,aAAOC,wBAACL,iBAAiBM,UAAQ;IAACF;IAAeD;;AACnD;AAEMI,IAAAA,sBAAsB,UAAMC,yBAAWR,gBAAAA;AAE7CE,yBAAyBO,YAAY;EACnCN,UAAUO,kBAAAA,QAAUC,KAAKC;EACzBR,OAAOM,kBAAAA,QAAUG,OAAOD;AAC1B;;;;;;;;ACbA,SAASE,iBAAiBC,YAAU;AAClC,UAAQA,YAAAA;IACN,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,iBAAOC,kBAAAA,SAAWD,WAAWE,QAAQ,SAAS,EAAIA,EAAAA,QAAQ,YAAY,EAAA,CAAA;EAC1E;AACF;;;ACvBMC,IAAAA,OAAO,CAACC,eAAcC,gBAAAA;AAC1B,QAAMC,YAAYC,OAAOC,KAAKH,WAAAA,EAC3BI,KAAI,EACJC,IAAI,CAACC,UAAU;IAAEA;IAAMC,QAAQ;IAAM;AAExC,SAAO;IAAE,GAAGR;IAAcE;EAAU;AACtC;;;;;;;;;;;;;;;ACHA,IAAMO,8BAA8BC;gBACpB,CAACC,UAAUA,MAAMC,MAAMC,OAAOC,UAAU;;;;;;AAOlDC,IAAAA,kBAAkBC,GAAOC,GAAAA;;;;;;;;cAQjB,CAACN,UAAUA,MAAMC,MAAMC,OAAOK,UAAU;;;;;IAKlD,CAACP,UAAUA,MAAMQ,YAAYV,2BAA4B;;MAEvDA,2BAA4B;;;;;ACblC,IAAMW,SAASC,GAAOC;;;0BAGI,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAOC,UAAU;;AAGhE,IAAMC,cAAc,CAAC,EAAEC,YAAW,MAAE;AAClC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,UAAUC,mBAAmBC,kBAAkBC,gBAAgBC,aAAY,IACjFC,oBAAAA;AAEF,QAAMC,gCAA4BC,uBAAQ,MAAA;AACxC,eAAOC,WAAAA,SAAIJ,cAAcP,YAAYY,MAAM,CAAA,CAAC;KAC3C;IAACL;IAAcP;EAAY,CAAA;AAE9B,QAAMa,4BAAwBH,uBAAQ,MAAA;AACpC,WAAOI,OAAOC,OAAON,yBAA2BO,EAAAA,MAAM,CAACC,WAAWA,OAAOC,YAAY,IAAA;KACpF;IAACT;EAA0B,CAAA;AAE9B,QAAMU,6BAAyBT,uBAAQ,MAAA;AACrC,WACEI,OAAOC,OAAON,yBAAAA,EAA2BW,KAAK,CAACH,WAAWA,OAAOC,YAAY,IAAA,KAC7E,CAACL;KAEF;IAACJ;IAA2BI;EAAsB,CAAA;AAErD,QAAMQ,4BAAwBC,2BAC5B,CAAC,EAAEC,QAAQ,EAAEX,KAAI,EAAE,MAAE;AACnBR,sBAAkB;MAAEmB,QAAQ;QAAEX;QAAMY,OAAO,CAACX;MAAsB;IAAE,CAAA;KAEtE;IAACA;IAAuBT;EAAkB,CAAA;AAG5C,QAAMqB,uBAAmBH,2BACvB,CAACI,eAAAA;AACC,WAAOpB,mBAAmBoB;KAE5B;IAACpB;EAAe,CAAA;AAGlB,aACEqB,0BAACC,KAAAA;;UACCD,0BAACE,MAAAA;QAAKC,gBAAe;QAAgBC,YAAW;;cAC9CC,yBAACJ,KAAAA;YAAIK,cAAc;YACjB,cAAAD,yBAACE,YAAAA;cAAWC,SAAQ;cAAQC,WAAU;cACnCpC,UAAAA,YAAYqC;;;cAGjBL,yBAACvC,QAAAA,CAAAA,CAAAA;cACDuC,yBAACJ,KAAAA;YAAIU,aAAa;YAChB,cAAAN,yBAACO,cAAAA;cACC3B,MAAMZ,YAAYY;cAClB4B,SAASrB,yBAAyB,kBAAkBN;cACpD4B,iBAAiB,CAACjB,UAChBH,sBAAsB;gBAAEE,QAAQ;kBAAEX,MAAMZ,YAAYY;kBAAMY;gBAAM;cAAE,CAAA;wBAGnEvB,cAAc;gBAAEyC,IAAI;gBAAwBC,gBAAgB;cAAa,CAAA;;;;;UAIhFX,yBAACH,MAAAA;QAAKe,YAAY;QAAGC,eAAe;sBAClCb,yBAACc,KAAKC,MAAI;UAACC,KAAK;UAAGC,OAAO;YAAEC,MAAM;UAAE;UACjClD,UAAAA,YAAYmD,QAAQC,IAAI,CAACnC,WAAAA;AACxB,kBAAML,OAAO,GAAGK,OAAOL,IAAI;AAE3B,uBACEoB,yBAACc,KAAKO,MAAI;cAACC,KAAK;cAAqBC,WAAU;cAASxB,YAAW;cACjE,cAAAJ,0BAAC6B,iBAAAA;gBAAgBC,UAAUhC,iBAAiBR,OAAOL,IAAI;gBAAG8C,SAAS;gBAAGC,WAAS;;sBAC7E3B,yBAACO,cAAAA;oBACCC,aAAS7B,WAAAA,SAAIJ,cAAcK,MAAM,KAAA;oBACjCA;oBACA6B,iBAAiB,CAACjB,UAAUrB,SAAS;sBAAEoB,QAAQ;wBAAEX;wBAAMY;sBAAM;oBAAE,CAAA;oBAE9DP,UAAAA,OAAOoB;;sBAEVV,0BAACiC,UAAAA;oBACCC,MAAK;oBACLC,SAAS,MAAMzD,iBAAiBY,OAAOL,IAAI;oBAC3CqC,OAAO;sBAAEc,SAAS;sBAAehC,YAAY;oBAAS;;0BAEtDC,yBAACgC,gBAAAA;wBAAeC,KAAI;kCACjBhE,cACC;0BACEyC,IAAI;0BACJC,gBAAgB;2BAElB;0BACEuB,OAAOjD,OAAOL;wBAChB,CAAA;;0BAGJoB,yBAACmC,eAAAA;wBAAIzB,IAAG;wBAAM0B,QAAO;;;;;;YAzBHnD,GAAAA,OAAOL,IAAI;UA8BvC,CAAA;;;;;AAKV;AAEAb,YAAYsE,YAAY;EACtBrE,aAAasE,mBAAAA,QAAUC,OAAOC;AAChC;;;AC/GA,IAAMC,gBAAgB,CAAC,EAAEC,MAAMC,YAAW,MAAE;AAC1C,QAAMC,oBAAgBC,uBAAQ,MAAA;AAC5B,eAAOC,cAAAA,SACLC,OAAOC,OAAOL,YAAYM,WAAW,EAAEC,OAAO,CAACC,KAAKC,MAAMC,UAAAA;AACxD,YAAMC,cAAc,GAAGZ,IAAAA,gBAAoBK,OAAOQ,KAAKZ,YAAYM,WAAW,EAAEI,KAAAA,CAAM;AACtF,YAAMG,cAAUV,cAAAA,SACdC,OAAOQ,KAAKH,IAAMF,EAAAA,OAAO,CAACC,MAAKM,YAAAA;AAC7B,eAAO;UACFN,GAAAA;UACH;YACE,GAAGC,KAAKK,OAAQ;YAChBC,OAAOD;YACPf,MAAM,GAAGY,WAAAA,IAAeG,OAAAA;UAC1B;QACD;MACH,GAAG,CAAA,CAAE,GACL,OAAA;AAGF,aAAO;QACFN,GAAAA;QACH;UACEK;UACAE,OAAOX,OAAOQ,KAAKZ,YAAYM,WAAW,EAAEI,KAAM;UAClDX,MAAMY;QACR;MACD;IACH,GAAG,CAAA,CAAE,GACL,OAAA;KAED;IAACZ;IAAMC;EAAY,CAAA;AAEtB,aACEgB,yBAACC,KAAAA;IAAIC,SAAS;IACXjB,UAAAA,cAAckB,IAAI,CAACC,oBAClBJ,yBAACK,aAAAA;MAAmCD;IAAlBA,GAAAA,YAAYrB,IAAI,CAAA;;AAI1C;AAEAD,cAAcwB,YAAY;EACxBvB,MAAMwB,mBAAAA,QAAUC,OAAOC;EACvBzB,aAAauB,mBAAAA,QAAUG,OAAOD;AAChC;;;AClDA,IAAME,eAAe;EACnBC,WAAW,CAAA;AACb;AAEA,IAAMC,UAAU,CAACC,OAAOC;;EAEtBC,GAAQF,OAAO,CAACG,eAAAA;AACd,YAAQF,OAAOG,MAAI;MACjB,KAAK,mBAAmB;AACtBD,mBAAWL,YAAYE,MAAMF,UAAUO,IAAI,CAACC,UAAUC,UAAAA;AACpD,cAAIA,UAAUN,OAAOM,OAAO;AAC1B,mBAAO;cAAE,GAAGD;cAAUE,QAAQ,CAACF,SAASE;YAAO;UACjD;AAEA,iBAAO;YAAE,GAAGF;YAAUE,QAAQ;UAAM;QACtC,CAAA;AAEA;MACF;MACA;AACE,eAAOL;IACX;EACF,CAAA;;;;ACZF,IAAMM,cAAc,MAAA;AAClB,QAAM,EAAEC,aAAY,IAAKC,oBAAAA;AACzB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAAC,EAAEC,UAAS,CAAE,QAAIC,0BAAWC,SAASC,cAAc,CAACC,UAAUC,KAAKD,OAAOR,YAAAA,CAAAA;AAEjF,aACEU,yBAACC,UAAUC,MAAI;IAACC,MAAK;IACnB,cAAAH,yBAACI,MAAAA;MAAKC,WAAU;MAASC,YAAW;MAAUC,KAAK;MAChDb,UAAAA,UAAUc,IAAI,CAACC,UAAUC,cACxBC,0BAACV,UAAUW,MAAI;QAAqBC,OAAOJ,SAASK;;cAClDd,yBAACC,UAAUc,QAAM;YAACC,SAASN,QAAQ,MAAM,IAAI,cAAcO;0BACzDjB,yBAACC,UAAUiB,SAAO;cAChBC,eAAc;cACdC,aAAa5B,cACX;gBACE6B,IAAI;gBACJC,gBAAgB;iBAElB;gBAAER,MAAML,SAASK;cAAK,CAAA;cAGvBS,UAAAA,iBAAiBd,SAASK,IAAI;;;cAGnCd,yBAACC,UAAUuB,SAAO;YAChB,cAAAxB,yBAACyB,eAAAA;cAAcC,aAAapC,aAAamB,SAASK,IAAI;cAAGA,MAAML,SAASK;;;;MAhBvDL,GAAAA,SAASK,IAAI,CAAA;;;AAuB5C;;;;;;;;;;;;;;;;;AC5CA,IAAMa,iBAAiB,CAACC,SAAAA;AACtB,UAAQA,MAAAA;IACN,KAAK,QAAQ;AACX,aAAO;QACLC,MAAM;QACNC,QAAQ;QACRC,YAAY;MACd;IACF;IACA,KAAK,OAAO;AACV,aAAO;QACLF,MAAM;QACNC,QAAQ;QACRC,YAAY;MACd;IACF;IACA,KAAK,OAAO;AACV,aAAO;QACLF,MAAM;QACNC,QAAQ;QACRC,YAAY;MACd;IACF;IACA,KAAK,UAAU;AACb,aAAO;QACLF,MAAM;QACNC,QAAQ;QACRC,YAAY;MACd;IACF;IACA,SAAS;AACP,aAAO;QACLF,MAAM;QACNC,QAAQ;QACRC,YAAY;MACd;IACF;EACF;AACF;;;AC3BA,IAAMC,YAAYC,GAAOC,GAAAA;;mBAEN,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAE,CAAA,QAAQ,CAAC,EAAED,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;AAGvF,SAASC,WAAW,EAAEC,MAAK,GAAE;AAC3B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAM,EAAEC,QAAQC,SAASC,OAAOC,KAAI,IAAKN;AACzC,QAAMO,iBAAiBD,WAAOE,YAAAA,SAAKF,KAAKG,MAAM,GAAA,CAAA,IAAQ,CAAA;AACtD,QAAM,CAACC,aAAa,IAAIC,SAAS,EAAE,IAAIN,QAAQA,MAAMI,MAAM,GAAA,IAAO,CAAA;AAClE,QAAMG,SAASC,eAAeb,MAAMG,MAAM;AAE1C,aACEW,0BAACC,MAAAA;IAAKC,WAAU;IAASC,YAAW;IAAUC,KAAK;;UACjDJ,0BAACK,YAAAA;QAAWC,SAAQ;QAAQC,KAAI;;UAC7BpB,cAAc;YACbqB,IAAI;YACJC,gBAAgB;UAClB,CAAA;UAAG;cAEHC,yBAACC,QAAAA;YAAMf,UAAAA;;cACPI,0BAACK,YAAAA;YAAWC,SAAQ;YAAQM,WAAU;;cAAa;cAC/Cf;;;;;UAGNG,0BAACC,MAAAA;QAAKY,WAAS;QAACC,YAAW;QAAWC,aAAY;QAAaX,KAAK;;cAClEM,yBAAC9B,WAAAA;YAAUkC,YAAYhB,OAAOgB;YAAYC,aAAajB,OAAOkB;YAAQC,SAAS;YAC7E,cAAAP,yBAACL,YAAAA;cAAWa,YAAW;cAAON,WAAWd,OAAOqB;cAC7C9B,UAAAA;;;cAGLqB,yBAAC5B,KAAAA;YAAIsC,aAAa;YAAGC,cAAc;0BAChCC,WAAAA,SAAI7B,gBAAgB,CAAC8B,cACpBvB,0BAACK,YAAAA;cAAuBO,WAAWW,MAAMC,SAAS,GAAA,IAAO,eAAe;;gBAAc;gBAClFD;;YADaA,GAAAA,KAAAA,CAAAA;;;;;;AAQ7B;AAEAtC,WAAWwC,eAAe;EACxBvC,OAAO;IACLI,SAAS;IACTD,QAAQ;IACRG,MAAM;EACR;AACF;AAEAP,WAAWyC,YAAY;EACrBxC,OAAOyC,mBAAAA,QAAUC,MAAM;IACrBtC,SAASqC,mBAAAA,QAAUE;IACnBxC,QAAQsC,mBAAAA,QAAUE;IAClBrC,MAAMmC,mBAAAA,QAAUE;EAClB,CAAA;AACF;;;AC1DA,IAAMC,WAAW,MAAA;AACf,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,gBAAgBC,OAAM,IAAKC,oBAAAA;AAEnC,QAAMC,WAAOC,eAAAA,SAAQJ,eAAeK,MAAM,GAAM,GAAA,aAAA;AAChD,QAAMC,uBAAmBC,YAAAA,SAAIN,QAAQE,KAAK,CAAE,CAAA;AAC5C,QAAMK,eAAeL,KAAKM,MAAM,CAAA,EAAGC,KAAK,GAAA;AAExC,QAAMC,sBAAkBC,eAAAA,SAAQN,gBAC5B,IAAA,CAAA,IACAA,iBAAiBO,OAAO,CAACC,MAAMA,EAAEC,QAAQC,SAASR,YAAAA,CAAAA;AAEtD,aACES,yBAACC,KAAKC,MAAI;IACRC,KAAK;IACLC,YAAW;IACXC,YAAY;IACZC,eAAe;IACfC,aAAa;IACbC,cAAc;IACdC,OAAO;MAAEC,WAAW;IAAO;IAC3BC,WAAU;IACVC,YAAW;IAEV7B,UAAAA,qBACCiB,yBAACa,MAAAA;MAAKF,WAAU;MAASC,YAAW;MAAUE,KAAK;MAChDpB,UAAAA,gBAAgBqB,IAAI,CAACC,OAAOC;;YAE3BjB,yBAACkB,YAAAA;UAAqBF;QAALC,GAAAA,GAAAA;OAAAA;aAIrBE,0BAACN,MAAAA;MAAKF,WAAU;MAASC,YAAW;MAAUE,KAAK;;YACjDd,yBAACoB,YAAAA;UAAWC,SAAQ;UAAQC,KAAI;oBAC7BzC,cAAc;YACb0C,IAAI;YACJC,gBAAgB;UAClB,CAAA;;YAEFxB,yBAACoB,YAAAA;UAAWE,KAAI;UAAIG,WAAU;oBAC3B5C,cAAc;YACb0C,IAAI;YACJC,gBACE;UACJ,CAAA;;;;;AAMZ;;;AC7DME,IAAAA,QAAO,CAACC,OAAOC,aAAaC,WAAAA;AAChC,SAAO;IACL,GAAGF;IACHG,aAAaF;IACbG,cAAcH;IACdC;EACF;AACF;;;;;;ICDaG,gBAAe;EAC1BC,aAAa,CAAA;EACbC,cAAc,CAAA;EACdC,QAAQ,CAAA;EACRC,gBAAgB;EAChBC,UAAU,CAAA;AACZ;AAEA,IAAMC,WAAU,CAACC,OAAOC,WACtBC,GAAQF,OAAO,CAACG,eAAAA;AACd,UAAQF,OAAOG,MAAI;IACjB,KAAK,aAAa;AAChB,YAAMC,aAAaJ,OAAOK,KAAKC;AAC/B,YAAMC,qBAAqBP,OAAOK,KAAKD,aAAa,CAAA,MAAO;AAE3D,UAAIJ,OAAOQ,SAASD,oBAAoB;AACtC,cAAMX,qBAAiBa,YAAAA,SAAKT,OAAOK,MAAMD,aAAa,CAAA,EAAGM,KAAK,GAAA;AAC9DR,mBAAWN,iBAAiBA;MAC9B;AAEAe,qBAAAA,SAAIT,YAAY;QAAC;QAAmBF,GAAAA,OAAOK;MAAK,GAAEL,OAAOQ,KAAK;AAC9D;IACF;IACA,KAAK,wBAAwB;AAC3B,YAAMI,cAAc;QAAC;QAAmBZ,GAAAA,OAAOK;MAAK;AACpD,YAAMQ,gBAAYC,YAAAA,SAAIf,OAAOa,aAAa,CAAA,CAAC;AAC3C,YAAMG,gBAAgBC,OAAOX,KAAKQ,SAAAA,EAAWI,OAAO,CAACC,KAAKC,YAAAA;AACxDD,YAAIC,OAAAA,IAAW;UAAE,GAAGN,UAAUM,OAAQ;UAAEC,SAASpB,OAAOQ;QAAM;AAE9D,eAAOU;MACT,GAAG,CAAA,CAAC;AAEJP,qBAAAA,SAAIT,YAAYU,aAAaG,aAAAA;AAE7B;IACF;IACA,KAAK,YAAY;AACfb,iBAAWR,eAAeK,MAAMN;AAChC;IACF;IACA,KAAK,uBAAuB;AAC1BS,iBAAWT,cAAcM,MAAML;AAC/B;IACF;IAEA,KAAK,iBAAiB;AACpB,YAAM,EAAE2B,eAAc,IAAKrB;AAC3BE,iBAAWN,iBAAiByB,mBAAmBtB,MAAMH,iBAAiB,KAAKyB;AAC3E;IACF;IACA;AACE,aAAOnB;EACX;AACF,CAAA;;;AC7CF,IAAMoB,wBAAmBC,0BAAW,CAAC,EAAEC,aAAaC,OAAM,GAAIC,QAAAA;AAC5D,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,OAAOC,QAAAA,QAAYC,0BAAWC,UAASC,eAAc,CAACJ,WAC3DK,MAAKL,QAAOL,aAAaC,MAAAA,CAAAA;AAG3BU,yCAAoBT,KAAK,OAAO;IAC9BU,iBAAAA;AACE,aAAO;QACLZ,aAAaK,MAAMQ;MACrB;IACF;IACAC,YAAAA;AACER,eAAS;QAAES,MAAM;MAAW,CAAA;IAC9B;IACAC,qBAAAA;AACEV,eAAS;QAAES,MAAM;MAAsB,CAAA;IACzC;IACF;AAEA,QAAME,eAAe,CAAC,EAAEC,QAAQ,EAAEC,MAAMC,MAAK,EAAE,MAC7Cd,SAAS;IACPS,MAAM;IACNM,MAAMF,KAAKG,MAAM,GAAA;IACjBF,OAAOA,UAAU,wBAAwB,KAAKA;EAChD,CAAA;AAEF,QAAMG,wBAAwB,CAAC,EAAEL,QAAQ,EAAEC,MAAMC,MAAK,EAAE,MACtDd,SAAS;IACPS,MAAM;IACNM,MAAMF,KAAKG,MAAM,GAAA;IACjBF;EACF,CAAA;AAEF,QAAMI,uBAAuB,CAACC,mBAC5BnB,SAAS;IACPS,MAAM;IACNU;EACF,CAAA;AAEF,QAAMC,gBAAgB;IACpB,GAAGrB;IACHsB,UAAUV;IACVW,mBAAmBL;IACnBM,kBAAkBL;EACpB;AAEA,aACEM,yBAACC,0BAAAA;IAAyBX,OAAOM;kBAC/BM,0BAACC,KAAKC,MAAI;MAACC,KAAK;MAAGC,QAAO;MAAeC,WAAS;MAACC,YAAW;;YAC5DR,yBAACG,KAAKM,MAAI;UACRC,KAAK;UACLC,YAAY;UACZC,eAAe;UACfC,aAAa;UACbC,cAAc;UACdC,WAAU;UACVC,YAAW;UAEX,cAAAd,0BAACe,MAAAA;YAAKF,WAAU;YAASC,YAAW;YAAUX,KAAK;;kBACjDH,0BAACe,MAAAA;gBAAKF,WAAU;gBAASC,YAAW;gBAAUX,KAAK;;sBACjDL,yBAACkB,YAAAA;oBAAWC,SAAQ;oBAAQC,KAAI;8BAC7B/C,cAAc;sBACbgD,IAAIC,QAAQ,sBAAA;sBACZC,gBAAgB;oBAClB,CAAA;;sBAEFvB,yBAACkB,YAAAA;oBAAWE,KAAI;oBAAII,WAAU;8BAC3BnD,cAAc;sBACbgD,IAAIC,QAAQ,4BAAA;sBACZC,gBAAgB;oBAClB,CAAA;;;;kBAGJvB,yBAACyB,aAAAA,CAAAA,CAAAA;;;;YAGLzB,yBAAC0B,UAAAA,CAAAA,CAAAA;;;;AAIT,CAAA;AAEA1D,kBAAiB2D,YAAY;EAC3BzD,aAAa0D,mBAAAA,QAAUC,OAAOC;EAC9B3D,QAAQyD,mBAAAA,QAAUC,OAAOC;AAC3B;AAEA,IAAA,yBAAeC,oBAAK/D,iBAAkB;;;ICnGzBgE,mBAAuBC,QAAM,EAAGC,MAAM;EACjDC,MAAUC,OAAM,EAAGC,SAASC,YAAiBD,SAASE,EAAE;EACxDC,aAAiBJ,OAAM,EAAGC,SAASC,YAAiBD,SAASE,EAAE;AACjE,CAAG;;;;;;;ACJGE,IAAAA,mBAAmB,CAACC,gBACxBC,OAAOC,KAAKF,WAAaG,EAAAA,OAAO,CAACC,KAAKC,YAAAA;AACpC,QAAMC,oBAAoBN,YAAYK,OAAAA,EAASE;AAC/C,QAAMC,qBAAqBP,OAAOC,KAAKI,iBAAAA,EAAmBH,OAAO,CAACM,MAAMC,SAAAA;AACtE,YAAIC,gBAAAA,SAAQL,kBAAkBI,IAAAA,CAAK,GAAG;AACpC,aAAOD;IACT;AAEAA,SAAKC,IAAAA,IAAQJ,kBAAkBI,IAAK;AAEpC,WAAOD;EACT,GAAG,CAAA,CAAC;AAEJ,UAAIE,gBAAAA,SAAQH,kBAAqB,GAAA;AAC/B,WAAOJ;EACT;AAEAA,MAAIC,OAAAA,IAAW;IAAEE,aAAaC;EAAmB;AAEjD,SAAOJ;AACT,GAAG,CAAA,CAAC;;;ICfOQ,aAAa,MAAA;AACxB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,KAAAA,KAAG,IAAKC,eAAAA;AAChB,QAAM,EAAEC,eAAc,IAAKC,mBAAmBC,OAAAA;AAE9C,QAAM,CACJ,EACEC,MAAMC,aACNC,WAAWC,sBACXC,OAAOC,kBACPC,SAASC,mBAAkB,GAE7B,EAAEP,MAAMQ,QAAQN,WAAWO,iBAAiBL,OAAOM,aAAaJ,SAASK,cAAa,CAAE,IACtFC,WAAW;IACb;MACEC,UAAU;QAAC;QAAqB;MAAc;MAC9C,MAAMC,UAAAA;AACJ,cAAM,EACJd,MAAM,EAAEC,aAAAA,aAAW,EAAE,IACnB,MAAMN,KAAI,gCAAgC;AAE9C,eAAOM;MACT;IACF;IACA;MACEY,UAAU;QAAC;QAAqB;MAAS;MACzC,MAAMC,UAAAA;AACJ,cAAM,EACJd,MAAM,EAAEQ,QAAAA,QAAM,EAAE,IACd,MAAMb,KAAI,2BAA2B;AAEzC,eAAOa;MACT;IACF;EACD,CAAA;AAED,QAAMO,iBAAiB,YAAA;AACrB,UAAMC,QAAQC,IAAI;MAACV,mBAAAA;MAAsBI,cAAAA;IAAgB,CAAA;EAC3D;AAEAO,+BAAU,MAAA;AACR,QAAIb,kBAAkB;AACpBZ,yBAAmB;QACjB0B,MAAM;QACNC,SAASvB,eAAeQ,gBAAAA;MAC1B,CAAA;IACF;KACC;IAACZ;IAAoBY;IAAkBR;EAAe,CAAA;AAEzDqB,+BAAU,MAAA;AACR,QAAIR,aAAa;AACfjB,yBAAmB;QACjB0B,MAAM;QACNC,SAASvB,eAAea,WAAAA;MAC1B,CAAA;IACF;KACC;IAACjB;IAAoBiB;IAAab;EAAe,CAAA;AAEpD,QAAMK,YAAYC,wBAAwBM;AAE1C,SAAO;;;;IAILR,aAAaA,cAAcoB,iBAAiBpB,WAAAA,IAAe,CAAA;IAC3DO,QAAQA,UAAU,CAAA;IAElBc,SAASP;IACTb;EACF;AACF;;;ICpDaqB,aAAa,MAAA;AACxB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,WAAWC,kBAAkBC,aAAaC,OAAM,IAAKC,WAAAA;AAC7D,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAMC,iBAAuBC,aAAM;AACnC,QAAM,EAAEC,KAAI,IAAKC,eAAAA;AACjB,QAAMC,WAAWC,YAAY,CAACC,SAASJ,KAAK,4BAA4BI,IAAO,GAAA;IAC7EC,UAAAA;AACElB,yBAAmB;QACjBmB,MAAM;QACNC,SAAStB,cAAc;UACrBuB,IAAI;UACJC,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;IAEAC,YAAAA;AACEd,iBAAW,eAAA;AAEXT,yBAAmB;QACjBmB,MAAM;QACNC,SAAStB,cAAc;UACrBuB,IAAIG,QAAQ,wBAAA;UACZF,gBAAgB;QAClB,CAAA;MACF,CAAA;AAGApB,eAAS,EAAC;IACZ;EACF,CAAA;AAEA,QAAMuB,yBAAyB,OAAOC,SAAAA;AAIpC,UAAMpB,eAAcK,eAAegB,QAAQC,eAAc;AAEzD,UAAMb,SAASc,OAAO;MAAE,GAAGH;MAAM,GAAGpB;MAAawB,OAAO,CAAA;IAAG,CAAA;EAC7D;AAEA,aACEC,0BAACC,MAAAA;;UACCC,yBAACC,KAAKC,OAAK;kBACRrC,cACC;UAAEuB,IAAI;UAAsBC,gBAAgB;WAC5C;UAAEc,MAAM;QAAQ,CAAA;;UAGpBH,yBAACI,QAAAA;QACCC,oBAAkB;QAClBC,eAAe;UAAEH,MAAM;UAAII,aAAa;QAAG;QAC3CC,UAAUhB;QACViB,kBAAkBC;kBAEjB,CAAC,EAAEC,cAAcC,QAAQC,cAAcC,OAAM,UAC5ChB,0BAACiB,MAAAA;UAAKC,YAAU;UAACR,UAAUG;;gBACzBX,yBAACiB,QAAQC,QAAM;cACbC,eACE,CAAC/C,wBACC4B,yBAACoB,QAAAA;gBAAOlC,MAAK;gBAASmC,SAASvC,SAASX;gBAAWmD,eAAWtB,yBAACuB,eAAAA,CAAAA,CAAAA;0BAC5D1D,cAAc;kBACbuB,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;cAINmC,OAAO3D,cAAc;gBACnBuB,IAAI;gBACJC,gBAAgB;cAClB,CAAA;cACAoC,UAAU5D,cAAc;gBACtBuB,IAAI;gBACJC,gBAAgB;cAClB,CAAA;;gBAEFW,yBAACiB,QAAQS,SAAO;cACd,cAAA5B,0BAAC6B,MAAAA;gBACCC,YAAW;gBACXC,WAAU;gBACVC,YAAW;gBACXC,KAAK;gBACLC,WAAS;gBACTC,YAAY;gBACZC,eAAe;gBACfC,aAAa;gBACbC,cAAc;gBACdC,QAAO;;sBAEPvC,0BAAC6B,MAAAA;oBAAKE,WAAU;oBAASC,YAAW;;0BAClC9B,yBAACsC,YAAAA;wBAAWC,SAAQ;wBAAQC,KAAI;kCAC7B3E,cAAc;0BACbuB,IAAIG,QAAQ,qBAAA;0BACZF,gBAAgB;wBAClB,CAAA;;0BAGFS,0BAAC2C,KAAKC,MAAI;wBAACX,KAAK;;8BACd/B,yBAACyC,KAAKE,MAAI;4BAACC,KAAK;4BAAGf,WAAU;4BAASC,YAAW;0CAC/ChC,0BAAC+C,MAAMH,MAAI;8BACTvC,MAAK;8BACL2C,QACEhC,iCAAQX,QACJtC,cAAc;gCAAEuB,IAAI0B,OAAOX;gCAAMd,gBAAgB;+BACjD,IAAA;8BAEN0D,UAAQ;;oCAER/C,yBAAC6C,MAAMG,OAAK;4CACTnF,cAAc;oCACbuB,IAAI;oCACJC,gBAAgB;kCAClB,CAAA;;oCAEFW,yBAACiD,WAAAA;kCAAUC,OAAOtC,OAAOT,QAAQ;kCAAIgD,UAAUtC;;oCAC/Cb,yBAAC6C,MAAMO,OAAK,CAAA,CAAA;;;;8BAGhBpD,yBAACyC,KAAKE,MAAI;4BAACC,KAAK;4BAAGf,WAAU;4BAASC,YAAW;0CAC/ChC,0BAAC+C,MAAMH,MAAI;8BACTvC,MAAK;8BACL2C,QACEhC,iCAAQP,eACJ1C,cAAc;gCACZuB,IAAI0B,OAAOP;gCACXlB,gBAAgB;+BAElB,IAAA;8BAEN0D,UAAQ;;oCAER/C,yBAAC6C,MAAMG,OAAK;4CACTnF,cAAc;oCACbuB,IAAI;oCACJC,gBAAgB;kCAClB,CAAA;;oCAEFW,yBAACqD,UAAAA;kCAASH,OAAOtC,OAAOL,eAAe;kCAAI4C,UAAUtC;;oCACrDb,yBAAC6C,MAAMO,OAAK,CAAA,CAAA;;;;;;;;kBAMnB,CAAChF,wBACA4B,yBAACsD,oBAAAA;oBACCC,KAAK7E;oBACLL;oBACAC;;;;;;;;;;AAUpB;AAEakF,IAAAA,2BAA2B,UACtCxD,yBAACC,KAAKwD,SAAO;EAACpF,aAAaqF,YAAYC;EACrC,cAAA3D,yBAACpC,YAAAA,CAAAA,CAAAA;AAEH,CAAA;;;;;ICjKWgG,WAAW,MAAA;AACtB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EACJC,QAAQ,EAAEC,GAAE,EAAE,IACZC,SAAS,uCAAuC;AACpD,QAAM,EAAEC,KAAAA,KAAG,IAAKC,eAAAA;AAChB,QAAM,EAAEC,WAAWC,kBAAkBC,OAAM,IAAKC,WAAAA;AAChD,QAAM,EACJC,MAAMC,MACNL,WAAWM,eACXC,SAASC,YAAW,IAClBC,SAAS;IAAC;IAAqB;IAAQb;KAAK,YAAA;AAE9C,UAAM,EACJQ,MAAM,EAAEC,MAAAA,MAAI,EAAE,IACZ,MAAMP,KAAI,4BAA4BF,EAAAA,EAAI;AAE9C,WAAOS;EACT,CAAA;AAEA,QAAMK,iBAAuBC,cAAM;AACnC,QAAM,EAAEC,IAAG,IAAKb,eAAAA;AAChB,QAAM,EAAEc,eAAc,IAAKC,mBAAAA;AAC3B,QAAMC,WAAWC,YAAY,CAACC,SAASL,IAAI,4BAA4BhB,EAAAA,IAAMqB,IAAO,GAAA;IAClFC,QAAQC,OAAK;AACX1B,yBAAmB;QACjB2B,MAAM;QACNC,SAASR,eAAeM,KAAAA;MAC1B,CAAA;IACF;IAEA,MAAMG,YAAAA;AACJ7B,yBAAmB;QACjB2B,MAAM;QACNC,SAAS9B,cAAc;UACrBK,IAAI2B,QAAQ,wBAAA;UACZC,gBAAgB;QAClB,CAAA;MACF,CAAA;AAEA,YAAMhB,YAAAA;IACR;EACF,CAAA;AAEA,QAAMiB,uBAAuB,OAAOrB,SAAAA;AAClC,UAAMsB,cAAchB,eAAeiB,QAAQC,eAAc;AAEzD,UAAMb,SAASc,OAAO;MAAE,GAAGzB;MAAM,GAAGsB;MAAaI,OAAO,CAAA;IAAG,CAAA;EAC7D;AAEA,MAAIxB,eAAe;AACjB,eAAOyB,yBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,0BAACC,MAAAA;;UACCJ,yBAACC,KAAKI,OAAK;kBACR7C,cACC;UAAEK,IAAI;UAAsB4B,gBAAgB;WAC5C;UAAEa,MAAM;QAAQ,CAAA;;UAGpBN,yBAACO,QAAAA;QACCC,oBAAkB;QAClBC,eAAe;UAAEH,MAAMhC,KAAKgC;UAAMI,aAAapC,KAAKoC;QAAY;QAChEC,UAAUjB;QACVkB,kBAAkBC;kBAEjB,CAAC,EAAEC,cAAcC,QAAQC,cAAcC,OAAM,UAC5Cd,0BAACe,MAAAA;UAAKC,YAAU;UAACR,UAAUG;;gBACzBd,yBAACoB,QAAQC,QAAM;cACbC,eACE,CAACpD,uBACC8B,yBAACuB,QAAAA;gBACCC,UAAUlD,KAAKmD,SAAS;gBACxBpC,MAAK;gBACLqC,SAAS1C,SAASf;gBAClB0D,eAAW3B,yBAAC4B,eAAAA,CAAAA,CAAAA;0BAEXpE,cAAc;kBACbK,IAAI;kBACJ4B,gBAAgB;gBAClB,CAAA;cAEA,CAAA,IAAA;cAENoC,OAAOvD,KAAKgC;cACZwB,UAAUxD,KAAKoC;cACfqB,sBAAkB/B,yBAACgC,YAAAA;gBAAWC,UAAS;;;gBAEzCjC,yBAACoB,QAAQc,SAAO;cACd,cAAA/B,0BAACgC,MAAAA;gBACCC,YAAW;gBACXC,WAAU;gBACVC,YAAW;gBACXC,KAAK;gBACLC,WAAS;gBACTC,YAAY;gBACZC,eAAe;gBACfC,aAAa;gBACbC,cAAc;gBACdC,QAAO;;sBAEP1C,0BAACgC,MAAAA;oBAAKE,WAAU;oBAASC,YAAW;oBAAUC,KAAK;;0BACjDvC,yBAAC8C,YAAAA;wBAAWC,SAAQ;wBAAQC,KAAI;kCAC7BxF,cAAc;0BACbK,IAAI2B,QAAQ,qBAAA;0BACZC,gBAAgB;wBAClB,CAAA;;0BAGFU,0BAAC8C,KAAKC,MAAI;wBAACX,KAAK;;8BACdvC,yBAACiD,KAAKE,MAAI;4BAACC,KAAK;4BAAGf,WAAU;4BAASC,YAAW;0CAC/CnC,0BAACkD,MAAMH,MAAI;8BACT5C,MAAK;8BACLlB,QACE6B,iCAAQX,QACJ9C,cAAc;gCACZK,IAAIoD,OAAOX;gCACXb,gBAAgB;+BAElB,IAAA;8BAEN6D,UAAQ;;oCAERtD,yBAACqD,MAAME,OAAK;4CACT/F,cAAc;oCACbK,IAAI;oCACJ4B,gBAAgB;kCAClB,CAAA;;oCAEFO,yBAACwD,WAAAA;kCAAUC,OAAO1C,OAAOT,QAAQ;kCAAIoD,UAAU1C;;oCAC/ChB,yBAACqD,MAAMM,OAAK,CAAA,CAAA;;;;8BAGhB3D,yBAACiD,KAAKE,MAAI;4BAACC,KAAK;4BAAGf,WAAU;4BAASC,YAAW;0CAC/CnC,0BAACkD,MAAMH,MAAI;8BACT5C,MAAK;8BACLlB,QACE6B,iCAAQP,eACJlD,cAAc;gCACZK,IAAIoD,OAAOP;gCACXjB,gBAAgB;+BAElB,IAAA;8BAEN6D,UAAQ;;oCAERtD,yBAACqD,MAAME,OAAK;4CACT/F,cAAc;oCACbK,IAAI;oCACJ4B,gBAAgB;kCAClB,CAAA;;oCAEFO,yBAAC4D,UAAAA;kCAASH,OAAO1C,OAAOL,eAAe;kCAAIgD,UAAU1C;;oCACrDhB,yBAACqD,MAAMM,OAAK,CAAA,CAAA;;;;;;;;kBAMnB,CAACzF,wBACA8B,yBAAC6D,oBAAAA;oBACCC,KAAKnF;oBACLgB,aAAarB,KAAKqB;oBAClBxB;;;;;;;;;;AAUpB;AAEa4F,IAAAA,yBAAyB,UACpC/D,yBAACC,KAAK+D,SAAO;EAACrE,aAAasE,YAAYC;EACrC,cAAAlE,yBAACzC,UAAAA,CAAAA,CAAAA;AAEH,CAAA;;;;;;;;;;;AC7MF,IAAM4G,WAAWC,GAAOC,IAAAA;;;;;;aAMX,CAAC,EAAEC,MAAK,MAAO,GAAGA,MAAMC,OAAO,CAAE,CAAA,EAAE;;;;;;;cAOlC,CAAC,EAAED,MAAK,MAAOA,MAAME,OAAOC,UAAU;;;;;;;;gBAQpC,CAAC,EAAEH,MAAK,MAAOA,MAAME,OAAOE,UAAU;;;;;AAMtD,IAAMC,YAAY,CAAC,EAAEC,aAAaC,WAAWC,WAAWC,iBAAiBC,SAAQ,MAAE;AACjF,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,WAAWC,YAAAA;AACjB,QAAM,CAACC,mBAAmBC,oBAAAA,IAAwBN;AAElD,QAAMO,qBAAqB,CAACC,SAC1BX,aAAa,CAAC;IAAC;IAAU;IAAiBY,SAASD,KAAKE,IAAI;AAE9D,QAAMC,oBAAoB,CAACC,OAAAA;AACzBb,oBAAgBa,EAAAA;AAChBN,yBAAqB,CAACD,iBAAAA;EACxB;AAEA,aACEQ,0BAACC,OAAAA;cACElB,2CAAamB,IAAI,CAACP,aACjBQ,2BAACC,IAAAA;MAAGC,QAAO;MAA0BC,SAAS,MAAMhB,SAASK,KAAKI,GAAGQ,SAAQ,CAAA;;YAC3EP,0BAACQ,IAAAA;UAAGC,OAAM;UACR,cAAAT,0BAACU,YAAAA;YAAYf,UAAAA,KAAKgB;;;YAEpBX,0BAACQ,IAAAA;UAAGC,OAAM;UACR,cAAAT,0BAACU,YAAAA;YAAYf,UAAAA,KAAKiB;;;YAEpBZ,0BAACQ,IAAAA;UAAGC,OAAM;UACR,cAAAT,0BAACU,YAAAA;sBACEtB,cACC;cACEW,IAAI;cACJc,gBAAgB;eAElB;cAAEC,QAAQnB,KAAKoB;YAAS,CAAA;;;YAI9Bf,0BAACQ,IAAAA;UACC,cAAAL,2BAACa,MAAAA;YAAKC,gBAAe;YAAMX,SAAS,CAACY,MAAMA,EAAEC,gBAAe;;cACzDlC,gBACCe,0BAAC1B,UAAAA;gBACC8C,KAAKC;gBACLC,IAAI3B,KAAKI,GAAGQ,SAAQ;gBACpBgB,cAAYnC,cACV;kBAAEW,IAAI;kBAA4Bc,gBAAgB;mBAClD;kBAAEW,QAAQ,GAAG7B,KAAKgB,IAAI;gBAAG,CAAA;gBAG3B,cAAAX,0BAACyB,eAAAA,CAAAA,CAAAA;cAED,CAAA,IAAA;cAEH/B,mBAAmBC,IAAAA,SAClBK,0BAAC0B,YAAAA;gBACCpB,SAAS,MAAMR,kBAAkBH,KAAKI,GAAGQ,SAAQ,CAAA;gBACjDoB,SAAQ;gBACRC,OAAOxC,cACL;kBAAEW,IAAI;kBAAwBc,gBAAgB;mBAC9C;kBAAEW,QAAQ,GAAG7B,KAAKgB,IAAI;gBAAG,CAAA;gBAG3B,cAAAX,0BAAC6B,cAAAA,CAAAA,CAAAA;;;;;;IA1CelC,GAAAA,KAAKgB,IAAI;;AAmD3C;AAIA7B,UAAUgD,eAAe;EACvB9C,WAAW;EACXC,WAAW;AACb;AAEAH,UAAUiD,YAAY;EACpB5C,UAAU6C,mBAAAA,QAAUC,MAAMC;EAC1BhD,iBAAiB8C,mBAAAA,QAAUG,KAAKD;EAChCnD,aAAaiD,mBAAAA,QAAUC,MAAMC;EAC7BlD,WAAWgD,mBAAAA,QAAUI;EACrBnD,WAAW+C,mBAAAA,QAAUI;AACvB;;;ICjFaC,gBAAgB,MAAA;AAC3B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,eAAeC,OAAM,IAAKC,QAAAA;AAClC,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,aAAY,IAAKC,YAAAA;AACzB,QAAM,CAAC,EAAEC,MAAK,CAAE,IAAIC,eAAAA;AACpB,QAAMC,MAAKF,+BAAOE,OAAM;AACxB,QAAM,CAACC,mBAAmBC,oBAAqB,QAAGC,yBAAS,KAAA;AAC3D,QAAM,CAACC,cAAcC,eAAAA,QAAmBF,yBAAAA;AACxC,QAAM,EAAEG,KAAKC,KAAAA,KAAG,IAAKC,eAAAA;AAErB,QAAM,EACJC,WAAWC,yBACXC,gBAAgB,EAAEC,SAASC,WAAWC,WAAWC,UAAS,EAAE,IAC1DC,QAAQ;IACVC,QAAQC,YAAYC;IACpBC,MAAMF,YAAYG;IAClBC,QAAQJ,YAAYK;IACpBC,QAAQN,YAAYO;EACtB,CAAA;AAEA,QAAM,EACJhB,WAAWiB,kBACXC,MAAM,EAAEC,MAAK,GACbC,YACAC,QAAO,IACLC,SAAS,aAAa,MAAMC,UAAUtC,oBAAoBH,eAAeK,YAAe,GAAA;IAC1FqC,aAAa,CAAA;IACbC,SAAStB;EACX,CAAA;AAEA,QAAM,EAAEuB,SAAQ,IAAKC,UAAU5C,QAAQ;IACrC6C,aAAa;EACf,CAAA;AAKA,QAAMC,YAAYC,YAAY/C,QAAQ;IACpC6C,aAAa;EACf,CAAA;AAEA,QAAM5B,YAAYiB,oBAAoBG,cAAcnB;AAEpD,QAAM8B,0BAA0B,MAAA;AAC9BtC,yBAAqB,CAACD,iBAAAA;EACxB;AAEA,QAAMwC,aAAa,OAAOC,IAAInD,gBAAeG,wBAAAA;AAC3C,QAAI;AACF,YAAMY,IAAI,4BAA4BoC,EAAAA,EAAI;IAC5C,SAASC,OAAO;AACdjD,MAAAA,oBAAmB;QACjBkD,MAAM;QACNC,SAAStD,eAAc;UAAEmD,IAAI;UAAsBI,gBAAgB;QAAmB,CAAA;MACxF,CAAA;IACF;EACF;AAEA,QAAMd,YAAY,OAAOtC,qBAAoBH,gBAAeK,kBAAAA;AAC1D,QAAI;AACF,YAAM,EAAE+B,KAAI,IAAK,MAAMpB,KAAI,0BAAA;AAC3BX,MAAAA,cAAa,oCAAA;AAEb,aAAO+B;IACT,SAASoB,KAAK;AACZrD,MAAAA,oBAAmB;QACjBkD,MAAM;QACNC,SAAStD,eAAc;UAAEmD,IAAI;UAAsBI,gBAAgB;QAAoB,CAAA;MACzF,CAAA;AAEA,YAAM,IAAIE,MAAMD,GAAAA;IAClB;EACF;AAEA,QAAME,cAAc;IAClBrB,OAAO;MACLc,IAAIQ,QAAQ,aAAA;MACZJ,gBAAgB;IAClB;IACAK,QAAQ;MACNT,IAAIQ,QAAQ,oBAAA;MACZJ,gBAAgB;IAClB;EACF;AAEA,QAAMM,YAAY7D,cAAc;IAC9BmD,IAAI;IACJI,gBAAgB;EAClB,CAAA;AAEA,QAAMO,iBAAiBC,YAAY,CAACZ,OAAOD,WAAWC,IAAInD,eAAeG,kBAAqB,GAAA;IAC5F,MAAM6D,YAAAA;AACJ,YAAMzB,QAAAA;IACR;EACF,CAAA;AAEA,QAAM0B,sBAAsB,YAAA;AAC1B,UAAMH,eAAeI,YAAYrD,YAAAA;AACjCF,yBAAqB,CAACD,iBAAAA;EACxB;AAEA,QAAMyD,eAAe9B,SAAS,CAAA,GAC3B+B,OAAO,CAACC,SAASzB,SAASyB,KAAKC,MAAM7D,EAAAA,KAAOmC,SAASyB,KAAKE,aAAa9D,EAAAA,CAAAA,EACvE+D,KACC,CAACC,GAAGC,MAAM3B,UAAU4B,QAAQF,EAAEH,MAAMI,EAAEJ,IAAI,KAAKvB,UAAU4B,QAAQF,EAAEF,aAAaG,EAAEH,WAAW,CAAA;AAGjG,QAAMK,eAAenE,MAAM,CAAC0D,YAAYU,SAAS,WAAW;AAE5D,QAAMC,WAAW;AACjB,QAAMC,aAAY1C,+BAAOwC,WAAU,KAAK;AAExC,MAAI3D,WAAW;AACb,eAAO8D,0BAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,aACEC,2BAACC,QAAQC,MAAI;;UACXL,0BAACC,KAAKK,OAAK;kBACRtF,cACC;UAAEmD,IAAI;UAAsBI,gBAAgB;WAC5C;UAAEe,MAAMT;QAAU,CAAA;;UAGtBsB,2BAACF,KAAKM,MAAI;;cACRP,0BAACI,QAAQI,QAAM;YACbC,OAAOzF,cAAc;cACnBmD,IAAI;cACJI,gBAAgB;YAClB,CAAA;YACAmC,UAAU1F,cAAc;cACtBmD,IAAI;cACJI,gBAAgB;YAClB,CAAA;YACAoC,eACEpE,gBACEyD,0BAACY,YAAAA;cACCC,IAAG;cACHC,KAAKC;cACLC,SAAS,MAAMlG,WAAW,gBAAA;cAC1BmG,eAAWjB,0BAACkB,eAAAA,CAAAA,CAAAA;cACZC,MAAK;wBAEJnG,cAAc;gBACbmD,IAAIQ,QAAQ,mBAAA;gBACZJ,gBAAgB;cAClB,CAAA;YAEA,CAAA,IAAA;;cAIRyB,0BAACI,QAAQgB,QAAM;YACbC,kBACErB,0BAACsB,aAAAA;cACCC,OAAOvG,cAAc;gBACnBmD,IAAI;gBACJI,gBAAgB;cAClB,CAAA;;;cAKN4B,2BAACC,QAAQoB,SAAO;;cACb,CAACnF,eAAW2D,0BAACC,KAAKwB,eAAa,CAAA,CAAA;cAC/BpF,WAAW8C,gBAAeA,2CAAaU,cACtCM,2BAACuB,OAAAA;gBAAM5B;gBAAoBC;;sBACzBC,0BAAC2B,OAAAA;oBACC,cAAAxB,2BAACyB,IAAAA;;4BACC5B,0BAAC6B,IAAAA;0BACC,cAAA7B,0BAAC8B,YAAAA;4BAAWC,SAAQ;4BAAQC,WAAU;sCACnChH,cAAc;8BAAEmD,IAAI;8BAAeI,gBAAgB;4BAAO,CAAA;;;4BAG/DyB,0BAAC6B,IAAAA;0BACC,cAAA7B,0BAAC8B,YAAAA;4BAAWC,SAAQ;4BAAQC,WAAU;sCACnChH,cAAc;8BACbmD,IAAI;8BACJI,gBAAgB;4BAClB,CAAA;;;4BAGJyB,0BAAC6B,IAAAA;0BACC,cAAA7B,0BAAC8B,YAAAA;4BAAWC,SAAQ;4BAAQC,WAAU;sCACnChH,cAAc;8BACbmD,IAAI;8BACJI,gBAAgB;4BAClB,CAAA;;;4BAGJyB,0BAAC6B,IAAAA;0BACC,cAAA7B,0BAACiC,gBAAAA;sCACEjH,cAAc;8BACbmD,IAAI;8BACJI,gBAAgB;4BAClB,CAAA;;;;;;sBAKRyB,0BAACkC,WAAAA;oBACC/C;oBACA7C;oBACAE;oBACA2F,aAAaxF;oBACbb;oBACAsG,UAAU;sBAAC1G;sBAAmBC;oBAAqB;;;uBAIvDqE,0BAACqC,kBAAAA;gBAAiBC,SAAStH,cAAc0D,YAAYkB,YAAa,CAAA;;;;cAGtEI,0BAACuC,OAAOlC,MAAI;YAACmC,MAAM9G;YAAmB+G,cAAcxE;YAClD,cAAA+B,0BAAC0C,eAAAA;cAAcC,WAAW1D;;;;;;;AAKpC;IAEa2D,yBAAyB,MAAA;AACpC,aACE5C,0BAACC,KAAK4C,SAAO;IAACV,aAAaxF,YAAYmG;IACrC,cAAA9C,0BAACnF,eAAAA,CAAAA,CAAAA;;AAGP;;;AC9PA,IAAMkI,QAAQ,MAAA;AACZ,aACEC,0BAACC,KAAKC,SAAO;IAACC,aAAaC,YAAYC;IACrC,cAAAC,2BAACC,QAAAA;;YACCP,0BAACQ,OAAAA;UAAMC,OAAK;UAACC,aAASV,0BAACW,wBAAAA,CAAAA,CAAAA;;YACvBX,0BAACQ,OAAAA;UAAMI,MAAK;UAAMF,aAASV,0BAACa,0BAAAA,CAAAA,CAAAA;;YAC5Bb,0BAACQ,OAAAA;UAAMI,MAAK;UAAMF,aAASV,0BAACc,wBAAAA,CAAAA,CAAAA;;;;;AAIpC;", "names": ["without", "take", "UsersPermissions", "createContext", "UsersPermissionsProvider", "children", "value", "_jsx", "Provider", "useUsersPermissions", "useContext", "propTypes", "PropTypes", "node", "isRequired", "object", "formatPluginName", "pluginSlug", "upperFirst", "replace", "init", "initialState", "permissions", "collapses", "Object", "keys", "sort", "map", "name", "isOpen", "activeCheckboxWrapperStyles", "css", "props", "theme", "colors", "primary100", "CheckboxWrapper", "styled", "Box", "primary600", "isActive", "Border", "styled", "div", "theme", "colors", "neutral150", "SubCategory", "subCategory", "formatMessage", "useIntl", "onChange", "onChangeSelectAll", "onSelectedAction", "selectedAction", "modifiedData", "useUsersPermissions", "currentScopedModifiedData", "useMemo", "get", "name", "hasAllActionsSelected", "Object", "values", "every", "action", "enabled", "hasSomeActionsSelected", "some", "handleChangeSelectAll", "useCallback", "target", "value", "isActionSelected", "actionName", "_jsxs", "Box", "Flex", "justifyContent", "alignItems", "_jsx", "paddingRight", "Typography", "variant", "textColor", "label", "paddingLeft", "Checkbox", "checked", "onCheckedChange", "id", "defaultMessage", "paddingTop", "paddingBottom", "Grid", "Root", "gap", "style", "flex", "actions", "map", "<PERSON><PERSON>", "col", "direction", "CheckboxWrapper", "isActive", "padding", "hasRadius", "button", "type", "onClick", "display", "VisuallyHidden", "tag", "route", "Cog", "cursor", "propTypes", "PropTypes", "object", "isRequired", "PermissionRow", "name", "permissions", "subCategories", "useMemo", "sortBy", "Object", "values", "controllers", "reduce", "acc", "curr", "index", "currentName", "keys", "actions", "current", "label", "_jsx", "Box", "padding", "map", "subCategory", "SubCategory", "propTypes", "PropTypes", "string", "isRequired", "object", "initialState", "collapses", "reducer", "state", "action", "produce", "draftState", "type", "map", "collapse", "index", "isOpen", "Permissions", "modifiedData", "useUsersPermissions", "formatMessage", "useIntl", "collapses", "useReducer", "reducer", "initialState", "state", "init", "_jsx", "Accordion", "Root", "size", "Flex", "direction", "alignItems", "gap", "map", "collapse", "index", "_jsxs", "<PERSON><PERSON>", "value", "name", "Header", "variant", "undefined", "<PERSON><PERSON>", "caretPosition", "description", "id", "defaultMessage", "formatPluginName", "Content", "PermissionRow", "permissions", "getMethodColor", "verb", "text", "border", "background", "MethodBox", "styled", "Box", "theme", "spaces", "BoundRoute", "route", "formatMessage", "useIntl", "method", "handler", "title", "path", "formattedRoute", "tail", "split", "controller", "action", "colors", "getMethodColor", "_jsxs", "Flex", "direction", "alignItems", "gap", "Typography", "variant", "tag", "id", "defaultMessage", "_jsx", "span", "textColor", "hasRadius", "background", "borderColor", "border", "padding", "fontWeight", "text", "paddingLeft", "paddingRight", "map", "value", "includes", "defaultProps", "propTypes", "PropTypes", "shape", "string", "Policies", "formatMessage", "useIntl", "selectedAction", "routes", "useUsersPermissions", "path", "without", "split", "controllerRoutes", "get", "pathResolved", "slice", "join", "displayedRoutes", "isEmpty", "filter", "o", "handler", "endsWith", "_jsx", "Grid", "<PERSON><PERSON>", "col", "background", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "style", "minHeight", "direction", "alignItems", "Flex", "gap", "map", "route", "key", "BoundRoute", "_jsxs", "Typography", "variant", "tag", "id", "defaultMessage", "textColor", "init", "state", "permissions", "routes", "initialData", "modifiedData", "initialState", "initialData", "modifiedData", "routes", "selectedAction", "policies", "reducer", "state", "action", "produce", "draftState", "type", "<PERSON><PERSON><PERSON><PERSON>", "keys", "length", "isChangingCheckbox", "value", "take", "join", "set", "pathToValue", "oldValues", "get", "updatedValues", "Object", "reduce", "acc", "current", "enabled", "actionToSelect", "UsersPermissions", "forwardRef", "permissions", "routes", "ref", "formatMessage", "useIntl", "state", "dispatch", "useReducer", "reducer", "initialState", "init", "useImperativeHandle", "getPermissions", "modifiedData", "resetForm", "type", "setFormAfterSubmit", "handleChange", "target", "name", "value", "keys", "split", "handleChangeSelectAll", "handleSelectedAction", "actionToSelect", "providerValue", "onChange", "onChangeSelectAll", "onSelectedAction", "_jsx", "UsersPermissionsProvider", "_jsxs", "Grid", "Root", "gap", "shadow", "hasRadius", "background", "<PERSON><PERSON>", "col", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "direction", "alignItems", "Flex", "Typography", "variant", "tag", "id", "getTrad", "defaultMessage", "textColor", "Permissions", "Policies", "propTypes", "PropTypes", "object", "isRequired", "memo", "createRoleSchema", "object", "shape", "name", "string", "required", "translatedErrors", "id", "description", "cleanPermissions", "permissions", "Object", "keys", "reduce", "acc", "current", "currentPermission", "controllers", "cleanedControllers", "acc2", "curr", "isEmpty", "usePlugins", "toggleNotification", "useNotification", "get", "useFetchClient", "formatAPIError", "useAPIErrorHandler", "getTrad", "data", "permissions", "isLoading", "isLoadingPermissions", "error", "permissionsError", "refetch", "refetchPermissions", "routes", "isLoadingRoutes", "routesError", "refetchRoutes", "useQueries", "query<PERSON><PERSON>", "queryFn", "refetchQueries", "Promise", "all", "useEffect", "type", "message", "cleanPermissions", "getData", "CreatePage", "formatMessage", "useIntl", "toggleNotification", "useNotification", "navigate", "useNavigate", "isLoading", "isLoadingPlugins", "permissions", "routes", "usePlugins", "trackUsage", "useTracking", "permissionsRef", "useRef", "post", "useFetchClient", "mutation", "useMutation", "body", "onError", "type", "message", "id", "defaultMessage", "onSuccess", "getTrad", "handleCreateRoleSubmit", "data", "current", "getPermissions", "mutate", "users", "_jsxs", "Main", "_jsx", "Page", "Title", "name", "<PERSON><PERSON>", "enableReinitialize", "initialValues", "description", "onSubmit", "validationSchema", "createRoleSchema", "handleSubmit", "values", "handleChange", "errors", "Form", "noValidate", "Layouts", "Header", "primaryAction", "<PERSON><PERSON>", "loading", "startIcon", "Check", "title", "subtitle", "Content", "Flex", "background", "direction", "alignItems", "gap", "hasRadius", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "shadow", "Typography", "variant", "tag", "Grid", "Root", "<PERSON><PERSON>", "col", "Field", "error", "required", "Label", "TextInput", "value", "onChange", "Error", "Textarea", "UsersPermissions", "ref", "ProtectedRolesCreatePage", "Protect", "PERMISSIONS", "createRole", "EditPage", "formatMessage", "useIntl", "toggleNotification", "useNotification", "params", "id", "useMatch", "get", "useFetchClient", "isLoading", "isLoadingPlugins", "routes", "usePlugins", "data", "role", "isLoadingRole", "refetch", "refetchRole", "useQuery", "permissionsRef", "useRef", "put", "formatAPIError", "useAPIErrorHandler", "mutation", "useMutation", "body", "onError", "error", "type", "message", "onSuccess", "getTrad", "defaultMessage", "handleEditRoleSubmit", "permissions", "current", "getPermissions", "mutate", "users", "_jsx", "Page", "Loading", "_jsxs", "Main", "Title", "name", "<PERSON><PERSON>", "enableReinitialize", "initialValues", "description", "onSubmit", "validationSchema", "createRoleSchema", "handleSubmit", "values", "handleChange", "errors", "Form", "noValidate", "Layouts", "Header", "primaryAction", "<PERSON><PERSON>", "disabled", "code", "loading", "startIcon", "Check", "title", "subtitle", "navigationAction", "BackButton", "fallback", "Content", "Flex", "background", "direction", "alignItems", "gap", "hasRadius", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "shadow", "Typography", "variant", "tag", "Grid", "Root", "<PERSON><PERSON>", "col", "Field", "required", "Label", "TextInput", "value", "onChange", "Error", "Textarea", "UsersPermissions", "ref", "ProtectedRolesEditPage", "Protect", "PERMISSIONS", "updateRole", "EditLink", "styled", "Link", "theme", "spaces", "colors", "neutral500", "neutral800", "TableBody", "sortedRoles", "canDelete", "canUpdate", "setRoleToDelete", "onDelete", "formatMessage", "useIntl", "navigate", "useNavigate", "showConfirmDelete", "setShowConfirmDelete", "checkCanDeleteRole", "role", "includes", "type", "handleClickDelete", "id", "_jsx", "Tbody", "map", "_jsxs", "Tr", "cursor", "onClick", "toString", "Td", "width", "Typography", "name", "description", "defaultMessage", "number", "nb_users", "Flex", "justifyContent", "e", "stopPropagation", "tag", "NavLink", "to", "aria-label", "target", "Pencil", "IconButton", "variant", "label", "Trash", "defaultProps", "propTypes", "PropTypes", "array", "isRequired", "func", "bool", "RolesListPage", "trackUsage", "useTracking", "formatMessage", "locale", "useIntl", "toggleNotification", "useNotification", "notify<PERSON><PERSON><PERSON>", "useNotifyAT", "query", "useQueryParams", "_q", "showConfirmDelete", "setShowConfirmDelete", "useState", "roleToDelete", "setRoleToDelete", "del", "get", "useFetchClient", "isLoading", "isLoadingForPermissions", "allowedActions", "canRead", "canDelete", "canCreate", "canUpdate", "useRBAC", "create", "PERMISSIONS", "createRole", "read", "readRoles", "update", "updateRole", "delete", "deleteRole", "isLoadingForData", "data", "roles", "isFetching", "refetch", "useQuery", "fetchData", "initialData", "enabled", "contains", "useFilter", "sensitivity", "formatter", "useCollator", "handleShowConfirmDelete", "deleteData", "id", "error", "type", "message", "defaultMessage", "err", "Error", "emptyLayout", "getTrad", "search", "pageTitle", "deleteMutation", "useMutation", "onSuccess", "handleConfirmDelete", "mutateAsync", "sortedRoles", "filter", "role", "name", "description", "sort", "a", "b", "compare", "emptyContent", "length", "col<PERSON>ount", "rowCount", "_jsx", "Page", "Loading", "_jsxs", "Layouts", "Root", "Title", "Main", "Header", "title", "subtitle", "primaryAction", "LinkButton", "to", "tag", "NavLink", "onClick", "startIcon", "Plus", "size", "Action", "startActions", "SearchInput", "label", "Content", "NoPermissions", "Table", "<PERSON><PERSON>", "Tr", "Th", "Typography", "variant", "textColor", "VisuallyHidden", "TableBody", "permissions", "onDelete", "EmptyStateLayout", "content", "Dialog", "open", "onOpenChange", "ConfirmDialog", "onConfirm", "ProtectedRolesListPage", "Protect", "accessRoles", "Roles", "_jsx", "Page", "Protect", "permissions", "PERMISSIONS", "accessRoles", "_jsxs", "Routes", "Route", "index", "element", "ProtectedRolesListPage", "path", "ProtectedRolesCreatePage", "ProtectedRolesEditPage"]}