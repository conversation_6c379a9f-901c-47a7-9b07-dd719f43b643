{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\dfghj\\\\site\\\\frontend\\\\src\\\\components\\\\OpportuniteForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction OpportuniteForm() {\n  _s();\n  var _opportunite$data;\n  const [opportunite, setOpportunite] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetch(\"http://localhost:1337/api/opportunites?populate=*\").then(res => res.json()).then(data => {\n      console.log(\"Opportunites API response:\", data);\n      console.log(\"First opportunite structure:\", data.data && data.data[0]);\n      if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {\n        setOpportunite(data.data[0]); // ناخذ أول opportunite\n      } else {\n        console.warn(\"No opportunites found or unexpected response structure:\", data);\n        setOpportunite(null);\n      }\n      setLoading(false);\n    }).catch(err => {\n      console.error(\"Fetch error:\", err);\n      setOpportunite(null);\n      setLoading(false);\n    });\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 23\n  }, this);\n  if (!opportunite) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Aucune opportunit\\xE9 trouv\\xE9e.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 28\n  }, this);\n  console.log(\"Processing opportunite:\", opportunite);\n\n  // Try different possible data structures\n  const attr = opportunite.attributes || ((_opportunite$data = opportunite.data) === null || _opportunite$data === void 0 ? void 0 : _opportunite$data.attributes) || opportunite;\n\n  // Additional safety check\n  if (!attr) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Erreur: donn\\xE9es d'opportunit\\xE9 non disponibles.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 21\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: [\"Objet: \", attr.objet]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Pays de destination: \", attr.pays_destination]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Date d\\xE9but: \", attr.date_debut]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Date fin: \", attr.date_fin]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Secteur: \", attr.secteur]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Importateur\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), ((_attr$importateur, _attr$importateur$dat, _attr$importateur2) => {\n      const importateur = ((_attr$importateur = attr.importateur) === null || _attr$importateur === void 0 ? void 0 : (_attr$importateur$dat = _attr$importateur.data) === null || _attr$importateur$dat === void 0 ? void 0 : _attr$importateur$dat.attributes) || ((_attr$importateur2 = attr.importateur) === null || _attr$importateur2 === void 0 ? void 0 : _attr$importateur2.attributes) || attr.importateur;\n      return importateur !== null && importateur !== void 0 && importateur.societe ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Societe: \", importateur.societe]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Aucun importateur\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 11\n      }, this);\n    })(), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Exportateurs\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), (_attr$exportateurs => {\n      const exportateurs = ((_attr$exportateurs = attr.exportateurs) === null || _attr$exportateurs === void 0 ? void 0 : _attr$exportateurs.data) || attr.exportateurs || [];\n      return exportateurs.length > 0 ? exportateurs.map(exp => {\n        var _exp$attributes;\n        const raisonSociale = (exp === null || exp === void 0 ? void 0 : (_exp$attributes = exp.attributes) === null || _exp$attributes === void 0 ? void 0 : _exp$attributes.raison_sociale) || (exp === null || exp === void 0 ? void 0 : exp.raison_sociale) || 'Nom non disponible';\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          children: raisonSociale\n        }, exp.id || exp.documentId || Math.random(), false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 15\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Aucun exportateur\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 11\n      }, this);\n    })()]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n}\n_s(OpportuniteForm, \"qZ8iRlquh9hBjsElIySvB3aUV40=\");\n_c = OpportuniteForm;\nexport default OpportuniteForm;\nvar _c;\n$RefreshReg$(_c, \"OpportuniteForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "OpportuniteForm", "_s", "_opportunite$data", "opportunite", "setOpportunite", "loading", "setLoading", "fetch", "then", "res", "json", "data", "console", "log", "Array", "isArray", "length", "warn", "catch", "err", "error", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "attr", "attributes", "objet", "pays_destination", "date_debut", "date_fin", "secteur", "_attr$importateur", "_attr$importateur$dat", "_attr$importateur2", "importateur", "societe", "_attr$exportateurs", "exportateurs", "map", "exp", "_exp$attributes", "raisonSociale", "raison_sociale", "id", "documentId", "Math", "random", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/dfghj/site/frontend/src/components/OpportuniteForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\n\r\nfunction OpportuniteForm() {\r\n  const [opportunite, setOpportunite] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    fetch(\"http://localhost:1337/api/opportunites?populate=*\")\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        console.log(\"Opportunites API response:\", data);\r\n        console.log(\"First opportunite structure:\", data.data && data.data[0]);\r\n        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {\r\n          setOpportunite(data.data[0]); // ناخذ أول opportunite\r\n        } else {\r\n          console.warn(\"No opportunites found or unexpected response structure:\", data);\r\n          setOpportunite(null);\r\n        }\r\n        setLoading(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Fetch error:\", err);\r\n        setOpportunite(null);\r\n        setLoading(false);\r\n      });\r\n  }, []);\r\n\r\n  if (loading) return <div>Loading...</div>;\r\n  if (!opportunite) return <div>Aucune opportunité trouvée.</div>;\r\n\r\n  console.log(\"Processing opportunite:\", opportunite);\r\n\r\n  // Try different possible data structures\r\n  const attr = opportunite.attributes || opportunite.data?.attributes || opportunite;\r\n\r\n  // Additional safety check\r\n  if (!attr) return <div>Erreur: données d'opportunité non disponibles.</div>;\r\n\r\n  return (\r\n    <div>\r\n      <h2>Objet: {attr.objet}</h2>\r\n      <p>Pays de destination: {attr.pays_destination}</p>\r\n      <p>Date début: {attr.date_debut}</p>\r\n      <p>Date fin: {attr.date_fin}</p>\r\n      <p>Secteur: {attr.secteur}</p>\r\n\r\n      <h3>Importateur</h3>\r\n      {(() => {\r\n        const importateur = attr.importateur?.data?.attributes ||\r\n                           attr.importateur?.attributes ||\r\n                           attr.importateur;\r\n        return importateur?.societe ? (\r\n          <p>Societe: {importateur.societe}</p>\r\n        ) : (\r\n          <p>Aucun importateur</p>\r\n        );\r\n      })()}\r\n\r\n      <h3>Exportateurs</h3>\r\n      {(() => {\r\n        const exportateurs = attr.exportateurs?.data || attr.exportateurs || [];\r\n        return exportateurs.length > 0 ? (\r\n          exportateurs.map((exp) => {\r\n            const raisonSociale = exp?.attributes?.raison_sociale ||\r\n                                 exp?.raison_sociale ||\r\n                                 'Nom non disponible';\r\n            return (\r\n              <div key={exp.id || exp.documentId || Math.random()}>\r\n                {raisonSociale}\r\n              </div>\r\n            );\r\n          })\r\n        ) : (\r\n          <p>Aucun exportateur</p>\r\n        );\r\n      })()}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default OpportuniteForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,iBAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdU,KAAK,CAAC,mDAAmD,CAAC,CACvDC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MACdC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,IAAI,CAAC;MAC/CC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC;MACtE,IAAIA,IAAI,IAAIA,IAAI,CAACA,IAAI,IAAIG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,IAAIA,IAAI,CAACA,IAAI,CAACK,MAAM,GAAG,CAAC,EAAE;QACzEZ,cAAc,CAACO,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC,CAAC,MAAM;QACLC,OAAO,CAACK,IAAI,CAAC,yDAAyD,EAAEN,IAAI,CAAC;QAC7EP,cAAc,CAAC,IAAI,CAAC;MACtB;MACAE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDY,KAAK,CAAEC,GAAG,IAAK;MACdP,OAAO,CAACQ,KAAK,CAAC,cAAc,EAAED,GAAG,CAAC;MAClCf,cAAc,CAAC,IAAI,CAAC;MACpBE,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAEN,IAAID,OAAO,EAAE,oBAAON,OAAA;IAAAsB,QAAA,EAAK;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACzC,IAAI,CAACtB,WAAW,EAAE,oBAAOJ,OAAA;IAAAsB,QAAA,EAAK;EAA2B;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAE/Db,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEV,WAAW,CAAC;;EAEnD;EACA,MAAMuB,IAAI,GAAGvB,WAAW,CAACwB,UAAU,MAAAzB,iBAAA,GAAIC,WAAW,CAACQ,IAAI,cAAAT,iBAAA,uBAAhBA,iBAAA,CAAkByB,UAAU,KAAIxB,WAAW;;EAElF;EACA,IAAI,CAACuB,IAAI,EAAE,oBAAO3B,OAAA;IAAAsB,QAAA,EAAK;EAA8C;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAE3E,oBACE1B,OAAA;IAAAsB,QAAA,gBACEtB,OAAA;MAAAsB,QAAA,GAAI,SAAO,EAACK,IAAI,CAACE,KAAK;IAAA;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC5B1B,OAAA;MAAAsB,QAAA,GAAG,uBAAqB,EAACK,IAAI,CAACG,gBAAgB;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnD1B,OAAA;MAAAsB,QAAA,GAAG,iBAAY,EAACK,IAAI,CAACI,UAAU;IAAA;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpC1B,OAAA;MAAAsB,QAAA,GAAG,YAAU,EAACK,IAAI,CAACK,QAAQ;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChC1B,OAAA;MAAAsB,QAAA,GAAG,WAAS,EAACK,IAAI,CAACM,OAAO;IAAA;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE9B1B,OAAA;MAAAsB,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACnB,CAAC,CAAAQ,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,KAAM;MACN,MAAMC,WAAW,GAAG,EAAAH,iBAAA,GAAAP,IAAI,CAACU,WAAW,cAAAH,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBtB,IAAI,cAAAuB,qBAAA,uBAAtBA,qBAAA,CAAwBP,UAAU,OAAAQ,kBAAA,GACnCT,IAAI,CAACU,WAAW,cAAAD,kBAAA,uBAAhBA,kBAAA,CAAkBR,UAAU,KAC5BD,IAAI,CAACU,WAAW;MACnC,OAAOA,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEC,OAAO,gBACzBtC,OAAA;QAAAsB,QAAA,GAAG,WAAS,EAACe,WAAW,CAACC,OAAO;MAAA;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,gBAErC1B,OAAA;QAAAsB,QAAA,EAAG;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACxB;IACH,CAAC,EAAE,CAAC,eAEJ1B,OAAA;MAAAsB,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACpB,CAACa,kBAAA,IAAM;MACN,MAAMC,YAAY,GAAG,EAAAD,kBAAA,GAAAZ,IAAI,CAACa,YAAY,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmB3B,IAAI,KAAIe,IAAI,CAACa,YAAY,IAAI,EAAE;MACvE,OAAOA,YAAY,CAACvB,MAAM,GAAG,CAAC,GAC5BuB,YAAY,CAACC,GAAG,CAAEC,GAAG,IAAK;QAAA,IAAAC,eAAA;QACxB,MAAMC,aAAa,GAAG,CAAAF,GAAG,aAAHA,GAAG,wBAAAC,eAAA,GAAHD,GAAG,CAAEd,UAAU,cAAAe,eAAA,uBAAfA,eAAA,CAAiBE,cAAc,MAChCH,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEG,cAAc,KACnB,oBAAoB;QACzC,oBACE7C,OAAA;UAAAsB,QAAA,EACGsB;QAAa,GADNF,GAAG,CAACI,EAAE,IAAIJ,GAAG,CAACK,UAAU,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE9C,CAAC;MAEV,CAAC,CAAC,gBAEF1B,OAAA;QAAAsB,QAAA,EAAG;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CACxB;IACH,CAAC,EAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV;AAACxB,EAAA,CA5EQD,eAAe;AAAAiD,EAAA,GAAfjD,eAAe;AA8ExB,eAAeA,eAAe;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}