import{r as l,cH as I,cI as Te}from"./strapi-z7ApxZZq.js";function Rn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return l.useMemo(()=>r=>{t.forEach(o=>o(r))},t)}const nt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";function we(e){const t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function bt(e){return"nodeType"in e}function z(e){var t,n;return e?we(e)?e:bt(e)&&(t=(n=e.ownerDocument)==null?void 0:n.defaultView)!=null?t:window:window}function mt(e){const{Document:t}=z(e);return e instanceof t}function Be(e){return we(e)?!1:e instanceof z(e).HTMLElement}function Ut(e){return e instanceof z(e).SVGElement}function xe(e){return e?we(e)?e.document:bt(e)?mt(e)?e:Be(e)||Ut(e)?e.ownerDocument:document:document:document}const V=nt?l.useLayoutEffect:l.useEffect;function rt(e){const t=l.useRef(e);return V(()=>{t.current=e}),l.useCallback(function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return t.current==null?void 0:t.current(...r)},[])}function En(){const e=l.useRef(null),t=l.useCallback((r,o)=>{e.current=setInterval(r,o)},[]),n=l.useCallback(()=>{e.current!==null&&(clearInterval(e.current),e.current=null)},[]);return[t,n]}function Pe(e,t){t===void 0&&(t=[e]);const n=l.useRef(e);return V(()=>{n.current!==e&&(n.current=e)},t),n}function Fe(e,t){const n=l.useRef();return l.useMemo(()=>{const r=e(n.current);return n.current=r,r},[...t])}function Je(e){const t=rt(e),n=l.useRef(null),r=l.useCallback(o=>{o!==n.current&&t?.(o,n.current),n.current=o},[]);return[n,r]}function _e(e){const t=l.useRef();return l.useEffect(()=>{t.current=e},[e]),t.current}let ut={};function $e(e,t){return l.useMemo(()=>{if(t)return t;const n=ut[e]==null?0:ut[e]+1;return ut[e]=n,e+"-"+n},[e,t])}function Wt(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce((i,s)=>{const a=Object.entries(s);for(const[c,u]of a){const d=i[c];d!=null&&(i[c]=d+e*u)}return i},{...t})}}const ye=Wt(1),Qe=Wt(-1);function An(e){return"clientX"in e&&"clientY"in e}function ot(e){if(!e)return!1;const{KeyboardEvent:t}=z(e.target);return t&&e instanceof t}function Mn(e){if(!e)return!1;const{TouchEvent:t}=z(e.target);return t&&e instanceof t}function Ze(e){if(Mn(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return An(e)?{x:e.clientX,y:e.clientY}:null}const fe=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[fe.Translate.toString(e),fe.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),Tt="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function On(e){return e.matches(Tt)?e:e.querySelector(Tt)}const In={display:"none"};function Tn(e){let{id:t,value:n}=e;return I.createElement("div",{id:t,style:In},n)}function Nn(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;const o={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return I.createElement("div",{id:t,style:o,role:"status","aria-live":r,"aria-atomic":!0},n)}function Ln(){const[e,t]=l.useState("");return{announce:l.useCallback(r=>{r!=null&&t(r)},[]),announcement:e}}const Ht=l.createContext(null);function kn(e){const t=l.useContext(Ht);l.useEffect(()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}function Pn(){const[e]=l.useState(()=>new Set),t=l.useCallback(r=>(e.add(r),()=>e.delete(r)),[e]);return[l.useCallback(r=>{let{type:o,event:i}=r;e.forEach(s=>{var a;return(a=s[o])==null?void 0:a.call(s,i)})},[e]),t]}const zn={draggable:`
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  `},Bn={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function Fn(e){let{announcements:t=Bn,container:n,hiddenTextDescribedById:r,screenReaderInstructions:o=zn}=e;const{announce:i,announcement:s}=Ln(),a=$e("DndLiveRegion"),[c,u]=l.useState(!1);if(l.useEffect(()=>{u(!0)},[]),kn(l.useMemo(()=>({onDragStart(f){let{active:h}=f;i(t.onDragStart({active:h}))},onDragMove(f){let{active:h,over:g}=f;t.onDragMove&&i(t.onDragMove({active:h,over:g}))},onDragOver(f){let{active:h,over:g}=f;i(t.onDragOver({active:h,over:g}))},onDragEnd(f){let{active:h,over:g}=f;i(t.onDragEnd({active:h,over:g}))},onDragCancel(f){let{active:h,over:g}=f;i(t.onDragCancel({active:h,over:g}))}}),[i,t])),!c)return null;const d=I.createElement(I.Fragment,null,I.createElement(Tn,{id:r,value:o.draggable}),I.createElement(Nn,{id:a,announcement:s}));return n?Te.createPortal(d,n):d}var N;(function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"})(N||(N={}));function et(){}function ho(e,t){return l.useMemo(()=>({sensor:e,options:{}}),[e,t])}function vo(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return l.useMemo(()=>[...t].filter(r=>r!=null),[...t])}const q=Object.freeze({x:0,y:0});function $n(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Xn(e,t){const n=Ze(e);if(!n)return"0 0";const r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}function jn(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function Yn(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function Kn(e,t){if(!e||e.length===0)return null;const[n]=e;return n[t]}function Nt(e,t,n){return t===void 0&&(t=e.left),n===void 0&&(n=e.top),{x:t+e.width*.5,y:n+e.height*.5}}const po=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=Nt(t,t.left,t.top),i=[];for(const s of r){const{id:a}=s,c=n.get(a);if(c){const u=$n(Nt(c),o);i.push({id:a,data:{droppableContainer:s,value:u}})}}return i.sort(jn)};function Un(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),s=o-r,a=i-n;if(r<o&&n<i){const c=t.width*t.height,u=e.width*e.height,d=s*a,f=d/(c+u-d);return Number(f.toFixed(4))}return 0}const Wn=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const i of r){const{id:s}=i,a=n.get(s);if(a){const c=Un(a,t);c>0&&o.push({id:s,data:{droppableContainer:i,value:c}})}}return o.sort(Yn)};function Hn(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}function Vt(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:q}function Vn(e){return function(n){for(var r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return o.reduce((s,a)=>({...s,top:s.top+e*a.y,bottom:s.bottom+e*a.y,left:s.left+e*a.x,right:s.right+e*a.x}),{...n})}}const qn=Vn(1);function qt(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function Gn(e,t,n){const r=qt(t);if(!r)return e;const{scaleX:o,scaleY:i,x:s,y:a}=r,c=e.left-s-(1-o)*parseFloat(n),u=e.top-a-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),d=o?e.width/o:e.width,f=i?e.height/i:e.height;return{width:d,height:f,top:u,right:c+d,bottom:u+f,left:c}}const Jn={ignoreTransform:!1};function De(e,t){t===void 0&&(t=Jn);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:u,transformOrigin:d}=z(e).getComputedStyle(e);u&&(n=Gn(n,u,d))}const{top:r,left:o,width:i,height:s,bottom:a,right:c}=n;return{top:r,left:o,width:i,height:s,bottom:a,right:c}}function Lt(e){return De(e,{ignoreTransform:!0})}function _n(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}function Qn(e,t){return t===void 0&&(t=z(e).getComputedStyle(e)),t.position==="fixed"}function Zn(e,t){t===void 0&&(t=z(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(o=>{const i=t[o];return typeof i=="string"?n.test(i):!1})}function yt(e,t){const n=[];function r(o){if(t!=null&&n.length>=t||!o)return n;if(mt(o)&&o.scrollingElement!=null&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!Be(o)||Ut(o)||n.includes(o))return n;const i=z(e).getComputedStyle(o);return o!==e&&Zn(o,i)&&n.push(o),Qn(o,i)?n:r(o.parentNode)}return e?r(e):n}function Gt(e){const[t]=yt(e,1);return t??null}function dt(e){return!nt||!e?null:we(e)?e:bt(e)?mt(e)||e===xe(e).scrollingElement?window:Be(e)?e:null:null}function Jt(e){return we(e)?e.scrollX:e.scrollLeft}function _t(e){return we(e)?e.scrollY:e.scrollTop}function ht(e){return{x:Jt(e),y:_t(e)}}var L;(function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"})(L||(L={}));function Qt(e){return!nt||!e?!1:e===document.scrollingElement}function Zt(e){const t={x:0,y:0},n=Qt(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},o=e.scrollTop<=t.y,i=e.scrollLeft<=t.x,s=e.scrollTop>=r.y,a=e.scrollLeft>=r.x;return{isTop:o,isLeft:i,isBottom:s,isRight:a,maxScroll:r,minScroll:t}}const er={x:.2,y:.2};function tr(e,t,n,r,o){let{top:i,left:s,right:a,bottom:c}=n;r===void 0&&(r=10),o===void 0&&(o=er);const{isTop:u,isBottom:d,isLeft:f,isRight:h}=Zt(e),g={x:0,y:0},D={x:0,y:0},v={height:t.height*o.y,width:t.width*o.x};return!u&&i<=t.top+v.height?(g.y=L.Backward,D.y=r*Math.abs((t.top+v.height-i)/v.height)):!d&&c>=t.bottom-v.height&&(g.y=L.Forward,D.y=r*Math.abs((t.bottom-v.height-c)/v.height)),!h&&a>=t.right-v.width?(g.x=L.Forward,D.x=r*Math.abs((t.right-v.width-a)/v.width)):!f&&s<=t.left+v.width&&(g.x=L.Backward,D.x=r*Math.abs((t.left+v.width-s)/v.width)),{direction:g,speed:D}}function nr(e){if(e===document.scrollingElement){const{innerWidth:i,innerHeight:s}=window;return{top:0,left:0,right:i,bottom:s,width:i,height:s}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function en(e){return e.reduce((t,n)=>ye(t,ht(n)),q)}function rr(e){return e.reduce((t,n)=>t+Jt(n),0)}function or(e){return e.reduce((t,n)=>t+_t(n),0)}function tn(e,t){if(t===void 0&&(t=De),!e)return;const{top:n,left:r,bottom:o,right:i}=t(e);Gt(e)&&(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}const ir=[["x",["left","right"],rr],["y",["top","bottom"],or]];class wt{constructor(t,n){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const r=yt(n),o=en(r);this.rect={...t},this.width=t.width,this.height=t.height;for(const[i,s,a]of ir)for(const c of s)Object.defineProperty(this,c,{get:()=>{const u=a(r),d=o[i]-u;return this.rect[c]+d},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class Ne{constructor(t){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(n=>{var r;return(r=this.target)==null?void 0:r.removeEventListener(...n)})},this.target=t}add(t,n,r){var o;(o=this.target)==null||o.addEventListener(t,n,r),this.listeners.push([t,n,r])}}function sr(e){const{EventTarget:t}=z(e);return e instanceof t?e:xe(e)}function ft(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return typeof t=="number"?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t?r>t.y:!1}var U;(function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"})(U||(U={}));function kt(e){e.preventDefault()}function ar(e){e.stopPropagation()}var R;(function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"})(R||(R={}));const nn={start:[R.Space,R.Enter],cancel:[R.Esc],end:[R.Space,R.Enter,R.Tab]},lr=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case R.Right:return{...n,x:n.x+25};case R.Left:return{...n,x:n.x-25};case R.Down:return{...n,y:n.y+25};case R.Up:return{...n,y:n.y-25}}};class rn{constructor(t){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=t;const{event:{target:n}}=t;this.props=t,this.listeners=new Ne(xe(n)),this.windowListeners=new Ne(z(n)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(U.Resize,this.handleCancel),this.windowListeners.add(U.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(U.Keydown,this.handleKeyDown))}handleStart(){const{activeNode:t,onStart:n}=this.props,r=t.node.current;r&&tn(r),n(q)}handleKeyDown(t){if(ot(t)){const{active:n,context:r,options:o}=this.props,{keyboardCodes:i=nn,coordinateGetter:s=lr,scrollBehavior:a="smooth"}=o,{code:c}=t;if(i.end.includes(c)){this.handleEnd(t);return}if(i.cancel.includes(c)){this.handleCancel(t);return}const{collisionRect:u}=r.current,d=u?{x:u.left,y:u.top}:q;this.referenceCoordinates||(this.referenceCoordinates=d);const f=s(t,{active:n,context:r.current,currentCoordinates:d});if(f){const h=Qe(f,d),g={x:0,y:0},{scrollableAncestors:D}=r.current;for(const v of D){const p=t.code,{isTop:b,isRight:y,isLeft:m,isBottom:C,maxScroll:S,minScroll:E}=Zt(v),w=nr(v),x={x:Math.min(p===R.Right?w.right-w.width/2:w.right,Math.max(p===R.Right?w.left:w.left+w.width/2,f.x)),y:Math.min(p===R.Down?w.bottom-w.height/2:w.bottom,Math.max(p===R.Down?w.top:w.top+w.height/2,f.y))},O=p===R.Right&&!y||p===R.Left&&!m,T=p===R.Down&&!C||p===R.Up&&!b;if(O&&x.x!==f.x){const M=v.scrollLeft+h.x,W=p===R.Right&&M<=S.x||p===R.Left&&M>=E.x;if(W&&!h.y){v.scrollTo({left:M,behavior:a});return}W?g.x=v.scrollLeft-M:g.x=p===R.Right?v.scrollLeft-S.x:v.scrollLeft-E.x,g.x&&v.scrollBy({left:-g.x,behavior:a});break}else if(T&&x.y!==f.y){const M=v.scrollTop+h.y,W=p===R.Down&&M<=S.y||p===R.Up&&M>=E.y;if(W&&!h.x){v.scrollTo({top:M,behavior:a});return}W?g.y=v.scrollTop-M:g.y=p===R.Down?v.scrollTop-S.y:v.scrollTop-E.y,g.y&&v.scrollBy({top:-g.y,behavior:a});break}}this.handleMove(t,ye(Qe(f,this.referenceCoordinates),g))}}}handleMove(t,n){const{onMove:r}=this.props;t.preventDefault(),r(n)}handleEnd(t){const{onEnd:n}=this.props;t.preventDefault(),this.detach(),n()}handleCancel(t){const{onCancel:n}=this.props;t.preventDefault(),this.detach(),n()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}rn.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=nn,onActivation:o}=t,{active:i}=n;const{code:s}=e.nativeEvent;if(r.start.includes(s)){const a=i.activatorNode.current;return a&&e.target!==a?!1:(e.preventDefault(),o?.({event:e.nativeEvent}),!0)}return!1}}];function Pt(e){return!!(e&&"distance"in e)}function zt(e){return!!(e&&"delay"in e)}class xt{constructor(t,n,r){var o;r===void 0&&(r=sr(t.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=t,this.events=n;const{event:i}=t,{target:s}=i;this.props=t,this.events=n,this.document=xe(s),this.documentListeners=new Ne(this.document),this.listeners=new Ne(r),this.windowListeners=new Ne(z(s)),this.initialCoordinates=(o=Ze(i))!=null?o:q,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:t,props:{options:{activationConstraint:n,bypassActivationConstraint:r}}}=this;if(this.listeners.add(t.move.name,this.handleMove,{passive:!1}),this.listeners.add(t.end.name,this.handleEnd),t.cancel&&this.listeners.add(t.cancel.name,this.handleCancel),this.windowListeners.add(U.Resize,this.handleCancel),this.windowListeners.add(U.DragStart,kt),this.windowListeners.add(U.VisibilityChange,this.handleCancel),this.windowListeners.add(U.ContextMenu,kt),this.documentListeners.add(U.Keydown,this.handleKeydown),n){if(r!=null&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(zt(n)){this.timeoutId=setTimeout(this.handleStart,n.delay),this.handlePending(n);return}if(Pt(n)){this.handlePending(n);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),this.timeoutId!==null&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(t,n){const{active:r,onPending:o}=this.props;o(r,t,this.initialCoordinates,n)}handleStart(){const{initialCoordinates:t}=this,{onStart:n}=this.props;t&&(this.activated=!0,this.documentListeners.add(U.Click,ar,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(U.SelectionChange,this.removeTextSelection),n(t))}handleMove(t){var n;const{activated:r,initialCoordinates:o,props:i}=this,{onMove:s,options:{activationConstraint:a}}=i;if(!o)return;const c=(n=Ze(t))!=null?n:q,u=Qe(o,c);if(!r&&a){if(Pt(a)){if(a.tolerance!=null&&ft(u,a.tolerance))return this.handleCancel();if(ft(u,a.distance))return this.handleStart()}if(zt(a)&&ft(u,a.tolerance))return this.handleCancel();this.handlePending(a,u);return}t.cancelable&&t.preventDefault(),s(c)}handleEnd(){const{onAbort:t,onEnd:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleCancel(){const{onAbort:t,onCancel:n}=this.props;this.detach(),this.activated||t(this.props.active),n()}handleKeydown(t){t.code===R.Esc&&this.handleCancel()}removeTextSelection(){var t;(t=this.document.getSelection())==null||t.removeAllRanges()}}const cr={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class on extends xt{constructor(t){const{event:n}=t,r=xe(n.target);super(t,cr,r)}}on.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!n.isPrimary||n.button!==0?!1:(r?.({event:n}),!0)}}];const ur={move:{name:"mousemove"},end:{name:"mouseup"}};var vt;(function(e){e[e.RightClick=2]="RightClick"})(vt||(vt={}));class dr extends xt{constructor(t){super(t,ur,xe(t.event.target))}}dr.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button===vt.RightClick?!1:(r?.({event:n}),!0)}}];const gt={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class fr extends xt{constructor(t){super(t,gt)}static setup(){return window.addEventListener(gt.move.name,t,{capture:!1,passive:!1}),function(){window.removeEventListener(gt.move.name,t)};function t(){}}}fr.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return o.length>1?!1:(r?.({event:n}),!0)}}];var Le;(function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"})(Le||(Le={}));var tt;(function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"})(tt||(tt={}));function gr(e){let{acceleration:t,activator:n=Le.Pointer,canScroll:r,draggingRect:o,enabled:i,interval:s=5,order:a=tt.TreeOrder,pointerCoordinates:c,scrollableAncestors:u,scrollableAncestorRects:d,delta:f,threshold:h}=e;const g=vr({delta:f,disabled:!i}),[D,v]=En(),p=l.useRef({x:0,y:0}),b=l.useRef({x:0,y:0}),y=l.useMemo(()=>{switch(n){case Le.Pointer:return c?{top:c.y,bottom:c.y,left:c.x,right:c.x}:null;case Le.DraggableRect:return o}},[n,o,c]),m=l.useRef(null),C=l.useCallback(()=>{const E=m.current;if(!E)return;const w=p.current.x*b.current.x,x=p.current.y*b.current.y;E.scrollBy(w,x)},[]),S=l.useMemo(()=>a===tt.TreeOrder?[...u].reverse():u,[a,u]);l.useEffect(()=>{if(!i||!u.length||!y){v();return}for(const E of S){if(r?.(E)===!1)continue;const w=u.indexOf(E),x=d[w];if(!x)continue;const{direction:O,speed:T}=tr(E,x,y,t,h);for(const M of["x","y"])g[M][O[M]]||(T[M]=0,O[M]=0);if(T.x>0||T.y>0){v(),m.current=E,D(C,s),p.current=T,b.current=O;return}}p.current={x:0,y:0},b.current={x:0,y:0},v()},[t,C,r,v,i,s,JSON.stringify(y),JSON.stringify(g),D,u,S,d,JSON.stringify(h)])}const hr={x:{[L.Backward]:!1,[L.Forward]:!1},y:{[L.Backward]:!1,[L.Forward]:!1}};function vr(e){let{delta:t,disabled:n}=e;const r=_e(t);return Fe(o=>{if(n||!r||!o)return hr;const i={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[L.Backward]:o.x[L.Backward]||i.x===-1,[L.Forward]:o.x[L.Forward]||i.x===1},y:{[L.Backward]:o.y[L.Backward]||i.y===-1,[L.Forward]:o.y[L.Forward]||i.y===1}}},[n,t,r])}function pr(e,t){const n=t!=null?e.get(t):void 0,r=n?n.node.current:null;return Fe(o=>{var i;return t==null?null:(i=r??o)!=null?i:null},[r,t])}function br(e,t){return l.useMemo(()=>e.reduce((n,r)=>{const{sensor:o}=r,i=o.activators.map(s=>({eventName:s.eventName,handler:t(s.handler,r)}));return[...n,...i]},[]),[e,t])}var ze;(function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"})(ze||(ze={}));var pt;(function(e){e.Optimized="optimized"})(pt||(pt={}));const Bt=new Map;function mr(e,t){let{dragging:n,dependencies:r,config:o}=t;const[i,s]=l.useState(null),{frequency:a,measure:c,strategy:u}=o,d=l.useRef(e),f=p(),h=Pe(f),g=l.useCallback(function(b){b===void 0&&(b=[]),!h.current&&s(y=>y===null?b:y.concat(b.filter(m=>!y.includes(m))))},[h]),D=l.useRef(null),v=Fe(b=>{if(f&&!n)return Bt;if(!b||b===Bt||d.current!==e||i!=null){const y=new Map;for(let m of e){if(!m)continue;if(i&&i.length>0&&!i.includes(m.id)&&m.rect.current){y.set(m.id,m.rect.current);continue}const C=m.node.current,S=C?new wt(c(C),C):null;m.rect.current=S,S&&y.set(m.id,S)}return y}return b},[e,i,n,f,c]);return l.useEffect(()=>{d.current=e},[e]),l.useEffect(()=>{f||g()},[n,f]),l.useEffect(()=>{i&&i.length>0&&s(null)},[JSON.stringify(i)]),l.useEffect(()=>{f||typeof a!="number"||D.current!==null||(D.current=setTimeout(()=>{g(),D.current=null},a))},[a,f,g,...r]),{droppableRects:v,measureDroppableContainers:g,measuringScheduled:i!=null};function p(){switch(u){case ze.Always:return!1;case ze.BeforeDragging:return n;default:return!n}}}function Dt(e,t){return Fe(n=>e?n||(typeof t=="function"?t(e):e):null,[t,e])}function yr(e,t){return Dt(e,t)}function wr(e){let{callback:t,disabled:n}=e;const r=rt(t),o=l.useMemo(()=>{if(n||typeof window>"u"||typeof window.MutationObserver>"u")return;const{MutationObserver:i}=window;return new i(r)},[r,n]);return l.useEffect(()=>()=>o?.disconnect(),[o]),o}function it(e){let{callback:t,disabled:n}=e;const r=rt(t),o=l.useMemo(()=>{if(n||typeof window>"u"||typeof window.ResizeObserver>"u")return;const{ResizeObserver:i}=window;return new i(r)},[n]);return l.useEffect(()=>()=>o?.disconnect(),[o]),o}function xr(e){return new wt(De(e),e)}function Ft(e,t,n){t===void 0&&(t=xr);const[r,o]=l.useState(null);function i(){o(c=>{if(!e)return null;if(e.isConnected===!1){var u;return(u=c??n)!=null?u:null}const d=t(e);return JSON.stringify(c)===JSON.stringify(d)?c:d})}const s=wr({callback(c){if(e)for(const u of c){const{type:d,target:f}=u;if(d==="childList"&&f instanceof HTMLElement&&f.contains(e)){i();break}}}}),a=it({callback:i});return V(()=>{i(),e?(a?.observe(e),s?.observe(document.body,{childList:!0,subtree:!0})):(a?.disconnect(),s?.disconnect())},[e]),r}function Dr(e){const t=Dt(e);return Vt(e,t)}const $t=[];function Cr(e){const t=l.useRef(e),n=Fe(r=>e?r&&r!==$t&&e&&t.current&&e.parentNode===t.current.parentNode?r:yt(e):$t,[e]);return l.useEffect(()=>{t.current=e},[e]),n}function Sr(e){const[t,n]=l.useState(null),r=l.useRef(e),o=l.useCallback(i=>{const s=dt(i.target);s&&n(a=>a?(a.set(s,ht(s)),new Map(a)):null)},[]);return l.useEffect(()=>{const i=r.current;if(e!==i){s(i);const a=e.map(c=>{const u=dt(c);return u?(u.addEventListener("scroll",o,{passive:!0}),[u,ht(u)]):null}).filter(c=>c!=null);n(a.length?new Map(a):null),r.current=e}return()=>{s(e),s(i)};function s(a){a.forEach(c=>{const u=dt(c);u?.removeEventListener("scroll",o)})}},[o,e]),l.useMemo(()=>e.length?t?Array.from(t.values()).reduce((i,s)=>ye(i,s),q):en(e):q,[e,t])}function Xt(e,t){t===void 0&&(t=[]);const n=l.useRef(null);return l.useEffect(()=>{n.current=null},t),l.useEffect(()=>{const r=e!==q;r&&!n.current&&(n.current=e),!r&&n.current&&(n.current=null)},[e]),n.current?Qe(e,n.current):q}function Rr(e){l.useEffect(()=>{if(!nt)return;const t=e.map(n=>{let{sensor:r}=n;return r.setup==null?void 0:r.setup()});return()=>{for(const n of t)n?.()}},e.map(t=>{let{sensor:n}=t;return n}))}function Er(e,t){return l.useMemo(()=>e.reduce((n,r)=>{let{eventName:o,handler:i}=r;return n[o]=s=>{i(s,t)},n},{}),[e,t])}function sn(e){return l.useMemo(()=>e?_n(e):null,[e])}const jt=[];function Ar(e,t){t===void 0&&(t=De);const[n]=e,r=sn(n?z(n):null),[o,i]=l.useState(jt);function s(){i(()=>e.length?e.map(c=>Qt(c)?r:new wt(t(c),c)):jt)}const a=it({callback:s});return V(()=>{a?.disconnect(),s(),e.forEach(c=>a?.observe(c))},[e]),o}function an(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return Be(t)?t:e}function Mr(e){let{measure:t}=e;const[n,r]=l.useState(null),o=l.useCallback(u=>{for(const{target:d}of u)if(Be(d)){r(f=>{const h=t(d);return f?{...f,width:h.width,height:h.height}:h});break}},[t]),i=it({callback:o}),s=l.useCallback(u=>{const d=an(u);i?.disconnect(),d&&i?.observe(d),r(d?t(d):null)},[t,i]),[a,c]=Je(s);return l.useMemo(()=>({nodeRef:a,rect:n,setRef:c}),[n,a,c])}const Or=[{sensor:on,options:{}},{sensor:rn,options:{}}],Ir={current:{}},Ge={draggable:{measure:Lt},droppable:{measure:Lt,strategy:ze.WhileDragging,frequency:pt.Optimized},dragOverlay:{measure:De}};class ke extends Map{get(t){var n;return t!=null&&(n=super.get(t))!=null?n:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(t=>{let{disabled:n}=t;return!n})}getNodeFor(t){var n,r;return(n=(r=this.get(t))==null?void 0:r.node.current)!=null?n:void 0}}const Tr={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new ke,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:et},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Ge,measureDroppableContainers:et,windowRect:null,measuringScheduled:!1},ln={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:et,draggableNodes:new Map,over:null,measureDroppableContainers:et},Xe=l.createContext(ln),cn=l.createContext(Tr);function Nr(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new ke}}}function Lr(e,t){switch(t.type){case N.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case N.DragMove:return e.draggable.active==null?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case N.DragEnd:case N.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case N.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new ke(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case N.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const s=new ke(e.droppable.containers);return s.set(n,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:s}}}case N.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const i=new ke(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function kr(e){let{disabled:t}=e;const{active:n,activatorEvent:r,draggableNodes:o}=l.useContext(Xe),i=_e(r),s=_e(n?.id);return l.useEffect(()=>{if(!t&&!r&&i&&s!=null){if(!ot(i)||document.activeElement===i.target)return;const a=o.get(s);if(!a)return;const{activatorNode:c,node:u}=a;if(!c.current&&!u.current)return;requestAnimationFrame(()=>{for(const d of[c.current,u.current]){if(!d)continue;const f=On(d);if(f){f.focus();break}}})}},[r,t,o,s,i]),null}function un(e,t){let{transform:n,...r}=t;return e!=null&&e.length?e.reduce((o,i)=>i({transform:o,...r}),n):n}function Pr(e){return l.useMemo(()=>({draggable:{...Ge.draggable,...e?.draggable},droppable:{...Ge.droppable,...e?.droppable},dragOverlay:{...Ge.dragOverlay,...e?.dragOverlay}}),[e?.draggable,e?.droppable,e?.dragOverlay])}function zr(e){let{activeNode:t,measure:n,initialRect:r,config:o=!0}=e;const i=l.useRef(!1),{x:s,y:a}=typeof o=="boolean"?{x:o,y:o}:o;V(()=>{if(!s&&!a||!t){i.current=!1;return}if(i.current||!r)return;const u=t?.node.current;if(!u||u.isConnected===!1)return;const d=n(u),f=Vt(d,r);if(s||(f.x=0),a||(f.y=0),i.current=!0,Math.abs(f.x)>0||Math.abs(f.y)>0){const h=Gt(u);h&&h.scrollBy({top:f.y,left:f.x})}},[t,s,a,r,n])}const st=l.createContext({...q,scaleX:1,scaleY:1});var ue;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"})(ue||(ue={}));const bo=l.memo(function(t){var n,r,o,i;let{id:s,accessibility:a,autoScroll:c=!0,children:u,sensors:d=Or,collisionDetection:f=Wn,measuring:h,modifiers:g,...D}=t;const v=l.useReducer(Lr,void 0,Nr),[p,b]=v,[y,m]=Pn(),[C,S]=l.useState(ue.Uninitialized),E=C===ue.Initialized,{draggable:{active:w,nodes:x,translate:O},droppable:{containers:T}}=p,M=w!=null?x.get(w):null,W=l.useRef({initial:null,translated:null}),H=l.useMemo(()=>{var P;return w!=null?{id:w,data:(P=M?.data)!=null?P:Ir,rect:W}:null},[w,M]),G=l.useRef(null),[Ce,je]=l.useState(null),[F,Ye]=l.useState(null),Z=Pe(D,Object.values(D)),Se=$e("DndDescribedBy",s),Ke=l.useMemo(()=>T.getEnabled(),[T]),B=Pr(h),{droppableRects:ee,measureDroppableContainers:de,measuringScheduled:Re}=mr(Ke,{dragging:E,dependencies:[O.x,O.y],config:B.droppable}),Y=pr(x,w),Ue=l.useMemo(()=>F?Ze(F):null,[F]),oe=Sn(),te=yr(Y,B.draggable.measure);zr({activeNode:w!=null?x.get(w):null,config:oe.layoutShiftCompensation,initialRect:te,measure:B.draggable.measure});const A=Ft(Y,B.draggable.measure,te),Ee=Ft(Y?Y.parentElement:null),J=l.useRef({activatorEvent:null,active:null,activeNode:Y,collisionRect:null,collisions:null,droppableRects:ee,draggableNodes:x,draggingNode:null,draggingNodeRect:null,droppableContainers:T,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ge=T.getNodeFor((n=J.current.over)==null?void 0:n.id),ne=Mr({measure:B.dragOverlay.measure}),he=(r=ne.nodeRef.current)!=null?r:Y,ve=E?(o=ne.rect)!=null?o:A:null,Ct=!!(ne.nodeRef.current&&ne.rect),St=Dr(Ct?null:A),at=sn(he?z(he):null),ie=Cr(E?ge??Y:null),We=Ar(ie),He=un(g,{transform:{x:O.x-St.x,y:O.y-St.y,scaleX:1,scaleY:1},activatorEvent:F,active:H,activeNodeRect:A,containerNodeRect:Ee,draggingNodeRect:ve,over:J.current.over,overlayNodeRect:ne.rect,scrollableAncestors:ie,scrollableAncestorRects:We,windowRect:at}),Rt=Ue?ye(Ue,O):null,Et=Sr(ie),bn=Xt(Et),mn=Xt(Et,[A]),pe=ye(He,bn),be=ve?qn(ve,He):null,Ae=H&&be?f({active:H,collisionRect:be,droppableRects:ee,droppableContainers:Ke,pointerCoordinates:Rt}):null,At=Kn(Ae,"id"),[se,Mt]=l.useState(null),yn=Ct?He:ye(He,mn),wn=Hn(yn,(i=se?.rect)!=null?i:null,A),lt=l.useRef(null),Ot=l.useCallback((P,$)=>{let{sensor:X,options:ae}=$;if(G.current==null)return;const K=x.get(G.current);if(!K)return;const j=P.nativeEvent,_=new X({active:G.current,activeNode:K,event:j,options:ae,context:J,onAbort(k){if(!x.get(k))return;const{onDragAbort:Q}=Z.current,re={id:k};Q?.(re),y({type:"onDragAbort",event:re})},onPending(k,le,Q,re){if(!x.get(k))return;const{onDragPending:Oe}=Z.current,ce={id:k,constraint:le,initialCoordinates:Q,offset:re};Oe?.(ce),y({type:"onDragPending",event:ce})},onStart(k){const le=G.current;if(le==null)return;const Q=x.get(le);if(!Q)return;const{onDragStart:re}=Z.current,Me={activatorEvent:j,active:{id:le,data:Q.data,rect:W}};Te.unstable_batchedUpdates(()=>{re?.(Me),S(ue.Initializing),b({type:N.DragStart,initialCoordinates:k,active:le}),y({type:"onDragStart",event:Me}),je(lt.current),Ye(j)})},onMove(k){b({type:N.DragMove,coordinates:k})},onEnd:me(N.DragEnd),onCancel:me(N.DragCancel)});lt.current=_;function me(k){return async function(){const{active:Q,collisions:re,over:Me,scrollAdjustedTranslate:Oe}=J.current;let ce=null;if(Q&&Oe){const{cancelDrop:Ie}=Z.current;ce={activatorEvent:j,active:Q,collisions:re,delta:Oe,over:Me},k===N.DragEnd&&typeof Ie=="function"&&await Promise.resolve(Ie(ce))&&(k=N.DragCancel)}G.current=null,Te.unstable_batchedUpdates(()=>{b({type:k}),S(ue.Uninitialized),Mt(null),je(null),Ye(null),lt.current=null;const Ie=k===N.DragEnd?"onDragEnd":"onDragCancel";if(ce){const ct=Z.current[Ie];ct?.(ce),y({type:Ie,event:ce})}})}}},[x]),xn=l.useCallback((P,$)=>(X,ae)=>{const K=X.nativeEvent,j=x.get(ae);if(G.current!==null||!j||K.dndKit||K.defaultPrevented)return;const _={active:j};P(X,$.options,_)===!0&&(K.dndKit={capturedBy:$.sensor},G.current=ae,Ot(X,$))},[x,Ot]),It=br(d,xn);Rr(d),V(()=>{A&&C===ue.Initializing&&S(ue.Initialized)},[A,C]),l.useEffect(()=>{const{onDragMove:P}=Z.current,{active:$,activatorEvent:X,collisions:ae,over:K}=J.current;if(!$||!X)return;const j={active:$,activatorEvent:X,collisions:ae,delta:{x:pe.x,y:pe.y},over:K};Te.unstable_batchedUpdates(()=>{P?.(j),y({type:"onDragMove",event:j})})},[pe.x,pe.y]),l.useEffect(()=>{const{active:P,activatorEvent:$,collisions:X,droppableContainers:ae,scrollAdjustedTranslate:K}=J.current;if(!P||G.current==null||!$||!K)return;const{onDragOver:j}=Z.current,_=ae.get(At),me=_&&_.rect.current?{id:_.id,rect:_.rect.current,data:_.data,disabled:_.disabled}:null,k={active:P,activatorEvent:$,collisions:X,delta:{x:K.x,y:K.y},over:me};Te.unstable_batchedUpdates(()=>{Mt(me),j?.(k),y({type:"onDragOver",event:k})})},[At]),V(()=>{J.current={activatorEvent:F,active:H,activeNode:Y,collisionRect:be,collisions:Ae,droppableRects:ee,draggableNodes:x,draggingNode:he,draggingNodeRect:ve,droppableContainers:T,over:se,scrollableAncestors:ie,scrollAdjustedTranslate:pe},W.current={initial:ve,translated:be}},[H,Y,Ae,be,x,he,ve,ee,T,se,ie,pe]),gr({...oe,delta:O,draggingRect:be,pointerCoordinates:Rt,scrollableAncestors:ie,scrollableAncestorRects:We});const Dn=l.useMemo(()=>({active:H,activeNode:Y,activeNodeRect:A,activatorEvent:F,collisions:Ae,containerNodeRect:Ee,dragOverlay:ne,draggableNodes:x,droppableContainers:T,droppableRects:ee,over:se,measureDroppableContainers:de,scrollableAncestors:ie,scrollableAncestorRects:We,measuringConfiguration:B,measuringScheduled:Re,windowRect:at}),[H,Y,A,F,Ae,Ee,ne,x,T,ee,se,de,ie,We,B,Re,at]),Cn=l.useMemo(()=>({activatorEvent:F,activators:It,active:H,activeNodeRect:A,ariaDescribedById:{draggable:Se},dispatch:b,draggableNodes:x,over:se,measureDroppableContainers:de}),[F,It,H,A,b,Se,x,se,de]);return I.createElement(Ht.Provider,{value:m},I.createElement(Xe.Provider,{value:Cn},I.createElement(cn.Provider,{value:Dn},I.createElement(st.Provider,{value:wn},u)),I.createElement(kr,{disabled:a?.restoreFocus===!1})),I.createElement(Fn,{...a,hiddenTextDescribedById:Se}));function Sn(){const P=Ce?.autoScrollEnabled===!1,$=typeof c=="object"?c.enabled===!1:c===!1,X=E&&!P&&!$;return typeof c=="object"?{...c,enabled:X}:{enabled:X}}}),Br=l.createContext(null),Yt="button",Fr="Draggable";function $r(e){let{id:t,data:n,disabled:r=!1,attributes:o}=e;const i=$e(Fr),{activators:s,activatorEvent:a,active:c,activeNodeRect:u,ariaDescribedById:d,draggableNodes:f,over:h}=l.useContext(Xe),{role:g=Yt,roleDescription:D="draggable",tabIndex:v=0}=o??{},p=c?.id===t,b=l.useContext(p?st:Br),[y,m]=Je(),[C,S]=Je(),E=Er(s,t),w=Pe(n);V(()=>(f.set(t,{id:t,key:i,node:y,activatorNode:C,data:w}),()=>{const O=f.get(t);O&&O.key===i&&f.delete(t)}),[f,t]);const x=l.useMemo(()=>({role:g,tabIndex:v,"aria-disabled":r,"aria-pressed":p&&g===Yt?!0:void 0,"aria-roledescription":D,"aria-describedby":d.draggable}),[r,g,v,p,D,d.draggable]);return{active:c,activatorEvent:a,activeNodeRect:u,attributes:x,isDragging:p,listeners:r?void 0:E,node:y,over:h,setNodeRef:m,setActivatorNodeRef:S,transform:b}}function dn(){return l.useContext(cn)}const Xr="Droppable",jr={timeout:25};function Yr(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:o}=e;const i=$e(Xr),{active:s,dispatch:a,over:c,measureDroppableContainers:u}=l.useContext(Xe),d=l.useRef({disabled:n}),f=l.useRef(!1),h=l.useRef(null),g=l.useRef(null),{disabled:D,updateMeasurementsFor:v,timeout:p}={...jr,...o},b=Pe(v??r),y=l.useCallback(()=>{if(!f.current){f.current=!0;return}g.current!=null&&clearTimeout(g.current),g.current=setTimeout(()=>{u(Array.isArray(b.current)?b.current:[b.current]),g.current=null},p)},[p]),m=it({callback:y,disabled:D||!s}),C=l.useCallback((x,O)=>{m&&(O&&(m.unobserve(O),f.current=!1),x&&m.observe(x))},[m]),[S,E]=Je(C),w=Pe(t);return l.useEffect(()=>{!m||!S.current||(m.disconnect(),f.current=!1,m.observe(S.current))},[S,m]),l.useEffect(()=>(a({type:N.RegisterDroppable,element:{id:r,key:i,disabled:n,node:S,rect:h,data:w}}),()=>a({type:N.UnregisterDroppable,key:i,id:r})),[r]),l.useEffect(()=>{n!==d.current.disabled&&(a({type:N.SetDroppableDisabled,id:r,key:i,disabled:n}),d.current.disabled=n)},[r,i,n,a]),{active:s,rect:h,isOver:c?.id===r,node:S,over:c,setNodeRef:E}}function Kr(e){let{animation:t,children:n}=e;const[r,o]=l.useState(null),[i,s]=l.useState(null),a=_e(n);return!n&&!r&&a&&o(a),V(()=>{if(!i)return;const c=r?.key,u=r?.props.id;if(c==null||u==null){o(null);return}Promise.resolve(t(u,i)).then(()=>{o(null)})},[t,r,i]),I.createElement(I.Fragment,null,n,r?l.cloneElement(r,{ref:s}):null)}const Ur={x:0,y:0,scaleX:1,scaleY:1};function Wr(e){let{children:t}=e;return I.createElement(Xe.Provider,{value:ln},I.createElement(st.Provider,{value:Ur},t))}const Hr={position:"fixed",touchAction:"none"},Vr=e=>ot(e)?"transform 250ms ease":void 0,qr=l.forwardRef((e,t)=>{let{as:n,activatorEvent:r,adjustScale:o,children:i,className:s,rect:a,style:c,transform:u,transition:d=Vr}=e;if(!a)return null;const f=o?u:{...u,scaleX:1,scaleY:1},h={...Hr,width:a.width,height:a.height,top:a.top,left:a.left,transform:fe.Transform.toString(f),transformOrigin:o&&r?Xn(r,a):void 0,transition:typeof d=="function"?d(r):d,...c};return I.createElement(n,{className:s,style:h,ref:t},i)}),Gr=e=>t=>{let{active:n,dragOverlay:r}=t;const o={},{styles:i,className:s}=e;if(i!=null&&i.active)for(const[a,c]of Object.entries(i.active))c!==void 0&&(o[a]=n.node.style.getPropertyValue(a),n.node.style.setProperty(a,c));if(i!=null&&i.dragOverlay)for(const[a,c]of Object.entries(i.dragOverlay))c!==void 0&&r.node.style.setProperty(a,c);return s!=null&&s.active&&n.node.classList.add(s.active),s!=null&&s.dragOverlay&&r.node.classList.add(s.dragOverlay),function(){for(const[c,u]of Object.entries(o))n.node.style.setProperty(c,u);s!=null&&s.active&&n.node.classList.remove(s.active)}},Jr=e=>{let{transform:{initial:t,final:n}}=e;return[{transform:fe.Transform.toString(t)},{transform:fe.Transform.toString(n)}]},_r={duration:250,easing:"ease",keyframes:Jr,sideEffects:Gr({styles:{active:{opacity:"0"}}})};function Qr(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:o}=e;return rt((i,s)=>{if(t===null)return;const a=n.get(i);if(!a)return;const c=a.node.current;if(!c)return;const u=an(s);if(!u)return;const{transform:d}=z(s).getComputedStyle(s),f=qt(d);if(!f)return;const h=typeof t=="function"?t:Zr(t);return tn(c,o.draggable.measure),h({active:{id:i,data:a.data,node:c,rect:o.draggable.measure(c)},draggableNodes:n,dragOverlay:{node:s,rect:o.dragOverlay.measure(u)},droppableContainers:r,measuringConfiguration:o,transform:f})})}function Zr(e){const{duration:t,easing:n,sideEffects:r,keyframes:o}={..._r,...e};return i=>{let{active:s,dragOverlay:a,transform:c,...u}=i;if(!t)return;const d={x:a.rect.left-s.rect.left,y:a.rect.top-s.rect.top},f={scaleX:c.scaleX!==1?s.rect.width*c.scaleX/a.rect.width:1,scaleY:c.scaleY!==1?s.rect.height*c.scaleY/a.rect.height:1},h={x:c.x-d.x,y:c.y-d.y,...f},g=o({...u,active:s,dragOverlay:a,transform:{initial:c,final:h}}),[D]=g,v=g[g.length-1];if(JSON.stringify(D)===JSON.stringify(v))return;const p=r?.({active:s,dragOverlay:a,...u}),b=a.node.animate(g,{duration:t,easing:n,fill:"forwards"});return new Promise(y=>{b.onfinish=()=>{p?.(),y()}})}}let Kt=0;function eo(e){return l.useMemo(()=>{if(e!=null)return Kt++,Kt},[e])}const mo=I.memo(e=>{let{adjustScale:t=!1,children:n,dropAnimation:r,style:o,transition:i,modifiers:s,wrapperElement:a="div",className:c,zIndex:u=999}=e;const{activatorEvent:d,active:f,activeNodeRect:h,containerNodeRect:g,draggableNodes:D,droppableContainers:v,dragOverlay:p,over:b,measuringConfiguration:y,scrollableAncestors:m,scrollableAncestorRects:C,windowRect:S}=dn(),E=l.useContext(st),w=eo(f?.id),x=un(s,{activatorEvent:d,active:f,activeNodeRect:h,containerNodeRect:g,draggingNodeRect:p.rect,over:b,overlayNodeRect:p.rect,scrollableAncestors:m,scrollableAncestorRects:C,transform:E,windowRect:S}),O=Dt(h),T=Qr({config:r,draggableNodes:D,droppableContainers:v,measuringConfiguration:y}),M=O?p.setRef:void 0;return I.createElement(Wr,null,I.createElement(Kr,{animation:T},f&&w?I.createElement(qr,{key:w,id:f.id,ref:M,as:a,activatorEvent:d,adjustScale:t,className:c,transition:i,rect:O,style:{zIndex:u,...o},transform:x},n):null))});function fn(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function to(e,t){return e.reduce((n,r,o)=>{const i=t.get(r);return i&&(n[o]=i),n},Array(e.length))}function Ve(e){return e!==null&&e>=0}function no(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function ro(e){return typeof e=="boolean"?{draggable:e,droppable:e}:e}const gn=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const i=fn(t,r,n),s=t[o],a=i[o];return!a||!s?null:{x:a.left-s.left,y:a.top-s.top,scaleX:a.width/s.width,scaleY:a.height/s.height}},qe={scaleX:1,scaleY:1},yo=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:i,overIndex:s}=e;const a=(t=i[n])!=null?t:r;if(!a)return null;if(o===n){const u=i[s];return u?{x:0,y:n<s?u.top+u.height-(a.top+a.height):u.top-a.top,...qe}:null}const c=oo(i,o,n);return o>n&&o<=s?{x:0,y:-a.height-c,...qe}:o<n&&o>=s?{x:0,y:a.height+c,...qe}:{x:0,y:0,...qe}};function oo(e,t,n){const r=e[t],o=e[t-1],i=e[t+1];return r?n<t?o?r.top-(o.top+o.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):o?r.top-(o.top+o.height):0:0}const hn="Sortable",vn=I.createContext({activeIndex:-1,containerId:hn,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:gn,disabled:{draggable:!1,droppable:!1}});function wo(e){let{children:t,id:n,items:r,strategy:o=gn,disabled:i=!1}=e;const{active:s,dragOverlay:a,droppableRects:c,over:u,measureDroppableContainers:d}=dn(),f=$e(hn,n),h=a.rect!==null,g=l.useMemo(()=>r.map(E=>typeof E=="object"&&"id"in E?E.id:E),[r]),D=s!=null,v=s?g.indexOf(s.id):-1,p=u?g.indexOf(u.id):-1,b=l.useRef(g),y=!no(g,b.current),m=p!==-1&&v===-1||y,C=ro(i);V(()=>{y&&D&&d(g)},[y,g,D,d]),l.useEffect(()=>{b.current=g},[g]);const S=l.useMemo(()=>({activeIndex:v,containerId:f,disabled:C,disableTransforms:m,items:g,overIndex:p,useDragOverlay:h,sortedRects:to(g,c),strategy:o}),[v,f,C.draggable,C.droppable,m,g,p,c,h,o]);return I.createElement(vn.Provider,{value:S},t)}const io=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return fn(n,r,o).indexOf(t)},so=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:i,newIndex:s,previousItems:a,previousContainerId:c,transition:u}=e;return!u||!r||a!==i&&o===s?!1:n?!0:s!==o&&t===c},ao={duration:200,easing:"ease"},pn="transform",lo=fe.Transition.toString({property:pn,duration:0,easing:"linear"}),co={roleDescription:"sortable"};function uo(e){let{disabled:t,index:n,node:r,rect:o}=e;const[i,s]=l.useState(null),a=l.useRef(n);return V(()=>{if(!t&&n!==a.current&&r.current){const c=o.current;if(c){const u=De(r.current,{ignoreTransform:!0}),d={x:c.left-u.left,y:c.top-u.top,scaleX:c.width/u.width,scaleY:c.height/u.height};(d.x||d.y)&&s(d)}}n!==a.current&&(a.current=n)},[t,n,r,o]),l.useEffect(()=>{i&&s(null)},[i]),i}function xo(e){let{animateLayoutChanges:t=so,attributes:n,disabled:r,data:o,getNewIndex:i=io,id:s,strategy:a,resizeObserverConfig:c,transition:u=ao}=e;const{items:d,containerId:f,activeIndex:h,disabled:g,disableTransforms:D,sortedRects:v,overIndex:p,useDragOverlay:b,strategy:y}=l.useContext(vn),m=fo(r,g),C=d.indexOf(s),S=l.useMemo(()=>({sortable:{containerId:f,index:C,items:d},...o}),[f,o,C,d]),E=l.useMemo(()=>d.slice(d.indexOf(s)),[d,s]),{rect:w,node:x,isOver:O,setNodeRef:T}=Yr({id:s,data:S,disabled:m.droppable,resizeObserverConfig:{updateMeasurementsFor:E,...c}}),{active:M,activatorEvent:W,activeNodeRect:H,attributes:G,setNodeRef:Ce,listeners:je,isDragging:F,over:Ye,setActivatorNodeRef:Z,transform:Se}=$r({id:s,data:S,attributes:{...co,...n},disabled:m.draggable}),Ke=Rn(T,Ce),B=!!M,ee=B&&!D&&Ve(h)&&Ve(p),de=!b&&F,Re=de&&ee?Se:null,Ue=ee?Re??(a??y)({rects:v,activeNodeRect:H,activeIndex:h,overIndex:p,index:C}):null,oe=Ve(h)&&Ve(p)?i({id:s,items:d,activeIndex:h,overIndex:p}):C,te=M?.id,A=l.useRef({activeId:te,items:d,newIndex:oe,containerId:f}),Ee=d!==A.current.items,J=t({active:M,containerId:f,isDragging:F,isSorting:B,id:s,index:C,items:d,newIndex:A.current.newIndex,previousItems:A.current.items,previousContainerId:A.current.containerId,transition:u,wasDragging:A.current.activeId!=null}),ge=uo({disabled:!J,index:C,node:x,rect:w});return l.useEffect(()=>{B&&A.current.newIndex!==oe&&(A.current.newIndex=oe),f!==A.current.containerId&&(A.current.containerId=f),d!==A.current.items&&(A.current.items=d)},[B,oe,f,d]),l.useEffect(()=>{if(te===A.current.activeId)return;if(te!=null&&A.current.activeId==null){A.current.activeId=te;return}const he=setTimeout(()=>{A.current.activeId=te},50);return()=>clearTimeout(he)},[te]),{active:M,activeIndex:h,attributes:G,data:S,rect:w,index:C,newIndex:oe,items:d,isOver:O,isSorting:B,isDragging:F,listeners:je,node:x,overIndex:p,over:Ye,setNodeRef:Ke,setActivatorNodeRef:Z,setDroppableNodeRef:T,setDraggableNodeRef:Ce,transform:ge??Ue,transition:ne()};function ne(){if(ge||Ee&&A.current.newIndex===C)return lo;if(!(de&&!ot(W)||!u)&&(B||J))return fe.Transition.toString({...u,property:pn})}}function fo(e,t){var n,r;return typeof e=="boolean"?{draggable:e,droppable:!1}:{draggable:(n=e?.draggable)!=null?n:t.draggable,droppable:(r=e?.droppable)!=null?r:t.droppable}}R.Down,R.Right,R.Up,R.Left;export{fe as C,bo as D,on as P,wo as S,mo as a,fn as b,xo as c,vo as d,ho as e,po as f,Yr as u,yo as v};
