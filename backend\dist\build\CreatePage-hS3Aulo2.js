import{aL as T,j as e,P as p,b9 as A,w as F,a as k,V as D,r as B,b as N,v as V,ba as q,bb as H,bc as Q,bd as _,aE as G,be as O,aP as U,bf as z,B as b,bg as J,e as u,D as y,E as W,$ as l,T as R,H as g,M as d,aJ as K,bh as X,aQ as P,aR as M,aT as E,k as Y}from"./strapi-z7ApxZZq.js";import{P as Z}from"./Permissions-B6GrFVsT.js";import{f as ee}from"./index-C3HeomYJ.js";import"./groupBy--H6Arigv.js";import"./_baseEach-ZnnftuGj.js";import"./index-BRVyLNfZ.js";const se=U().shape({name:P().required(M.required.id),description:P().required(M.required.id)}),re=()=>{const{id:s}=A(),{toggleNotification:o}=F(),{formatMessage:r}=k(),S=D(),m=B.useRef(null),{trackUsage:h}=N(),{_unstableFormatAPIError:f,_unstableFormatValidationErrors:x}=V(),{isLoading:w,currentData:j}=q({role:s??""}),{currentData:C,isLoading:v}=H({id:s},{skip:!s,refetchOnMountOrArgChange:!0}),[$]=Q(),[I]=_(),L=async(c,t)=>{try{h(s?"willDuplicateRole":"willCreateNewRole");const a=await $(c);if("error"in a){E(a.error)&&a.error.name==="ValidationError"?t.setErrors(x(a.error)):o({type:"danger",message:f(a.error)});return}const{permissionsToSend:n}=m.current?.getPermissions()??{};if(a.data.id&&Array.isArray(n)&&n.length>0){const i=await I({id:a.data.id,permissions:n});if("error"in i){E(i.error)&&i.error.name==="ValidationError"?t.setErrors(x(i.error)):o({type:"danger",message:f(i.error)});return}}o({type:"success",message:r({id:"Settings.roles.created",defaultMessage:"created"})}),S(`../roles/${a.data.id.toString()}`,{replace:!0})}catch{o({type:"danger",message:r({id:"notification.error",defaultMessage:"An error occurred"})})}};return w&&v||!j?e.jsx(p.Loading,{}):e.jsxs(G,{children:[e.jsx(p.Title,{children:r({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Roles"})}),e.jsx(O,{initialValues:{name:"",description:`${r({id:"Settings.roles.form.created",defaultMessage:"Created"})} ${ee(new Date,"PPP")}`},onSubmit:L,validationSchema:se,validateOnChange:!1,children:({values:c,errors:t,handleReset:a,handleChange:n,isSubmitting:i})=>e.jsx(z,{children:e.jsxs(e.Fragment,{children:[e.jsx(b.Header,{primaryAction:e.jsxs(u,{gap:2,children:[e.jsx(y,{variant:"secondary",onClick:()=>{a(),m.current?.resetForm()},children:r({id:"app.components.Button.reset",defaultMessage:"Reset"})}),e.jsx(y,{type:"submit",loading:i,startIcon:e.jsx(W,{}),children:r({id:"global.save",defaultMessage:"Save"})})]}),title:r({id:"Settings.roles.create.title",defaultMessage:"Create a role"}),subtitle:r({id:"Settings.roles.create.description",defaultMessage:"Define the rights given to the role"}),navigationAction:e.jsx(J,{fallback:"../roles"})}),e.jsx(b.Content,{children:e.jsxs(u,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsx(l,{background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:e.jsxs(u,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsxs(u,{justifyContent:"space-between",children:[e.jsxs(l,{children:[e.jsx(l,{children:e.jsx(R,{fontWeight:"bold",children:r({id:"global.details",defaultMessage:"Details"})})}),e.jsx(l,{children:e.jsx(R,{variant:"pi",textColor:"neutral600",children:r({id:"Settings.roles.form.description",defaultMessage:"Name and description of the role"})})})]}),e.jsx(ae,{children:r({id:"Settings.roles.form.button.users-with-role",defaultMessage:"{number, plural, =0 {# users} one {# user} other {# users}} with this role"},{number:0})})]}),e.jsxs(g.Root,{gap:4,children:[e.jsx(g.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(d.Root,{name:"name",error:t.name&&r({id:t.name}),required:!0,children:[e.jsx(d.Label,{children:r({id:"global.name",defaultMessage:"Name"})}),e.jsx(K,{onChange:n,value:c.name}),e.jsx(d.Error,{})]})}),e.jsx(g.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(d.Root,{name:"description",error:t.description&&r({id:t.description}),children:[e.jsx(d.Label,{children:r({id:"global.description",defaultMessage:"Description"})}),e.jsx(X,{onChange:n,value:c.description})]})})]})]})}),e.jsx(l,{shadow:"filterShadow",hasRadius:!0,children:e.jsx(Z,{isFormDisabled:!1,ref:m,permissions:C,layout:j})})]})})]})})})]})},ae=Y.div`
  border: 1px solid ${({theme:s})=>s.colors.primary200};
  background: ${({theme:s})=>s.colors.primary100};
  padding: ${({theme:s})=>`${s.spaces[2]} ${s.spaces[4]}`};
  color: ${({theme:s})=>s.colors.primary600};
  border-radius: ${({theme:s})=>s.borderRadius};
  font-size: 1.2rem;
  font-weight: bold;
`,ce=()=>{const s=T(o=>o.admin_app.permissions.settings?.roles.create);return e.jsx(p.Protect,{permissions:s,children:e.jsx(re,{})})};export{re as CreatePage,ce as ProtectedCreatePage};
