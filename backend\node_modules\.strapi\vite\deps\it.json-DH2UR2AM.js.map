{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/it.json.mjs"], "sourcesContent": ["var Analytics = \"Analytics\";\nvar Documentation = \"Documentazione\";\nvar Email = \"Email\";\nvar Password = \"Password\";\nvar Provider = \"Provider\";\nvar ResetPasswordToken = \"Reimposta Token Password\";\nvar Role = \"Ruolo\";\nvar Username = \"Nome utente\";\nvar Users = \"Utenti\";\nvar it = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Il tuo account è stato sospeso\",\n    \"Auth.form.button.forgot-password\": \"Invia email\",\n    \"Auth.form.button.go-home\": \"TORNA ALLA HOME\",\n    \"Auth.form.button.login\": \"Accedi\",\n    \"Auth.form.button.register\": \"Inizia adesso\",\n    \"Auth.form.confirmPassword.label\": \"Conferma Password\",\n    \"Auth.form.email.label\": \"Email\",\n    \"Auth.form.email.placeholder\": \"<EMAIL>\",\n    \"Auth.form.error.blocked\": \"Il tuo account è stato bloccato dall'amministratore.\",\n    \"Auth.form.error.code.provide\": \"Codice fornito non corretto.\",\n    \"Auth.form.error.confirmed\": \"L'email del tuo account non è stata confermata.\",\n    \"Auth.form.error.email.invalid\": \"Questa email non è valida.\",\n    \"Auth.form.error.email.provide\": \"Per favore inserisci il tuo nome utente o la tua email.\",\n    \"Auth.form.error.email.taken\": \"Email già utilizzata.\",\n    \"Auth.form.error.invalid\": \"Identificatore o password non valida.\",\n    \"Auth.form.error.params.provide\": \"I parametri forniti non sono corretti.\",\n    \"Auth.form.error.password.format\": \"La tua password non può contenere il simbolo `$` per più di tre volte.\",\n    \"Auth.form.error.password.local\": \"Questo utente non ha mai impostato una password locale, accedi gentilmente tramite il provider usato durante la creazione dell'account\",\n    \"Auth.form.error.password.matching\": \"La password non corrisponde.\",\n    \"Auth.form.error.password.provide\": \"Per favore fornisci la tua password\",\n    \"Auth.form.error.ratelimit\": \"Troppi tentativi, riprova tra un minuto.\",\n    \"Auth.form.error.user.not-exist\": \"Questa email non esiste.\",\n    \"Auth.form.error.username.taken\": \"Nome utente già utilizzato.\",\n    \"Auth.form.firstname.label\": \"Nome\",\n    \"Auth.form.firstname.placeholder\": \"Kai\",\n    \"Auth.form.forgot-password.email.label\": \"Inserisci la tua email\",\n    \"Auth.form.forgot-password.email.label.success\": \"Email inviata correttamente\",\n    \"Auth.form.lastname.label\": \"Cognome\",\n    \"Auth.form.lastname.placeholder\": \"Doe\",\n    \"Auth.form.register.news.label\": \"Tienimi aggiornato in merito a nuove funzionalità e futuri sviluppi (così facendo accetti {terms} e {policy}).\",\n    \"Auth.form.rememberMe.label\": \"Ricordami\",\n    \"Auth.form.username.label\": \"Nome utente\",\n    \"Auth.form.username.placeholder\": \"Kai Doe\",\n    \"Auth.link.forgot-password\": \"Password dimenticata?\",\n    \"Auth.link.ready\": \"Sei pronto per accedere?\",\n    \"Auth.link.signin\": \"Accedi\",\n    \"Auth.link.signin.account\": \"Hai già un account?\",\n    \"Auth.privacy-policy-agreement.policy\": \"privacy policy\",\n    \"Auth.privacy-policy-agreement.terms\": \"termini\",\n    \"Content Manager\": \"Gestione Contenuti\",\n    \"Content Type Builder\": \"Content-Types Builder\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Caricamento Files\",\n    \"HomePage.head.title\": \"Homepage\",\n    \"HomePage.roadmap\": \"Guarda la nostra roadmap\",\n    \"HomePage.welcome.congrats\": \"Congratulazioni!\",\n    \"HomePage.welcome.congrats.content\": \"Ti sei loggato come primo amministratore. Per scoprire le funzionalità di Strapi,\",\n    \"HomePage.welcome.congrats.content.bold\": \"ti consigliamo di creare la tua prima Collezione.\",\n    \"Media Library\": \"Libreria media\",\n    \"New entry\": \"Nuovo elemento\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Ruoli e permessi\",\n    \"Roles.ListPage.notification.delete-all-not-allowed\": \"Alcuni ruoli non possono essere eleminati poiché sono associati agli utenti\",\n    \"Roles.ListPage.notification.delete-not-allowed\": \"Un ruolo non può essere eliminato se associato ad utenti\",\n    \"Roles.components.List.empty.withSearch\": \"Nessun ruolo corrisponde alla ricerca ({search})...\",\n    \"Settings.PageTitle\": \"Impostazioni - {name}\",\n    \"Settings.application.description\": \"Vedi i dettagli del tuo progetto\",\n    \"Settings.application.edition-title\": \"Edizione attuale\",\n    \"Settings.application.link-pricing\": \"Vedi tutti i prezzi\",\n    \"Settings.application.link-upgrade\": \"Aggiorna il tuo progetto\",\n    \"Settings.application.node-version\": \"VERSIONE NODE\",\n    \"Settings.application.strapi-version\": \"VERSIONE STRAPI\",\n    \"Settings.application.title\": \"Applicazione\",\n    \"Settings.error\": \"Errore\",\n    \"Settings.global\": \"Impostazioni Globali\",\n    \"Settings.permissions\": \"Pannello di amministazione\",\n    \"Settings.permissions.category\": \"Impostazioni permessi per la categoria {category}\",\n    \"Settings.permissions.category.plugins\": \"Permissions settings for the {category} plugin\",\n    \"Settings.permissions.conditions.anytime\": \"In ogni momento\",\n    \"Settings.permissions.conditions.apply\": \"Applica\",\n    \"Settings.permissions.conditions.can\": \"Può\",\n    \"Settings.permissions.conditions.conditions\": \"Definisci le condizioni\",\n    \"Settings.permissions.conditions.links\": \"Link\",\n    \"Settings.permissions.conditions.no-actions\": \"Non ci sono azioni\",\n    \"Settings.permissions.conditions.or\": \"Oppure\",\n    \"Settings.permissions.conditions.when\": \"Quando\",\n    \"Settings.permissions.users.create\": \"Crea nuovo utente\",\n    \"Settings.permissions.users.email\": \"Email\",\n    \"Settings.permissions.users.firstname\": \"Nome\",\n    \"Settings.permissions.users.lastname\": \"Cognome\",\n    \"Settings.roles.create.description\": \"Definisci permessi del ruolo\",\n    \"Settings.roles.create.title\": \"Crea ruolo\",\n    \"Settings.roles.created\": \"Ruolo creato\",\n    \"Settings.roles.edit.title\": \"Modifica ruolo\",\n    \"Settings.roles.form.button.users-with-role\": \"Utenti con questo ruolo\",\n    \"Settings.roles.form.created\": \"Creato\",\n    \"Settings.roles.form.description\": \"Nome e descrizione ruolo\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Permessi per i campi\",\n    \"Settings.roles.form.permissions.create\": \"Crea\",\n    \"Settings.roles.form.permissions.delete\": \"Elimina\",\n    \"Settings.roles.form.permissions.publish\": \"Pubblica\",\n    \"Settings.roles.form.permissions.read\": \"Leggi\",\n    \"Settings.roles.form.permissions.update\": \"Aggiorna\",\n    \"Settings.roles.list.button.add\": \"Aggiungi nuovo ruolo\",\n    \"Settings.roles.list.description\": \"Lista dei ruoli\",\n    \"Settings.roles.title.singular\": \"Ruolo\",\n    \"Settings.webhooks.create\": \"Crea un webhook\",\n    \"Settings.webhooks.create.header\": \"Crea un nuovo header\",\n    \"Settings.webhooks.created\": \"Webhook creato\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Evento disponibile solo per contenuti con gestione stati Bozza/Pubblicazione abilitati\",\n    \"Settings.webhooks.events.create\": \"Crea\",\n    \"Settings.webhooks.events.update\": \"Aggiorna\",\n    \"Settings.webhooks.form.events\": \"Eventi\",\n    \"Settings.webhooks.form.headers\": \"Headers\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.key\": \"Chiave\",\n    \"Settings.webhooks.list.button.add\": \"Aggiungi nuovo webhook\",\n    \"Settings.webhooks.list.description\": \"Ricevi notifiche di cambiamenti in POST.\",\n    \"Settings.webhooks.list.empty.description\": \"Aggiungi il primo alla lista\",\n    \"Settings.webhooks.list.empty.link\": \"Leggi la documentazione\",\n    \"Settings.webhooks.list.empty.title\": \"Non ci sono webhooks\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhooks\",\n    \"Settings.webhooks.trigger\": \"Trigger\",\n    \"Settings.webhooks.trigger.cancel\": \"Annulla trigger\",\n    \"Settings.webhooks.trigger.pending\": \"In corso…\",\n    \"Settings.webhooks.trigger.save\": \"Salva trigger\",\n    \"Settings.webhooks.trigger.success\": \"Successo!\",\n    \"Settings.webhooks.trigger.success.label\": \"Trigger eseguito\",\n    \"Settings.webhooks.trigger.test\": \"Test trigger\",\n    \"Settings.webhooks.trigger.title\": \"Salva prima di eseguire trigger\",\n    \"Settings.webhooks.value\": \"Valore\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Utenti & Permessi\",\n    \"Users.components.List.empty\": \"Non ci sono utenti...\",\n    \"Users.components.List.empty.withFilters\": \"Nessun utente trovato con i filtri applicati...\",\n    \"Users.components.List.empty.withSearch\": \"Nessun utente corrisponde alla ricerca ({search})...\",\n    \"app.components.BlockLink.code\": \"Esempi di codice\",\n    \"app.components.Button.cancel\": \"Annulla\",\n    \"app.components.Button.reset\": \"Ripristina\",\n    \"app.components.ComingSoonPage.comingSoon\": \"In arrivo\",\n    \"app.components.DownloadInfo.download\": \"Download in corso...\",\n    \"app.components.DownloadInfo.text\": \"Potrebbe volerci un minuto. Grazie della pazienza.\",\n    \"app.components.EmptyAttributes.title\": \"Campi non ancora presenti.\",\n    \"app.components.HomePage.button.blog\": \"LEGGI DI PIÙ SUL BLOG\",\n    \"app.components.HomePage.community\": \"Trova la community sul web\",\n    \"app.components.HomePage.community.content\": \"Discuti con i membri del team, i contributori e gli sviluppatori tramite i nostri canali.\",\n    \"app.components.HomePage.create\": \"Crea il tuo primo Content-Type\",\n    \"app.components.HomePage.welcome\": \"Benvenuto a bordo!\",\n    \"app.components.HomePage.welcome.again\": \"Benvenuto \",\n    \"app.components.HomePage.welcomeBlock.content\": \"Siamo felici di averti come membro della comunità. Siamo costantemente alla ricerca di feedback, quindi sentitevi liberi di inviarci messaggi diretti su \",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Speriamo che tu stia facendo progressi sul tuo progetto ... Sentiti libero di leggere l'ultima novità riguardo Strapi. Stiamo dando il massimo per migliorare il prodotto in base al tuo feedback.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"problemi.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" o solleva \",\n    \"app.components.ImgPreview.hint\": \"Trascina il tuo file in quest'area o {browse} un file da caricare.\",\n    \"app.components.ImgPreview.hint.browse\": \"cerca\",\n    \"app.components.InputFile.newFile\": \"Aggiungi nuovo file\",\n    \"app.components.InputFileDetails.open\": \"Apri in una nuova tab\",\n    \"app.components.InputFileDetails.originalName\": \"Nome originale:\",\n    \"app.components.InputFileDetails.remove\": \"Rimuovi questo file\",\n    \"app.components.InputFileDetails.size\": \"Dimensione:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Il download e l'installazione del plugin potrebbero richiedere qualche secondo.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Scaricando...\",\n    \"app.components.InstallPluginPage.description\": \"Estendi la tua app senza sforzi.\",\n    \"app.components.LeftMenuFooter.help\": \"Supporto\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Offerto da \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Collezioni\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Configurazioni\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Generale\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Nessun plugin ancora installato\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Plugins\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Entità singole\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"L'installazione del plugin potrebbe richiedere qualche secondo.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Disinstalla\",\n    \"app.components.ListPluginsPage.description\": \"Lista dei plugin installati nel progetto.\",\n    \"app.components.ListPluginsPage.head.title\": \"Lista plugin\",\n    \"app.components.Logout.logout\": \"Disconnetti\",\n    \"app.components.Logout.profile\": \"Profilo\",\n    \"app.components.NotFoundPage.back\": \"Torna alla home\",\n    \"app.components.NotFoundPage.description\": \"Non trovato\",\n    \"app.components.Official\": \"Ufficiale\",\n    \"app.components.Onboarding.label.completed\": \"% completato\",\n    \"app.components.Onboarding.title\": \"Video di introduzione\",\n    \"app.components.PluginCard.Button.label.download\": \"Download\",\n    \"app.components.PluginCard.Button.label.install\": \"Già installato\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"La funzione autoReload necessità di essere abilitata. Per favore, avvia la app con il comando `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Ho capito!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Per ragioni di sicurezza, il plugin puo essere scaricato solo in ambiente di sviluppo.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Impossibile scaricare\",\n    \"app.components.PluginCard.compatible\": \"Compatibile con la tua app\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Compatibile con la comunità\",\n    \"app.components.PluginCard.more-details\": \"Più dettagli\",\n    \"app.components.Users.MagicLink.connect\": \"Invia link all'utente per connettersi.\",\n    \"app.components.Users.ModalCreateBody.block-title.details\": \"Dettagli\",\n    \"app.components.Users.ModalCreateBody.block-title.roles\": \"Ruoli utente\",\n    \"app.components.Users.SortPicker.button-label\": \"Ordina per\",\n    \"app.components.Users.SortPicker.sortby.email_asc\": \"Email (A - Z)\",\n    \"app.components.Users.SortPicker.sortby.email_desc\": \"Email (Z - A)\",\n    \"app.components.Users.SortPicker.sortby.firstname_asc\": \"Noma (A - Z)\",\n    \"app.components.Users.SortPicker.sortby.firstname_desc\": \"Nome (Z - A)\",\n    \"app.components.Users.SortPicker.sortby.lastname_asc\": \"Cognome (A - Z)\",\n    \"app.components.Users.SortPicker.sortby.lastname_desc\": \"Cognome (Z - A)\",\n    \"app.components.Users.SortPicker.sortby.username_asc\": \"Nome utente (A - Z)\",\n    \"app.components.Users.SortPicker.sortby.username_desc\": \"Nome utente (Z - A)\",\n    \"app.components.listPlugins.button\": \"Aggiungi nuovo plugin\",\n    \"app.components.listPlugins.title.none\": \"Nessun plugin installato\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Si è verificato un errore durante l'installazione del plugin\",\n    \"app.containers.App.notification.error.init\": \"Si è verificato un errore durante la richiesta dell'API\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Se non ricevi questo link, contatta l'amministratore.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"La ricezione del link per reimpostare la password potrebbere richiedere qualche secondo.\",\n    \"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email inviata\",\n    \"app.containers.Users.EditPage.form.active.label\": \"Attivo\",\n    \"app.containers.Users.EditPage.header.label\": \"Modifica {name}\",\n    \"app.containers.Users.EditPage.header.label-loading\": \"Modifica utente\",\n    \"app.containers.Users.EditPage.roles-bloc-title\": \"Ruoli assegnati\",\n    \"app.containers.Users.ModalForm.footer.button-success\": \"Crea utente\",\n    \"app.links.configure-view\": \"Configura la visualizzazione\",\n    \"app.static.links.cheatsheet\": \"CheatSheet\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Aggiungi filtro\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.errors.file-too-big.message\": \"Dimensioni file troppo grandi\",\n    \"app.utils.filters\": \"Filtri\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Pubblica\",\n    \"app.utils.select-all\": \"Seleziona tutti\",\n    \"app.utils.unpublish\": \"Converti in bozza\",\n    \"component.Input.error.validation.integer\": \"Il valore deve essere un intero\",\n    \"components.AutoReloadBlocker.description\": \"Avvia Strapi con uno dei seguenti comandi:\",\n    \"components.AutoReloadBlocker.header\": \"Ricarica funzionalità è richiesto per questo plugin.\",\n    \"components.ErrorBoundary.title\": \"Qualcosa è andato storto...\",\n    \"components.Input.error.attribute.key.taken\": \"Valore già esistente\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Non può essere uguale\",\n    \"components.Input.error.attribute.taken\": \"Nome campo già esistente\",\n    \"components.Input.error.contain.lowercase\": \"Password deve contenere almeno una carattere minuscolo\",\n    \"components.Input.error.contain.number\": \"Password deve contenere almeno un numero\",\n    \"components.Input.error.contain.uppercase\": \"Password deve contenere almeno una carattere maiuscolo\",\n    \"components.Input.error.contentTypeName.taken\": \"Nome già esistente\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"La password non corrisponde\",\n    \"components.Input.error.validation.email\": \"Non è un'email\",\n    \"components.Input.error.validation.json\": \"Formato JSON non corrisponde\",\n    \"components.Input.error.validation.max\": \"Valore troppo alto {max}.\",\n    \"components.Input.error.validation.maxLength\": \"Valore troppo lungo {max}.\",\n    \"components.Input.error.validation.min\": \"Valore troppo basso {min}.\",\n    \"components.Input.error.validation.minLength\": \"Valore troppo corto {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Non può essere superiore\",\n    \"components.Input.error.validation.regex\": \"Questo valore non coincide con la regex.\",\n    \"components.Input.error.validation.required\": \"Valore obbligatorio.\",\n    \"components.Input.error.validation.unique\": \"Questo valore è già usato\",\n    \"components.InputSelect.option.placeholder\": \"Seleziona\",\n    \"components.ListRow.empty\": \"Non ci sono dati da mostrare.\",\n    \"components.OverlayBlocker.description\": \"Stai utilizzando una funzionalità che necessita del riavvio del server. Per favore, attendi che il server ritorni attivo.\",\n    \"components.OverlayBlocker.description.serverError\": \"Il server deve essere riavviato, per favore controlla i tuoi log nel terminale.\",\n    \"components.OverlayBlocker.title\": \"Attendo il riavvio...\",\n    \"components.OverlayBlocker.title.serverError\": \"Il riavvio sta impiegando più del previsto\",\n    \"components.PageFooter.select\": \"elementi per pagina\",\n    \"components.ProductionBlocker.description\": \"Per ragioni di sicurezza dobbiamo disabilitare questo plugin in altri ambienti.\",\n    \"components.ProductionBlocker.header\": \"Questo plugin è disponibile solo in sviluppo.\",\n    \"components.Search.placeholder\": \"Cerca...\",\n    \"components.Wysiwyg.collapse\": \"Chiudi\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Titolo H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Titolo H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Titolo H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Titolo H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Titolo H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Titolo H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Aggiungi un titolo\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"caratteri\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Espandi\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Trascina & rilascia file, incolla dagli appunti o {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"selezionali\",\n    \"components.popUpWarning.button.cancel\": \"No, annulla\",\n    \"components.popUpWarning.button.confirm\": \"Sì, conferma\",\n    \"components.popUpWarning.message\": \"Sei sicuro di volerlo cancellare?\",\n    \"components.popUpWarning.title\": \"Conferma richiesta\",\n    \"form.button.done\": \"Fatto\",\n    \"global.prompt.unsaved\": \"Sei sicuro di voler lasciare questa pagina? Tutte le modifiche effettuate verranno perse.\",\n    \"notification.contentType.relations.conflict\": \"Questo Tipo di Contenuto ha delle relazioni in conflitto\",\n    \"notification.error\": \"Si è verificato un errore\",\n    \"notification.error.layout\": \"Non è stato possibile recuperare il layout\",\n    \"notification.form.error.fields\": \"Il form contiene degli errori\",\n    \"notification.form.success.fields\": \"Modifiche salvate\",\n    \"notification.link-copied\": \"Link copiato negli appunti\",\n    \"notification.permission.not-allowed-read\": \"Non sei abilitato a visualizzare questo documento\",\n    \"notification.success.delete\": \"L'elemento è stato eliminato\",\n    \"notification.success.saved\": \"Salvato\",\n    \"notification.version.update.message\": \"Una nuova versione di Strapi è disponibile!\",\n    \"request.error.model.unknown\": \"Modello inesistente\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, it as default };\n//# sourceMappingURL=it.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,qBAAqB;AAAA,EACrB,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,+CAA+C;AAAA,EAC/C,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,+BAA+B;AACnC;", "names": []}