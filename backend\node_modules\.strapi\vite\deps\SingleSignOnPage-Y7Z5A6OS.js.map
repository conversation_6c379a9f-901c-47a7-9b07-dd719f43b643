{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/SingleSignOnPage.tsx"], "sourcesContent": ["import {\n  Button,\n  Flex,\n  Grid,\n  MultiSelect,\n  MultiSelectOption,\n  Typography,\n  Field,\n} from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport * as yup from 'yup';\n\nimport {\n  Form,\n  FormHelpers,\n  InputProps,\n  useField,\n} from '../../../../../../admin/src/components/Form';\nimport { InputRenderer } from '../../../../../../admin/src/components/FormInputs/Renderer';\nimport { GradientBadge } from '../../../../../../admin/src/components/GradientBadge';\nimport { Layouts } from '../../../../../../admin/src/components/Layouts/Layout';\nimport { Page } from '../../../../../../admin/src/components/PageHelpers';\nimport { useTypedSelector } from '../../../../../../admin/src/core/store/hooks';\nimport { useNotification } from '../../../../../../admin/src/features/Notifications';\nimport { useAdminRoles } from '../../../../../../admin/src/hooks/useAdminRoles';\nimport { useAPIErrorHandler } from '../../../../../../admin/src/hooks/useAPIErrorHandler';\nimport { useRBAC } from '../../../../../../admin/src/hooks/useRBAC';\nimport {\n  useGetProviderOptionsQuery,\n  useUpdateProviderOptionsMutation,\n} from '../../../../../../admin/src/services/auth';\nimport { isBaseQueryError } from '../../../../../../admin/src/utils/baseQuery';\nimport { translatedErrors } from '../../../../../../admin/src/utils/translatedErrors';\nimport { ProvidersOptions } from '../../../../../../shared/contracts/admin';\n\nconst SCHEMA = yup.object().shape({\n  autoRegister: yup.bool().required(translatedErrors.required),\n  defaultRole: yup.mixed().when('autoRegister', (value, initSchema) => {\n    return value ? initSchema.required(translatedErrors.required) : initSchema.nullable();\n  }),\n  ssoLockedRoles: yup\n    .array()\n    .nullable()\n    .of(\n      yup.mixed().when('ssoLockedRoles', (value, initSchema) => {\n        return value ? initSchema.required(translatedErrors.required) : initSchema.nullable();\n      })\n    ),\n});\n\nexport const SingleSignOnPage = () => {\n  const { formatMessage } = useIntl();\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n  const { toggleNotification } = useNotification();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n\n  const { isLoading: isLoadingProviderOptions, data } = useGetProviderOptionsQuery();\n\n  const [updateProviderOptions, { isLoading: isSubmittingForm }] =\n    useUpdateProviderOptionsMutation();\n\n  const {\n    isLoading: isLoadingPermissions,\n    allowedActions: { canUpdate, canRead: canReadRoles },\n  } = useRBAC({\n    ...permissions.settings?.sso,\n    readRoles: permissions.settings?.roles.read ?? [],\n  });\n\n  const { roles, isLoading: isLoadingRoles } = useAdminRoles(undefined, {\n    skip: !canReadRoles,\n  });\n\n  const handleSubmit = async (\n    body: ProvidersOptions.Request['body'],\n    helpers: FormHelpers<ProvidersOptions.Request['body']>\n  ) => {\n    try {\n      const res = await updateProviderOptions(body);\n\n      if ('error' in res) {\n        if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          helpers.setErrors(formatValidationErrors(res.error));\n        } else {\n          toggleNotification({\n            type: 'danger',\n            message: formatAPIError(res.error),\n          });\n        }\n\n        return;\n      }\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: 'notification.success.saved' }),\n      });\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred, please try again.',\n        }),\n      });\n    }\n  };\n\n  const isLoadingData = isLoadingRoles || isLoadingPermissions || isLoadingProviderOptions;\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'SSO',\n          }\n        )}\n      </Page.Title>\n      <Page.Main aria-busy={isSubmittingForm || isLoadingData} tabIndex={-1}>\n        <Form\n          method=\"PUT\"\n          onSubmit={handleSubmit}\n          validationSchema={SCHEMA}\n          disabled={!canUpdate}\n          initialValues={\n            data || {\n              autoRegister: false,\n              defaultRole: null,\n              ssoLockedRoles: null,\n            }\n          }\n        >\n          {({ modified, isSubmitting }) => (\n            <>\n              <Layouts.Header\n                primaryAction={\n                  <Button\n                    disabled={!modified}\n                    loading={isSubmitting}\n                    startIcon={<Check />}\n                    type=\"submit\"\n                  >\n                    {formatMessage({\n                      id: 'global.save',\n                      defaultMessage: 'Save',\n                    })}\n                  </Button>\n                }\n                title={formatMessage({\n                  id: 'Settings.sso.title',\n                  defaultMessage: 'Single Sign-On',\n                })}\n                subtitle={formatMessage({\n                  id: 'Settings.sso.description',\n                  defaultMessage: 'Configure the settings for the Single Sign-On feature.',\n                })}\n                secondaryAction={\n                  <GradientBadge\n                    label={formatMessage({\n                      id: 'components.premiumFeature.title',\n                      defaultMessage: 'Premium feature',\n                    })}\n                  />\n                }\n              />\n              <Layouts.Content>\n                {isSubmitting || isLoadingData ? (\n                  <Page.Loading />\n                ) : (\n                  <Flex\n                    direction=\"column\"\n                    alignItems=\"stretch\"\n                    gap={4}\n                    background=\"neutral0\"\n                    padding={6}\n                    shadow=\"filterShadow\"\n                    hasRadius\n                  >\n                    <Typography variant=\"delta\" tag=\"h2\">\n                      {formatMessage({\n                        id: 'global.settings',\n                        defaultMessage: 'Settings',\n                      })}\n                    </Typography>\n                    <Grid.Root gap={4}>\n                      {[\n                        {\n                          hint: formatMessage({\n                            id: 'Settings.sso.form.registration.description',\n                            defaultMessage: 'Create new user on SSO login if no account exists',\n                          }),\n                          label: formatMessage({\n                            id: 'Settings.sso.form.registration.label',\n                            defaultMessage: 'Auto-registration',\n                          }),\n                          name: 'autoRegister',\n                          size: 6,\n                          type: 'boolean' as const,\n                        },\n                        {\n                          hint: formatMessage({\n                            id: 'Settings.sso.form.defaultRole.description',\n                            defaultMessage:\n                              'It will attach the new authenticated user to the selected role',\n                          }),\n                          label: formatMessage({\n                            id: 'Settings.sso.form.defaultRole.label',\n                            defaultMessage: 'Default role',\n                          }),\n                          name: 'defaultRole',\n                          options: roles.map(({ id, name }) => ({\n                            label: name,\n                            value: id.toString(),\n                          })),\n                          placeholder: formatMessage({\n                            id: 'components.InputSelect.option.placeholder',\n                            defaultMessage: 'Choose here',\n                          }),\n                          size: 6,\n                          type: 'enumeration' as const,\n                        },\n                        {\n                          hint: formatMessage({\n                            id: 'Settings.sso.form.localAuthenticationLock.description',\n                            defaultMessage:\n                              'Select the roles for which you want to disable the local authentication',\n                          }),\n                          label: formatMessage({\n                            id: 'Settings.sso.form.localAuthenticationLock.label',\n                            defaultMessage: 'Local authentication lock-out',\n                          }),\n                          name: 'ssoLockedRoles',\n                          options: roles.map(({ id, name }) => ({\n                            label: name,\n                            value: id.toString(),\n                          })),\n                          placeholder: formatMessage({\n                            id: 'components.InputSelect.option.placeholder',\n                            defaultMessage: 'Choose here',\n                          }),\n                          size: 6,\n                          type: 'multi' as const,\n                        },\n                      ].map(({ size, ...field }) => (\n                        <Grid.Item\n                          key={field.name}\n                          col={size}\n                          direction=\"column\"\n                          alignItems=\"stretch\"\n                        >\n                          <FormInputRenderer {...field} />\n                        </Grid.Item>\n                      ))}\n                    </Grid.Root>\n                  </Flex>\n                )}\n              </Layouts.Content>\n            </>\n          )}\n        </Form>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\ntype FormInputProps = InputProps | MultiSelectInputProps;\n\nconst FormInputRenderer = (props: FormInputProps) => {\n  switch (props.type) {\n    case 'multi':\n      return <MultiSelectInput {...props} />;\n    default:\n      return <InputRenderer {...props} />;\n  }\n};\n\ntype MultiSelectInputProps = Omit<Extract<InputProps, { type: 'enumeration' }>, 'type'> & {\n  type: 'multi';\n};\n\nconst MultiSelectInput = ({ hint, label, name, options, ...props }: MultiSelectInputProps) => {\n  const field = useField(name);\n\n  return (\n    <Field.Root name={name} hint={hint} error={field.error}>\n      <Field.Label>{label}</Field.Label>\n      <MultiSelect\n        onChange={(value) => field.onChange('ssoLockedRoles', value)}\n        onClear={() => field.onChange('ssoLockedRoles', [])}\n        value={field.value ?? []}\n        withTags\n        {...props}\n      >\n        {options.map(({ label, value }) => (\n          <MultiSelectOption key={value} value={value}>\n            {label}\n          </MultiSelectOption>\n        ))}\n      </MultiSelect>\n      <Field.Hint />\n      <Field.Error />\n    </Field.Root>\n  );\n};\n\nexport const ProtectedSSO = () => {\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings?.sso?.main);\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <SingleSignOnPage />\n    </Page.Protect>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA,IAAMA,SAAaC,QAAM,EAAGC,MAAM;EAChCC,cAAkBC,QAAI,EAAGC,SAASC,YAAiBD,QAAQ;EAC3DE,aAAiBC,OAAK,EAAGC,KAAK,gBAAgB,CAACC,OAAOC,eAAAA;AACpD,WAAOD,QAAQC,WAAWN,SAASC,YAAiBD,QAAQ,IAAIM,WAAWC,SAAQ;EACrF,CAAA;EACAC,gBACGC,QAAK,EACLF,SAAQ,EACRG,GACKP,OAAK,EAAGC,KAAK,kBAAkB,CAACC,OAAOC,eAAAA;AACzC,WAAOD,QAAQC,WAAWN,SAASC,YAAiBD,QAAQ,IAAIM,WAAWC,SAAQ;EACrF,CAAA,CAAA;AAEN,CAAA;IAEaI,mBAAmB,MAAA;;AAC9B,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,cAAcC,iBAAiB,CAACC,UAAUA,MAAMC,UAAUH,WAAW;AAC3E,QAAM,EAAEI,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EACJC,yBAAyBC,gBACzBC,iCAAiCC,uBAAsB,IACrDC,mBAAAA;AAEJ,QAAM,EAAEC,WAAWC,0BAA0BC,KAAI,IAAKC,2BAAAA;AAEtD,QAAM,CAACC,uBAAuB,EAAEJ,WAAWK,iBAAgB,CAAE,IAC3DC,iCAAAA;AAEF,QAAM,EACJN,WAAWO,sBACXC,gBAAgB,EAAEC,WAAWC,SAASC,aAAY,EAAE,IAClDC,QAAQ;IACV,IAAGvB,iBAAYwB,aAAZxB,mBAAsByB;IACzBC,aAAW1B,iBAAYwB,aAAZxB,mBAAsB2B,MAAMC,SAAQ,CAAA;EACjD,CAAA;AAEA,QAAM,EAAED,OAAOhB,WAAWkB,eAAc,IAAKC,cAAcC,QAAW;IACpEC,MAAM,CAACV;EACT,CAAA;AAEA,QAAMW,eAAe,OACnBC,MACAC,YAAAA;AAEA,QAAI;AACF,YAAMC,MAAM,MAAMrB,sBAAsBmB,IAAAA;AAExC,UAAI,WAAWE,KAAK;AAClB,YAAIC,iBAAiBD,IAAIE,KAAK,KAAKF,IAAIE,MAAMC,SAAS,mBAAmB;AACvEJ,kBAAQK,UAAU/B,uBAAuB2B,IAAIE,KAAK,CAAA;eAC7C;AACLlC,6BAAmB;YACjBqC,MAAM;YACNC,SAASnC,eAAe6B,IAAIE,KAAK;UACnC,CAAA;QACF;AAEA;MACF;AAEAlC,yBAAmB;QACjBqC,MAAM;QACNC,SAAS5C,cAAc;UAAE6C,IAAI;QAA6B,CAAA;MAC5D,CAAA;IACF,SAASC,KAAK;AACZxC,yBAAmB;QACjBqC,MAAM;QACNC,SAAS5C,cAAc;UACrB6C,IAAI;UACJE,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,QAAMC,gBAAgBjB,kBAAkBX,wBAAwBN;AAEhE,aACEmC,yBAACC,QAAQC,MAAI;;UACXC,wBAACC,KAAKC,OAAK;kBACRtD,cACC;UAAE6C,IAAI;UAAsBE,gBAAgB;WAC5C;UACEN,MAAM;QACR,CAAA;;UAGJW,wBAACC,KAAKE,MAAI;QAACC,aAAWtC,oBAAoB8B;QAAeS,UAAU;QACjE,cAAAL,wBAACM,MAAAA;UACCC,QAAO;UACPC,UAAUzB;UACV0B,kBAAkB9E;UAClB+E,UAAU,CAACxC;UACXyC,eACEhD,QAAQ;YACN7B,cAAc;YACdI,aAAa;YACbM,gBAAgB;UAClB;UAGD,UAAA,CAAC,EAAEoE,UAAUC,aAAY,UACxBhB,yBAAAiB,6BAAA;;kBACEd,wBAACF,QAAQiB,QAAM;gBACbC,mBACEhB,wBAACiB,QAAAA;kBACCP,UAAU,CAACE;kBACXM,SAASL;kBACTM,eAAWnB,wBAACoB,eAAAA,CAAAA,CAAAA;kBACZ7B,MAAK;4BAEJ3C,cAAc;oBACb6C,IAAI;oBACJE,gBAAgB;kBAClB,CAAA;;gBAGJ0B,OAAOzE,cAAc;kBACnB6C,IAAI;kBACJE,gBAAgB;gBAClB,CAAA;gBACA2B,UAAU1E,cAAc;kBACtB6C,IAAI;kBACJE,gBAAgB;gBAClB,CAAA;gBACA4B,qBACEvB,wBAACwB,uBAAAA;kBACCC,OAAO7E,cAAc;oBACnB6C,IAAI;oBACJE,gBAAgB;kBAClB,CAAA;;;kBAINK,wBAACF,QAAQ4B,SAAO;gBACbb,UAAAA,gBAAgBjB,oBACfI,wBAACC,KAAK0B,SAAO,CAAA,CAAA,QAEb9B,yBAAC+B,MAAAA;kBACCC,WAAU;kBACVC,YAAW;kBACXC,KAAK;kBACLC,YAAW;kBACXC,SAAS;kBACTC,QAAO;kBACPC,WAAS;;wBAETnC,wBAACoC,YAAAA;sBAAWC,SAAQ;sBAAQC,KAAI;gCAC7B1F,cAAc;wBACb6C,IAAI;wBACJE,gBAAgB;sBAClB,CAAA;;wBAEFK,wBAACuC,KAAKxC,MAAI;sBAACgC,KAAK;sBACb,UAAA;wBACC;0BACES,MAAM5F,cAAc;4BAClB6C,IAAI;4BACJE,gBAAgB;0BAClB,CAAA;0BACA8B,OAAO7E,cAAc;4BACnB6C,IAAI;4BACJE,gBAAgB;0BAClB,CAAA;0BACAN,MAAM;0BACNoD,MAAM;0BACNlD,MAAM;wBACR;wBACA;0BACEiD,MAAM5F,cAAc;4BAClB6C,IAAI;4BACJE,gBACE;0BACJ,CAAA;0BACA8B,OAAO7E,cAAc;4BACnB6C,IAAI;4BACJE,gBAAgB;0BAClB,CAAA;0BACAN,MAAM;0BACNqD,SAASjE,MAAMkE,IAAI,CAAC,EAAElD,IAAIJ,KAAI,OAAQ;4BACpCoC,OAAOpC;4BACPhD,OAAOoD,GAAGmD,SAAQ;4BACpB;0BACAC,aAAajG,cAAc;4BACzB6C,IAAI;4BACJE,gBAAgB;0BAClB,CAAA;0BACA8C,MAAM;0BACNlD,MAAM;wBACR;wBACA;0BACEiD,MAAM5F,cAAc;4BAClB6C,IAAI;4BACJE,gBACE;0BACJ,CAAA;0BACA8B,OAAO7E,cAAc;4BACnB6C,IAAI;4BACJE,gBAAgB;0BAClB,CAAA;0BACAN,MAAM;0BACNqD,SAASjE,MAAMkE,IAAI,CAAC,EAAElD,IAAIJ,KAAI,OAAQ;4BACpCoC,OAAOpC;4BACPhD,OAAOoD,GAAGmD,SAAQ;4BACpB;0BACAC,aAAajG,cAAc;4BACzB6C,IAAI;4BACJE,gBAAgB;0BAClB,CAAA;0BACA8C,MAAM;0BACNlD,MAAM;wBACR;wBACAoD,IAAI,CAAC,EAAEF,MAAM,GAAGK,MAAO,UACvB9C,wBAACuC,KAAKQ,MAAI;wBAERC,KAAKP;wBACLZ,WAAU;wBACVC,YAAW;wBAEX,cAAA9B,wBAACiD,mBAAAA;0BAAmB,GAAGH;;sBALlBA,GAAAA,MAAMzD,IAAI,CAAA;;;;;;;;;;;AAkBzC;AAIA,IAAM4D,oBAAoB,CAACC,UAAAA;AACzB,UAAQA,MAAM3D,MAAI;IAChB,KAAK;AACH,iBAAOS,wBAACmD,kBAAAA;QAAkB,GAAGD;;IAC/B;AACE,iBAAOlD,wBAACoD,uBAAAA;QAAe,GAAGF;;EAC9B;AACF;AAMA,IAAMC,mBAAmB,CAAC,EAAEX,MAAMf,OAAOpC,MAAMqD,SAAS,GAAGQ,MAA8B,MAAA;AACvF,QAAMJ,QAAQO,SAAShE,IAAAA;AAEvB,aACEQ,yBAACyD,MAAMvD,MAAI;IAACV;IAAYmD;IAAYpD,OAAO0D,MAAM1D;;UAC/CY,wBAACsD,MAAMC,OAAK;QAAE9B,UAAAA;;UACdzB,wBAACwD,aAAAA;QACCC,UAAU,CAACpH,UAAUyG,MAAMW,SAAS,kBAAkBpH,KAAAA;QACtDqH,SAAS,MAAMZ,MAAMW,SAAS,kBAAkB,CAAA,CAAE;QAClDpH,OAAOyG,MAAMzG,SAAS,CAAA;QACtBsH,UAAQ;QACP,GAAGT;kBAEHR,QAAQC,IAAI,CAAC,EAAElB,OAAAA,QAAOpF,MAAK,UAC1B2D,wBAAC4D,mBAAAA;UAA8BvH;UAC5BoF,UAAAA;QADqBpF,GAAAA,KAAAA,CAAAA;;UAK5B2D,wBAACsD,MAAMO,MAAI,CAAA,CAAA;UACX7D,wBAACsD,MAAMQ,OAAK,CAAA,CAAA;;;AAGlB;IAEaC,eAAe,MAAA;AAC1B,QAAMjH,cAAcC,iBAAiB,CAACC,UAAUA;;AAAAA,6BAAMC,UAAUH,YAAYwB,aAA5BtB,mBAAsCuB,QAAtCvB,mBAA2CgH;GAAAA;AAE3F,aACEhE,wBAACC,KAAKgE,SAAO;IAACnH;IACZ,cAAAkD,wBAACrD,kBAAAA,CAAAA,CAAAA;;AAGP;", "names": ["SCHEMA", "object", "shape", "autoRegister", "bool", "required", "translatedErrors", "defaultRole", "mixed", "when", "value", "initSchema", "nullable", "ssoLockedRoles", "array", "of", "SingleSignOnPage", "formatMessage", "useIntl", "permissions", "useTypedSelector", "state", "admin_app", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "isLoading", "isLoadingProviderOptions", "data", "useGetProviderOptionsQuery", "updateProviderOptions", "isSubmittingForm", "useUpdateProviderOptionsMutation", "isLoadingPermissions", "allowedActions", "canUpdate", "canRead", "canReadRoles", "useRBAC", "settings", "sso", "readRoles", "roles", "read", "isLoadingRoles", "useAdminRoles", "undefined", "skip", "handleSubmit", "body", "helpers", "res", "isBaseQueryError", "error", "name", "setErrors", "type", "message", "id", "err", "defaultMessage", "isLoadingData", "_jsxs", "Layouts", "Root", "_jsx", "Page", "Title", "Main", "aria-busy", "tabIndex", "Form", "method", "onSubmit", "validationSchema", "disabled", "initialValues", "modified", "isSubmitting", "_Fragment", "Header", "primaryAction", "<PERSON><PERSON>", "loading", "startIcon", "Check", "title", "subtitle", "secondaryAction", "GradientBadge", "label", "Content", "Loading", "Flex", "direction", "alignItems", "gap", "background", "padding", "shadow", "hasRadius", "Typography", "variant", "tag", "Grid", "hint", "size", "options", "map", "toString", "placeholder", "field", "<PERSON><PERSON>", "col", "FormInput<PERSON><PERSON>er", "props", "MultiSelectInput", "InputR<PERSON><PERSON>", "useField", "Field", "Label", "MultiSelect", "onChange", "onClear", "withTags", "MultiSelectOption", "Hint", "Error", "ProtectedSSO", "main", "Protect"]}