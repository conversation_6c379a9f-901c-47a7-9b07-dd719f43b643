import{bx as o}from"./strapi-z7ApxZZq.js";const s=o.enhanceEndpoints({addTagTypes:["ApiToken","GuidedTourMeta"]}).injectEndpoints({endpoints:t=>({getAPITokens:t.query({query:()=>"/admin/api-tokens",transformResponse:e=>e.data,providesTags:(e,n)=>[...e?.map(({id:a})=>({type:"ApiToken",id:a}))??[],{type:"ApiToken",id:"LIST"}]}),getAPIToken:t.query({query:e=>`/admin/api-tokens/${e}`,transformResponse:e=>e.data,providesTags:(e,n,a)=>[{type:"ApiToken",id:a}]}),createAPIToken:t.mutation({query:e=>({url:"/admin/api-tokens",method:"POST",data:e}),transformResponse:e=>e.data,invalidatesTags:[{type:"ApiToken",id:"LIST"},"GuidedTourMeta"]}),deleteAPIToken:t.mutation({query:e=>({url:`/admin/api-tokens/${e}`,method:"DELETE"}),transformResponse:e=>e.data,invalidatesTags:(e,n,a)=>[{type:"ApiToken",id:a}]}),updateAPIToken:t.mutation({query:({id:e,...n})=>({url:`/admin/api-tokens/${e}`,method:"PUT",data:n}),transformResponse:e=>e.data,invalidatesTags:(e,n,{id:a})=>[{type:"ApiToken",id:a}]})})}),{useGetAPITokensQuery:r,useGetAPITokenQuery:p,useCreateAPITokenMutation:d,useDeleteAPITokenMutation:T,useUpdateAPITokenMutation:u}=s;export{T as a,p as b,d as c,u as d,r as u};
