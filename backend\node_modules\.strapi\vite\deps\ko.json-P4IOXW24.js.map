{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/ko.json.mjs"], "sourcesContent": ["var groups = \"그룹\";\nvar models = \"콜렉션 타입\";\nvar pageNotFound = \"페이지를 찾을 수 없습니다.\";\nvar ko = {\n    \"App.schemas.data-loaded\": \"스키마를 불러왔습니다.\",\n    \"ListViewTable.relation-loaded\": \"릴레이션을 불러왔습니다.\",\n    \"EditRelations.title\": \"관계 데이터\",\n    \"HeaderLayout.button.label-add-entry\": \"새 항목 추가\",\n    \"api.id\": \"API ID\",\n    \"components.AddFilterCTA.add\": \"필터\",\n    \"components.AddFilterCTA.hide\": \"필터\",\n    \"components.DragHandle-label\": \"드래그\",\n    \"components.DraggableAttr.edit\": \"클릭하여 수정\",\n    \"components.DraggableCard.delete.field\": \"{item} 삭제\",\n    \"components.DraggableCard.edit.field\": \"{item} 수정\",\n    \"components.DraggableCard.move.field\": \"{item} 이동\",\n    \"components.ListViewTable.row-line\": \"item line {number}\",\n    \"components.DynamicZone.ComponentPicker-label\": \"Pick one component\",\n    \"components.DynamicZone.add-component\": \"Add a component to {componentName}\",\n    \"components.DynamicZone.delete-label\": \"{name} 삭제\",\n    \"components.DynamicZone.error-message\": \"The component contains error(s)\",\n    \"components.DynamicZone.missing-components\": \"{number, plural, =0 {# 개} one {is # 개} other {are # 개}}의 누락된 컴포넌트가 있습니다.\",\n    \"components.DynamicZone.move-down-label\": \"컴포넌트 아래로 이동\",\n    \"components.DynamicZone.move-up-label\": \"컴포넌트 위로 이동\",\n    \"components.DynamicZone.pick-compo\": \"Pick one component\",\n    \"components.DynamicZone.required\": \"컴포넌트는 필수 항목입니다.\",\n    \"components.EmptyAttributesBlock.button\": \"설정 페이지 이동\",\n    \"components.EmptyAttributesBlock.description\": \"설정을 변경할 수 있습니다.\",\n    \"components.FieldItem.linkToComponentLayout\": \"컴포넌트 레이아웃 설정\",\n    \"components.FieldSelect.label\": \"필드 추가\",\n    \"components.FilterOptions.button.apply\": \"적용\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"적용\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"모두 재설정\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"필터링 조건을 설정하세요.\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"필터\",\n    \"components.FiltersPickWrapper.hide\": \"숨김\",\n    \"components.LeftMenu.Search.label\": \"콘텐츠 타입 검색\",\n    \"components.LeftMenu.collection-types\": \"콜렉션 타입\",\n    \"components.LeftMenu.single-types\": \"싱글 타입\",\n    \"components.LimitSelect.itemsPerPage\": \"항목 수 / 페이지\",\n    \"components.NotAllowedInput.text\": \"이 필드를 볼 수 있는 권한이 없습니다.\",\n    \"components.RepeatableComponent.error-message\": \"The component(s) contain error(s)\",\n    \"components.Search.placeholder\": \"검색 중입니다...\",\n    \"components.Select.draft-info-title\": \"상태: 초안\",\n    \"components.Select.publish-info-title\": \"상태: 발행됨\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"편집 보기 화면을 구성합니다.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"목록 보기 화면을 구성합니다.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"보기 설정 - {name}\",\n    \"components.TableDelete.delete\": \"모두 삭제\",\n    \"components.TableDelete.deleteSelected\": \"선택항목 삭제\",\n    \"components.TableDelete.label\": \"{number, plural, one {# 개} other {# 개}}의 항목이 선택됨\",\n    \"components.TableEmpty.withFilters\": \"필터 조건에 맞는 {contentType} 목록이 없습니다.\",\n    \"components.TableEmpty.withSearch\": \"\\\"{search}\\\" 검색. {contentType} 목록이 없습니다.\",\n    \"components.TableEmpty.withoutFilter\": \"{contentType} 목록이 없습니다.\",\n    \"components.empty-repeatable\": \"항목이 없습니다. 항목을 추가하려면 아래 버튼을 클릭해주세요.\",\n    \"components.notification.info.maximum-requirement\": \"이미 최대 필드 수에 도달했습니다.\",\n    \"components.notification.info.minimum-requirement\": \"최소 요구 사항과 일치하도록 필드가 추가되었습니다.\",\n    \"components.repeatable.reorder.error\": \"컴포넌트 필드를 재정렬하는 중에 오류가 발생했습니다. 다시 시도하십시오.\",\n    \"components.reset-entry\": \"Reset entry\",\n    \"components.uid.apply\": \"적용\",\n    \"components.uid.available\": \"사용 가능\",\n    \"components.uid.regenerate\": \"재생성\",\n    \"components.uid.suggested\": \"제안됨\",\n    \"components.uid.unavailable\": \"사용 불가\",\n    \"containers.Edit.Link.Layout\": \"레이아웃 설정\",\n    \"containers.Edit.Link.Model\": \"콜렉션 타입 수정\",\n    \"containers.Edit.addAnItem\": \"추가할 항목...\",\n    \"containers.Edit.clickToJump\": \"해당 항목으로 이동하려면 클릭\",\n    \"containers.Edit.delete\": \"삭제\",\n    \"containers.Edit.delete-entry\": \"이 항목 삭제\",\n    \"containers.Edit.editing\": \"수정 중...\",\n    \"containers.Edit.information\": \"정보\",\n    \"containers.Edit.information.by\": \"편집자\",\n    \"containers.Edit.information.draftVersion\": \"초안 버전\",\n    \"containers.Edit.information.editing\": \"수정중 -\",\n    \"containers.Edit.information.lastUpdate\": \"최근 업데이트\",\n    \"containers.Edit.information.publishedVersion\": \"발행 버전\",\n    \"containers.Edit.pluginHeader.title.new\": \"항목 생성\",\n    \"containers.Edit.reset\": \"초기화\",\n    \"containers.Edit.returnList\": \"목록\",\n    \"containers.Edit.seeDetails\": \"세부 사항\",\n    \"containers.Edit.submit\": \"저장\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"필드 수정\",\n    \"containers.EditView.add.new-entry\": \"항목 추가\",\n    \"containers.EditView.notification.errors\": \"잘못 입력된 필드가 존재합니다.\",\n    \"containers.Home.introduction\": \"항목을 수정하려면 왼편 링크를 클릭하세요. 이 플러그인은 설정을 편집할 수 있는 방법을 개발 중입니다.\",\n    \"containers.Home.pluginHeaderDescription\": \"쉽고 강력한 UI를 통해 항목들을 관리 하세요.\",\n    \"containers.Home.pluginHeaderTitle\": \"콘텐츠 관리\",\n    \"containers.List.draft\": \"초안\",\n    \"containers.List.errorFetchRecords\": \"에러\",\n    \"containers.List.published\": \"발행됨\",\n    \"containers.list.displayedFields\": \"표시 필드\",\n    \"containers.list.items\": \"{number, plural, =0 {items} one {item} other {items}}\",\n    \"containers.list.table-headers.publishedAt\": \"상태\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"{fieldName} 수정\",\n    \"containers.SettingPage.add.field\": \"다른 필드 추가\",\n    \"containers.SettingPage.attributes\": \"속성\",\n    \"containers.SettingPage.attributes.description\": \"속성의 순서를 지정합니다\",\n    \"containers.SettingPage.editSettings.description\": \"레이아웃을 구성하려면 필드를 드래그 & 드롭하세요.\",\n    \"containers.SettingPage.editSettings.entry.title\": \"항목 제목\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"제목으로 보여줄 필드를 선택하세요.\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"편집 및 목록 보기 화면에 모두 표시되는 필드를 설정합니다.\",\n    \"containers.SettingPage.editSettings.title\": \"화면 수정 (설정)\",\n    \"containers.SettingPage.layout\": \"레이아웃\",\n    \"containers.SettingPage.listSettings.description\": \"이 컬렉션 타입에 대한 옵션을 구성합니다.\",\n    \"containers.SettingPage.listSettings.title\": \"목록 (설정)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"이 컬렉션 타입에 대한 특정 설정을 구성합니다.\",\n    \"containers.SettingPage.settings\": \"설정\",\n    \"containers.SettingPage.view\": \"보기\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"콘텐츠 매니저 - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"특정 설정을 구성합니다.\",\n    \"containers.SettingsPage.Block.contentType.title\": \"콜렉션 타입\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"콜렉션 타입에 대한 기본 옵션을 구성합니다.\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"일반\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"모든 콘텐츠 타입 및 그룹에 대한 설정을 구성합니다.\",\n    \"containers.SettingsView.list.subtitle\": \"콘텐츠 타입 및 그룹의 레이아웃과 표시를 구성합니다.\",\n    \"containers.SettingsView.list.title\": \"표시 설정\",\n    \"edit-settings-view.link-to-ctb.components\": \"컴포넌트 수정\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"콘텐츠 타입 수정\",\n    \"emptyAttributes.button\": \"콜렉션 타입 빌더로 이동\",\n    \"emptyAttributes.description\": \"콜렉션 타입에 첫 필드를 추가해보세요.\",\n    \"emptyAttributes.title\": \"아직 필드가 없습니다.\",\n    \"error.attribute.key.taken\": \"이미 사용중인 키입니다.\",\n    \"error.attribute.sameKeyAndName\": \"같은 값을 사용할 수 없습니다.\",\n    \"error.attribute.taken\": \"이미 사용중인 이름입니다.\",\n    \"error.contentTypeName.taken\": \"이미 사용중인 이름입니다.\",\n    \"error.model.fetch\": \"모델 설정을 가져오는 도중 에러가 발생했습니다.\",\n    \"error.record.create\": \"데이터를 생성하는 도중 에러가 발생했습니다.\",\n    \"error.record.delete\": \"데이터를 삭제하는 도중 에러가 발생했습니다.\",\n    \"error.record.fetch\": \"데이터를 가져오는 도중 에러가 발생했습니다.\",\n    \"error.record.update\": \"데이터를 업데이트하는 도중 에러가 발생했습니다.\",\n    \"error.records.count\": \"데이터 수를 가져오는 도중 에러가 발생했습니다.\",\n    \"error.records.fetch\": \"데이터를 가져오는 도중 에러가 발생했습니다.\",\n    \"error.schema.generation\": \"스키마를 생성하는 도중 에러가 발생했습니다.\",\n    \"error.validation.json\": \"JSON 형식이 아닙니다.\",\n    \"error.validation.max\": \"입력한 내용이 너무 큽니다.\",\n    \"error.validation.maxLength\": \"입력한 내용이 너무 깁니다.\",\n    \"error.validation.min\": \"입력한 내용이 너무 작습니다.\",\n    \"error.validation.minLength\": \"입력한 내용이 너무 짧습니다.\",\n    \"error.validation.minSupMax\": \"이 보다 더 클 수 없습니다.\",\n    \"error.validation.regex\": \"입력한 내용이 맞지 않습니다.\",\n    \"error.validation.required\": \"내용을 입력해 주세요.\",\n    \"form.Input.bulkActions\": \"대규모 액션 활성화\",\n    \"form.Input.defaultSort\": \"기본 정렬 속성\",\n    \"form.Input.description\": \"설명\",\n    \"form.Input.description.placeholder\": \"Display name in the profile\",\n    \"form.Input.editable\": \"필드 수정가능 여부\",\n    \"form.Input.filters\": \"필더 활성화\",\n    \"form.Input.label\": \"라벨\",\n    \"form.Input.label.inputDescription\": \"이 값은 테이블 머리에 표시된 라벨을 덮어씌웁니다.\",\n    \"form.Input.pageEntries\": \"페이지 당 요소\",\n    \"form.Input.pageEntries.inputDescription\": \"참고: 콘텐츠 타입 설정 페이지에서 이 값을 재정의(override)할 수 있습니다.\",\n    \"form.Input.placeholder\": \"Placeholder\",\n    \"form.Input.placeholder.placeholder\": \"My awesome value\",\n    \"form.Input.search\": \"검색 활성화\",\n    \"form.Input.search.field\": \"이 필드에 검색 활성화\",\n    \"form.Input.sort.field\": \"이 필드에 정렬 활성화\",\n    \"form.Input.sort.order\": \"기본 정렬 순서\",\n    \"form.Input.wysiwyg\": \"WYSIWYG로 보기\",\n    \"global.displayedFields\": \"표시 필드\",\n    groups: groups,\n    \"groups.numbered\": \"그룹 ({number}개)\",\n    \"header.name\": \"콘텐츠\",\n    \"link-to-ctb\": \"모델 수정\",\n    models: models,\n    \"models.numbered\": \"콜렉션 타입 ({number})\",\n    \"notification.error.displayedFields\": \"표시될 필드가 최소 하나 이상 필요합니다.\",\n    \"notification.error.relationship.fetch\": \"데이터 관계를 가져오는 도중 에러가 발생했습니다.\",\n    \"notification.info.SettingPage.disableSort\": \"정렬이 활성화된 한 개의 속성이 필요합니다.\",\n    \"notification.info.minimumFields\": \"표시될 필드가 최소 하나 이상 필요합니다.\",\n    \"notification.upload.error\": \"파일 업로드 중에 에러가 발생했습니다.\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{number, plural, =0 {# 개} one {# 개} other {# 개}} 항목을 찾았습니다.\",\n    \"pages.NoContentType.button\": \"첫 콘텐츠 타입 생성하기\",\n    \"pages.NoContentType.text\": \"아직 콘텐츠가 없습니다. 첫 콘텐츠 타입을 생성해보세요.\",\n    \"permissions.not-allowed.create\": \"문서를 생성할 수 있는 권한이 없습니다.\",\n    \"permissions.not-allowed.update\": \"이 문서를 볼 수 있는 권한이 없습니다.\",\n    \"plugin.description.long\": \"데이터를 쉽게 확인 하고 수정, 삭제 할 수 있습니다.\",\n    \"plugin.description.short\": \"데이터를 쉽게 확인 하고 수정, 삭제 할 수 있습니다.\",\n    \"popover.display-relations.label\": \"Display relations\",\n    \"success.record.delete\": \"삭제\",\n    \"success.record.publish\": \"발행됨\",\n    \"success.record.save\": \"저장\",\n    \"success.record.unpublish\": \"발행이 취소됨\",\n    \"utils.data-loaded\": \"The {number, plural, =1 {개} other {개}}의 항목을 불러왔습니다.\",\n    \"popUpWarning.warning.publish-question\": \"정말 발행하시겠습니까?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"네, 발행합니다.\"\n};\n\nexport { ko as default, groups, models, pageNotFound };\n//# sourceMappingURL=ko.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,yCAAyC;AAAA,EACzC,2DAA2D;AAC/D;", "names": []}