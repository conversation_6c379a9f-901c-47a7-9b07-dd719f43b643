{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/routes/settings/index.tsx"], "sourcesContent": ["/* eslint-disable check-file/no-index */\n/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport { Page, useTracking, ConfirmDialog, useRBAC, Table } from '@strapi/admin/strapi-admin';\nimport { useLicenseLimits } from '@strapi/admin/strapi-admin/ee';\nimport { Flex, IconButton, TF<PERSON>er, Typography, LinkButton, Dialog } from '@strapi/design-system';\nimport { Pencil, Plus, Trash } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { NavLink, Link, useNavigate } from 'react-router-dom';\n\nimport { LimitsModal } from '../../components/LimitsModal';\nimport { CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME } from '../../constants';\nimport { useTypedSelector } from '../../modules/hooks';\nimport { ContentType, useGetContentTypesQuery } from '../../services/content-manager';\n\nimport * as Layout from './components/Layout';\nimport { useReviewWorkflows } from './hooks/useReviewWorkflows';\n\nexport const ReviewWorkflowsListView = () => {\n  const { formatMessage } = useIntl();\n  const navigate = useNavigate();\n  const { trackUsage } = useTracking();\n  const [workflowToDelete, setWorkflowToDelete] = React.useState<string | null>(null);\n  const [showLimitModal, setShowLimitModal] = React.useState<boolean>(false);\n  const { data, isLoading: isLoadingModels } = useGetContentTypesQuery();\n  const { meta, workflows, isLoading, delete: deleteAction } = useReviewWorkflows();\n  const { getFeature, isLoading: isLicenseLoading } = useLicenseLimits();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['review-workflows']\n  );\n  const {\n    allowedActions: { canCreate, canRead, canUpdate, canDelete },\n  } = useRBAC(permissions);\n\n  const limits = getFeature('review-workflows');\n  const numberOfWorkflows = limits?.[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME] as string;\n\n  const handleDeleteWorkflow = (workflowId: string) => {\n    setWorkflowToDelete(workflowId);\n  };\n\n  const toggleConfirmDeleteDialog = () => {\n    setWorkflowToDelete(null);\n  };\n\n  const handleConfirmDeleteDialog = async () => {\n    if (!workflowToDelete) return;\n\n    await deleteAction(workflowToDelete);\n\n    setWorkflowToDelete(null);\n  };\n\n  const handleCreateClick: React.MouseEventHandler<HTMLAnchorElement> &\n    ((event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void) = (event) => {\n    event.preventDefault();\n    /**\n     * If the current license has a workflow limit:\n     * check if the total count of workflows exceeds that limit. If so,\n     * prevent the navigation and show the limits overlay.\n     *\n     * If the current license does not have a limit (e.g. offline license):\n     * allow the user to navigate to the create-view. In case they exceed the\n     * current hard-limit of 200 they will see an error thrown by the API.\n     */\n\n    if (numberOfWorkflows && meta && meta?.workflowCount >= parseInt(numberOfWorkflows, 10)) {\n      event.preventDefault();\n      setShowLimitModal(true);\n    } else {\n      navigate('create');\n      trackUsage('willCreateWorkflow');\n    }\n  };\n\n  /**\n   * If the current license has a limit:\n   * check if the total count of workflows or stages exceeds that limit and display\n   * the limits modal on page load. It can be closed by the user, but the\n   * API will throw an error in case they try to create a new workflow or update the\n   * stages.\n   *\n   * If the current license does not have a limit (e.g. offline license):\n   * do nothing (for now). In case they are trying to create the 201st workflow/ stage\n   * the API will throw an error.\n   *\n   */\n  React.useEffect(() => {\n    if (!isLoading && !isLicenseLoading) {\n      if (numberOfWorkflows && meta && meta?.workflowCount > parseInt(numberOfWorkflows, 10)) {\n        setShowLimitModal(true);\n      }\n    }\n  }, [isLicenseLoading, isLoading, meta, meta?.workflowCount, numberOfWorkflows]);\n\n  const headers = [\n    {\n      label: formatMessage({\n        id: 'Settings.review-workflows.list.page.list.column.name.title',\n        defaultMessage: 'Name',\n      }),\n      name: 'name',\n    },\n    {\n      label: formatMessage({\n        id: 'Settings.review-workflows.list.page.list.column.stages.title',\n        defaultMessage: 'Stages',\n      }),\n      name: 'stages',\n    },\n    {\n      label: formatMessage({\n        id: 'Settings.review-workflows.list.page.list.column.contentTypes.title',\n        defaultMessage: 'Content Types',\n      }),\n      name: 'content-types',\n    },\n  ];\n\n  if (isLoading || isLoadingModels) {\n    return <Page.Loading />;\n  }\n\n  const contentTypes = Object.values(data ?? {}).reduce<ContentType[]>((acc, curr) => {\n    acc.push(...curr);\n    return acc;\n  }, []);\n\n  return (\n    <>\n      <Layout.Header\n        primaryAction={\n          canCreate ? (\n            <LinkButton\n              startIcon={<Plus />}\n              size=\"S\"\n              tag={NavLink}\n              to=\"create\"\n              onClick={handleCreateClick}\n            >\n              {formatMessage({\n                id: 'Settings.review-workflows.list.page.create',\n                defaultMessage: 'Create new workflow',\n              })}\n            </LinkButton>\n          ) : null\n        }\n        subtitle={formatMessage({\n          id: 'Settings.review-workflows.list.page.subtitle',\n          defaultMessage: 'Manage your content review process',\n        })}\n        title={formatMessage({\n          id: 'Settings.review-workflows.list.page.title',\n          defaultMessage: 'Review Workflows',\n        })}\n      />\n\n      <Layout.Root>\n        <Table.Root\n          isLoading={isLoading}\n          rows={workflows}\n          footer={\n            canCreate ? (\n              <TFooter cursor=\"pointer\" icon={<Plus />} onClick={handleCreateClick}>\n                {formatMessage({\n                  id: 'Settings.review-workflows.list.page.create',\n                  defaultMessage: 'Create new workflow',\n                })}\n              </TFooter>\n            ) : null\n          }\n          headers={headers}\n        >\n          <Table.Content>\n            <Table.Head>\n              {headers.map((head) => (\n                <Table.HeaderCell key={head.name} {...head} />\n              ))}\n            </Table.Head>\n\n            <Table.Body>\n              {workflows.map((workflow) => (\n                <Table.Row\n                  onClick={() => {\n                    navigate(`${workflow.id}`);\n                  }}\n                  key={workflow.id}\n                >\n                  <Table.Cell width=\"25rem\">\n                    <Typography textColor=\"neutral800\" fontWeight=\"bold\" ellipsis>\n                      {workflow.name}\n                    </Typography>\n                  </Table.Cell>\n                  <Table.Cell>\n                    <Typography textColor=\"neutral800\">{workflow.stages.length}</Typography>\n                  </Table.Cell>\n                  <Table.Cell>\n                    <Typography textColor=\"neutral800\">\n                      {workflow.contentTypes\n                        .map((uid: string) => {\n                          const contentType = contentTypes.find(\n                            (contentType) => contentType.uid === uid\n                          );\n\n                          return contentType?.info.displayName ?? '';\n                        })\n                        .join(', ')}\n                    </Typography>\n                  </Table.Cell>\n                  <Table.Cell>\n                    <Flex alignItems=\"center\" justifyContent=\"end\">\n                      {canRead || canUpdate ? (\n                        <IconButton\n                          tag={Link}\n                          to={workflow.id.toString()}\n                          label={formatMessage(\n                            {\n                              id: 'Settings.review-workflows.list.page.list.column.actions.edit.label',\n                              defaultMessage: 'Edit {name}',\n                            },\n                            { name: workflow.name }\n                          )}\n                          variant=\"ghost\"\n                        >\n                          <Pencil />\n                        </IconButton>\n                      ) : null}\n                      {workflows.length > 1 && canDelete ? (\n                        <IconButton\n                          withTooltip={false}\n                          label={formatMessage(\n                            {\n                              id: 'Settings.review-workflows.list.page.list.column.actions.delete.label',\n                              defaultMessage: 'Delete {name}',\n                            },\n                            { name: 'Default workflow' }\n                          )}\n                          variant=\"ghost\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            handleDeleteWorkflow(String(workflow.id));\n                          }}\n                        >\n                          <Trash />\n                        </IconButton>\n                      ) : null}\n                    </Flex>\n                  </Table.Cell>\n                </Table.Row>\n              ))}\n            </Table.Body>\n          </Table.Content>\n        </Table.Root>\n\n        <Dialog.Root open={!!workflowToDelete} onOpenChange={toggleConfirmDeleteDialog}>\n          <ConfirmDialog onConfirm={handleConfirmDeleteDialog}>\n            {formatMessage({\n              id: 'Settings.review-workflows.list.page.delete.confirm.body',\n              defaultMessage:\n                'If you remove this worfklow, all stage-related information will be removed for this content-type. Are you sure you want to remove it?',\n            })}\n          </ConfirmDialog>\n        </Dialog.Root>\n\n        <LimitsModal.Root open={showLimitModal} onOpenChange={() => setShowLimitModal(false)}>\n          <LimitsModal.Title>\n            {formatMessage({\n              id: 'Settings.review-workflows.list.page.workflows.limit.title',\n              defaultMessage: 'You’ve reached the limit of workflows in your plan',\n            })}\n          </LimitsModal.Title>\n\n          <LimitsModal.Body>\n            {formatMessage({\n              id: 'Settings.review-workflows.list.page.workflows.limit.body',\n              defaultMessage: 'Delete a workflow or contact Sales to enable more workflows.',\n            })}\n          </LimitsModal.Body>\n        </LimitsModal.Root>\n      </Layout.Root>\n    </>\n  );\n};\n\nconst ProtectedListPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['review-workflows']?.main\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ReviewWorkflowsListView />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedListPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmBaA,0BAA0B,MAAA;AACrC,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAMC,WAAWC,YAAAA;AACjB,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,CAACC,kBAAkBC,mBAAAA,IAA6BC,eAAwB,IAAA;AAC9E,QAAM,CAACC,gBAAgBC,iBAAAA,IAA2BF,eAAkB,KAAA;AACpE,QAAM,EAAEG,MAAMC,WAAWC,gBAAe,IAAKC,wBAAAA;AAC7C,QAAM,EAAEC,MAAMC,WAAWJ,WAAWK,QAAQC,aAAY,IAAKC,mBAAAA;AAC7D,QAAM,EAAEC,YAAYR,WAAWS,iBAAgB,IAAKC,iBAAAA;AACpD,QAAMC,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,uBAAMC,UAAUH,YAAYI,aAA5BF,mBAAuC;GAAmB;AAEvE,QAAM,EACJG,gBAAgB,EAAEC,WAAWC,SAASC,WAAWC,UAAS,EAAE,IAC1DC,QAAQV,WAAAA;AAEZ,QAAMW,SAASd,WAAW,kBAAA;AAC1B,QAAMe,oBAAoBD,iCAASE;AAEnC,QAAMC,uBAAuB,CAACC,eAAAA;AAC5B/B,wBAAoB+B,UAAAA;EACtB;AAEA,QAAMC,4BAA4B,MAAA;AAChChC,wBAAoB,IAAA;EACtB;AAEA,QAAMiC,4BAA4B,YAAA;AAChC,QAAI,CAAClC,iBAAkB;AAEvB,UAAMY,aAAaZ,gBAAAA;AAEnBC,wBAAoB,IAAA;EACtB;AAEA,QAAMkC,oBACiE,CAACC,UAAAA;AACtEA,UAAMC,eAAc;AAWpB,QAAIR,qBAAqBpB,SAAQA,6BAAM6B,kBAAiBC,SAASV,mBAAmB,EAAK,GAAA;AACvFO,YAAMC,eAAc;AACpBjC,wBAAkB,IAAA;WACb;AACLR,eAAS,QAAA;AACTE,iBAAW,oBAAA;IACb;EACF;AAcA0C,EAAMC,gBAAU,MAAA;AACd,QAAI,CAACnC,aAAa,CAACS,kBAAkB;AACnC,UAAIc,qBAAqBpB,SAAQA,6BAAM6B,iBAAgBC,SAASV,mBAAmB,EAAK,GAAA;AACtFzB,0BAAkB,IAAA;MACpB;IACF;KACC;IAACW;IAAkBT;IAAWG;IAAMA,6BAAM6B;IAAeT;EAAkB,CAAA;AAE9E,QAAMa,UAAU;IACd;MACEC,OAAOjD,cAAc;QACnBkD,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAC,MAAM;IACR;IACA;MACEH,OAAOjD,cAAc;QACnBkD,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAC,MAAM;IACR;IACA;MACEH,OAAOjD,cAAc;QACnBkD,IAAI;QACJC,gBAAgB;MAClB,CAAA;MACAC,MAAM;IACR;EACD;AAED,MAAIxC,aAAaC,iBAAiB;AAChC,eAAOwC,wBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,QAAMC,eAAeC,OAAOC,OAAO/C,QAAQ,CAAA,CAAIgD,EAAAA,OAAsB,CAACC,KAAKC,SAAAA;AACzED,QAAIE,KAAQD,GAAAA,IAAAA;AACZ,WAAOD;EACT,GAAG,CAAA,CAAE;AAEL,aACEG,yBAAAC,6BAAA;;UACEX,wBAACY,QAAa;QACZC,eACErC,gBACEwB,wBAACc,YAAAA;UACCC,eAAWf,wBAACgB,eAAAA,CAAAA,CAAAA;UACZC,MAAK;UACLC,KAAKC;UACLC,IAAG;UACHC,SAASjC;oBAERzC,cAAc;YACbkD,IAAI;YACJC,gBAAgB;UAClB,CAAA;QAEA,CAAA,IAAA;QAENwB,UAAU3E,cAAc;UACtBkD,IAAI;UACJC,gBAAgB;QAClB,CAAA;QACAyB,OAAO5E,cAAc;UACnBkD,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAGFY,yBAACE,MAAW;;cACVZ,wBAACwB,MAAMC,MAAI;YACTlE;YACAmE,MAAM/D;YACNgE,QACEnD,gBACEwB,wBAAC4B,SAAAA;cAAQC,QAAO;cAAUC,UAAM9B,wBAACgB,eAAAA,CAAAA,CAAAA;cAASK,SAASjC;wBAChDzC,cAAc;gBACbkD,IAAI;gBACJC,gBAAgB;cAClB,CAAA;YAEA,CAAA,IAAA;YAENH;0BAEAe,yBAACc,MAAMO,SAAO;;oBACZ/B,wBAACwB,MAAMQ,MAAI;kBACRrC,UAAAA,QAAQsC,IAAI,CAACC,aACZlC,wBAACwB,MAAMW,YAAU;oBAAkB,GAAGD;kBAAfA,GAAAA,KAAKnC,IAAI,CAAA;;oBAIpCC,wBAACwB,MAAMY,MAAI;kBACRzE,UAAAA,UAAUsE,IAAI,CAACI,iBACd3B,yBAACc,MAAMc,KAAG;oBACRjB,SAAS,MAAA;AACPxE,+BAAS,GAAGwF,SAASxC,EAAE,EAAE;oBAC3B;;0BAGAG,wBAACwB,MAAMe,MAAI;wBAACC,OAAM;wBAChB,cAAAxC,wBAACyC,YAAAA;0BAAWC,WAAU;0BAAaC,YAAW;0BAAOC,UAAQ;0BAC1DP,UAAAA,SAAStC;;;0BAGdC,wBAACwB,MAAMe,MAAI;wBACT,cAAAvC,wBAACyC,YAAAA;0BAAWC,WAAU;oCAAcL,SAASQ,OAAOC;;;0BAEtD9C,wBAACwB,MAAMe,MAAI;wBACT,cAAAvC,wBAACyC,YAAAA;0BAAWC,WAAU;0BACnBL,UAAAA,SAASlC,aACP8B,IAAI,CAACc,QAAAA;AACJ,kCAAMC,cAAc7C,aAAa8C,KAC/B,CAACD,iBAAgBA,aAAYD,QAAQA,GAAAA;AAGvC,oCAAOC,2CAAaE,KAAKC,gBAAe;0BAC1C,CAAA,EACCC,KAAK,IAAA;;;0BAGZpD,wBAACwB,MAAMe,MAAI;wBACT,cAAA7B,yBAAC2C,MAAAA;0BAAKC,YAAW;0BAASC,gBAAe;;4BACtC9E,WAAWC,gBACVsB,wBAACwD,YAAAA;8BACCtC,KAAKuC;8BACLrC,IAAIiB,SAASxC,GAAG6D,SAAQ;8BACxB9D,OAAOjD,cACL;gCACEkD,IAAI;gCACJC,gBAAgB;iCAElB;gCAAEC,MAAMsC,SAAStC;8BAAK,CAAA;8BAExB4D,SAAQ;8BAER,cAAA3D,wBAAC4D,eAAAA,CAAAA,CAAAA;4BAED,CAAA,IAAA;4BACHjG,UAAUmF,SAAS,KAAKnE,gBACvBqB,wBAACwD,YAAAA;8BACCK,aAAa;8BACbjE,OAAOjD,cACL;gCACEkD,IAAI;gCACJC,gBAAgB;iCAElB;gCAAEC,MAAM;8BAAmB,CAAA;8BAE7B4D,SAAQ;8BACRtC,SAAS,CAACyC,MAAAA;AACRA,kCAAEC,gBAAe;AACjB/E,qDAAqBgF,OAAO3B,SAASxC,EAAE,CAAA;8BACzC;8BAEA,cAAAG,wBAACiE,cAAAA,CAAAA,CAAAA;4BAED,CAAA,IAAA;;;;;kBA3DH5B,GAAAA,SAASxC,EAAE,CAAA;;;;;cAoE1BG,wBAACkE,OAAOzC,MAAI;YAAC0C,MAAM,CAAC,CAAClH;YAAkBmH,cAAclF;YACnD,cAAAc,wBAACqE,eAAAA;cAAcC,WAAWnF;wBACvBxC,cAAc;gBACbkD,IAAI;gBACJC,gBACE;cACJ,CAAA;;;cAIJY,yBAAC6D,YAAY9C,MAAI;YAAC0C,MAAM/G;YAAgBgH,cAAc,MAAM/G,kBAAkB,KAAA;;kBAC5E2C,wBAACuE,YAAYC,OAAK;0BACf7H,cAAc;kBACbkD,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;kBAGFE,wBAACuE,YAAYnC,MAAI;0BACdzF,cAAc;kBACbkD,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;;;;;;;;AAMZ;AAEA,IAAM2E,oBAAoB,MAAA;AACxB,QAAMvG,cAAcC,iBAClB,CAACC,UAAAA;;AAAUA,6BAAMC,UAAUH,YAAYI,aAA5BF,mBAAuC,wBAAvCA,mBAA4DsG;GAAAA;AAGzE,aACE1E,wBAACC,KAAK0E,SAAO;IAACzG;IACZ,cAAA8B,wBAACtD,yBAAAA,CAAAA,CAAAA;;AAGP;", "names": ["ReviewWorkflowsListView", "formatMessage", "useIntl", "navigate", "useNavigate", "trackUsage", "useTracking", "workflowToDelete", "setWorkflowToDelete", "useState", "showLimitModal", "setShowLimitModal", "data", "isLoading", "isLoadingModels", "useGetContentTypesQuery", "meta", "workflows", "delete", "deleteAction", "useReviewWorkflows", "getFeature", "isLicenseLoading", "useLicenseLimits", "permissions", "useTypedSelector", "state", "admin_app", "settings", "allowedActions", "canCreate", "canRead", "canUpdate", "canDelete", "useRBAC", "limits", "numberOfWorkflows", "CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME", "handleDeleteWorkflow", "workflowId", "toggleConfirmDeleteDialog", "handleConfirmDeleteDialog", "handleCreateClick", "event", "preventDefault", "workflowCount", "parseInt", "React", "useEffect", "headers", "label", "id", "defaultMessage", "name", "_jsx", "Page", "Loading", "contentTypes", "Object", "values", "reduce", "acc", "curr", "push", "_jsxs", "_Fragment", "Layout", "primaryAction", "LinkButton", "startIcon", "Plus", "size", "tag", "NavLink", "to", "onClick", "subtitle", "title", "Table", "Root", "rows", "footer", "TF<PERSON>er", "cursor", "icon", "Content", "Head", "map", "head", "<PERSON><PERSON><PERSON><PERSON>", "Body", "workflow", "Row", "Cell", "width", "Typography", "textColor", "fontWeight", "ellipsis", "stages", "length", "uid", "contentType", "find", "info", "displayName", "join", "Flex", "alignItems", "justifyContent", "IconButton", "Link", "toString", "variant", "Pencil", "withTooltip", "e", "stopPropagation", "String", "Trash", "Dialog", "open", "onOpenChange", "ConfirmDialog", "onConfirm", "LimitsModal", "Title", "ProtectedListPage", "main", "Protect"]}