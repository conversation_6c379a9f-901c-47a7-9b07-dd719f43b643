var e="Analytics",o="Documentazione",t="Email",i="Password",n="Provider",a="Reimposta Token Password",s="Ruolo",r="Nome utente",l="Utenti",p={Analytics:e,"Auth.components.Oops.text":"Il tuo account è stato sospeso","Auth.form.button.forgot-password":"Invia email","Auth.form.button.go-home":"TORNA ALLA HOME","Auth.form.button.login":"Accedi","Auth.form.button.register":"Inizia adesso","Auth.form.confirmPassword.label":"Conferma Password","Auth.form.email.label":"Email","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"Il tuo account è stato bloccato dall'amministratore.","Auth.form.error.code.provide":"Codice fornito non corretto.","Auth.form.error.confirmed":"L'email del tuo account non è stata confermata.","Auth.form.error.email.invalid":"Questa email non è valida.","Auth.form.error.email.provide":"Per favore inserisci il tuo nome utente o la tua email.","Auth.form.error.email.taken":"Email già utilizzata.","Auth.form.error.invalid":"Identificatore o password non valida.","Auth.form.error.params.provide":"I parametri forniti non sono corretti.","Auth.form.error.password.format":"La tua password non può contenere il simbolo `$` per più di tre volte.","Auth.form.error.password.local":"Questo utente non ha mai impostato una password locale, accedi gentilmente tramite il provider usato durante la creazione dell'account","Auth.form.error.password.matching":"La password non corrisponde.","Auth.form.error.password.provide":"Per favore fornisci la tua password","Auth.form.error.ratelimit":"Troppi tentativi, riprova tra un minuto.","Auth.form.error.user.not-exist":"Questa email non esiste.","Auth.form.error.username.taken":"Nome utente già utilizzato.","Auth.form.firstname.label":"Nome","Auth.form.firstname.placeholder":"Kai","Auth.form.forgot-password.email.label":"Inserisci la tua email","Auth.form.forgot-password.email.label.success":"Email inviata correttamente","Auth.form.lastname.label":"Cognome","Auth.form.lastname.placeholder":"Doe","Auth.form.register.news.label":"Tienimi aggiornato in merito a nuove funzionalità e futuri sviluppi (così facendo accetti {terms} e {policy}).","Auth.form.rememberMe.label":"Ricordami","Auth.form.username.label":"Nome utente","Auth.form.username.placeholder":"Kai Doe","Auth.link.forgot-password":"Password dimenticata?","Auth.link.ready":"Sei pronto per accedere?","Auth.link.signin":"Accedi","Auth.link.signin.account":"Hai già un account?","Auth.privacy-policy-agreement.policy":"privacy policy","Auth.privacy-policy-agreement.terms":"termini","Content Manager":"Gestione Contenuti","Content Type Builder":"Content-Types Builder",Documentation:o,Email:t,"Files Upload":"Caricamento Files","HomePage.head.title":"Homepage","HomePage.roadmap":"Guarda la nostra roadmap","HomePage.welcome.congrats":"Congratulazioni!","HomePage.welcome.congrats.content":"Ti sei loggato come primo amministratore. Per scoprire le funzionalità di Strapi,","HomePage.welcome.congrats.content.bold":"ti consigliamo di creare la tua prima Collezione.","Media Library":"Libreria media","New entry":"Nuovo elemento",Password:i,Provider:n,ResetPasswordToken:a,Role:s,"Roles & Permissions":"Ruoli e permessi","Roles.ListPage.notification.delete-all-not-allowed":"Alcuni ruoli non possono essere eleminati poiché sono associati agli utenti","Roles.ListPage.notification.delete-not-allowed":"Un ruolo non può essere eliminato se associato ad utenti","Roles.components.List.empty.withSearch":"Nessun ruolo corrisponde alla ricerca ({search})...","Settings.PageTitle":"Impostazioni - {name}","Settings.application.description":"Vedi i dettagli del tuo progetto","Settings.application.edition-title":"Edizione attuale","Settings.application.link-pricing":"Vedi tutti i prezzi","Settings.application.link-upgrade":"Aggiorna il tuo progetto","Settings.application.node-version":"VERSIONE NODE","Settings.application.strapi-version":"VERSIONE STRAPI","Settings.application.title":"Applicazione","Settings.error":"Errore","Settings.global":"Impostazioni Globali","Settings.permissions":"Pannello di amministazione","Settings.permissions.category":"Impostazioni permessi per la categoria {category}","Settings.permissions.category.plugins":"Permissions settings for the {category} plugin","Settings.permissions.conditions.anytime":"In ogni momento","Settings.permissions.conditions.apply":"Applica","Settings.permissions.conditions.can":"Può","Settings.permissions.conditions.conditions":"Definisci le condizioni","Settings.permissions.conditions.links":"Link","Settings.permissions.conditions.no-actions":"Non ci sono azioni","Settings.permissions.conditions.or":"Oppure","Settings.permissions.conditions.when":"Quando","Settings.permissions.users.create":"Crea nuovo utente","Settings.permissions.users.email":"Email","Settings.permissions.users.firstname":"Nome","Settings.permissions.users.lastname":"Cognome","Settings.roles.create.description":"Definisci permessi del ruolo","Settings.roles.create.title":"Crea ruolo","Settings.roles.created":"Ruolo creato","Settings.roles.edit.title":"Modifica ruolo","Settings.roles.form.button.users-with-role":"Utenti con questo ruolo","Settings.roles.form.created":"Creato","Settings.roles.form.description":"Nome e descrizione ruolo","Settings.roles.form.permissions.attributesPermissions":"Permessi per i campi","Settings.roles.form.permissions.create":"Crea","Settings.roles.form.permissions.delete":"Elimina","Settings.roles.form.permissions.publish":"Pubblica","Settings.roles.form.permissions.read":"Leggi","Settings.roles.form.permissions.update":"Aggiorna","Settings.roles.list.button.add":"Aggiungi nuovo ruolo","Settings.roles.list.description":"Lista dei ruoli","Settings.roles.title.singular":"Ruolo","Settings.webhooks.create":"Crea un webhook","Settings.webhooks.create.header":"Crea un nuovo header","Settings.webhooks.created":"Webhook creato","Settings.webhooks.event.publish-tooltip":"Evento disponibile solo per contenuti con gestione stati Bozza/Pubblicazione abilitati","Settings.webhooks.events.create":"Crea","Settings.webhooks.events.update":"Aggiorna","Settings.webhooks.form.events":"Eventi","Settings.webhooks.form.headers":"Headers","Settings.webhooks.form.url":"Url","Settings.webhooks.key":"Chiave","Settings.webhooks.list.button.add":"Aggiungi nuovo webhook","Settings.webhooks.list.description":"Ricevi notifiche di cambiamenti in POST.","Settings.webhooks.list.empty.description":"Aggiungi il primo alla lista","Settings.webhooks.list.empty.link":"Leggi la documentazione","Settings.webhooks.list.empty.title":"Non ci sono webhooks","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooks","Settings.webhooks.trigger":"Trigger","Settings.webhooks.trigger.cancel":"Annulla trigger","Settings.webhooks.trigger.pending":"In corso…","Settings.webhooks.trigger.save":"Salva trigger","Settings.webhooks.trigger.success":"Successo!","Settings.webhooks.trigger.success.label":"Trigger eseguito","Settings.webhooks.trigger.test":"Test trigger","Settings.webhooks.trigger.title":"Salva prima di eseguire trigger","Settings.webhooks.value":"Valore",Username:r,Users:l,"Users & Permissions":"Utenti & Permessi","Users.components.List.empty":"Non ci sono utenti...","Users.components.List.empty.withFilters":"Nessun utente trovato con i filtri applicati...","Users.components.List.empty.withSearch":"Nessun utente corrisponde alla ricerca ({search})...","app.components.BlockLink.code":"Esempi di codice","app.components.Button.cancel":"Annulla","app.components.Button.reset":"Ripristina","app.components.ComingSoonPage.comingSoon":"In arrivo","app.components.DownloadInfo.download":"Download in corso...","app.components.DownloadInfo.text":"Potrebbe volerci un minuto. Grazie della pazienza.","app.components.EmptyAttributes.title":"Campi non ancora presenti.","app.components.HomePage.button.blog":"LEGGI DI PIÙ SUL BLOG","app.components.HomePage.community":"Trova la community sul web","app.components.HomePage.community.content":"Discuti con i membri del team, i contributori e gli sviluppatori tramite i nostri canali.","app.components.HomePage.create":"Crea il tuo primo Content-Type","app.components.HomePage.welcome":"Benvenuto a bordo!","app.components.HomePage.welcome.again":"Benvenuto ","app.components.HomePage.welcomeBlock.content":"Siamo felici di averti come membro della comunità. Siamo costantemente alla ricerca di feedback, quindi sentitevi liberi di inviarci messaggi diretti su ","app.components.HomePage.welcomeBlock.content.again":"Speriamo che tu stia facendo progressi sul tuo progetto ... Sentiti libero di leggere l'ultima novità riguardo Strapi. Stiamo dando il massimo per migliorare il prodotto in base al tuo feedback.","app.components.HomePage.welcomeBlock.content.issues":"problemi.","app.components.HomePage.welcomeBlock.content.raise":" o solleva ","app.components.ImgPreview.hint":"Trascina il tuo file in quest'area o {browse} un file da caricare.","app.components.ImgPreview.hint.browse":"cerca","app.components.InputFile.newFile":"Aggiungi nuovo file","app.components.InputFileDetails.open":"Apri in una nuova tab","app.components.InputFileDetails.originalName":"Nome originale:","app.components.InputFileDetails.remove":"Rimuovi questo file","app.components.InputFileDetails.size":"Dimensione:","app.components.InstallPluginPage.Download.description":"Il download e l'installazione del plugin potrebbero richiedere qualche secondo.","app.components.InstallPluginPage.Download.title":"Scaricando...","app.components.InstallPluginPage.description":"Estendi la tua app senza sforzi.","app.components.LeftMenuFooter.help":"Supporto","app.components.LeftMenuFooter.poweredBy":"Offerto da ","app.components.LeftMenuLinkContainer.collectionTypes":"Collezioni","app.components.LeftMenuLinkContainer.configuration":"Configurazioni","app.components.LeftMenuLinkContainer.general":"Generale","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Nessun plugin ancora installato","app.components.LeftMenuLinkContainer.plugins":"Plugins","app.components.LeftMenuLinkContainer.singleTypes":"Entità singole","app.components.ListPluginsPage.deletePlugin.description":"L'installazione del plugin potrebbe richiedere qualche secondo.","app.components.ListPluginsPage.deletePlugin.title":"Disinstalla","app.components.ListPluginsPage.description":"Lista dei plugin installati nel progetto.","app.components.ListPluginsPage.head.title":"Lista plugin","app.components.Logout.logout":"Disconnetti","app.components.Logout.profile":"Profilo","app.components.NotFoundPage.back":"Torna alla home","app.components.NotFoundPage.description":"Non trovato","app.components.Official":"Ufficiale","app.components.Onboarding.label.completed":"% completato","app.components.Onboarding.title":"Video di introduzione","app.components.PluginCard.Button.label.download":"Download","app.components.PluginCard.Button.label.install":"Già installato","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"La funzione autoReload necessità di essere abilitata. Per favore, avvia la app con il comando `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Ho capito!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Per ragioni di sicurezza, il plugin puo essere scaricato solo in ambiente di sviluppo.","app.components.PluginCard.PopUpWarning.install.impossible.title":"Impossibile scaricare","app.components.PluginCard.compatible":"Compatibile con la tua app","app.components.PluginCard.compatibleCommunity":"Compatibile con la comunità","app.components.PluginCard.more-details":"Più dettagli","app.components.Users.MagicLink.connect":"Invia link all'utente per connettersi.","app.components.Users.ModalCreateBody.block-title.details":"Dettagli","app.components.Users.ModalCreateBody.block-title.roles":"Ruoli utente","app.components.Users.SortPicker.button-label":"Ordina per","app.components.Users.SortPicker.sortby.email_asc":"Email (A - Z)","app.components.Users.SortPicker.sortby.email_desc":"Email (Z - A)","app.components.Users.SortPicker.sortby.firstname_asc":"Noma (A - Z)","app.components.Users.SortPicker.sortby.firstname_desc":"Nome (Z - A)","app.components.Users.SortPicker.sortby.lastname_asc":"Cognome (A - Z)","app.components.Users.SortPicker.sortby.lastname_desc":"Cognome (Z - A)","app.components.Users.SortPicker.sortby.username_asc":"Nome utente (A - Z)","app.components.Users.SortPicker.sortby.username_desc":"Nome utente (Z - A)","app.components.listPlugins.button":"Aggiungi nuovo plugin","app.components.listPlugins.title.none":"Nessun plugin installato","app.components.listPluginsPage.deletePlugin.error":"Si è verificato un errore durante l'installazione del plugin","app.containers.App.notification.error.init":"Si è verificato un errore durante la richiesta dell'API","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"Se non ricevi questo link, contatta l'amministratore.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"La ricezione del link per reimpostare la password potrebbere richiedere qualche secondo.","app.containers.AuthPage.ForgotPasswordSuccess.title":"Email inviata","app.containers.Users.EditPage.form.active.label":"Attivo","app.containers.Users.EditPage.header.label":"Modifica {name}","app.containers.Users.EditPage.header.label-loading":"Modifica utente","app.containers.Users.EditPage.roles-bloc-title":"Ruoli assegnati","app.containers.Users.ModalForm.footer.button-success":"Crea utente","app.links.configure-view":"Configura la visualizzazione","app.static.links.cheatsheet":"CheatSheet","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Aggiungi filtro","app.utils.defaultMessage":" ","app.utils.errors.file-too-big.message":"Dimensioni file troppo grandi","app.utils.filters":"Filtri","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Pubblica","app.utils.select-all":"Seleziona tutti","app.utils.unpublish":"Converti in bozza","component.Input.error.validation.integer":"Il valore deve essere un intero","components.AutoReloadBlocker.description":"Avvia Strapi con uno dei seguenti comandi:","components.AutoReloadBlocker.header":"Ricarica funzionalità è richiesto per questo plugin.","components.ErrorBoundary.title":"Qualcosa è andato storto...","components.Input.error.attribute.key.taken":"Valore già esistente","components.Input.error.attribute.sameKeyAndName":"Non può essere uguale","components.Input.error.attribute.taken":"Nome campo già esistente","components.Input.error.contain.lowercase":"Password deve contenere almeno una carattere minuscolo","components.Input.error.contain.number":"Password deve contenere almeno un numero","components.Input.error.contain.uppercase":"Password deve contenere almeno una carattere maiuscolo","components.Input.error.contentTypeName.taken":"Nome già esistente","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"La password non corrisponde","components.Input.error.validation.email":"Non è un'email","components.Input.error.validation.json":"Formato JSON non corrisponde","components.Input.error.validation.max":"Valore troppo alto {max}.","components.Input.error.validation.maxLength":"Valore troppo lungo {max}.","components.Input.error.validation.min":"Valore troppo basso {min}.","components.Input.error.validation.minLength":"Valore troppo corto {min}.","components.Input.error.validation.minSupMax":"Non può essere superiore","components.Input.error.validation.regex":"Questo valore non coincide con la regex.","components.Input.error.validation.required":"Valore obbligatorio.","components.Input.error.validation.unique":"Questo valore è già usato","components.InputSelect.option.placeholder":"Seleziona","components.ListRow.empty":"Non ci sono dati da mostrare.","components.OverlayBlocker.description":"Stai utilizzando una funzionalità che necessita del riavvio del server. Per favore, attendi che il server ritorni attivo.","components.OverlayBlocker.description.serverError":"Il server deve essere riavviato, per favore controlla i tuoi log nel terminale.","components.OverlayBlocker.title":"Attendo il riavvio...","components.OverlayBlocker.title.serverError":"Il riavvio sta impiegando più del previsto","components.PageFooter.select":"elementi per pagina","components.ProductionBlocker.description":"Per ragioni di sicurezza dobbiamo disabilitare questo plugin in altri ambienti.","components.ProductionBlocker.header":"Questo plugin è disponibile solo in sviluppo.","components.Search.placeholder":"Cerca...","components.Wysiwyg.collapse":"Chiudi","components.Wysiwyg.selectOptions.H1":"Titolo H1","components.Wysiwyg.selectOptions.H2":"Titolo H2","components.Wysiwyg.selectOptions.H3":"Titolo H3","components.Wysiwyg.selectOptions.H4":"Titolo H4","components.Wysiwyg.selectOptions.H5":"Titolo H5","components.Wysiwyg.selectOptions.H6":"Titolo H6","components.Wysiwyg.selectOptions.title":"Aggiungi un titolo","components.WysiwygBottomControls.charactersIndicators":"caratteri","components.WysiwygBottomControls.fullscreen":"Espandi","components.WysiwygBottomControls.uploadFiles":"Trascina & rilascia file, incolla dagli appunti o {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"selezionali","components.popUpWarning.button.cancel":"No, annulla","components.popUpWarning.button.confirm":"Sì, conferma","components.popUpWarning.message":"Sei sicuro di volerlo cancellare?","components.popUpWarning.title":"Conferma richiesta","form.button.done":"Fatto","global.prompt.unsaved":"Sei sicuro di voler lasciare questa pagina? Tutte le modifiche effettuate verranno perse.","notification.contentType.relations.conflict":"Questo Tipo di Contenuto ha delle relazioni in conflitto","notification.error":"Si è verificato un errore","notification.error.layout":"Non è stato possibile recuperare il layout","notification.form.error.fields":"Il form contiene degli errori","notification.form.success.fields":"Modifiche salvate","notification.link-copied":"Link copiato negli appunti","notification.permission.not-allowed-read":"Non sei abilitato a visualizzare questo documento","notification.success.delete":"L'elemento è stato eliminato","notification.success.saved":"Salvato","notification.version.update.message":"Una nuova versione di Strapi è disponibile!","request.error.model.unknown":"Modello inesistente"};export{e as Analytics,o as Documentation,t as Email,i as Password,n as Provider,a as ResetPasswordToken,s as Role,r as Username,l as Users,p as default};
