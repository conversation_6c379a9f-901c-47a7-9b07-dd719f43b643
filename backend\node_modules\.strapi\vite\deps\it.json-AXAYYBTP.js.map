{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/it.json.mjs"], "sourcesContent": ["var it = {\n    \"BoundRoute.title\": \"Vincola route a\",\n    \"EditForm.inputSelect.description.role\": \"Questa operazione assocerà i nuovi utenti autenticati al ruolo selezionato.\",\n    \"EditForm.inputSelect.label.role\": \"Ruolo di default per gli utenti registrati\",\n    \"EditForm.inputToggle.description.email\": \"Non consentire all'utente di creare account multipli usando lo stesso indirizzo email con provider di autenticazione diversi.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"Quando abilitato (ON), i nuovi utenti registrati riceveranno una richiesta di conferma via email.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Scegli dove redirigere gli utenti che completano la conferma dell'indirizzo email.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"URL della pagina per il reset della password della tua applicazione\",\n    \"EditForm.inputToggle.description.sign-up\": \"<PERSON>uando disabilitata (OFF), il processo di registrazione è proibito. Nessuno può iscriversi indipendentemente dal provider utilizzato.\",\n    \"EditForm.inputToggle.label.email\": \"Un solo account per indirizzo email\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Abilita conferma email\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"URL di reindirizzamento\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"Pagina reset password\",\n    \"EditForm.inputToggle.label.sign-up\": \"Abilita registrazione\",\n    \"Email.template.email_confirmation\": \"Conferma dell'indirizzo Email\",\n    \"HeaderNav.link.advancedSettings\": \"Impostazioni avanzate\",\n    \"HeaderNav.link.emailTemplates\": \"Template delle Email\",\n    \"HeaderNav.link.providers\": \"Provider\",\n    \"Plugin.permissions.plugins.description\": \"Definisce tutte le azioni consentite per il plugin {name}.\",\n    \"Plugins.header.description\": \"Di seguito sono elencate solo le azioni vincolate da una route.\",\n    \"Plugins.header.title\": \"Permessi\",\n    \"Policies.header.hint\": \"Seleziona le azioni dell'applicazione o del plugin e clicca sull'ingranaggio per mostrare il percorso corrispondente\",\n    \"Policies.header.title\": \"Impostazioni avanzate\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"Se non sai bene come usare le variabili, {link}\",\n    \"PopUpForm.Email.link.documentation\": \"controlla la nostra documentazione.\",\n    \"PopUpForm.Email.options.from.email.label\": \"Email del mittente\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Nome del mittente\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n    \"PopUpForm.Email.options.message.label\": \"Messaggio\",\n    \"PopUpForm.Email.options.object.label\": \"Oggetto\",\n    \"PopUpForm.Email.options.object.placeholder\": \"Conferma il tuo indirizzo email per %APP_NAME%\",\n    \"PopUpForm.Email.options.response_email.label\": \"Email di risposta\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"Se disabilitato, gli utenti non potranno usare questo provider.\",\n    \"PopUpForm.Providers.enabled.label\": \"Abilita\",\n    \"PopUpForm.Providers.key.label\": \"Client ID\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"L'URL di reindirizzamento per la tua app di front-end\",\n    \"PopUpForm.Providers.redirectURL.label\": \"L'URL di reindirizzamento da aggiungere nelle impostazioni dell'applicazione di {provider}\",\n    \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.subdomain.label\": \"Host URI (Sottodominio)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Modifica i Template Email\",\n    \"PopUpForm.header.edit.providers\": \"Modifica Provider\",\n    \"Settings.roles.deleted\": \"Ruolo eliminato\",\n    \"Settings.roles.edited\": \"Ruolo modificato\",\n    \"Settings.section-label\": \"Plugin Utenti & Permessi\",\n    \"notification.success.submit\": \"Impostazioni aggiornate\",\n    \"plugin.description.long\": \"Proteggi le tue API con un processo di autenticazione completo basato su JWT. Questo plugin è implementato con una strategia ACL che ti consente di gestire i permessi tra i gruppi di utenti.\",\n    \"plugin.description.short\": \"Proteggi le tue API con un processo di autenticazione completo basato su JWT\",\n    \"plugin.name\": \"Plugin Utenti & Permessi\",\n    \"popUpWarning.button.cancel\": \"Annulla\",\n    \"popUpWarning.button.confirm\": \"Conferma\",\n    \"popUpWarning.title\": \"Si prega di confermare\",\n    \"popUpWarning.warning.cancel\": \"Sei sicuro di voler annullare le tue modifiche?\"\n};\n\nexport { it as default };\n//# sourceMappingURL=it.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,+BAA+B;AACnC;", "names": []}