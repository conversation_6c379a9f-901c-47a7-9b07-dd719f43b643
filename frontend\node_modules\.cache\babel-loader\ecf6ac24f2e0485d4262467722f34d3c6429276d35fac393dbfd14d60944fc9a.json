{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\dfghj\\\\site\\\\frontend\\\\src\\\\components\\\\OpportuniteForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction OpportuniteForm() {\n  _s();\n  const [opportunite, setOpportunite] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetch(\"http://localhost:1337/api/opportunites?populate=*\").then(res => res.json()).then(data => {\n      if (data.data && data.data.length > 0) {\n        setOpportunite(data.data[0]); // ناخذ أول opportunite\n      }\n      setLoading(false);\n    }).catch(err => {\n      console.error(\"Fetch error:\", err);\n      setLoading(false);\n    });\n  }, []);\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 23\n  }, this);\n  if (!opportunite) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Aucune opportunit\\xE9 trouv\\xE9e.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 28\n  }, this);\n\n  // باش نستعمل attributes من opportunite\n  const attr = opportunite.attributes;\n\n  // Additional safety check\n  if (!attr) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Erreur: donn\\xE9es d'opportunit\\xE9 non disponibles.\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 21\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: [\"Objet: \", attr.objet]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Pays de destination: \", attr.pays_destination]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Date d\\xE9but: \", attr.date_debut]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Date fin: \", attr.date_fin]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Secteur: \", attr.secteur]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Importateur\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), attr.importateur && attr.importateur.data ? /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Societe: \", attr.importateur.data.attributes.societe]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Aucun importateur\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Exportateurs\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), attr.exportateurs && attr.exportateurs.data && attr.exportateurs.data.length > 0 ? attr.exportateurs.data.map(exp => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: exp.attributes.raison_sociale\n    }, exp.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 11\n    }, this)) : /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Aucun exportateur\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n}\n_s(OpportuniteForm, \"qZ8iRlquh9hBjsElIySvB3aUV40=\");\n_c = OpportuniteForm;\nexport default OpportuniteForm;\nvar _c;\n$RefreshReg$(_c, \"OpportuniteForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "OpportuniteForm", "_s", "opportunite", "setOpportunite", "loading", "setLoading", "fetch", "then", "res", "json", "data", "length", "catch", "err", "console", "error", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "attr", "attributes", "objet", "pays_destination", "date_debut", "date_fin", "secteur", "importateur", "societe", "exportateurs", "map", "exp", "raison_sociale", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/dfghj/site/frontend/src/components/OpportuniteForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\n\r\nfunction OpportuniteForm() {\r\n  const [opportunite, setOpportunite] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    fetch(\"http://localhost:1337/api/opportunites?populate=*\")\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        if (data.data && data.data.length > 0) {\r\n          setOpportunite(data.data[0]); // ناخذ أول opportunite\r\n        }\r\n        setLoading(false);\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Fetch error:\", err);\r\n        setLoading(false);\r\n      });\r\n  }, []);\r\n\r\n  if (loading) return <div>Loading...</div>;\r\n  if (!opportunite) return <div>Aucune opportunité trouvée.</div>;\r\n\r\n  // باش نستعمل attributes من opportunite\r\n  const attr = opportunite.attributes;\r\n\r\n  // Additional safety check\r\n  if (!attr) return <div>Erreur: données d'opportunité non disponibles.</div>;\r\n\r\n  return (\r\n    <div>\r\n      <h2>Objet: {attr.objet}</h2>\r\n      <p>Pays de destination: {attr.pays_destination}</p>\r\n      <p>Date début: {attr.date_debut}</p>\r\n      <p>Date fin: {attr.date_fin}</p>\r\n      <p>Secteur: {attr.secteur}</p>\r\n\r\n      <h3>Importateur</h3>\r\n      {attr.importateur && attr.importateur.data ? (\r\n        <p>Societe: {attr.importateur.data.attributes.societe}</p>\r\n      ) : (\r\n        <p>Aucun importateur</p>\r\n      )}\r\n\r\n      <h3>Exportateurs</h3>\r\n      {attr.exportateurs && attr.exportateurs.data && attr.exportateurs.data.length > 0 ? (\r\n        attr.exportateurs.data.map((exp) => (\r\n          <div key={exp.id}>{exp.attributes.raison_sociale}</div>\r\n        ))\r\n      ) : (\r\n        <p>Aucun exportateur</p>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default OpportuniteForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdS,KAAK,CAAC,mDAAmD,CAAC,CACvDC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MACd,IAAIA,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACrCR,cAAc,CAACO,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChC;MACAL,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDO,KAAK,CAAEC,GAAG,IAAK;MACdC,OAAO,CAACC,KAAK,CAAC,cAAc,EAAEF,GAAG,CAAC;MAClCR,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAEN,IAAID,OAAO,EAAE,oBAAOL,OAAA;IAAAiB,QAAA,EAAK;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EACzC,IAAI,CAAClB,WAAW,EAAE,oBAAOH,OAAA;IAAAiB,QAAA,EAAK;EAA2B;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;;EAE/D;EACA,MAAMC,IAAI,GAAGnB,WAAW,CAACoB,UAAU;;EAEnC;EACA,IAAI,CAACD,IAAI,EAAE,oBAAOtB,OAAA;IAAAiB,QAAA,EAAK;EAA8C;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAE3E,oBACErB,OAAA;IAAAiB,QAAA,gBACEjB,OAAA;MAAAiB,QAAA,GAAI,SAAO,EAACK,IAAI,CAACE,KAAK;IAAA;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eAC5BrB,OAAA;MAAAiB,QAAA,GAAG,uBAAqB,EAACK,IAAI,CAACG,gBAAgB;IAAA;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACnDrB,OAAA;MAAAiB,QAAA,GAAG,iBAAY,EAACK,IAAI,CAACI,UAAU;IAAA;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpCrB,OAAA;MAAAiB,QAAA,GAAG,YAAU,EAACK,IAAI,CAACK,QAAQ;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAChCrB,OAAA;MAAAiB,QAAA,GAAG,WAAS,EAACK,IAAI,CAACM,OAAO;IAAA;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE9BrB,OAAA;MAAAiB,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACnBC,IAAI,CAACO,WAAW,IAAIP,IAAI,CAACO,WAAW,CAAClB,IAAI,gBACxCX,OAAA;MAAAiB,QAAA,GAAG,WAAS,EAACK,IAAI,CAACO,WAAW,CAAClB,IAAI,CAACY,UAAU,CAACO,OAAO;IAAA;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,gBAE1DrB,OAAA;MAAAiB,QAAA,EAAG;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CACxB,eAEDrB,OAAA;MAAAiB,QAAA,EAAI;IAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACpBC,IAAI,CAACS,YAAY,IAAIT,IAAI,CAACS,YAAY,CAACpB,IAAI,IAAIW,IAAI,CAACS,YAAY,CAACpB,IAAI,CAACC,MAAM,GAAG,CAAC,GAC/EU,IAAI,CAACS,YAAY,CAACpB,IAAI,CAACqB,GAAG,CAAEC,GAAG,iBAC7BjC,OAAA;MAAAiB,QAAA,EAAmBgB,GAAG,CAACV,UAAU,CAACW;IAAc,GAAtCD,GAAG,CAACE,EAAE;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAsC,CACvD,CAAC,gBAEFrB,OAAA;MAAAiB,QAAA,EAAG;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CACxB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACnB,EAAA,CArDQD,eAAe;AAAAmC,EAAA,GAAfnC,eAAe;AAuDxB,eAAeA,eAAe;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}