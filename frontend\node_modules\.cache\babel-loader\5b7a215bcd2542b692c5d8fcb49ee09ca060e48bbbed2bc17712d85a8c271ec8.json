{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\dfghj\\\\site\\\\frontend\\\\src\\\\components\\\\ImportateurForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ImportateurForm() {\n  _s();\n  const [importateurs, setImportateurs] = useState([]);\n  const [formData, setFormData] = useState({\n    societe: \"\",\n    pays: \"\",\n    nom_responsable: \"\",\n    prenom_responsable: \"\",\n    telephone_whatsapp: \"\",\n    email: \"\",\n    region: \"\"\n  });\n\n  // Fetch all importateurs\n  useEffect(() => {\n    fetch(\"http://localhost:1337/api/importateurs?populate=*\").then(res => res.json()).then(data => setImportateurs(data.data)).catch(err => console.error(err));\n  }, []);\n\n  // Handle form input change\n  function handleChange(e) {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  }\n\n  // Submit new importateur\n  function handleSubmit(e) {\n    e.preventDefault();\n    fetch(\"http://localhost:1337/api/importateurs\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        data: formData\n      })\n    }).then(res => res.json()).then(data => {\n      setImportateurs([...importateurs, data.data]);\n      setFormData({\n        societe: \"\",\n        pays: \"\",\n        nom_responsable: \"\",\n        prenom_responsable: \"\",\n        telephone_whatsapp: \"\",\n        email: \"\",\n        region: \"\"\n      });\n    }).catch(err => console.error(err));\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Liste des Importateurs\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: importateurs.map(imp => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: imp.attributes.societe\n      }, imp.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Ajouter un Importateur\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"societe\",\n        placeholder: \"Soci\\xE9t\\xE9\",\n        value: formData.societe,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"pays\",\n        placeholder: \"Pays\",\n        value: formData.pays,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"nom_responsable\",\n        placeholder: \"Nom du responsable\",\n        value: formData.nom_responsable,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"prenom_responsable\",\n        placeholder: \"Pr\\xE9nom du responsable\",\n        value: formData.prenom_responsable,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"telephone_whatsapp\",\n        placeholder: \"T\\xE9l\\xE9phone WhatsApp\",\n        value: formData.telephone_whatsapp,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"email\",\n        placeholder: \"Email\",\n        type: \"email\",\n        value: formData.email,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"region\",\n        placeholder: \"R\\xE9gion\",\n        value: formData.region,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Ajouter\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n}\n_s(ImportateurForm, \"rXusMj+woPQUBUSuS4/h9wV5R/4=\");\n_c = ImportateurForm;\nvar _c;\n$RefreshReg$(_c, \"ImportateurForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ImportateurForm", "_s", "importateurs", "setImportateurs", "formData", "setFormData", "societe", "pays", "nom_responsable", "prenom_responsable", "telephone_whatsapp", "email", "region", "fetch", "then", "res", "json", "data", "catch", "err", "console", "error", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "method", "headers", "body", "JSON", "stringify", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "imp", "attributes", "id", "onSubmit", "placeholder", "onChange", "required", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/dfghj/site/frontend/src/components/ImportateurForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\n\r\nexport default function ImportateurForm() {\r\n  const [importateurs, setImportateurs] = useState([]);\r\n  const [formData, setFormData] = useState({\r\n    societe: \"\",\r\n    pays: \"\",\r\n    nom_responsable: \"\",\r\n    prenom_responsable: \"\",\r\n    telephone_whatsapp: \"\",\r\n    email: \"\",\r\n    region: \"\",\r\n  });\r\n\r\n  // Fetch all importateurs\r\n  useEffect(() => {\r\n    fetch(\"http://localhost:1337/api/importateurs?populate=*\")\r\n      .then((res) => res.json())\r\n      .then((data) => setImportateurs(data.data))\r\n      .catch((err) => console.error(err));\r\n  }, []);\r\n\r\n  // Handle form input change\r\n  function handleChange(e) {\r\n    setFormData({ ...formData, [e.target.name]: e.target.value });\r\n  }\r\n\r\n  // Submit new importateur\r\n  function handleSubmit(e) {\r\n    e.preventDefault();\r\n\r\n    fetch(\"http://localhost:1337/api/importateurs\", {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      body: JSON.stringify({ data: formData }),\r\n    })\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        setImportateurs([...importateurs, data.data]);\r\n        setFormData({\r\n          societe: \"\",\r\n          pays: \"\",\r\n          nom_responsable: \"\",\r\n          prenom_responsable: \"\",\r\n          telephone_whatsapp: \"\",\r\n          email: \"\",\r\n          region: \"\",\r\n        });\r\n      })\r\n      .catch((err) => console.error(err));\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <h2>Liste des Importateurs</h2>\r\n      <ul>\r\n        {importateurs.map((imp) => (\r\n          <li key={imp.id}>{imp.attributes.societe}</li>\r\n        ))}\r\n      </ul>\r\n\r\n      <h3>Ajouter un Importateur</h3>\r\n      <form onSubmit={handleSubmit}>\r\n        <input\r\n          name=\"societe\"\r\n          placeholder=\"Société\"\r\n          value={formData.societe}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"pays\"\r\n          placeholder=\"Pays\"\r\n          value={formData.pays}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"nom_responsable\"\r\n          placeholder=\"Nom du responsable\"\r\n          value={formData.nom_responsable}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"prenom_responsable\"\r\n          placeholder=\"Prénom du responsable\"\r\n          value={formData.prenom_responsable}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"telephone_whatsapp\"\r\n          placeholder=\"Téléphone WhatsApp\"\r\n          value={formData.telephone_whatsapp}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"email\"\r\n          placeholder=\"Email\"\r\n          type=\"email\"\r\n          value={formData.email}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"region\"\r\n          placeholder=\"Région\"\r\n          value={formData.region}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <button type=\"submit\">Ajouter</button>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACxC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,eAAe,EAAE,EAAE;IACnBC,kBAAkB,EAAE,EAAE;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACAf,SAAS,CAAC,MAAM;IACdgB,KAAK,CAAC,mDAAmD,CAAC,CACvDC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAKd,eAAe,CAACc,IAAI,CAACA,IAAI,CAAC,CAAC,CAC1CC,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC;EACvC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,SAASG,YAAYA,CAACC,CAAC,EAAE;IACvBlB,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACmB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EAC/D;;EAEA;EACA,SAASC,YAAYA,CAACJ,CAAC,EAAE;IACvBA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElBf,KAAK,CAAC,wCAAwC,EAAE;MAC9CgB,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEhB,IAAI,EAAEb;MAAS,CAAC;IACzC,CAAC,CAAC,CACCU,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MACdd,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAEe,IAAI,CAACA,IAAI,CAAC,CAAC;MAC7CZ,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,IAAI,EAAE,EAAE;QACRC,eAAe,EAAE,EAAE;QACnBC,kBAAkB,EAAE,EAAE;QACtBC,kBAAkB,EAAE,EAAE;QACtBC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,CACDM,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC;EACvC;EAEA,oBACEpB,OAAA;IAAAmC,QAAA,gBACEnC,OAAA;MAAAmC,QAAA,EAAI;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/BvC,OAAA;MAAAmC,QAAA,EACGhC,YAAY,CAACqC,GAAG,CAAEC,GAAG,iBACpBzC,OAAA;QAAAmC,QAAA,EAAkBM,GAAG,CAACC,UAAU,CAACnC;MAAO,GAA/BkC,GAAG,CAACE,EAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA8B,CAC9C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAELvC,OAAA;MAAAmC,QAAA,EAAI;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/BvC,OAAA;MAAM4C,QAAQ,EAAEhB,YAAa;MAAAO,QAAA,gBAC3BnC,OAAA;QACE0B,IAAI,EAAC,SAAS;QACdmB,WAAW,EAAC,eAAS;QACrBlB,KAAK,EAAEtB,QAAQ,CAACE,OAAQ;QACxBuC,QAAQ,EAAEvB,YAAa;QACvBwB,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFvC,OAAA;QACE0B,IAAI,EAAC,MAAM;QACXmB,WAAW,EAAC,MAAM;QAClBlB,KAAK,EAAEtB,QAAQ,CAACG,IAAK;QACrBsC,QAAQ,EAAEvB,YAAa;QACvBwB,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFvC,OAAA;QACE0B,IAAI,EAAC,iBAAiB;QACtBmB,WAAW,EAAC,oBAAoB;QAChClB,KAAK,EAAEtB,QAAQ,CAACI,eAAgB;QAChCqC,QAAQ,EAAEvB,YAAa;QACvBwB,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFvC,OAAA;QACE0B,IAAI,EAAC,oBAAoB;QACzBmB,WAAW,EAAC,0BAAuB;QACnClB,KAAK,EAAEtB,QAAQ,CAACK,kBAAmB;QACnCoC,QAAQ,EAAEvB,YAAa;QACvBwB,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFvC,OAAA;QACE0B,IAAI,EAAC,oBAAoB;QACzBmB,WAAW,EAAC,0BAAoB;QAChClB,KAAK,EAAEtB,QAAQ,CAACM,kBAAmB;QACnCmC,QAAQ,EAAEvB,YAAa;QACvBwB,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFvC,OAAA;QACE0B,IAAI,EAAC,OAAO;QACZmB,WAAW,EAAC,OAAO;QACnBG,IAAI,EAAC,OAAO;QACZrB,KAAK,EAAEtB,QAAQ,CAACO,KAAM;QACtBkC,QAAQ,EAAEvB,YAAa;QACvBwB,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFvC,OAAA;QACE0B,IAAI,EAAC,QAAQ;QACbmB,WAAW,EAAC,WAAQ;QACpBlB,KAAK,EAAEtB,QAAQ,CAACQ,MAAO;QACvBiC,QAAQ,EAAEvB,YAAa;QACvBwB,QAAQ;MAAA;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFvC,OAAA;QAAQgD,IAAI,EAAC,QAAQ;QAAAb,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACrC,EAAA,CAnHuBD,eAAe;AAAAgD,EAAA,GAAfhD,eAAe;AAAA,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}