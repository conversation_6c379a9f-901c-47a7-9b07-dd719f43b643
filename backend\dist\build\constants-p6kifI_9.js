import{V as h,a as u,b5 as c,j as s,aB as x,aD as p,aE as g,aF as j,bU as f,aG as v,$ as t,T as a,e,L as m,D as w,bH as L,au as b,k as C,b7 as S}from"./strapi-z7ApxZZq.js";import{S as k}from"./SSOProviders-CURKLlPV.js";const y=()=>{const d=h(),{formatMessage:i}=u(),{isLoading:n,data:r=[]}=c(void 0,{skip:!window.strapi.features.isEnabled(window.strapi.features.SSO)}),l=()=>{d("/auth/login")};return!window.strapi.features.isEnabled(window.strapi.features.SSO)||!n&&r.length===0?s.jsx(x,{to:"/auth/login"}):s.jsx(p,{children:s.jsxs(g,{children:[s.jsxs(j,{children:[s.jsxs(f,{children:[s.jsx(v,{}),s.jsx(t,{paddingTop:6,paddingBottom:1,children:s.jsx(a,{tag:"h1",variant:"alpha",children:i({id:"Auth.form.welcome.title"})})}),s.jsx(t,{paddingBottom:7,children:s.jsx(a,{variant:"epsilon",textColor:"neutral600",children:i({id:"Auth.login.sso.subtitle"})})})]}),s.jsxs(e,{direction:"column",alignItems:"stretch",gap:7,children:[n?s.jsx(e,{justifyContent:"center",children:s.jsx(m,{children:i({id:"Auth.login.sso.loading"})})}):s.jsx(k,{providers:r}),s.jsxs(e,{children:[s.jsx(o,{}),s.jsx(t,{paddingLeft:3,paddingRight:3,children:s.jsx(a,{variant:"sigma",textColor:"neutral600",children:i({id:"or"})})}),s.jsx(o,{})]}),s.jsx(w,{fullWidth:!0,size:"L",onClick:l,children:i({id:"Auth.form.button.login.strapi"})})]})]}),s.jsx(e,{justifyContent:"center",children:s.jsx(t,{paddingTop:4,children:s.jsx(L,{tag:b,to:"/auth/forgot-password",children:s.jsx(a,{variant:"pi",children:i({id:"Auth.link.forgot-password"})})})})})]})})},o=C(S)`
  flex: 1;
`,D={providers:y};export{D as FORMS};
