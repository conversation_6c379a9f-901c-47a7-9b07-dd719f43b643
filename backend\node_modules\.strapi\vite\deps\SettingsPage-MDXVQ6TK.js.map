{"version": 3, "sources": ["../../../@strapi/i18n/admin/src/utils/baseQuery.ts", "../../../@strapi/i18n/admin/src/components/CreateLocale.tsx", "../../../@strapi/i18n/admin/src/components/DeleteLocale.tsx", "../../../@strapi/i18n/admin/src/components/EditLocale.tsx", "../../../@strapi/i18n/admin/src/components/LocaleTable.tsx", "../../../@strapi/i18n/admin/src/pages/SettingsPage.tsx"], "sourcesContent": ["import { SerializedError } from '@reduxjs/toolkit';\nimport { type ApiError, type UnknownApiError } from '@strapi/admin/strapi-admin';\n\ntype BaseQueryError = ApiError | UnknownApiError | SerializedError;\n\nconst isBaseQueryError = (error: BaseQueryError): error is ApiError | UnknownApiError => {\n  return error.name !== undefined;\n};\n\nexport { isBaseQueryError };\n", "import * as React from 'react';\n\nimport {\n  Form,\n  type InputProps,\n  Input<PERSON>enderer,\n  useField,\n  type FormHelpers,\n  useForm,\n  useAP<PERSON><PERSON>r<PERSON>and<PERSON>,\n  useNotification,\n  useAuth,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Box,\n  Button,\n  ButtonProps,\n  Divider,\n  Field,\n  Flex,\n  Grid,\n  Modal,\n  SingleSelect,\n  SingleSelectOption,\n  Tabs,\n  Typography,\n  useId,\n} from '@strapi/design-system';\nimport { Check, Plus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport * as yup from 'yup';\n\nimport { CreateLocale } from '../../../shared/contracts/locales';\nimport { useCreateLocaleMutation, useGetDefaultLocalesQuery } from '../services/locales';\nimport { isBaseQueryError } from '../utils/baseQuery';\nimport { getTranslation } from '../utils/getTranslation';\n\n/* -------------------------------------------------------------------------------------------------\n * CreateLocale\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CreateLocaleProps extends Pick<ButtonProps, 'disabled' | 'variant'> {}\n\nconst CreateLocale = ({ disabled, variant = 'default' }: CreateLocaleProps) => {\n  const { formatMessage } = useIntl();\n  const [visible, setVisible] = React.useState(false);\n\n  return (\n    <Modal.Root open={visible} onOpenChange={setVisible}>\n      <Modal.Trigger>\n        <Button\n          variant={variant}\n          disabled={disabled}\n          startIcon={<Plus />}\n          onClick={() => setVisible(true)}\n          size=\"S\"\n        >\n          {formatMessage({\n            id: getTranslation('Settings.list.actions.add'),\n            defaultMessage: 'Add new locale',\n          })}\n        </Button>\n      </Modal.Trigger>\n      <CreateModal onClose={() => setVisible(false)} />\n    </Modal.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * CreateModal\n * -----------------------------------------------------------------------------------------------*/\n\nconst LOCALE_SCHEMA = yup.object().shape({\n  code: yup.string().nullable().required({\n    id: 'Settings.locales.modal.create.code.error',\n    defaultMessage: 'Please select a locale',\n  }),\n  name: yup\n    .string()\n    .nullable()\n    .max(50, {\n      id: 'Settings.locales.modal.create.name.error.min',\n      defaultMessage: 'The locale display name can only be less than 50 characters.',\n    })\n    .required({\n      id: 'Settings.locales.modal.create.name.error.required',\n      defaultMessage: 'Please give the locale a display name',\n    }),\n  isDefault: yup.boolean(),\n});\n\ntype FormValues = CreateLocale.Request['body'];\n\nconst initialFormValues = {\n  code: '',\n  name: '',\n  isDefault: false,\n} satisfies FormValues;\n\ntype ModalCreateProps = {\n  onClose: () => void;\n};\n\nconst CreateModal = ({ onClose }: ModalCreateProps) => {\n  const titleId = useId();\n  const { toggleNotification } = useNotification();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n  const [createLocale] = useCreateLocaleMutation();\n  const { formatMessage } = useIntl();\n  const refetchPermissions = useAuth('CreateModal', (state) => state.refetchPermissions);\n\n  const handleSubmit = async (values: FormValues, helpers: FormHelpers<FormValues>) => {\n    try {\n      const res = await createLocale(values);\n\n      if ('error' in res) {\n        if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          helpers.setErrors(formatValidationErrors(res.error));\n        } else {\n          toggleNotification({ type: 'danger', message: formatAPIError(res.error) });\n        }\n\n        return;\n      }\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTranslation('Settings.locales.modal.create.success'),\n          defaultMessage: 'Created locale',\n        }),\n      });\n\n      refetchPermissions();\n      onClose();\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred, please try again',\n        }),\n      });\n    }\n  };\n\n  return (\n    <Modal.Content>\n      <Form\n        method=\"POST\"\n        initialValues={initialFormValues}\n        validationSchema={LOCALE_SCHEMA}\n        onSubmit={handleSubmit}\n      >\n        <Modal.Header>\n          <Modal.Title>\n            {formatMessage({\n              id: getTranslation('Settings.list.actions.add'),\n              defaultMessage: 'Add new locale',\n            })}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Tabs.Root variant=\"simple\" defaultValue=\"basic\">\n            <Flex justifyContent=\"space-between\">\n              <Typography tag=\"h2\" variant=\"beta\" id={titleId}>\n                {formatMessage({\n                  id: getTranslation('Settings.locales.modal.title'),\n                  defaultMessage: 'Configuration',\n                })}\n              </Typography>\n              <Tabs.List aria-labelledby={titleId}>\n                <Tabs.Trigger value=\"basic\">\n                  {formatMessage({\n                    id: getTranslation('Settings.locales.modal.base'),\n                    defaultMessage: 'Basic settings',\n                  })}\n                </Tabs.Trigger>\n                <Tabs.Trigger value=\"advanced\">\n                  {formatMessage({\n                    id: getTranslation('Settings.locales.modal.advanced'),\n                    defaultMessage: 'Advanced settings',\n                  })}\n                </Tabs.Trigger>\n              </Tabs.List>\n            </Flex>\n\n            <Divider />\n\n            <Box paddingTop={7} paddingBottom={7}>\n              <Tabs.Content value=\"basic\">\n                <BaseForm />\n              </Tabs.Content>\n              <Tabs.Content value=\"advanced\">\n                <AdvancedForm />\n              </Tabs.Content>\n            </Box>\n          </Tabs.Root>\n        </Modal.Body>\n        <Modal.Footer>\n          <Modal.Close>\n            <Button variant=\"tertiary\">\n              {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n            </Button>\n          </Modal.Close>\n          <SubmitButton />\n        </Modal.Footer>\n      </Form>\n    </Modal.Content>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * SubmitButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst SubmitButton = () => {\n  const { formatMessage } = useIntl();\n  const isSubmitting = useForm('SubmitButton', (state) => state.isSubmitting);\n  const modified = useForm('SubmitButton', (state) => state.modified);\n\n  return (\n    <Button type=\"submit\" startIcon={<Check />} disabled={isSubmitting || !modified}>\n      {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n    </Button>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * BaseForm\n * -----------------------------------------------------------------------------------------------*/\n\ninterface BaseFormProps {\n  mode?: 'create' | 'edit';\n}\n\nconst BaseForm = ({ mode = 'create' }: BaseFormProps) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { data: defaultLocales, error } = useGetDefaultLocalesQuery();\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  if (!Array.isArray(defaultLocales)) {\n    return null;\n  }\n\n  const options = defaultLocales.map((locale) => ({\n    label: locale.name,\n    value: locale.code,\n  }));\n\n  const translatedForm = [\n    {\n      disabled: mode !== 'create',\n      label: {\n        id: getTranslation('Settings.locales.modal.create.code.label'),\n        defaultMessage: 'Locales',\n      },\n      name: 'code',\n      options,\n      placeholder: {\n        id: 'components.placeholder.select',\n        defaultMessage: 'Select',\n      },\n      required: true,\n      size: 6,\n      type: 'enumeration' as const,\n    },\n    {\n      hint: {\n        id: getTranslation('Settings.locales.modal.create.name.label.description'),\n        defaultMessage: 'Locale will be displayed under that name in the administration panel',\n      },\n      label: {\n        id: getTranslation('Settings.locales.modal.create.name.label'),\n        defaultMessage: 'Locale display name',\n      },\n      name: 'name',\n      required: true,\n      size: 6,\n      type: 'string' as const,\n    },\n  ].map((field) => ({\n    ...field,\n    hint: field.hint ? formatMessage(field.hint) : undefined,\n    label: formatMessage(field.label),\n    placeholder: field.placeholder ? formatMessage(field.placeholder) : undefined,\n  }));\n\n  return (\n    <Grid.Root gap={4}>\n      {translatedForm.map(({ size, ...field }) => (\n        <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n          <FormRenderer {...field} />\n        </Grid.Item>\n      ))}\n    </Grid.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * AdvancedForm\n * -----------------------------------------------------------------------------------------------*/\n\ntype AdvancedFormProps = {\n  isDefaultLocale?: boolean;\n};\n\nconst AdvancedForm = ({ isDefaultLocale }: AdvancedFormProps) => {\n  const { formatMessage } = useIntl();\n\n  const form = [\n    {\n      disabled: isDefaultLocale,\n      hint: {\n        id: getTranslation('Settings.locales.modal.advanced.setAsDefault.hint'),\n        defaultMessage: 'One default locale is required, change it by selecting another one',\n      },\n      label: {\n        id: getTranslation('Settings.locales.modal.advanced.setAsDefault'),\n        defaultMessage: 'Set as default locale',\n      },\n      name: 'isDefault',\n      size: 6,\n      type: 'boolean' as const,\n    },\n  ].map((field) => ({\n    ...field,\n    hint: field.hint ? formatMessage(field.hint) : undefined,\n    label: formatMessage(field.label),\n  })) satisfies InputProps[];\n\n  return (\n    <Grid.Root gap={4}>\n      {form.map(({ size, ...field }) => (\n        <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n          <FormRenderer {...field} />\n        </Grid.Item>\n      ))}\n    </Grid.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * FormRenderer\n * -----------------------------------------------------------------------------------------------*/\n\nconst FormRenderer = (field: InputProps) => {\n  switch (field.type) {\n    /**\n     * This will override the default input renderer\n     * choice for `enumeration`.\n     */\n    case 'enumeration':\n      return <EnumerationInput {...field} />;\n    default:\n      return <InputRenderer {...field} />;\n  }\n};\n\nconst EnumerationInput = ({\n  disabled,\n  hint,\n  label,\n  name,\n  options,\n  placeholder,\n  required,\n}: Extract<InputProps, { type: 'enumeration' }>) => {\n  const { value, error, onChange } = useField(name);\n  const { data: defaultLocales = [] } = useGetDefaultLocalesQuery();\n\n  const handleChange = (value: string) => {\n    if (Array.isArray(defaultLocales)) {\n      // We know it exists because the options are created from the list of default locales\n      const locale = defaultLocales.find((locale) => locale.code === value)!;\n\n      onChange(name, value);\n      // This lets us automatically fill the name field with the locale name\n      onChange('name', locale.name);\n    } else {\n      onChange(name, value);\n    }\n  };\n\n  return (\n    <Field.Root error={error} hint={hint} name={name} required={required}>\n      <Field.Label>{label}</Field.Label>\n      <SingleSelect\n        disabled={disabled}\n        // @ts-expect-error – This will dissapear when the DS removes support for numbers to be returned by SingleSelect.\n        onChange={handleChange}\n        placeholder={placeholder}\n        value={value}\n      >\n        {options.map((option) => (\n          <SingleSelectOption value={option.value} key={option.value}>\n            {option.label}\n          </SingleSelectOption>\n        ))}\n      </SingleSelect>\n      <Field.Error />\n      <Field.Hint />\n    </Field.Root>\n  );\n};\n\nexport { CreateLocale, BaseForm, AdvancedForm, SubmitButton, LOCALE_SCHEMA };\n", "import * as React from 'react';\n\nimport { Confirm<PERSON>ial<PERSON>, useAPIErrorHand<PERSON>, useNotification } from '@strapi/admin/strapi-admin';\nimport { Dialog, IconButton } from '@strapi/design-system';\nimport { Trash } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { useDeleteLocaleMutation } from '../services/locales';\nimport { getTranslation } from '../utils/getTranslation';\n\nimport type { Locale } from '../../../shared/contracts/locales';\n\n/* -------------------------------------------------------------------------------------------------\n * DeleteLocale\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DeleteLocaleProps extends Locale {}\n\nconst DeleteLocale = ({ id, name }: DeleteLocaleProps) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const [visible, setVisible] = React.useState(false);\n\n  const [deleteLocale] = useDeleteLocaleMutation();\n  const handleConfirm = async () => {\n    try {\n      const res = await deleteLocale(id);\n\n      if ('error' in res) {\n        toggleNotification({ type: 'danger', message: formatAPIError(res.error) });\n\n        return;\n      }\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTranslation('Settings.locales.modal.delete.success'),\n          defaultMessage: 'Deleted locale',\n        }),\n      });\n\n      setVisible(false);\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred, please try again',\n        }),\n      });\n    }\n  };\n\n  return (\n    <Dialog.Root open={visible} onOpenChange={setVisible}>\n      <Dialog.Trigger>\n        <IconButton\n          onClick={() => setVisible(true)}\n          label={formatMessage(\n            {\n              id: getTranslation('Settings.list.actions.delete'),\n              defaultMessage: 'Delete {name} locale',\n            },\n            {\n              name,\n            }\n          )}\n          variant=\"ghost\"\n        >\n          <Trash />\n        </IconButton>\n      </Dialog.Trigger>\n      <ConfirmDialog onConfirm={handleConfirm} />\n    </Dialog.Root>\n  );\n};\n\nexport { DeleteLocale };\n", "import * as React from 'react';\n\nimport {\n  useNotification,\n  useAP<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  useAuth,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Box,\n  Button,\n  Divider,\n  Flex,\n  IconButton,\n  Modal,\n  Tabs,\n  Typography,\n  useId,\n} from '@strapi/design-system';\nimport { Pencil } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { Locale, UpdateLocale } from '../../../shared/contracts/locales';\nimport { useUpdateLocaleMutation } from '../services/locales';\nimport { isBaseQueryError } from '../utils/baseQuery';\nimport { getTranslation } from '../utils/getTranslation';\n\nimport { AdvancedForm, BaseForm, LOCALE_SCHEMA, SubmitButton } from './CreateLocale';\n\n/* -------------------------------------------------------------------------------------------------\n * EditLocale\n * -----------------------------------------------------------------------------------------------*/\n\ninterface EditLocaleProps extends Omit<EditModalProps, 'open' | 'onOpenChange'> {}\n\nconst EditLocale = (props: EditLocaleProps) => {\n  const { formatMessage } = useIntl();\n  const [visible, setVisible] = React.useState(false);\n\n  return (\n    <>\n      <IconButton\n        onClick={() => setVisible(true)}\n        label={formatMessage(\n          {\n            id: getTranslation('Settings.list.actions.edit'),\n            defaultMessage: 'Edit {name} locale',\n          },\n          {\n            name: props.name,\n          }\n        )}\n        variant=\"ghost\"\n      >\n        <Pencil />\n      </IconButton>\n      <EditModal {...props} open={visible} onOpenChange={setVisible} />\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * EditModal\n * -----------------------------------------------------------------------------------------------*/\n\ninterface EditModalProps extends Pick<Locale, 'id' | 'isDefault' | 'name' | 'code'> {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\ntype FormValues = UpdateLocale.Request['body'] & { code: string };\n\n/**\n * @internal\n * @description Exported to be used when someone clicks on a table row.\n */\nconst EditModal = ({ id, code, isDefault, name, open, onOpenChange }: EditModalProps) => {\n  const { toggleNotification } = useNotification();\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidationErrors,\n  } = useAPIErrorHandler();\n  const refetchPermissions = useAuth('EditModal', (state) => state.refetchPermissions);\n  const { formatMessage } = useIntl();\n  const titleId = useId();\n\n  const [updateLocale] = useUpdateLocaleMutation();\n  const handleSubmit = async (\n    { code: _code, ...data }: FormValues,\n    helpers: FormHelpers<FormValues>\n  ) => {\n    try {\n      /**\n       * We don't need to send the code, because the\n       * code can never be changed.\n       */\n      const res = await updateLocale({\n        id,\n        ...data,\n      });\n\n      if ('error' in res) {\n        if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          helpers.setErrors(formatValidationErrors(res.error));\n        } else {\n          toggleNotification({ type: 'danger', message: formatAPIError(res.error) });\n        }\n\n        return;\n      }\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTranslation('Settings.locales.modal.edit.success'),\n          defaultMessage: 'Updated locale',\n        }),\n      });\n\n      refetchPermissions();\n      onOpenChange(false);\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred, please try again',\n        }),\n      });\n    }\n  };\n\n  return (\n    <Modal.Root open={open} onOpenChange={onOpenChange}>\n      <Modal.Content>\n        <Form\n          method=\"PUT\"\n          onSubmit={handleSubmit}\n          initialValues={{\n            code,\n            name,\n            isDefault,\n          }}\n          validationSchema={LOCALE_SCHEMA}\n        >\n          <Modal.Header>\n            <Modal.Title>\n              {formatMessage(\n                {\n                  id: getTranslation('Settings.list.actions.edit'),\n                  defaultMessage: 'Edit a locale',\n                },\n                {\n                  name,\n                }\n              )}\n            </Modal.Title>\n          </Modal.Header>\n          <Modal.Body>\n            <Tabs.Root variant=\"simple\" defaultValue=\"basic\">\n              <Flex justifyContent=\"space-between\">\n                <Typography tag=\"h2\" variant=\"beta\" id={titleId}>\n                  {formatMessage({\n                    id: getTranslation('Settings.locales.modal.title'),\n                    defaultMessage: 'Configuration',\n                  })}\n                </Typography>\n                <Tabs.List aria-labelledby={titleId}>\n                  <Tabs.Trigger value=\"basic\">\n                    {formatMessage({\n                      id: getTranslation('Settings.locales.modal.base'),\n                      defaultMessage: 'Basic settings',\n                    })}\n                  </Tabs.Trigger>\n                  <Tabs.Trigger value=\"advanced\">\n                    {formatMessage({\n                      id: getTranslation('Settings.locales.modal.advanced'),\n                      defaultMessage: 'Advanced settings',\n                    })}\n                  </Tabs.Trigger>\n                </Tabs.List>\n              </Flex>\n              <Divider />\n              <Box paddingTop={7} paddingBottom={7}>\n                <Tabs.Content value=\"basic\">\n                  <BaseForm mode=\"edit\" />\n                </Tabs.Content>\n                <Tabs.Content value=\"advanced\">\n                  <AdvancedForm isDefaultLocale={isDefault} />\n                </Tabs.Content>\n              </Box>\n            </Tabs.Root>\n          </Modal.Body>\n          <Modal.Footer>\n            <Modal.Close>\n              <Button variant=\"tertiary\">\n                {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n              </Button>\n            </Modal.Close>\n            <SubmitButton />\n          </Modal.Footer>\n        </Form>\n      </Modal.Content>\n    </Modal.Root>\n  );\n};\n\nexport { EditLocale, EditModal };\n", "import * as React from 'react';\n\nimport {\n  Flex,\n  Table,\n  Tbody,\n  Td,\n  Th,\n  Thead,\n  Tr,\n  Typography,\n  VisuallyH<PERSON><PERSON>,\n} from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { getTranslation } from '../utils/getTranslation';\n\nimport { DeleteLocale } from './DeleteLocale';\nimport { EditLocale, EditModal } from './EditLocale';\n\nimport type { Locale } from '../../../shared/contracts/locales';\n\n/* -------------------------------------------------------------------------------------------------\n * LocaleTable\n * -----------------------------------------------------------------------------------------------*/\n\ntype LocaleTableProps = {\n  locales?: Locale[];\n  canDelete?: boolean;\n  canUpdate?: boolean;\n  onDeleteLocale?: (locale: Locale) => void;\n  onEditLocale?: (locale: Locale) => void;\n};\n\nconst LocaleTable = ({ locales = [], canDelete, canUpdate }: LocaleTableProps) => {\n  const [editLocaleId, setEditLocaleId] = React.useState<Locale['id']>();\n  const { formatMessage } = useIntl();\n\n  const handleClick = (localeId: Locale['id']) => () => {\n    if (canUpdate) {\n      setEditLocaleId(localeId);\n    }\n  };\n\n  return (\n    <Table colCount={4} rowCount={locales.length + 1}>\n      <Thead>\n        <Tr>\n          <Th>\n            <Typography variant=\"sigma\" textColor=\"neutral600\">\n              {formatMessage({\n                id: getTranslation('Settings.locales.row.id'),\n                defaultMessage: 'ID',\n              })}\n            </Typography>\n          </Th>\n          <Th>\n            <Typography variant=\"sigma\" textColor=\"neutral600\">\n              {formatMessage({\n                id: getTranslation('Settings.locales.row.displayName'),\n                defaultMessage: 'Display name',\n              })}\n            </Typography>\n          </Th>\n          <Th>\n            <Typography variant=\"sigma\" textColor=\"neutral600\">\n              {formatMessage({\n                id: getTranslation('Settings.locales.row.default-locale'),\n                defaultMessage: 'Default locale',\n              })}\n            </Typography>\n          </Th>\n          <Th>\n            <VisuallyHidden>Actions</VisuallyHidden>\n          </Th>\n        </Tr>\n      </Thead>\n      <Tbody>\n        {locales.map((locale) => (\n          <React.Fragment key={locale.id}>\n            <Tr\n              onClick={handleClick(locale.id)}\n              style={{ cursor: canUpdate ? 'pointer' : 'default' }}\n            >\n              <Td>\n                <Typography textColor=\"neutral800\">{locale.id}</Typography>\n              </Td>\n              <Td>\n                <Typography textColor=\"neutral800\">{locale.name}</Typography>\n              </Td>\n              <Td>\n                <Typography textColor=\"neutral800\">\n                  {locale.isDefault\n                    ? formatMessage({\n                        id: getTranslation('Settings.locales.default'),\n                        defaultMessage: 'Default',\n                      })\n                    : null}\n                </Typography>\n              </Td>\n              <Td>\n                <Flex gap={1} justifyContent=\"flex-end\" onClick={(e) => e.stopPropagation()}>\n                  {canUpdate && <EditLocale {...locale} />}\n                  {canDelete && !locale.isDefault && <DeleteLocale {...locale} />}\n                </Flex>\n              </Td>\n            </Tr>\n            <EditModal\n              {...locale}\n              onOpenChange={() => setEditLocaleId(undefined)}\n              open={editLocaleId === locale.id}\n            />\n          </React.Fragment>\n        ))}\n      </Tbody>\n    </Table>\n  );\n};\n\nexport { LocaleTable };\nexport type { LocaleTableProps };\n", "import * as React from 'react';\n\nimport {\n  Page,\n  useAPIErrorHandler,\n  useNotification,\n  useRBAC,\n  Layouts,\n} from '@strapi/admin/strapi-admin';\nimport { EmptyStateLayout } from '@strapi/design-system';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\nimport { CreateLocale } from '../components/CreateLocale';\nimport { LocaleTable } from '../components/LocaleTable';\nimport { PERMISSIONS } from '../constants';\nimport { useGetLocalesQuery } from '../services/locales';\nimport { getTranslation } from '../utils/getTranslation';\n\nconst SettingsPage = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { data: locales, isLoading: isLoadingLocales, error } = useGetLocalesQuery();\n  const {\n    isLoading: isLoadingRBAC,\n    allowedActions: { canUpdate, canCreate, canDelete },\n  } = useRBAC(PERMISSIONS);\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  const isLoading = isLoadingLocales || isLoadingRBAC;\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (error || !Array.isArray(locales)) {\n    return <Page.Error />;\n  }\n\n  return (\n    <Page.Main tabIndex={-1}>\n      <Layouts.Header\n        primaryAction={<CreateLocale disabled={!canCreate} />}\n        title={formatMessage({\n          id: getTranslation('plugin.name'),\n          defaultMessage: 'Internationalization',\n        })}\n        subtitle={formatMessage({\n          id: getTranslation('Settings.list.description'),\n          defaultMessage: 'Configure the settings',\n        })}\n      />\n      <Layouts.Content>\n        {locales.length > 0 ? (\n          <LocaleTable locales={locales} canDelete={canDelete} canUpdate={canUpdate} />\n        ) : (\n          <EmptyStateLayout\n            icon={<EmptyDocuments width={undefined} height={undefined} />}\n            content={formatMessage({\n              id: getTranslation('Settings.list.empty.title'),\n              defaultMessage: 'There are no locales',\n            })}\n            action={<CreateLocale disabled={!canCreate} variant=\"secondary\" />}\n          />\n        )}\n      </Layouts.Content>\n    </Page.Main>\n  );\n};\n\nconst ProtectedSettingsPage = () => {\n  return (\n    <Page.Protect permissions={PERMISSIONS.read}>\n      <SettingsPage />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedSettingsPage, SettingsPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAMA,mBAAmB,CAACC,UAAAA;AACxB,SAAOA,MAAMC,SAASC;AACxB;;;ACoCA,IAAMC,eAAe,CAAC,EAAEC,UAAUC,UAAU,UAAS,MAAqB;AACxE,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,SAASC,UAAAA,IAAoBC,eAAS,KAAA;AAE7C,aACEC,yBAACC,MAAMC,MAAI;IAACC,MAAMN;IAASO,cAAcN;;UACvCO,wBAACJ,MAAMK,SAAO;QACZ,cAAAD,wBAACE,QAAAA;UACCb;UACAD;UACAe,eAAWH,wBAACI,eAAAA,CAAAA,CAAAA;UACZC,SAAS,MAAMZ,WAAW,IAAA;UAC1Ba,MAAK;oBAEJhB,cAAc;YACbiB,IAAIC,eAAe,2BAAA;YACnBC,gBAAgB;UAClB,CAAA;;;UAGJT,wBAACU,aAAAA;QAAYC,SAAS,MAAMlB,WAAW,KAAA;;;;AAG7C;AAIkG,IAE5FmB,gBAAoBC,QAAM,EAAGC,MAAM;EACvCC,MAAUC,QAAM,EAAGC,SAAQ,EAAGC,SAAS;IACrCX,IAAI;IACJE,gBAAgB;EAClB,CAAA;EACAU,MACGH,QAAM,EACNC,SAAQ,EACRG,IAAI,IAAI;IACPb,IAAI;IACJE,gBAAgB;EAClB,CAAA,EACCS,SAAS;IACRX,IAAI;IACJE,gBAAgB;EAClB,CAAA;EACFY,WAAeC,OAAO;AACxB,CAAA;AAIA,IAAMC,oBAAoB;EACxBR,MAAM;EACNI,MAAM;EACNE,WAAW;AACb;AAMA,IAAMX,cAAc,CAAC,EAAEC,QAAO,MAAoB;AAChD,QAAMa,UAAUC,MAAAA;AAChB,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EACJC,yBAAyBC,gBACzBC,iCAAiCC,uBAAsB,IACrDC,mBAAAA;AACJ,QAAM,CAACC,YAAAA,IAAgBC,wBAAAA;AACvB,QAAM,EAAE5C,cAAa,IAAKC,QAAAA;AAC1B,QAAM4C,qBAAqBC,QAAQ,eAAe,CAACC,UAAUA,MAAMF,kBAAkB;AAErF,QAAMG,eAAe,OAAOC,QAAoBC,YAAAA;AAC9C,QAAI;AACF,YAAMC,MAAM,MAAMR,aAAaM,MAAAA;AAE/B,UAAI,WAAWE,KAAK;AAClB,YAAIC,iBAAiBD,IAAIE,KAAK,KAAKF,IAAIE,MAAMxB,SAAS,mBAAmB;AACvEqB,kBAAQI,UAAUb,uBAAuBU,IAAIE,KAAK,CAAA;eAC7C;AACLjB,6BAAmB;YAAEmB,MAAM;YAAUC,SAASjB,eAAeY,IAAIE,KAAK;UAAE,CAAA;QAC1E;AAEA;MACF;AAEAjB,yBAAmB;QACjBmB,MAAM;QACNC,SAASxD,cAAc;UACrBiB,IAAIC,eAAe,uCAAA;UACnBC,gBAAgB;QAClB,CAAA;MACF,CAAA;AAEA0B,yBAAAA;AACAxB,cAAAA;IACF,SAASoC,KAAK;AACZrB,yBAAmB;QACjBmB,MAAM;QACNC,SAASxD,cAAc;UACrBiB,IAAI;UACJE,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,aACET,wBAACJ,MAAMoD,SAAO;IACZ,cAAArD,yBAACsD,MAAAA;MACCC,QAAO;MACPC,eAAe5B;MACf6B,kBAAkBxC;MAClByC,UAAUf;;YAEVtC,wBAACJ,MAAM0D,QAAM;wBACXtD,wBAACJ,MAAM2D,OAAK;sBACTjE,cAAc;cACbiB,IAAIC,eAAe,2BAAA;cACnBC,gBAAgB;YAClB,CAAA;;;YAGJT,wBAACJ,MAAM4D,MAAI;wBACT7D,yBAAC8D,KAAK5D,MAAI;YAACR,SAAQ;YAASqE,cAAa;;kBACvC/D,yBAACgE,MAAAA;gBAAKC,gBAAe;;sBACnB5D,wBAAC6D,YAAAA;oBAAWC,KAAI;oBAAKzE,SAAQ;oBAAOkB,IAAIiB;8BACrClC,cAAc;sBACbiB,IAAIC,eAAe,8BAAA;sBACnBC,gBAAgB;oBAClB,CAAA;;sBAEFd,yBAAC8D,KAAKM,MAAI;oBAACC,mBAAiBxC;;0BAC1BxB,wBAACyD,KAAKxD,SAAO;wBAACgE,OAAM;kCACjB3E,cAAc;0BACbiB,IAAIC,eAAe,6BAAA;0BACnBC,gBAAgB;wBAClB,CAAA;;0BAEFT,wBAACyD,KAAKxD,SAAO;wBAACgE,OAAM;kCACjB3E,cAAc;0BACbiB,IAAIC,eAAe,iCAAA;0BACnBC,gBAAgB;wBAClB,CAAA;;;;;;kBAKNT,wBAACkE,SAAAA,CAAAA,CAAAA;kBAEDvE,yBAACwE,KAAAA;gBAAIC,YAAY;gBAAGC,eAAe;;sBACjCrE,wBAACyD,KAAKT,SAAO;oBAACiB,OAAM;oBAClB,cAAAjE,wBAACsE,UAAAA,CAAAA,CAAAA;;sBAEHtE,wBAACyD,KAAKT,SAAO;oBAACiB,OAAM;oBAClB,cAAAjE,wBAACuE,cAAAA,CAAAA,CAAAA;;;;;;;YAKT5E,yBAACC,MAAM4E,QAAM;;gBACXxE,wBAACJ,MAAM6E,OAAK;cACV,cAAAzE,wBAACE,QAAAA;gBAAOb,SAAQ;0BACbC,cAAc;kBAAEiB,IAAI;kBAAgCE,gBAAgB;gBAAS,CAAA;;;gBAGlFT,wBAAC0E,cAAAA,CAAAA,CAAAA;;;;;;AAKX;AAIkG,IAE5FA,eAAe,MAAA;AACnB,QAAM,EAAEpF,cAAa,IAAKC,QAAAA;AAC1B,QAAMoF,eAAeC,QAAQ,gBAAgB,CAACvC,UAAUA,MAAMsC,YAAY;AAC1E,QAAME,WAAWD,QAAQ,gBAAgB,CAACvC,UAAUA,MAAMwC,QAAQ;AAElE,aACE7E,wBAACE,QAAAA;IAAO2C,MAAK;IAAS1C,eAAWH,wBAAC8E,eAAAA,CAAAA,CAAAA;IAAU1F,UAAUuF,gBAAgB,CAACE;cACpEvF,cAAc;MAAEiB,IAAI;MAAeE,gBAAgB;IAAO,CAAA;;AAGjE;AAUA,IAAM6D,WAAW,CAAC,EAAES,OAAO,SAAQ,MAAiB;AAClD,QAAM,EAAEzF,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEmC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKG,mBAAAA;AAEpD,QAAM,EAAEgD,MAAMC,gBAAgBtC,MAAK,IAAKuC,0BAAAA;AAExCC,EAAMC,gBAAU,MAAA;AACd,QAAIzC,OAAO;AACTjB,yBAAmB;QACjBmB,MAAM;QACNC,SAASjB,eAAec,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAOd;IAAgBH;EAAmB,CAAA;AAE9C,MAAI,CAAC2D,MAAMC,QAAQL,cAAiB,GAAA;AAClC,WAAO;EACT;AAEA,QAAMM,UAAUN,eAAeO,IAAI,CAACC,YAAY;IAC9CC,OAAOD,OAAOtE;IACd8C,OAAOwB,OAAO1E;IAChB;AAEA,QAAM4E,iBAAiB;IACrB;MACEvG,UAAU2F,SAAS;MACnBW,OAAO;QACLnF,IAAIC,eAAe,0CAAA;QACnBC,gBAAgB;MAClB;MACAU,MAAM;MACNoE;MACAK,aAAa;QACXrF,IAAI;QACJE,gBAAgB;MAClB;MACAS,UAAU;MACVZ,MAAM;MACNuC,MAAM;IACR;IACA;MACEgD,MAAM;QACJtF,IAAIC,eAAe,sDAAA;QACnBC,gBAAgB;MAClB;MACAiF,OAAO;QACLnF,IAAIC,eAAe,0CAAA;QACnBC,gBAAgB;MAClB;MACAU,MAAM;MACND,UAAU;MACVZ,MAAM;MACNuC,MAAM;IACR;EACD,EAAC2C,IAAI,CAACM,WAAW;IAChB,GAAGA;IACHD,MAAMC,MAAMD,OAAOvG,cAAcwG,MAAMD,IAAI,IAAIE;IAC/CL,OAAOpG,cAAcwG,MAAMJ,KAAK;IAChCE,aAAaE,MAAMF,cAActG,cAAcwG,MAAMF,WAAW,IAAIG;IACtE;AAEA,aACE/F,wBAACgG,KAAKnG,MAAI;IAACoG,KAAK;cACbN,eAAeH,IAAI,CAAC,EAAElF,MAAM,GAAGwF,MAAO,UACrC9F,wBAACgG,KAAKE,MAAI;MAAkBC,KAAK7F;MAAM8F,WAAU;MAASC,YAAW;MACnE,cAAArG,wBAACsG,cAAAA;QAAc,GAAGR;;IADJA,GAAAA,MAAM3E,IAAI,CAAA;;AAMlC;AAUA,IAAMoD,eAAe,CAAC,EAAEgC,gBAAe,MAAqB;AAC1D,QAAM,EAAEjH,cAAa,IAAKC,QAAAA;AAE1B,QAAMiH,OAAO;IACX;MACEpH,UAAUmH;MACVV,MAAM;QACJtF,IAAIC,eAAe,mDAAA;QACnBC,gBAAgB;MAClB;MACAiF,OAAO;QACLnF,IAAIC,eAAe,8CAAA;QACnBC,gBAAgB;MAClB;MACAU,MAAM;MACNb,MAAM;MACNuC,MAAM;IACR;EACD,EAAC2C,IAAI,CAACM,WAAW;IAChB,GAAGA;IACHD,MAAMC,MAAMD,OAAOvG,cAAcwG,MAAMD,IAAI,IAAIE;IAC/CL,OAAOpG,cAAcwG,MAAMJ,KAAK;IAClC;AAEA,aACE1F,wBAACgG,KAAKnG,MAAI;IAACoG,KAAK;cACbO,KAAKhB,IAAI,CAAC,EAAElF,MAAM,GAAGwF,MAAO,UAC3B9F,wBAACgG,KAAKE,MAAI;MAAkBC,KAAK7F;MAAM8F,WAAU;MAASC,YAAW;MACnE,cAAArG,wBAACsG,cAAAA;QAAc,GAAGR;;IADJA,GAAAA,MAAM3E,IAAI,CAAA;;AAMlC;AAMA,IAAMmF,eAAe,CAACR,UAAAA;AACpB,UAAQA,MAAMjD,MAAI;IAKhB,KAAK;AACH,iBAAO7C,wBAACyG,kBAAAA;QAAkB,GAAGX;;IAC/B;AACE,iBAAO9F,wBAAC0G,uBAAAA;QAAe,GAAGZ;;EAC9B;AACF;AAEA,IAAMW,mBAAmB,CAAC,EACxBrH,UACAyG,MACAH,OACAvE,MACAoE,SACAK,aACA1E,SAAQ,MACqC;AAC7C,QAAM,EAAE+C,OAAOtB,OAAOgE,SAAQ,IAAKC,SAASzF,IAAAA;AAC5C,QAAM,EAAE6D,MAAMC,iBAAiB,CAAA,EAAE,IAAKC,0BAAAA;AAEtC,QAAM2B,eAAe,CAAC5C,WAAAA;AACpB,QAAIoB,MAAMC,QAAQL,cAAiB,GAAA;AAEjC,YAAMQ,SAASR,eAAe6B,KAAK,CAACrB,YAAWA,QAAO1E,SAASkD,MAAAA;AAE/D0C,eAASxF,MAAM8C,MAAAA;AAEf0C,eAAS,QAAQlB,OAAOtE,IAAI;WACvB;AACLwF,eAASxF,MAAM8C,MAAAA;IACjB;EACF;AAEA,aACEtE,yBAACoH,MAAMlH,MAAI;IAAC8C;IAAckD;IAAY1E;IAAYD;;UAChDlB,wBAAC+G,MAAMC,OAAK;QAAEtB,UAAAA;;UACd1F,wBAACiH,cAAAA;QACC7H;;QAEAuH,UAAUE;QACVjB;QACA3B;QAECsB,UAAAA,QAAQC,IAAI,CAAC0B,eACZlH,wBAACmH,oBAAAA;UAAmBlD,OAAOiD,OAAOjD;UAC/BiD,UAAAA,OAAOxB;QADoCwB,GAAAA,OAAOjD,KAAK,CAAA;;UAK9DjE,wBAAC+G,MAAMK,OAAK,CAAA,CAAA;UACZpH,wBAAC+G,MAAMM,MAAI,CAAA,CAAA;;;AAGjB;;;;;;;;;AChZA,IAAMC,eAAe,CAAC,EAAEC,IAAIC,KAAI,MAAqB;AACnD,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AAEpD,QAAM,CAACC,SAASC,UAAAA,IAAoBC,gBAAS,KAAA;AAE7C,QAAM,CAACC,YAAAA,IAAgBC,wBAAAA;AACvB,QAAMC,gBAAgB,YAAA;AACpB,QAAI;AACF,YAAMC,MAAM,MAAMH,aAAaZ,EAAAA;AAE/B,UAAI,WAAWe,KAAK;AAClBX,2BAAmB;UAAEY,MAAM;UAAUC,SAASV,eAAeQ,IAAIG,KAAK;QAAE,CAAA;AAExE;MACF;AAEAd,yBAAmB;QACjBY,MAAM;QACNC,SAASf,cAAc;UACrBF,IAAImB,eAAe,uCAAA;UACnBC,gBAAgB;QAClB,CAAA;MACF,CAAA;AAEAV,iBAAW,KAAA;IACb,SAASW,KAAK;AACZjB,yBAAmB;QACjBY,MAAM;QACNC,SAASf,cAAc;UACrBF,IAAI;UACJoB,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,aACEE,0BAACC,OAAOC,MAAI;IAACC,MAAMhB;IAASiB,cAAchB;;UACxCiB,yBAACJ,OAAOK,SAAO;QACb,cAAAD,yBAACE,YAAAA;UACCC,SAAS,MAAMpB,WAAW,IAAA;UAC1BqB,OAAO7B,cACL;YACEF,IAAImB,eAAe,8BAAA;YACnBC,gBAAgB;aAElB;YACEnB;UACF,CAAA;UAEF+B,SAAQ;UAER,cAAAL,yBAACM,cAAAA,CAAAA,CAAAA;;;UAGLN,yBAACO,eAAAA;QAAcC,WAAWrB;;;;AAGhC;;;;;AC1CA,IAAMsB,aAAa,CAACC,UAAAA;AAClB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,CAACC,SAASC,UAAAA,IAAoBC,gBAAS,KAAA;AAE7C,aACEC,0BAAAC,8BAAA;;UACEC,yBAACC,YAAAA;QACCC,SAAS,MAAMN,WAAW,IAAA;QAC1BO,OAAOV,cACL;UACEW,IAAIC,eAAe,4BAAA;UACnBC,gBAAgB;WAElB;UACEC,MAAMf,MAAMe;QACd,CAAA;QAEFC,SAAQ;QAER,cAAAR,yBAACS,eAAAA,CAAAA,CAAAA;;UAEHT,yBAACU,WAAAA;QAAW,GAAGlB;QAAOmB,MAAMhB;QAASiB,cAAchB;;;;AAGzD;AAiBMc,IAAAA,YAAY,CAAC,EAAEN,IAAIS,MAAMC,WAAWP,MAAMI,MAAMC,aAAY,MAAkB;AAClF,QAAM,EAAEG,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EACJC,yBAAyBC,gBACzBC,iCAAiCC,uBAAsB,IACrDC,mBAAAA;AACJ,QAAMC,qBAAqBC,QAAQ,aAAa,CAACC,UAAUA,MAAMF,kBAAkB;AACnF,QAAM,EAAE7B,cAAa,IAAKC,QAAAA;AAC1B,QAAM+B,UAAUC,MAAAA;AAEhB,QAAM,CAACC,YAAAA,IAAgBC,wBAAAA;AACvB,QAAMC,eAAe,OACnB,EAAEhB,MAAMiB,OAAO,GAAGC,KAAAA,GAClBC,YAAAA;AAEA,QAAI;AAKF,YAAMC,MAAM,MAAMN,aAAa;QAC7BvB;QACA,GAAG2B;MACL,CAAA;AAEA,UAAI,WAAWE,KAAK;AAClB,YAAIC,iBAAiBD,IAAIE,KAAK,KAAKF,IAAIE,MAAM5B,SAAS,mBAAmB;AACvEyB,kBAAQI,UAAUhB,uBAAuBa,IAAIE,KAAK,CAAA;eAC7C;AACLpB,6BAAmB;YAAEsB,MAAM;YAAUC,SAASpB,eAAee,IAAIE,KAAK;UAAE,CAAA;QAC1E;AAEA;MACF;AAEApB,yBAAmB;QACjBsB,MAAM;QACNC,SAAS7C,cAAc;UACrBW,IAAIC,eAAe,qCAAA;UACnBC,gBAAgB;QAClB,CAAA;MACF,CAAA;AAEAgB,yBAAAA;AACAV,mBAAa,KAAA;IACf,SAAS2B,KAAK;AACZxB,yBAAmB;QACjBsB,MAAM;QACNC,SAAS7C,cAAc;UACrBW,IAAI;UACJE,gBAAgB;QAClB,CAAA;MACF,CAAA;IACF;EACF;AAEA,aACEN,yBAACwC,MAAMC,MAAI;IAAC9B;IAAYC;kBACtBZ,yBAACwC,MAAME,SAAO;MACZ,cAAA5C,0BAAC6C,MAAAA;QACCC,QAAO;QACPC,UAAUhB;QACViB,eAAe;UACbjC;UACAN;UACAO;QACF;QACAiC,kBAAkBC;;cAElBhD,yBAACwC,MAAMS,QAAM;0BACXjD,yBAACwC,MAAMU,OAAK;wBACTzD,cACC;gBACEW,IAAIC,eAAe,4BAAA;gBACnBC,gBAAgB;iBAElB;gBACEC;cACF,CAAA;;;cAINP,yBAACwC,MAAMW,MAAI;0BACTrD,0BAACsD,KAAKX,MAAI;cAACjC,SAAQ;cAAS6C,cAAa;;oBACvCvD,0BAACwD,MAAAA;kBAAKC,gBAAe;;wBACnBvD,yBAACwD,YAAAA;sBAAWC,KAAI;sBAAKjD,SAAQ;sBAAOJ,IAAIqB;gCACrChC,cAAc;wBACbW,IAAIC,eAAe,8BAAA;wBACnBC,gBAAgB;sBAClB,CAAA;;wBAEFR,0BAACsD,KAAKM,MAAI;sBAACC,mBAAiBlC;;4BAC1BzB,yBAACoD,KAAKQ,SAAO;0BAACC,OAAM;oCACjBpE,cAAc;4BACbW,IAAIC,eAAe,6BAAA;4BACnBC,gBAAgB;0BAClB,CAAA;;4BAEFN,yBAACoD,KAAKQ,SAAO;0BAACC,OAAM;oCACjBpE,cAAc;4BACbW,IAAIC,eAAe,iCAAA;4BACnBC,gBAAgB;0BAClB,CAAA;;;;;;oBAINN,yBAAC8D,SAAAA,CAAAA,CAAAA;oBACDhE,0BAACiE,KAAAA;kBAAIC,YAAY;kBAAGC,eAAe;;wBACjCjE,yBAACoD,KAAKV,SAAO;sBAACmB,OAAM;sBAClB,cAAA7D,yBAACkE,UAAAA;wBAASC,MAAK;;;wBAEjBnE,yBAACoD,KAAKV,SAAO;sBAACmB,OAAM;sBAClB,cAAA7D,yBAACoE,cAAAA;wBAAaC,iBAAiBvD;;;;;;;;cAKvChB,0BAAC0C,MAAM8B,QAAM;;kBACXtE,yBAACwC,MAAM+B,OAAK;gBACV,cAAAvE,yBAACwE,QAAAA;kBAAOhE,SAAQ;4BACbf,cAAc;oBAAEW,IAAI;oBAAgCE,gBAAgB;kBAAS,CAAA;;;kBAGlFN,yBAACyE,cAAAA,CAAAA,CAAAA;;;;;;;AAMb;;;AC5KMC,IAAAA,cAAc,CAAC,EAAEC,UAAU,CAAA,GAAIC,WAAWC,UAAS,MAAoB;AAC3E,QAAM,CAACC,cAAcC,eAAgB,IAASC,gBAAQ;AACtD,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAE1B,QAAMC,cAAc,CAACC,aAA2B,MAAA;AAC9C,QAAIP,WAAW;AACbE,sBAAgBK,QAAAA;IAClB;EACF;AAEA,aACEC,0BAACC,OAAAA;IAAMC,UAAU;IAAGC,UAAUb,QAAQc,SAAS;;UAC7CC,yBAACC,OAAAA;QACC,cAAAN,0BAACO,IAAAA;;gBACCF,yBAACG,IAAAA;cACC,cAAAH,yBAACI,YAAAA;gBAAWC,SAAQ;gBAAQC,WAAU;0BACnCf,cAAc;kBACbgB,IAAIC,eAAe,yBAAA;kBACnBC,gBAAgB;gBAClB,CAAA;;;gBAGJT,yBAACG,IAAAA;cACC,cAAAH,yBAACI,YAAAA;gBAAWC,SAAQ;gBAAQC,WAAU;0BACnCf,cAAc;kBACbgB,IAAIC,eAAe,kCAAA;kBACnBC,gBAAgB;gBAClB,CAAA;;;gBAGJT,yBAACG,IAAAA;cACC,cAAAH,yBAACI,YAAAA;gBAAWC,SAAQ;gBAAQC,WAAU;0BACnCf,cAAc;kBACbgB,IAAIC,eAAe,qCAAA;kBACnBC,gBAAgB;gBAClB,CAAA;;;gBAGJT,yBAACG,IAAAA;cACC,cAAAH,yBAACU,gBAAAA;gBAAe,UAAA;;;;;;UAItBV,yBAACW,OAAAA;QACE1B,UAAAA,QAAQ2B,IAAI,CAACC,eACZlB,0BAAOmB,iBAAQ;;gBACbnB,0BAACO,IAAAA;cACCa,SAAStB,YAAYoB,OAAON,EAAE;cAC9BS,OAAO;gBAAEC,QAAQ9B,YAAY,YAAY;cAAU;;oBAEnDa,yBAACkB,IAAAA;kBACC,cAAAlB,yBAACI,YAAAA;oBAAWE,WAAU;oBAAcO,UAAAA,OAAON;;;oBAE7CP,yBAACkB,IAAAA;kBACC,cAAAlB,yBAACI,YAAAA;oBAAWE,WAAU;oBAAcO,UAAAA,OAAOM;;;oBAE7CnB,yBAACkB,IAAAA;kBACC,cAAAlB,yBAACI,YAAAA;oBAAWE,WAAU;8BACnBO,OAAOO,YACJ7B,cAAc;sBACZgB,IAAIC,eAAe,0BAAA;sBACnBC,gBAAgB;qBAElB,IAAA;;;oBAGRT,yBAACkB,IAAAA;kBACC,cAAAvB,0BAAC0B,MAAAA;oBAAKC,KAAK;oBAAGC,gBAAe;oBAAWR,SAAS,CAACS,MAAMA,EAAEC,gBAAe;;sBACtEtC,iBAAaa,yBAAC0B,YAAAA;wBAAY,GAAGb;;sBAC7B3B,aAAa,CAAC2B,OAAOO,iBAAapB,yBAAC2B,cAAAA;wBAAc,GAAGd;;;;;;;gBAI3Db,yBAAC4B,WAAAA;cACE,GAAGf;cACJgB,cAAc,MAAMxC,gBAAgByC,MAAAA;cACpCC,MAAM3C,iBAAiByB,OAAON;;;QA/BbM,GAAAA,OAAON,EAAE,CAAA;;;;AAsCxC;;;AClGA,IAAMyB,eAAe,MAAA;AACnB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,mBAAkB,IAAKC,gBAAAA;AAC/B,QAAM,EAAEC,yBAAyBC,eAAc,IAAKC,mBAAAA;AACpD,QAAM,EAAEC,MAAMC,SAASC,WAAWC,kBAAkBC,MAAK,IAAKC,mBAAAA;AAC9D,QAAM,EACJH,WAAWI,eACXC,gBAAgB,EAAEC,WAAWC,WAAWC,UAAS,EAAE,IACjDC,QAAQC,WAAAA;AAEZC,EAAMC,iBAAU,MAAA;AACd,QAAIV,OAAO;AACTT,yBAAmB;QACjBoB,MAAM;QACNC,SAASlB,eAAeM,KAAAA;MAC1B,CAAA;IACF;KACC;IAACA;IAAON;IAAgBH;EAAmB,CAAA;AAE9C,QAAMO,YAAYC,oBAAoBG;AAEtC,MAAIJ,WAAW;AACb,eAAOe,yBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,MAAIf,SAAS,CAACgB,MAAMC,QAAQpB,OAAU,GAAA;AACpC,eAAOgB,yBAACC,KAAKI,OAAK,CAAA,CAAA;EACpB;AAEA,aACEC,0BAACL,KAAKM,MAAI;IAACC,UAAU;;UACnBR,yBAACS,QAAQC,QAAM;QACbC,mBAAeX,yBAACY,cAAAA;UAAaC,UAAU,CAACrB;;QACxCsB,OAAOtC,cAAc;UACnBuC,IAAIC,eAAe,aAAA;UACnBC,gBAAgB;QAClB,CAAA;QACAC,UAAU1C,cAAc;UACtBuC,IAAIC,eAAe,2BAAA;UACnBC,gBAAgB;QAClB,CAAA;;UAEFjB,yBAACS,QAAQU,SAAO;kBACbnC,QAAQoC,SAAS,QAChBpB,yBAACqB,aAAAA;UAAYrC;UAAkBS;UAAsBF;iBAErDS,yBAACsB,kBAAAA;UACCC,UAAMvB,yBAACwB,cAAAA;YAAeC,OAAOC;YAAWC,QAAQD;;UAChDE,SAASpD,cAAc;YACrBuC,IAAIC,eAAe,2BAAA;YACnBC,gBAAgB;UAClB,CAAA;UACAY,YAAQ7B,yBAACY,cAAAA;YAAaC,UAAU,CAACrB;YAAWsC,SAAQ;;;;;;AAMhE;AAEA,IAAMC,wBAAwB,MAAA;AAC5B,aACE/B,yBAACC,KAAK+B,SAAO;IAACC,aAAatC,YAAYuC;IACrC,cAAAlC,yBAACzB,cAAAA,CAAAA,CAAAA;;AAGP;", "names": ["isBaseQueryError", "error", "name", "undefined", "CreateLocale", "disabled", "variant", "formatMessage", "useIntl", "visible", "setVisible", "useState", "_jsxs", "Modal", "Root", "open", "onOpenChange", "_jsx", "<PERSON><PERSON>", "<PERSON><PERSON>", "startIcon", "Plus", "onClick", "size", "id", "getTranslation", "defaultMessage", "CreateModal", "onClose", "LOCALE_SCHEMA", "object", "shape", "code", "string", "nullable", "required", "name", "max", "isDefault", "boolean", "initialFormValues", "titleId", "useId", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "createLocale", "useCreateLocaleMutation", "refetchPermissions", "useAuth", "state", "handleSubmit", "values", "helpers", "res", "isBaseQueryError", "error", "setErrors", "type", "message", "err", "Content", "Form", "method", "initialValues", "validationSchema", "onSubmit", "Header", "Title", "Body", "Tabs", "defaultValue", "Flex", "justifyContent", "Typography", "tag", "List", "aria-<PERSON>by", "value", "Divider", "Box", "paddingTop", "paddingBottom", "BaseForm", "AdvancedForm", "Footer", "Close", "SubmitButton", "isSubmitting", "useForm", "modified", "Check", "mode", "data", "defaultLocales", "useGetDefaultLocalesQuery", "React", "useEffect", "Array", "isArray", "options", "map", "locale", "label", "translatedForm", "placeholder", "hint", "field", "undefined", "Grid", "gap", "<PERSON><PERSON>", "col", "direction", "alignItems", "<PERSON><PERSON><PERSON><PERSON>", "isDefaultLocale", "form", "EnumerationInput", "InputR<PERSON><PERSON>", "onChange", "useField", "handleChange", "find", "Field", "Label", "SingleSelect", "option", "SingleSelectOption", "Error", "Hint", "DeleteLocale", "id", "name", "formatMessage", "useIntl", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "visible", "setVisible", "useState", "deleteLocale", "useDeleteLocaleMutation", "handleConfirm", "res", "type", "message", "error", "getTranslation", "defaultMessage", "err", "_jsxs", "Dialog", "Root", "open", "onOpenChange", "_jsx", "<PERSON><PERSON>", "IconButton", "onClick", "label", "variant", "Trash", "ConfirmDialog", "onConfirm", "EditLocale", "props", "formatMessage", "useIntl", "visible", "setVisible", "useState", "_jsxs", "_Fragment", "_jsx", "IconButton", "onClick", "label", "id", "getTranslation", "defaultMessage", "name", "variant", "Pencil", "EditModal", "open", "onOpenChange", "code", "isDefault", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "_unstableFormatValidationErrors", "formatValidationErrors", "useAPIErrorHandler", "refetchPermissions", "useAuth", "state", "titleId", "useId", "updateLocale", "useUpdateLocaleMutation", "handleSubmit", "_code", "data", "helpers", "res", "isBaseQueryError", "error", "setErrors", "type", "message", "err", "Modal", "Root", "Content", "Form", "method", "onSubmit", "initialValues", "validationSchema", "LOCALE_SCHEMA", "Header", "Title", "Body", "Tabs", "defaultValue", "Flex", "justifyContent", "Typography", "tag", "List", "aria-<PERSON>by", "<PERSON><PERSON>", "value", "Divider", "Box", "paddingTop", "paddingBottom", "BaseForm", "mode", "AdvancedForm", "isDefaultLocale", "Footer", "Close", "<PERSON><PERSON>", "SubmitButton", "LocaleTable", "locales", "canDelete", "canUpdate", "editLocaleId", "setEditLocaleId", "useState", "formatMessage", "useIntl", "handleClick", "localeId", "_jsxs", "Table", "col<PERSON>ount", "rowCount", "length", "_jsx", "<PERSON><PERSON>", "Tr", "Th", "Typography", "variant", "textColor", "id", "getTranslation", "defaultMessage", "VisuallyHidden", "Tbody", "map", "locale", "Fragment", "onClick", "style", "cursor", "Td", "name", "isDefault", "Flex", "gap", "justifyContent", "e", "stopPropagation", "EditLocale", "DeleteLocale", "EditModal", "onOpenChange", "undefined", "open", "SettingsPage", "formatMessage", "useIntl", "toggleNotification", "useNotification", "_unstableFormatAPIError", "formatAPIError", "useAPIErrorHandler", "data", "locales", "isLoading", "isLoadingLocales", "error", "useGetLocalesQuery", "isLoadingRBAC", "allowedActions", "canUpdate", "canCreate", "canDelete", "useRBAC", "PERMISSIONS", "React", "useEffect", "type", "message", "_jsx", "Page", "Loading", "Array", "isArray", "Error", "_jsxs", "Main", "tabIndex", "Layouts", "Header", "primaryAction", "CreateLocale", "disabled", "title", "id", "getTranslation", "defaultMessage", "subtitle", "Content", "length", "LocaleTable", "EmptyStateLayout", "icon", "EmptyDocuments", "width", "undefined", "height", "content", "action", "variant", "ProtectedSettingsPage", "Protect", "permissions", "read"]}