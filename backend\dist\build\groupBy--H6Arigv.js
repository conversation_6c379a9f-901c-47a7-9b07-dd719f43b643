import{bM as n,bN as b,bG as c,cG as A}from"./strapi-z7ApxZZq.js";import{_ as p}from"./_baseEach-ZnnftuGj.js";function u(r,e,a,t){for(var g=-1,o=r==null?0:r.length;++g<o;){var s=r[g];e(t,s,a(s),r)}return t}var f=u,i=p;function v(r,e,a,t){return i(r,function(g,o,s){e(t,g,a(g),s)}),t}var _=v,h=f,y=_,m=n,$=b;function j(r,e){return function(a,t){var g=$(a)?h:y,o=e?e():{};return g(a,r,m(t),o)}}var w=j,x=A,B=w,E=Object.prototype,O=E.hasOwnProperty,P=B(function(r,e,a){O.call(r,a)?r[a].push(e):x(r,a,[e])}),l=P;const V=c(l);export{V as g};
