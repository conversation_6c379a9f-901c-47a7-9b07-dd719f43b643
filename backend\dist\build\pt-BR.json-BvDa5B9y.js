var e="Monitoramento",o="Documentação",a="E-mail",t="Senha",n="Provedor",s="Redefinir o token de senha",i="Função",r="Claro",p="Escuro",l="Nome de usuário",c="Usuários",m="Ops! Algo deu errado. Por favor, tente novamente.",d="Limpar",u="OU",g="Pular para o conteúdo",b="Enviar",f={Analytics:e,"Auth.components.Oops.text":"Sua conta foi suspensa.","Auth.components.Oops.text.admin":"Se isso foi um erro, por favor, contate seu administrador.","Auth.components.Oops.title":"Ops...","Auth.form.active.label":"Ativo","Auth.form.button.forgot-password":"Enviar e-mail","Auth.form.button.go-home":"VOLTAR PARA O INÍCIO","Auth.form.button.login":"Entrar","Auth.form.button.login.providers.error":"Não foi possível conectar você pelo provedor selecionado.","Auth.form.button.login.strapi":"Entrar com Strapi","Auth.form.button.password-recovery":"Recuperação de Senha","Auth.form.button.register":"Pronto para começar","Auth.form.confirmPassword.label":"Confirmação de senha","Auth.form.currentPassword.label":"Senha atual","Auth.form.email.label":"E-mail","Auth.form.email.placeholder":"<EMAIL>","Auth.form.error.blocked":"Sua conta foi bloqueada pelo administrador.","Auth.form.error.code.provide":"Código incorreto fornecido.","Auth.form.error.confirmed":"O email da sua conta não foi confirmado.","Auth.form.error.email.invalid":"Este email é inválido.","Auth.form.error.email.provide":"Por favor, forneça seu nome de usuário ou seu e-mail.","Auth.form.error.email.taken":"O email já foi utilizado","Auth.form.error.invalid":"Identificador ou senha inválida.","Auth.form.error.params.provide":"Params incorretos fornecidos.","Auth.form.error.password.format":"Sua senha não pode conter o símbolo` $ `mais de três vezes.","Auth.form.error.password.local":"Este usuário nunca definiu uma senha local, por favor faça o login através do provedor usado durante a criação da conta.","Auth.form.error.password.matching":"As senhas não coincidem.","Auth.form.error.password.provide":"Por favor, forneça sua senha","Auth.form.error.ratelimit":"Muitas tentativas, tente novamente em um minuto.","Auth.form.error.user.not-exist":"Este e-mail não existe.","Auth.form.error.username.taken":"Nome de usuário já foi obtido","Auth.form.firstname.label":"Primeiro nome","Auth.form.firstname.placeholder":"ex: Kai","Auth.form.forgot-password.email.label":"Digite seu email","Auth.form.forgot-password.email.label.success":"E-mail enviado com sucesso para","Auth.form.lastname.label":"Último nome","Auth.form.lastname.placeholder":"ex: Doe","Auth.form.password.hide-password":"Esconder senha","Auth.form.password.hint":"A senha deve conter pelo menos 8 caracteres, 1 letra maiúscula, 1 letra minúscula, e 1 número","Auth.form.password.show-password":"Mostrar senha","Auth.form.register.news.label":"Mantenha-me atualizado sobre os novos recursos e as próximas melhorias (ao fazer isso, você aceita os {terms} e a {policy}).","Auth.form.register.subtitle":"Suas credenciais são utilizadas somente para autenticar você ao painel de admin. Todos os dados serão salvos na sua própria base de dados.","Auth.form.rememberMe.label":"Lembre-se de mim","Auth.form.username.label":"Nome de usuário","Auth.form.username.placeholder":"Kai Doe","Auth.form.welcome.subtitle":"Entrar na sua conta do Strapi","Auth.form.welcome.title":"Bem-vindo(a)!","Auth.link.forgot-password":"Esqueceu sua senha?","Auth.link.ready":"Pronto para logar?","Auth.link.signin":"Entrar","Auth.link.signin.account":"Já tem uma conta?","Auth.login.sso.divider":"Ou entre com","Auth.login.sso.loading":"Carregando provedores...","Auth.login.sso.subtitle":"Entre na sua conta com SSO","Auth.privacy-policy-agreement.policy":"política de privacidade","Auth.privacy-policy-agreement.terms":"termos","Auth.reset-password.title":"Redefinir senha","Content Manager":"Gerenciador de conteúdo","Content Type Builder":"Criador de Tipos de Conteúdo",Documentation:o,Email:a,"Files Upload":"Enviar arquivos","HomePage.head.title":"Pagina inicial","HomePage.roadmap":"Veja nosso roadmap","HomePage.welcome.congrats":"Parabéns!","HomePage.welcome.congrats.content":"Você está logado como o primeiro administrador. Para descobrir os recursos avançados fornecidos pelo Strapi,","HomePage.welcome.congrats.content.bold":"nós recomendados que você crie o seu primeiro Tipo de Conteúdo.","Media Library":"Biblioteca de Mídia","New entry":"Novo registro",Password:t,Provider:n,ResetPasswordToken:s,Role:i,"Roles & Permissions":"Funções e Permissões","Roles.ListPage.notification.delete-all-not-allowed":"Algumas funções não puderam ser removidos por estarem associadas a alguns usuários","Roles.ListPage.notification.delete-not-allowed":"A função não pode ser removida se ainda estiver associada a algum usuário","Roles.RoleRow.select-all":"Selecione {name} para ações em massa","Roles.RoleRow.user-count":"{number, plural, =0 {#  user} um {#  user} outros {# users}}","Roles.components.List.empty.withSearch":"Não existe uma função correspondente à busca ({search})...","Settings.PageTitle":"Configurações - {name}","Settings.apiTokens.addFirstToken":"Adicione sua primeira chave de API","Settings.apiTokens.addNewToken":"Adicionar nova chave de API","Settings.tokens.copy.editMessage":"Por motivos de segurança, você só poderá ver sua chave uma única vez.","Settings.tokens.copy.editTitle":"Essa chave não está mais acessível.","Settings.tokens.copy.lastWarning":"Certifique-se de copiar esta chave, pois não será possível vê-la novamente!","Settings.apiTokens.create":"Adicionar","Settings.apiTokens.description":"lista de chaves geradas para consumo da API","Settings.apiTokens.emptyStateLayout":"Você não tem nada aqui ainda...","Settings.tokens.notification.copied":"Chave copiada pra área de transferência.","Settings.apiTokens.title":"Chaves de API","Settings.tokens.types.full-access":"Acesso total","Settings.tokens.types.read-only":"Somente leitura","Settings.application.description":"Informações globais do painel administrativo","Settings.application.edition-title":"edição atual","Settings.application.get-help":"Ajuda","Settings.application.link-pricing":"Ver todos os preços","Settings.application.link-upgrade":"Atualize seu painel administrativo","Settings.application.node-version":"versão do node","Settings.application.strapi-version":"versão do strapi","Settings.application.strapiVersion":"versão do strapi","Settings.application.title":"Visão geral","Settings.application.customization":"Customização","Settings.application.customization.carousel.title":"Logo","Settings.application.customization.carousel.change-action":"Alterar logotipo","Settings.application.customization.carousel.reset-action":"Redefinir logotipo","Settings.application.customization.carousel-slide.label":"Slide do logotipo","Settings.application.customization.carousel-hint":"Alterar o logotipo do painel de administração (Dimensão máxima: {dimension}x{dimension}, Tamanho máx.: {size}KB)","Settings.application.customization.modal.cancel":"Cancelar","Settings.application.customization.modal.upload":"Enviar","Settings.application.customization.modal.tab.label":"Como você deseja enviar seus arquivos?","Settings.application.customization.modal.upload.from-computer":"Do computador","Settings.application.customization.modal.upload.file-validation":"Dimensão máxima: {dimension}x{dimension}, Tamanho máximo: {size}KB","Settings.application.customization.modal.upload.error-format":"Formato incorreto (formatos aceitos: jpeg, jpg, png, svg).","Settings.application.customization.modal.upload.error-size":"O arquivo enviado é muito grande (dimensão máxima: {dimension}x{dimension}, tamanho máximo do arquivo: {size}KB)","Settings.application.customization.modal.upload.error-network":"Erro ao enviar o arquivo","Settings.application.customization.modal.upload.cta.browse":"Procurar arquivo","Settings.application.customization.modal.upload.drag-drop":"Arraste e solte o arquivo aqui ou","Settings.application.customization.modal.upload.from-url":"Da URL","Settings.application.customization.modal.upload.from-url.input-label":"URL","Settings.application.customization.modal.upload.next":"Próximo","Settings.application.customization.modal.pending":"Logotipo pendente","Settings.application.customization.modal.pending.choose-another":"Escolha outro logotipo","Settings.application.customization.modal.pending.title":"Logotipo pronto para ser enviado","Settings.application.customization.modal.pending.subtitle":"Gerencie o logotipo escolhido antes de fazer o upload","Settings.application.customization.modal.pending.upload":"Enviar","Settings.application.customization.modal.pending.card-badge":"imagem","Settings.error":"Erro","Settings.global":"Configurações Globais","Settings.permissions":"Painel administrativo","Settings.permissions.category":"Configurações de permissão da categoria {category}","Settings.permissions.category.plugins":"Configurações de permissão da extensão {category}","Settings.permissions.conditions.anytime":"A qualquer hora","Settings.permissions.conditions.apply":"Aplicar","Settings.permissions.conditions.can":"Pode","Settings.permissions.conditions.conditions":"Definir condições","Settings.permissions.conditions.links":"Links","Settings.permissions.conditions.no-actions":"Você precisa selecionar ações (criar, ler, editar, ...) antes de definir uma condição.","Settings.permissions.conditions.none-selected":"A qualquer hora","Settings.permissions.conditions.or":"OU","Settings.permissions.conditions.when":"Quando","Settings.permissions.select-all-by-permission":"Selecionar todas as permissões de {label}","Settings.permissions.select-by-permission":"Selecionar permissão de {label}","Settings.permissions.users.create":"Convidar novo usuário","Settings.permissions.users.email":"E-mail","Settings.permissions.users.firstname":"Primeiro nome","Settings.permissions.users.lastname":"Último nome","Settings.permissions.users.form.sso":"Conectar com SSO","Settings.permissions.users.form.sso.description":"Quando ativado, usuários podem se conectar com SSO","Settings.permissions.users.listview.header.subtitle":"Todos os usuários com acesso ao painel administrativo","Settings.permissions.users.tabs.label":"Permissões de Abas","Settings.profile.form.notify.data.loaded":"Os dados do seu pefil foram carregados","Settings.profile.form.section.experience.clear.select":"Limpar a linguagem da interface selecionada","Settings.profile.form.section.experience.here":"aqui","Settings.profile.form.section.experience.interfaceLanguage":"Linguagem da interface","Settings.profile.form.section.experience.interfaceLanguage.hint":"Somente sua interface será exibida com a linguagem selecionada.","Settings.profile.form.section.experience.interfaceLanguageHelp":"A nova linguagem seleciona só será exibida a você. Por favor, leia essa {documentation} para disponibilizar outras linguagens para sua equipe.","Settings.profile.form.section.experience.mode.label":"Modo de interface","Settings.profile.form.section.experience.mode.hint":"Selecione o modo de interface que você deseja usar","Settings.profile.form.section.experience.mode.option-label":"modo {name}",light:r,dark:p,"Settings.profile.form.section.experience.title":"Experiência","Settings.profile.form.section.head.title":"Perfil do usuário","Settings.profile.form.section.profile.page.title":"Página do perfil","Settings.roles.create.description":"Defina as permissões desse papel","Settings.roles.create.title":"Criar uma função","Settings.roles.created":"Função criada","Settings.roles.edit.title":"Editar uma função","Settings.roles.form.button.users-with-role":"{number, plural, =0 {# usuários} um {# usuário} outros {# usuários}} com esse papel","Settings.roles.form.created":"Criado","Settings.roles.form.description":"Nome e descrição da função","Settings.roles.form.permission.property-label":"Permissões de {label}","Settings.roles.form.permissions.attributesPermissions":"Permissões de campos","Settings.roles.form.permissions.create":"Criar","Settings.roles.form.permissions.delete":"Remover","Settings.roles.form.permissions.publish":"Publicar","Settings.roles.form.permissions.read":"Ler","Settings.roles.form.permissions.update":"Editar","Settings.roles.list.button.add":"Adicionar novo papel","Settings.roles.list.description":"lista de papéis","Settings.roles.title.singular":"função","Settings.sso.description":"Configurações da funcionalidade de Single Sign-On.","Settings.sso.form.defaultRole.description":"Isso irá atribuir o novo usuário ao papel selecionado","Settings.sso.form.defaultRole.description-not-allowed":"Você precisa de permissão para ver os papéis administrativos","Settings.sso.form.defaultRole.label":"Função padrão","Settings.sso.form.registration.description":"Criar novo usuário ao logar por SSO e nenhuma conta existe previamente","Settings.sso.form.registration.label":"Inscrição automática","Settings.sso.title":"Single Sign-On","Settings.webhooks.create":"Criar um webhook","Settings.webhooks.create.header":"Adicionar um novo header","Settings.webhooks.created":"Webhook criado","Settings.webhooks.event.publish-tooltip":"Esse evento só está disponível para conteúdos com a funcionalidade de Rascunho/Publicado habilitada","Settings.webhooks.events.create":"Criar","Settings.webhooks.events.update":"Atualizar","Settings.webhooks.form.events":"Eventos","Settings.webhooks.form.headers":"Headers","Settings.webhooks.form.url":"Url","Settings.webhooks.headers.remove":"Remover header {number}","Settings.webhooks.key":"Chave","Settings.webhooks.list.button.add":"Adicionar novo webhook","Settings.webhooks.list.description":"Receba notificações de mudanças POST.","Settings.webhooks.list.empty.description":"Adicione o seu primeiro a essa lista.","Settings.webhooks.list.empty.link":"Veja nossa documentação","Settings.webhooks.list.empty.title":"Nenhum webhook adicionado ainda","Settings.webhooks.list.th.actions":"ações","Settings.webhooks.list.th.status":"estado","Settings.webhooks.singular":"webhook","Settings.webhooks.title":"Webhooks","Settings.webhooks.to.delete":"{webhooksToDeleteLength, plural, one {# elemento} other {# elementos}} selecionado(s)","Settings.webhooks.trigger":"Disparo","Settings.webhooks.trigger.cancel":"Cancelar disparo","Settings.webhooks.trigger.pending":"Pendente…","Settings.webhooks.trigger.save":"Por favor salve para disparar","Settings.webhooks.trigger.success":"Sucesso!","Settings.webhooks.trigger.success.label":"Disparo realizado com sucesso","Settings.webhooks.trigger.test":"Disparo de teste","Settings.webhooks.trigger.title":"Salvar antes do Disparo","Settings.webhooks.value":"Valor","Usecase.back-end":"Desenvolvedor Back-end","Usecase.button.skip":"Pular esta pergunta","Usecase.content-creator":"Criador de Conteúdo","Usecase.front-end":"Desenvolvedor Front-end","Usecase.full-stack":"Desenvolvedor Full-stack","Usecase.input.work-type":"Qual é o seu tipo de trabalho?","Usecase.notification.success.project-created":"Projeto criado com sucesso","Usecase.other":"Outro","Usecase.title":"Conte-nos um pouco mais sobre você",Username:l,Users:c,"Users & Permissions":"Usuários & Permissões","Users.components.List.empty":"Não há usuários...","Users.components.List.empty.withFilters":"Não há usuários correspondentes aos filtros...","Users.components.List.empty.withSearch":"Não há usuários correspondentes à busca ({search})...","admin.pages.MarketPlacePage.head":"Loja - Extensões","admin.pages.MarketPlacePage.offline.title":"Você está offline","admin.pages.MarketPlacePage.offline.subtitle":"Você precisa estar online para ver as extensões","admin.pages.MarketPlacePage.plugins":"Extensões","admin.pages.MarketPlacePage.plugin.copy":"Copiar comando de instalação","admin.pages.MarketPlacePage.plugin.copy.success":"Comando de instalação pronto para ser colado em seu terminal","admin.pages.MarketPlacePage.plugin.info":"Saber mais","admin.pages.MarketPlacePage.plugin.info.label":"Saiba mais sobre {pluginName}","admin.pages.MarketPlacePage.plugin.info.text":"Saber mais","admin.pages.MarketPlacePage.plugin.installed":"Instalado","admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi":"Criado por Strapi","admin.pages.MarketPlacePage.plugin.tooltip.verified":"Extensão verificada por Strapi","admin.pages.MarketPlacePage.providers":"Fornecedores","admin.pages.MarketPlacePage.search.clear":"Limpar busca","admin.pages.MarketPlacePage.search.empty":'Sem resultado para "{target}"',"admin.pages.MarketPlacePage.search.placeholder":"Pesquisar","admin.pages.MarketPlacePage.submit.plugin.link":"Submeta sua extensão","admin.pages.MarketPlacePage.submit.provider.link":"Submeta provedor","admin.pages.MarketPlacePage.subtitle":"Faça mais com o Strapi","admin.pages.MarketPlacePage.tab-group.label":"Plugins e Provedores para Strapi","admin.pages.MarketPlacePage.missingPlugin.title":"Falta-lhe um plugin?","admin.pages.MarketPlacePage.missingPlugin.description":"Diga-nos qual extensão você está procurando e informaremos nossos desenvolvedores de extensões da comunidade caso eles estejam em busca de inspiração!",anErrorOccurred:m,"app.component.CopyToClipboard.label":"Copiar pra área de transferência","app.component.search.label":"Buscar por {target}","app.component.table.duplicate":"Duplicar {target}","app.component.table.edit":"Editar {target}","app.component.table.select.one-entry":"Selecionar {target}","app.components.BlockLink.blog":"Blog","app.components.BlockLink.blog.content":"Leia as últimas notícias do Strapi e seu ecossistema.","app.components.BlockLink.code":"Códigos de Exemplo","app.components.BlockLink.code.content":"Aprenda testando projetos desenvolvidos pela comunidade.","app.components.BlockLink.documentation.content":"Descubra os conceitos essenciais, guias e instruções.","app.components.BlockLink.tutorial":"Tutoriais","app.components.BlockLink.tutorial.content":"Siga o passo-a-passo para usar e customizar o Strapi.","app.components.Button.cancel":"Cancelar","app.components.Button.confirm":"Confirmar","app.components.Button.reset":"Resetar","app.components.ComingSoonPage.comingSoon":"Em breve","app.components.ConfirmDialog.title":"Confirmação","app.components.DownloadInfo.download":"Transferência em andamento...","app.components.DownloadInfo.text":"Isto poderá levar alguns minutos. Obrigado pela sua paciência","app.components.EmptyAttributes.title":"Ainda não existem campos","app.components.EmptyStateLayout.content-document":"Nenhum conteúdo encontrado","app.components.EmptyStateLayout.content-permissions":"Você não tem permissão para acessar esse conteúdo","app.components.GuidedTour.CM.create.content":"<p>Crie e gerencie todo o conteúdo aqui no Gerenciador de Conteúdos.</p><p>Ex: Levando ainda mais o exemplo do site do Blog, pode-se escrever um artigo, salvá-lo e publicá-lo como quiser.</p><p>💡 Dica rápida - Não se esqueça de clicar em publicar no conteúdo que você criar.</p>","app.components.GuidedTour.CM.create.title":"⚡️ Crie seu primeiro conteúdo","app.components.GuidedTour.CM.success.content":"<p>Incrível, falta um último passo!</p><b>🚀  Ver conteúdo em ação</b>","app.components.GuidedTour.CM.success.cta.title":"Teste a API","app.components.GuidedTour.CM.success.title":"Passo 2: Concluído ✅","app.components.GuidedTour.CTB.create.content":"<p>Os tipos de coleção ajudam a gerenciar várias entradas, os tipos únicos são adequados para gerenciar apenas uma entrada.</p> <p>Ex: Para um site de blog, os artigos seriam um tipo de coleção, enquanto uma página inicial seria um tipo único.</p>","app.components.GuidedTour.CTB.create.cta.title":"Criar um Tipo de Coleção","app.components.GuidedTour.CTB.create.title":"🧠 Criar um primeiro tipo de coleção","app.components.GuidedTour.CTB.success.content":"<p>Bom trabalho!</p><b>⚡️ O que você gostaria de compartilhar com o mundo?</b>","app.components.GuidedTour.CTB.success.title":"Passo 1: Concluído ✅","app.components.GuidedTour.apiTokens.create.content":"<p>Gere um token de autenticação aqui e recupere o conteúdo que você acabou de criar.</p>","app.components.GuidedTour.apiTokens.create.cta.title":"Gerar um token de API","app.components.GuidedTour.apiTokens.create.title":"🚀 Visualizar conteúdo em ação ","app.components.GuidedTour.apiTokens.success.content":"<p>Visualize o conteúdo em ação fazendo uma solicitação HTTP:</p><ul><li><p>Para esta URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Com o cabeçalho: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Para mais formas de interagir com o conteúdo, consulte a <documentationLink>documentação</documentationLink>.</p>","app.components.GuidedTour.apiTokens.success.cta.title":"Voltar para a página inicial","app.components.GuidedTour.apiTokens.success.title":"Passo 3: Concluído ✅","app.components.GuidedTour.create-content":"Crie um conteúdo","app.components.GuidedTour.home.CM.title":"⚡️ O que você gostaria de compartilhar com o mundo?","app.components.GuidedTour.home.CTB.cta.title":"Ir para o Criador de tipos de conteúdo","app.components.GuidedTour.home.CTB.title":"🧠 Construir a estrutura de conteúdo","app.components.GuidedTour.home.apiTokens.cta.title":"Testar a API","app.components.GuidedTour.skip":"Pular o tutorial","app.components.GuidedTour.title":"3 passos para começar","app.components.HomePage.button.blog":"Veja mais no blog","app.components.HomePage.community":"Nossa comunidade na web","app.components.HomePage.community.content":"Converse com membros da equipe, colaboradores e desenvolvedores em diversos canais.","app.components.HomePage.create":"Crie seu primeiro Tipo de Conteúdo","app.components.HomePage.roadmap":"Veja nosso roadmap","app.components.HomePage.welcome":"Bem-vindo(a) a bordo 👋","app.components.HomePage.welcome.again":"Bem-vindo(a) 👋","app.components.HomePage.welcomeBlock.content":"Estamos muito felizes em tê-lo(a) como um membro da nossa comunidade. Estamos sempre querendo saber sua opinião, então fique a vontade em nos enviar uma mensagem em privado no ","app.components.HomePage.welcomeBlock.content.again":"Desejamos que você esteja progredindo em seu projeto... Fique por dentro das últimas novidades sobre o Strapi. Estamos sempre dando o nosso melhor para melhorar o produto sempre baseando-se em sua opinião.","app.components.HomePage.welcomeBlock.content.issues":"problemas.","app.components.HomePage.welcomeBlock.content.raise":" ou aponte ","app.components.ImgPreview.hint":"Arraste e solte o seu arquivo sobre a área ou {browse} um arquivo para fazer o envio","app.components.ImgPreview.hint.browse":"selecione","app.components.InputFile.newFile":"Adicionar um novo arquivo","app.components.InputFileDetails.open":"Abrir numa nova aba","app.components.InputFileDetails.originalName":"Nome original:","app.components.InputFileDetails.remove":"Remova este arquivo","app.components.InputFileDetails.size":"Tamanho:","app.components.InstallPluginPage.Download.description":"Pode demorar alguns segundos para baixar e instalar a extensão.","app.components.InstallPluginPage.Download.title":"Baixando...","app.components.InstallPluginPage.description":"Estenda seu aplicativo sem esforço.","app.components.LeftMenu.collapse":"Recolher barra de navegação","app.components.LeftMenu.expand":"Expandir barra de navegação","app.components.LeftMenu.general":"Geral","app.components.LeftMenu.logout":"Sair","app.components.LeftMenu.logo.alt":"logo da aplicação","app.components.LeftMenu.plugins":"Extensões","app.components.LeftMenu.trialCountdown":"Sua prova termina em {date}.","app.components.LeftMenu.navbrand.title":"Painel do Strapi","app.components.LeftMenu.navbrand.workplace":"Local de trabalho","app.components.LeftMenuFooter.help":"Ajuda","app.components.LeftMenuFooter.poweredBy":"Mantido por ","app.components.LeftMenuLinkContainer.collectionTypes":"Tipos de coleção","app.components.LeftMenuLinkContainer.configuration":"Configurações","app.components.LeftMenuLinkContainer.general":"Geral","app.components.LeftMenuLinkContainer.noPluginsInstalled":"Nenhuma extensão instalada ainda","app.components.LeftMenuLinkContainer.plugins":"Extensões","app.components.LeftMenuLinkContainer.singleTypes":"Tipos singulares","app.components.ListPluginsPage.deletePlugin.description":"Pode demorar alguns segundos para desinstalar a extensão.","app.components.ListPluginsPage.deletePlugin.title":"Desinstalando","app.components.ListPluginsPage.description":"Lista de extensões instaladas no projeto.","app.components.ListPluginsPage.head.title":"Lista de extensões","app.components.Logout.logout":"Sair","app.components.Logout.profile":"Perfil","app.components.MarketplaceBanner":"Descubra extensões da comunidade, e muito mais para dar início ao seu projeto, em Strapi Awesome.","app.components.MarketplaceBanner.image.alt":"um logo de foguete do strapi","app.components.MarketplaceBanner.link":"Ver agora","app.components.NotFoundPage.back":"Voltar à página inicial","app.components.NotFoundPage.description":"Não encontrado","app.components.Official":"Oficial","app.components.Onboarding.help.button":"Botão de ajuda","app.components.Onboarding.label.completed":"% concluído","app.components.Onboarding.title":"Vídeos de Introdução","app.components.PluginCard.Button.label.download":"Baixar","app.components.PluginCard.Button.label.install":"Já instalado","app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed":"O recurso autoReload precisa estar ativado. Por favor, inicie seu aplicativo com `yarn develop`.","app.components.PluginCard.PopUpWarning.install.impossible.confirm":"Eu compreendo!","app.components.PluginCard.PopUpWarning.install.impossible.environment":"Por motivos de segurança, uma extensão só pode ser baixada no ambiente de desenvolvimento.","app.components.PluginCard.PopUpWarning.install.impossible.title":"Não é possível baixar","app.components.PluginCard.compatible":"Compatível com a sua aplicação","app.components.PluginCard.compatibleCommunity":"Compatível com a comunidade","app.components.PluginCard.more-details":"Mais detalhes","app.components.ToggleCheckbox.off-label":"Desativado","app.components.ToggleCheckbox.on-label":"Ativado","app.components.Users.MagicLink.connect":"Copie e compartilhe esse link para dar acesso ao usuário","app.components.Users.MagicLink.connect.sso":"Envie esse link para o usuário. O primeiro login pode ser feito por um provedor de SSO","app.components.Users.ModalCreateBody.block-title.details":"Detalhes do usuário","app.components.Users.ModalCreateBody.block-title.roles":"Papéis do usuário","app.components.Users.ModalCreateBody.block-title.roles.description":"Um usuário pode ter um ou mais papéis","app.components.Users.SortPicker.button-label":"Ordenar por","app.components.Users.SortPicker.sortby.email_asc":"E-mail (A a Z)","app.components.Users.SortPicker.sortby.email_desc":"E-mail (Z a A)","app.components.Users.SortPicker.sortby.firstname_asc":"Primeiro nome (A a Z)","app.components.Users.SortPicker.sortby.firstname_desc":"Primeiro nome (Z a A)","app.components.Users.SortPicker.sortby.lastname_asc":"Último nome (A a Z)","app.components.Users.SortPicker.sortby.lastname_desc":"Último nome (Z a A)","app.components.Users.SortPicker.sortby.username_asc":"Nome de usuário (A a Z)","app.components.Users.SortPicker.sortby.username_desc":"Nome de usuário (Z a A)","app.components.listPlugins.button":"Adicionar nova Extensão","app.components.listPlugins.title.none":"Nenhuma extensão instalada","app.components.listPluginsPage.deletePlugin.error":"Ocorreu um erro ao desinstalar extensão","app.containers.App.notification.error.init":"Ocorreu um erro ao solicitar a API","app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin":"Se você não receber esse link, por favor contate seu administrador.","app.containers.AuthPage.ForgotPasswordSuccess.text.email":"Pode levar alguns minutos para receber seu link de recuperação de senha.","app.containers.AuthPage.ForgotPasswordSuccess.title":"E-mail enviado","app.containers.Users.EditPage.form.active.label":"Ativo","app.containers.Users.EditPage.header.label":"Editar {name}","app.containers.Users.EditPage.header.label-loading":"Editar usuário","app.containers.Users.EditPage.roles-bloc-title":"Papéis atribuídos","app.containers.Users.ModalForm.footer.button-success":"Convidar usuário","app.links.configure-view":"Configurar a visualização","app.page.not.found":"Ops! Não conseguimos encontrar a página que você está procurando...","app.static.links.cheatsheet":"CheatSheet","app.utils.SelectOption.defaultMessage":" ","app.utils.add-filter":"Adicionar filtro","app.utils.close-label":"Fechar","app.utils.defaultMessage":" ","app.utils.duplicate":"Duplicar","app.utils.edit":"Editar","app.utils.errors.file-too-big.message":"O arquivo é muito grande","app.utils.filter-value":"Filtrar valor","app.utils.filters":"Filtros","app.utils.notify.data-loaded":"{target} foi carregado","app.utils.placeholder.defaultMessage":" ","app.utils.publish":"Publicar","app.utils.select-all":"Selecionar todos","app.utils.select-field":"Selecionar campo","app.utils.select-filter":"Selecionar filtro","app.utils.unpublish":"Despublicar",clearLabel:d,"coming.soon":"Esse conteúdo está sendo construído e voltará dentro de algumas semanas!","component.Input.error.validation.integer":"O valor deve ser um inteiro","components.AutoReloadBlocker.description":"Execute o Strapi com um dos seguintes comandos:","components.AutoReloadBlocker.header":"Auto recarregamento é necessário para esta extensão.","components.ErrorBoundary.title":"Algo deu errado...","components.FilterOptions.FILTER_TYPES.$contains":"contém","components.FilterOptions.FILTER_TYPES.$containsi":"contém (não diferencia maiúsculas de minúsculas)","components.FilterOptions.FILTER_TYPES.$endsWith":"termina com","components.FilterOptions.FILTER_TYPES.$endsWithi":"termina com (não diferencia maiúsculas de minúsculas)","components.FilterOptions.FILTER_TYPES.$eq":"é igual a","components.FilterOptions.FILTER_TYPES.$eqi":"é igual a (não diferencia maiúsculas de minúsculas)","components.FilterOptions.FILTER_TYPES.$gt":"é maior que","components.FilterOptions.FILTER_TYPES.$gte":"é maior que ou igual a","components.FilterOptions.FILTER_TYPES.$lt":"é menor que","components.FilterOptions.FILTER_TYPES.$lte":"é menor que ou igual a","components.FilterOptions.FILTER_TYPES.$ne":"é diferente de","components.FilterOptions.FILTER_TYPES.$nei":"é diferente de (não diferencia maiúsculas de minúsculas)","components.FilterOptions.FILTER_TYPES.$notContains":"não contém","components.FilterOptions.FILTER_TYPES.$notContainsi":"não contém (não diferencia maiúsculas de minúsculas)","components.FilterOptions.FILTER_TYPES.$notNull":"não é vazio","components.FilterOptions.FILTER_TYPES.$null":"é vazio","components.FilterOptions.FILTER_TYPES.$startsWith":"começa com","components.FilterOptions.FILTER_TYPES.$startsWithi":"começa com (não diferencia maiúsculas de minúsculas)","components.Input.error.attribute.key.taken":"Este valor já existe","components.Input.error.attribute.sameKeyAndName":"Não pode ser igual","components.Input.error.attribute.taken":"O nome deste campo já existe","components.Input.error.contain.lowercase":"A senha deve conter pelo menos uma letra minúscula","components.Input.error.contain.number":"A senha deve conter pelo menos um número","components.Input.error.contain.uppercase":"A senha deve conter pelo menos uma letra maiúscula","components.Input.error.contentTypeName.taken":"Este tipo de conteúdo já existe","components.Input.error.custom-error":"{errorMessage} ","components.Input.error.password.noMatch":"As senhas não conferem","components.Input.error.validation.email":"Isto não é um endereço de e-mail","components.Input.error.validation.json":"Isto não corresponde ao formato JSON","components.Input.error.validation.lowercase":"O valor deve ser um texto com letras minúsculas","components.Input.error.validation.max":"O valor é muito alto {max}.","components.Input.error.validation.maxLength":"O valor é muito longo {max}.","components.Input.error.validation.min":"O valor é muito baixo {min}.","components.Input.error.validation.minLength":"O valor é muito curto {min}.","components.Input.error.validation.minSupMax":"Não pode ser superior","components.Input.error.validation.regex":"O valor não corresponde ao regex.","components.Input.error.validation.required":"Este valor é obrigatório.","components.Input.error.validation.unique":"Este valor já foi usado.","components.InputSelect.option.placeholder":"Escolha aqui","components.ListRow.empty":"Não existe nenhum registro para ser exibido","components.NotAllowedInput.text":"Sem permissões para ver esse campo","components.OverlayBlocker.description":"Você está usando um recurso que precisa que o servidor seja reiniciado. Por favor, aguarde até que o servidor esteja totalmente reiniciado.","components.OverlayBlocker.description.serverError":"O servidor deve ter sido reiniciado. Verifique seus registros no terminal.","components.OverlayBlocker.title":"Aguardando pela reinicialização...","components.OverlayBlocker.title.serverError":"A reinicialização levou mais tempo que o esperado","components.PageFooter.select":"registros por página","components.ProductionBlocker.description":"Por motivos de segurança, temos que desativar esta extensão em outros ambientes.","components.ProductionBlocker.header":"Esta extensão está disponível apenas em modo de desenvolvimento.","components.Search.placeholder":"Buscar...","components.TableHeader.sort":"Ordenar por {label}","components.Wysiwyg.ToggleMode.markdown-mode":"Modo de edição","components.Wysiwyg.ToggleMode.preview-mode":"Modo de visualização","components.Wysiwyg.collapse":"Fechar","components.Wysiwyg.selectOptions.H1":"Título H1","components.Wysiwyg.selectOptions.H2":"Título H2","components.Wysiwyg.selectOptions.H3":"Título H3","components.Wysiwyg.selectOptions.H4":"Título H4","components.Wysiwyg.selectOptions.H5":"Título H5","components.Wysiwyg.selectOptions.H6":"Título H6","components.Wysiwyg.selectOptions.title":"Adicionar um título","components.WysiwygBottomControls.charactersIndicators":"caracteres","components.WysiwygBottomControls.fullscreen":"Expandir","components.WysiwygBottomControls.uploadFiles":"Arraste e solte arquivos, cole na área de transferência ou {browse}.","components.WysiwygBottomControls.uploadFiles.browse":"Selecione-os","components.pagination.go-to":"Ir pra página {page}","components.pagination.go-to-next":"Ir pra próxima página","components.pagination.go-to-previous":"Ir pra página anterior","components.pagination.remaining-links":"E {number} outros links","components.popUpWarning.button.cancel":"Não, cancelar","components.popUpWarning.button.confirm":"Sim, confirmar","components.popUpWarning.message":"Tem certeza que deseja remover isso?","components.popUpWarning.title":"Por favor, confirme","form.button.continue":"Continuar","form.button.done":"Pronto","global.search":"Pesquisar","global.actions":"Ações","global.active":"Ativo","global.inactive":"Inativo","global.back":"Voltar","global.cancel":"Cancelar","global.change-password":"Alterar senha","global.content-manager":"Gerenciador de Conteúdo","global.continue":"Continuar","global.delete":"Deletar","global.delete-target":"Deletar {target}","global.description":"Descrição","global.details":"Detalhes","global.disabled":"Desabilitado","global.documentation":"Documentação","global.enabled":"Habilitado","global.finish":"Finalizar","global.marketplace":"Loja","global.name":"Nome","global.none":"Nenhum","global.password":"Senha","global.plugins":"Extensões","global.plugins.content-manager":"Gerenciador de Conteúdo","global.plugins.content-manager.description":"Maneira rápida de ver, editar e excluir os dados em seu banco de dados.","global.plugins.content-type-builder":"Criador de Tipos de Conteúdo","global.plugins.content-type-builder.description":"Modele a estrutura de dados da sua API. Crie novos campos e relações em apenas um minuto. Os arquivos são criados e atualizados automaticamente em seu projeto.","global.plugins.email":"Email","global.plugins.email.description":"Configure seu aplicativo para enviar emails.","global.plugins.upload":"Biblioteca de mídia","global.plugins.upload.description":"Gerenciamento de arquivos de mídia.","global.plugins.graphql":"GraphQL","global.plugins.graphql.description":"Adiciona o endpoint GraphQL com métodos de API padrão.","global.plugins.documentation":"Documentação","global.plugins.documentation.description":"Crie um documento OpenAPI e visualize sua API com SWAGGER UI.","global.plugins.i18n":"Internacionalização","global.plugins.i18n.description":"Esta extensão permite criar, ler e atualizar conteúdo em diferentes idiomas, tanto do Painel Administrativo quanto da API.","global.plugins.sentry":"Sentry","global.plugins.sentry.description":"Envie eventos de erro do Strapi para o Sentry.","global.plugins.users-permissions":"Funções e permissões","global.plugins.users-permissions.description":"Proteja sua API com um processo de autenticação completo baseado em JWT. Esta extensão também vem com uma estratégia de ACL que permite gerenciar as permissões entre os grupos de usuários.","global.profile":"Perfil","global.prompt.unsaved":"Você tem certeza que deseja sair desta página? Todas as suas modificações serão perdidas","global.reset-password":"Redefinir senha","global.roles":"Funções","global.save":"Salvar","global.see-more":"Ver mais","global.select":"Selecionar","global.select-all-entries":"Selecionar todas as entradas","global.settings":"Configurações","global.strapi-super-admin":"Super Admin","global.strapi-editor":"Editor","global.strapi-author":"Autor","global.table.header.email":"Email","global.table.header.firstname":"Nome","global.table.header.isActive":"Status do usuário","global.table.header.lastname":"Sobrenome","global.table.header.roles":"Funções","global.table.header.username":"Usuário","global.type":"Tipo","global.users":"Usuários","notification.contentType.relations.conflict":"Tipo de conteúdo tem relacionamentos conflitantes","notification.default.title":"Informação:","notification.error":"Ocorreu um erro","notification.error.layout":"Não foi possível recuperar o layout","notification.form.error.fields":"O formulário contém alguns erros","notification.form.success.fields":"Mudanças salvas","notification.link-copied":"Link copiado pra área de transferência","notification.permission.not-allowed-read":"Você não tem permissão para ver esse documento","notification.success.delete":"O item foi removido","notification.success.saved":"Salvo","notification.success.title":"Sucesso:","notification.version.update.message":"Uma nova versão do Strapi está disponível!","notification.warning.title":"Aviso:","notification.warning.404":"404 - Página não encontrada",or:u,"request.error.model.unknown":"Este modelo não existe",skipToContent:g,submit:b};export{e as Analytics,o as Documentation,a as Email,t as Password,n as Provider,s as ResetPasswordToken,i as Role,l as Username,c as Users,m as anErrorOccurred,d as clearLabel,p as dark,f as default,r as light,u as or,g as skipToContent,b as submit};
