import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/plugin-users-permissions/dist/admin/translations/it.json.mjs
var it = {
  "BoundRoute.title": "Vincola route a",
  "EditForm.inputSelect.description.role": "Questa operazione assocerà i nuovi utenti autenticati al ruolo selezionato.",
  "EditForm.inputSelect.label.role": "Ruolo di default per gli utenti registrati",
  "EditForm.inputToggle.description.email": "Non consentire all'utente di creare account multipli usando lo stesso indirizzo email con provider di autenticazione diversi.",
  "EditForm.inputToggle.description.email-confirmation": "Quando abilitato (ON), i nuovi utenti registrati riceveranno una richiesta di conferma via email.",
  "EditForm.inputToggle.description.email-confirmation-redirection": "Scegli dove redirigere gli utenti che completano la conferma dell'indirizzo email.",
  "EditForm.inputToggle.description.email-reset-password": "URL della pagina per il reset della password della tua applicazione",
  "EditForm.inputToggle.description.sign-up": "Quando disabilitata (OFF), il processo di registrazione è proibito. Nessuno può iscriversi indipendentemente dal provider utilizzato.",
  "EditForm.inputToggle.label.email": "Un solo account per indirizzo email",
  "EditForm.inputToggle.label.email-confirmation": "Abilita conferma email",
  "EditForm.inputToggle.label.email-confirmation-redirection": "URL di reindirizzamento",
  "EditForm.inputToggle.label.email-reset-password": "Pagina reset password",
  "EditForm.inputToggle.label.sign-up": "Abilita registrazione",
  "Email.template.email_confirmation": "Conferma dell'indirizzo Email",
  "HeaderNav.link.advancedSettings": "Impostazioni avanzate",
  "HeaderNav.link.emailTemplates": "Template delle Email",
  "HeaderNav.link.providers": "Provider",
  "Plugin.permissions.plugins.description": "Definisce tutte le azioni consentite per il plugin {name}.",
  "Plugins.header.description": "Di seguito sono elencate solo le azioni vincolate da una route.",
  "Plugins.header.title": "Permessi",
  "Policies.header.hint": "Seleziona le azioni dell'applicazione o del plugin e clicca sull'ingranaggio per mostrare il percorso corrispondente",
  "Policies.header.title": "Impostazioni avanzate",
  "PopUpForm.Email.email_templates.inputDescription": "Se non sai bene come usare le variabili, {link}",
  "PopUpForm.Email.link.documentation": "controlla la nostra documentazione.",
  "PopUpForm.Email.options.from.email.label": "Email del mittente",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Nome del mittente",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "Messaggio",
  "PopUpForm.Email.options.object.label": "Oggetto",
  "PopUpForm.Email.options.object.placeholder": "Conferma il tuo indirizzo email per %APP_NAME%",
  "PopUpForm.Email.options.response_email.label": "Email di risposta",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "Se disabilitato, gli utenti non potranno usare questo provider.",
  "PopUpForm.Providers.enabled.label": "Abilita",
  "PopUpForm.Providers.key.label": "Client ID",
  "PopUpForm.Providers.key.placeholder": "TEXT",
  "PopUpForm.Providers.redirectURL.front-end.label": "L'URL di reindirizzamento per la tua app di front-end",
  "PopUpForm.Providers.redirectURL.label": "L'URL di reindirizzamento da aggiungere nelle impostazioni dell'applicazione di {provider}",
  "PopUpForm.Providers.secret.label": "Client Secret",
  "PopUpForm.Providers.secret.placeholder": "TEXT",
  "PopUpForm.Providers.subdomain.label": "Host URI (Sottodominio)",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "Modifica i Template Email",
  "PopUpForm.header.edit.providers": "Modifica Provider",
  "Settings.roles.deleted": "Ruolo eliminato",
  "Settings.roles.edited": "Ruolo modificato",
  "Settings.section-label": "Plugin Utenti & Permessi",
  "notification.success.submit": "Impostazioni aggiornate",
  "plugin.description.long": "Proteggi le tue API con un processo di autenticazione completo basato su JWT. Questo plugin è implementato con una strategia ACL che ti consente di gestire i permessi tra i gruppi di utenti.",
  "plugin.description.short": "Proteggi le tue API con un processo di autenticazione completo basato su JWT",
  "plugin.name": "Plugin Utenti & Permessi",
  "popUpWarning.button.cancel": "Annulla",
  "popUpWarning.button.confirm": "Conferma",
  "popUpWarning.title": "Si prega di confermare",
  "popUpWarning.warning.cancel": "Sei sicuro di voler annullare le tue modifiche?"
};
export {
  it as default
};
//# sourceMappingURL=it.json-AXAYYBTP.js.map
