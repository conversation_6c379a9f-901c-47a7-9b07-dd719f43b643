{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/Widgets.tsx"], "sourcesContent": ["import { useAuth } from '@strapi/admin/strapi-admin';\nimport { Avatar, Badge, Flex, Typography } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nimport { getDisplayName, getInitials } from '../utils/users';\n\n/* -------------------------------------------------------------------------------------------------\n * ProfileWidget\n * -----------------------------------------------------------------------------------------------*/\n\nconst DisplayNameTypography = styled(Typography)`\n  font-size: 2.4rem;\n`;\n\nconst ProfileWidget = () => {\n  const user = useAuth('User', (state) => state.user);\n  const userDisplayName = getDisplayName(user);\n  const initials = getInitials(user);\n\n  return (\n    <Flex direction=\"column\" gap={3} height=\"100%\" justifyContent=\"center\">\n      <Avatar.Item delayMs={0} fallback={initials} />\n      {userDisplayName && (\n        <DisplayNameTypography fontWeight=\"bold\" textTransform=\"none\" textAlign=\"center\">\n          {userDisplayName}\n        </DisplayNameTypography>\n      )}\n      {user?.email && (\n        <Typography variant=\"omega\" textColor=\"neutral600\">\n          {user?.email}\n        </Typography>\n      )}\n      {user?.roles?.length && (\n        <Flex marginTop={2} gap={1} wrap=\"wrap\">\n          {user?.roles?.map((role) => <Badge key={role.id}>{role.name}</Badge>)}\n        </Flex>\n      )}\n    </Flex>\n  );\n};\n\nexport { ProfileWidget };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAMA,wBAAwBC,GAAOC,UAAAA;;;AAIrC,IAAMC,gBAAgB,MAAA;;AACpB,QAAMC,OAAOC,QAAQ,QAAQ,CAACC,UAAUA,MAAMF,IAAI;AAClD,QAAMG,kBAAkBC,eAAeJ,IAAAA;AACvC,QAAMK,WAAWC,YAAYN,IAAAA;AAE7B,aACEO,yBAACC,MAAAA;IAAKC,WAAU;IAASC,KAAK;IAAGC,QAAO;IAAOC,gBAAe;;UAC5DC,wBAACC,OAAOC,MAAI;QAACC,SAAS;QAAGC,UAAUZ;;MAClCF,uBACCU,wBAACjB,uBAAAA;QAAsBsB,YAAW;QAAOC,eAAc;QAAOC,WAAU;QACrEjB,UAAAA;;OAGJH,6BAAMqB,cACLR,wBAACf,YAAAA;QAAWwB,SAAQ;QAAQC,WAAU;kBACnCvB,6BAAMqB;;QAGVrB,kCAAMwB,UAANxB,mBAAayB,eACZZ,wBAACL,MAAAA;QAAKkB,WAAW;QAAGhB,KAAK;QAAGiB,MAAK;QAC9B3B,WAAAA,kCAAMwB,UAANxB,mBAAa4B,IAAI,CAACC,aAAShB,wBAACiB,OAAAA;UAAqBD,UAAAA,KAAKE;QAAfF,GAAAA,KAAKG,EAAE;;;;AAKzD;", "names": ["DisplayNameTypography", "styled", "Typography", "ProfileWidget", "user", "useAuth", "state", "userDisplayName", "getDisplayName", "initials", "getInitials", "_jsxs", "Flex", "direction", "gap", "height", "justifyContent", "_jsx", "Avatar", "<PERSON><PERSON>", "delayMs", "fallback", "fontWeight", "textTransform", "textAlign", "email", "variant", "textColor", "roles", "length", "marginTop", "wrap", "map", "role", "Badge", "name", "id"]}