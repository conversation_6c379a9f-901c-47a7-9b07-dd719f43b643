const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["index-sxriPiMC.js","strapi-z7ApxZZq.js","strapi-D6821bIJ.css","useReviewWorkflows-DZsAibvX.js","useDragLayer-DybN2c0t.js","id-BGCvlLJa.js"])))=>i.map(i=>d[i]);
import{j as e,R as r,r as o,Q as s,_ as a}from"./strapi-z7ApxZZq.js";const d=o.lazy(()=>a(()=>import("./index-sxriPiMC.js"),__vite__mapDeps([0,1,2,3,4])).then(t=>({default:t.ProtectedListPage}))),i=o.lazy(()=>a(()=>import("./id-BGCvlLJa.js"),__vite__mapDeps([5,1,2,3,4])).then(t=>({default:t.ProtectedEditPage}))),_=[{path:"/",Component:d},{path:":id",Component:i}],p=()=>e.jsx(r,{children:_.map(t=>e.jsx(s,{...t},t.path))});export{p as Router};
