import{bV as C,c4 as k,j as e,P as d,c5 as z,a as L,w as S,bW as T,r as x,c0 as I,c6 as w,c1 as R,c7 as i,B as m,D as A,E as P,e as f,$ as F,T as H,H as r,M as a,c8 as h}from"./strapi-z7ApxZZq.js";const N=t=>t,_={initialData:{responsiveDimensions:!0,sizeOptimization:!0,autoOrientation:!1,videoPreview:!1},modifiedData:{responsiveDimensions:!0,sizeOptimization:!0,autoOrientation:!1,videoPreview:!1}},G=(t,n)=>C(t,o=>{switch(n.type){case"GET_DATA_SUCCEEDED":{o.initialData=n.data,o.modifiedData=n.data;break}case"ON_CHANGE":{k(o,["modifiedData",...n.keys.split(".")],n.value);break}default:return t}}),q=()=>{const{formatMessage:t}=L(),{toggleNotification:n}=S(),{get:o,put:j}=T(),[{initialData:y,modifiedData:l},p]=x.useReducer(G,_,N),{data:c,isLoading:M,refetch:D}=I({queryKey:["upload","settings"],async queryFn(){const{data:{data:s}}=await o("/upload/settings");return s}});x.useEffect(()=>{c&&p({type:"GET_DATA_SUCCEEDED",data:c})},[c]);const b=w(y,l),{mutateAsync:O,isLoading:v}=R(async s=>{const{data:u}=await j("/upload/settings",s);return u},{onSuccess(){D(),n({type:"success",message:t({id:"notification.form.success.fields"})})},onError(s){console.error(s)}}),E=async s=>{s.preventDefault(),!b&&await O(l)},g=({target:{name:s,value:u}})=>{p({type:"ON_CHANGE",keys:s,value:u})};return M?e.jsx(d.Loading,{}):e.jsxs(d.Main,{tabIndex:-1,children:[e.jsx(d.Title,{children:t({id:i("page.title"),defaultMessage:"Settings - Media Libray"})}),e.jsxs("form",{onSubmit:E,children:[e.jsx(m.Header,{title:t({id:i("settings.header.label"),defaultMessage:"Media Library"}),primaryAction:e.jsx(A,{disabled:b,loading:v,type:"submit",startIcon:e.jsx(P,{}),size:"S",children:t({id:"global.save",defaultMessage:"Save"})}),subtitle:t({id:i("settings.sub-header.label"),defaultMessage:"Configure the settings for the Media Library"})}),e.jsx(m.Content,{children:e.jsx(m.Root,{children:e.jsx(f,{direction:"column",alignItems:"stretch",gap:12,children:e.jsx(F,{background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:e.jsxs(f,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(f,{children:e.jsx(H,{variant:"delta",tag:"h2",children:t({id:i("settings.blockTitle"),defaultMessage:"Asset management"})})}),e.jsxs(r.Root,{gap:6,children:[e.jsx(r.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(a.Root,{hint:t({id:i("settings.form.responsiveDimensions.description"),defaultMessage:"Enabling this option will generate multiple formats (small, medium and large) of the uploaded asset."}),name:"responsiveDimensions",children:[e.jsx(a.Label,{children:t({id:i("settings.form.responsiveDimensions.label"),defaultMessage:"Responsive friendly upload"})}),e.jsx(h,{checked:l?.responsiveDimensions,offLabel:t({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"Off"}),onLabel:t({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"On"}),onChange:s=>{g({target:{name:"responsiveDimensions",value:s.target.checked}})}}),e.jsx(a.Hint,{})]})}),e.jsx(r.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(a.Root,{hint:t({id:i("settings.form.sizeOptimization.description"),defaultMessage:"Enabling this option will reduce the image size and slightly reduce its quality."}),name:"sizeOptimization",children:[e.jsx(a.Label,{children:t({id:i("settings.form.sizeOptimization.label"),defaultMessage:"Size optimization"})}),e.jsx(h,{checked:l?.sizeOptimization,offLabel:t({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"Off"}),onLabel:t({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"On"}),onChange:s=>{g({target:{name:"sizeOptimization",value:s.target.checked}})}}),e.jsx(a.Hint,{})]})}),e.jsx(r.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(a.Root,{hint:t({id:i("settings.form.autoOrientation.description"),defaultMessage:"Enabling this option will automatically rotate the image according to EXIF orientation tag."}),name:"autoOrientation",children:[e.jsx(a.Label,{children:t({id:i("settings.form.autoOrientation.label"),defaultMessage:"Auto orientation"})}),e.jsx(h,{checked:l?.autoOrientation,offLabel:t({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"Off"}),onLabel:t({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"On"}),onChange:s=>{g({target:{name:"autoOrientation",value:s.target.checked}})}}),e.jsx(a.Hint,{})]})})]})]})})})})})]})]})},U=()=>e.jsx(d.Protect,{permissions:z.settings,children:e.jsx(q,{})});export{U as ProtectedSettingsPage,q as SettingsPage};
