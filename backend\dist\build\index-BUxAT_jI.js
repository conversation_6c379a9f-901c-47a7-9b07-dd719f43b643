import{aP as x,aQ as u,aR as o,a as y,j as e,cc as m,cq as Z,cr as w,bZ as s,bq as T,C as G,H as S,aU as X,D as R,W as Y,bn as J,Y as b,bo as E,T as M,X as K,Z as c,$ as v,cF as ee,I as U,g as I,E as se,b as ae,aM as ie,w as te,b_ as le,bW as re,v as ne,r as _,z as de,b$ as $,c0 as oe,c1 as me,P as g,B as k}from"./strapi-z7ApxZZq.js";import{P as i}from"./index-BpJMkQJ_.js";const ce=x().shape({options:x().shape({from:x().shape({name:u().required({id:o.required.id,defaultMessage:"This field is required"}),email:u().email(o.email).required({id:o.required.id,defaultMessage:"This field is required"})}).required(),response_email:u().email(o.email),object:u().required({id:o.required.id,defaultMessage:"This field is required"}),message:u().required({id:o.required.id,defaultMessage:"This field is required"})}).required(o.required.id)}),P=({template:a={},onToggle:n,open:t,onSubmit:d})=>{const{formatMessage:l}=y();return e.jsx(m.Root,{open:t,onOpenChange:n,children:e.jsxs(m.Content,{children:[e.jsxs(m.Header,{children:[e.jsxs(Z,{label:`${l({id:s("PopUpForm.header.edit.email-templates"),defaultMessage:"Edit email template"})}, ${a.display?l({id:s(a.display),defaultMessage:a.display}):""}`,children:[e.jsx(w,{children:l({id:s("PopUpForm.header.edit.email-templates"),defaultMessage:"Edit email template"})}),e.jsx(w,{isCurrent:!0,children:a.display?l({id:s(a.display),defaultMessage:a.display}):""})]}),e.jsx(T,{children:e.jsx(m.Title,{children:`${l({id:s("PopUpForm.header.edit.email-templates"),defaultMessage:"Edit email template"})}, ${a.display?l({id:s(a.display),defaultMessage:a.display}):""}`})})]}),e.jsx(G,{onSubmit:d,initialValues:a,validationSchema:ce,children:({isSubmitting:h})=>e.jsxs(e.Fragment,{children:[e.jsx(m.Body,{children:e.jsx(S.Root,{gap:5,children:[{label:l({id:s("PopUpForm.Email.options.from.name.label"),defaultMessage:"Shipper name"}),name:"options.from.name",size:6,type:"string"},{label:l({id:s("PopUpForm.Email.options.from.email.label"),defaultMessage:"Shipper email"}),name:"options.from.email",size:6,type:"string"},{label:l({id:s("PopUpForm.Email.options.response_email.label"),defaultMessage:"Response email"}),name:"options.response_email",size:6,type:"string"},{label:l({id:s("PopUpForm.Email.options.object.label"),defaultMessage:"Subject"}),name:"options.object",size:6,type:"string"},{label:l({id:s("PopUpForm.Email.options.message.label"),defaultMessage:"Message"}),name:"options.message",size:12,type:"text"}].map(({size:f,...p})=>e.jsx(S.Item,{col:f,direction:"column",alignItems:"stretch",children:e.jsx(X,{...p})},p.name))})}),e.jsxs(m.Footer,{children:[e.jsx(m.Close,{children:e.jsx(R,{variant:"tertiary",children:"Cancel"})}),e.jsx(R,{loading:h,type:"submit",children:"Finish"})]})]})})]})})};P.defaultProps={template:{}};P.propTypes={template:i.shape({display:i.string,icon:i.string,options:i.shape({from:i.shape({name:i.string,email:i.string}),message:i.string,object:i.string,response_email:i.string})}),open:i.bool.isRequired,onSubmit:i.func.isRequired,onToggle:i.func.isRequired};const L=({canUpdate:a,onEditClick:n})=>{const{formatMessage:t}=y();return e.jsxs(Y,{colCount:3,rowCount:3,children:[e.jsx(J,{children:e.jsxs(b,{children:[e.jsx(E,{width:"1%",children:e.jsx(T,{children:t({id:s("Email.template.table.icon.label"),defaultMessage:"icon"})})}),e.jsx(E,{children:e.jsx(M,{variant:"sigma",textColor:"neutral600",children:t({id:s("Email.template.table.name.label"),defaultMessage:"name"})})}),e.jsx(E,{width:"1%",children:e.jsx(T,{children:t({id:s("Email.template.table.action.label"),defaultMessage:"action"})})})]})}),e.jsxs(K,{children:[e.jsxs(b,{cursor:"pointer",onClick:()=>n("reset_password"),children:[e.jsx(c,{children:e.jsx(v,{width:"3.2rem",height:"3.2rem",padding:"0.8rem",children:e.jsx(ee,{"aria-label":t({id:"global.reset-password",defaultMessage:"Reset password"})})})}),e.jsx(c,{children:e.jsx(M,{children:t({id:"global.reset-password",defaultMessage:"Reset password"})})}),e.jsx(c,{onClick:d=>d.stopPropagation(),children:e.jsx(U,{onClick:()=>n("reset_password"),label:t({id:s("Email.template.form.edit.label"),defaultMessage:"Edit a template"}),variant:"ghost",disabled:!a,children:e.jsx(I,{})})})]}),e.jsxs(b,{cursor:"pointer",onClick:()=>n("email_confirmation"),children:[e.jsx(c,{children:e.jsx(v,{width:"3.2rem",height:"3.2rem",padding:"0.8rem",children:e.jsx(se,{"aria-label":t({id:s("Email.template.email_confirmation"),defaultMessage:"Email address confirmation"})})})}),e.jsx(c,{children:e.jsx(M,{children:t({id:s("Email.template.email_confirmation"),defaultMessage:"Email address confirmation"})})}),e.jsx(c,{onClick:d=>d.stopPropagation(),children:e.jsx(U,{onClick:()=>n("email_confirmation"),label:t({id:s("Email.template.form.edit.label"),defaultMessage:"Edit a template"}),variant:"ghost",disabled:!a,children:e.jsx(I,{})})})]})]})]})};L.propTypes={canUpdate:i.bool.isRequired,onEditClick:i.func.isRequired};const he=()=>e.jsx(g.Protect,{permissions:$.readEmailTemplates,children:e.jsx(pe,{})}),pe=()=>{const{formatMessage:a}=y(),{trackUsage:n}=ae(),{notifyStatus:t}=ie(),{toggleNotification:d}=te(),l=le(),{get:h,put:f}=re(),{formatAPIError:p}=ne(),[z,B]=_.useState(!1),[q,H]=_.useState(null),{isLoading:A,allowedActions:{canUpdate:N}}=de({update:$.updateEmailTemplates}),{isLoading:O,data:C}=oe(["users-permissions","email-templates"],async()=>{const{data:r}=await h("/users-permissions/email-templates");return r},{onSuccess(){t(a({id:s("Email.template.data.loaded"),defaultMessage:"Email templates has been loaded"}))},onError(r){d({type:"danger",message:p(r)})}}),Q=A||O,j=()=>{B(r=>!r)},D=r=>{H(r),j()},F=me(r=>f("/users-permissions/email-templates",{"email-templates":r}),{async onSuccess(){await l.invalidateQueries(["users-permissions","email-templates"]),d({type:"success",message:a({id:"notification.success.saved",defaultMessage:"Saved"})}),n("didEditEmailTemplates"),j()},onError(r){d({type:"danger",message:p(r)})},refetchActive:!0}),V=r=>{n("willEditEmailTemplates");const W={...C,[q]:r};F.mutate(W)};return Q?e.jsx(g.Loading,{}):e.jsxs(g.Main,{"aria-busy":F.isLoading,children:[e.jsx(g.Title,{children:a({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:a({id:s("HeaderNav.link.emailTemplates"),defaultMessage:"Email templates"})})}),e.jsx(k.Header,{title:a({id:s("HeaderNav.link.emailTemplates"),defaultMessage:"Email templates"})}),e.jsxs(k.Content,{children:[e.jsx(L,{onEditClick:D,canUpdate:N}),e.jsx(P,{template:C[q],onToggle:j,open:z,onSubmit:V})]})]})};export{pe as EmailTemplatesPage,he as ProtectedEmailTemplatesPage};
