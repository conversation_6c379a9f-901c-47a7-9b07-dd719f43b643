{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/ms.json.mjs"], "sourcesContent": ["var Analytics = \"Analisis\";\nvar Documentation = \"Dokumen\";\nvar Email = \"Email\";\nvar Password = \"Kata Laluan\";\nvar Provider = \"Penyedia\";\nvar ResetPasswordToken = \"Token penetapan semula kata laluan\";\nvar Role = \"Peranan\";\nvar Username = \"Nama Pengg<PERSON>\";\nvar Users = \"Para Pengguna\";\nvar ms = {\n    Analytics: Analytics,\n    \"Auth.form.button.forgot-password\": \"Hantar e-mel\",\n    \"Auth.form.button.login\": \"Log masuk\",\n    \"Auth.form.button.register\": \"Sedia untuk mulakan\",\n    \"Auth.form.error.blocked\": \"<PERSON><PERSON>un anda telah disekat oleh pengelola.\",\n    \"Auth.form.error.code.provide\": \"Kod yang salah terlah diberikan.\",\n    \"Auth.form.error.confirmed\": \"E-mel akaun anda tidak disahkan lagi.\",\n    \"Auth.form.error.email.invalid\": \"E-mel ini tidak sah.\",\n    \"Auth.form.error.email.provide\": \"Sila berikan nama pengguna atau e-mel anda.\",\n    \"Auth.form.error.email.taken\": \"E-mel sudah diambil.\",\n    \"Auth.form.error.invalid\": \"Username/Email atau kata laluan tidak tepat.\",\n    \"Auth.form.error.params.provide\": \"Parameter yang tidak tepat telah diberikan\",\n    \"Auth.form.error.password.format\": \"Kata laluan anda tidak boleh mengandungi simbol `$` lebih dari tiga kali.\",\n    \"Auth.form.error.password.local\": \"Pengguna ini tidak pernah menetapkan kata laluan local, sila log masuk melalui provider yang digunakan sewaktu pembuatan akaun.\",\n    \"Auth.form.error.password.matching\": \"Kata laluan tidak sepadan.\",\n    \"Auth.form.error.password.provide\": \"Sila isikan kata laluan anda.\",\n    \"Auth.form.error.ratelimit\": \"Terlalu banyak percubaan, sila cuba sebentar lagi.\",\n    \"Auth.form.error.user.not-exist\": \"E-mel ini tidak wujud.\",\n    \"Auth.form.error.username.taken\": \"Nama pengguna sudah diambil.\",\n    \"Auth.form.forgot-password.email.label\": \"Masukkan emel anda\",\n    \"Auth.form.forgot-password.email.label.success\": \"E-mel berjaya dihantar ke\",\n    \"Auth.form.register.news.label\": \"Ikuti perkembangan terkini mengenai ciri baru dan penambahbaikan yang akan datang (dengan melakukan ini, anda menerima {terms} dan {policy}).\",\n    \"Auth.link.forgot-password\": \"Lupa kata laluan anda ?\",\n    \"Auth.link.ready\": \"Sedia untuk log masuk?\",\n    \"Auth.privacy-policy-agreement.policy\": \"dasar privasi\",\n    \"Auth.privacy-policy-agreement.terms\": \"syarat\",\n    \"Content Manager\": \"Pengurus Kandungan\",\n    \"Content Type Builder\": \"Pembina Jenis Kandungan\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Fail Muat Naik\",\n    \"HomePage.head.title\": \"Halaman Utama\",\n    \"HomePage.roadmap\": \"Lihat jadual kerja kami\",\n    \"HomePage.welcome.congrats\": \"Tahniah!\",\n    \"HomePage.welcome.congrats.content\": \"Anda telah masuk sebagai pengelola yang pertama. Untuk meneroka ciri yang bagus disediakan oleh Strapi,\",\n    \"HomePage.welcome.congrats.content.bold\": \"kami mengesyorkan anda untuk buat Jenis Koleksi yang pertama.\",\n    \"New entry\": \"Entri baru\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Roles & Permissions\": \"Peranan & Keizinan\",\n    \"Settings.error\": \"Ralat\",\n    \"Settings.global\": \"Tetapan global\",\n    \"Settings.webhooks.create\": \"Buat satu webhook\",\n    \"Settings.webhooks.create.header\": \"Buat header baru\",\n    \"Settings.webhooks.created\": \"Webhook telah dibuat\",\n    \"Settings.webhooks.events.create\": \"Cipta\",\n    \"Settings.webhooks.form.events\": \"Sewaktu\",\n    \"Settings.webhooks.form.headers\": \"Tajuk\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.key\": \"Kata Kunci\",\n    \"Settings.webhooks.list.button.add\": \"Tambah webhook baru\",\n    \"Settings.webhooks.list.description\": \"Dapatkan pemberitahuan perubahan untuk POST.\",\n    \"Settings.webhooks.list.empty.description\": \"Tambah satu dalam senarai.\",\n    \"Settings.webhooks.list.empty.link\": \"Lihat dokumen kami\",\n    \"Settings.webhooks.list.empty.title\": \"Belum ada webhook\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhooks\",\n    \"Settings.webhooks.trigger\": \"Cetus\",\n    \"Settings.webhooks.trigger.cancel\": \"Batalkan pencetusan\",\n    \"Settings.webhooks.trigger.pending\": \"Belum selesai…\",\n    \"Settings.webhooks.trigger.save\": \"Sila simpan untuk cetuskan\",\n    \"Settings.webhooks.trigger.success\": \"Berjaya!\",\n    \"Settings.webhooks.trigger.success.label\": \"Cetusan berjaya\",\n    \"Settings.webhooks.trigger.test\": \"Uji Cetusan\",\n    \"Settings.webhooks.trigger.title\": \"Simpan sebelum cetus\",\n    \"Settings.webhooks.value\": \"Kandungan\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Para Pengguna & Keizinan\",\n    \"app.components.BlockLink.code\": \"Contoh Kod\",\n    \"app.components.Button.cancel\": \"Batal\",\n    \"app.components.Button.reset\": \"Set Semula\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Akan Datang\",\n    \"app.components.DownloadInfo.download\": \"Muat turun sedang dijalankan...\",\n    \"app.components.DownloadInfo.text\": \"Ini akan mengambil masa, terima kasih atas kesabaran anda.\",\n    \"app.components.EmptyAttributes.title\": \"Tiada ruang disini\",\n    \"app.components.HomePage.button.blog\": \"LIHAT LEBIH LAGI DI BLOG\",\n    \"app.components.HomePage.community\": \"Cari komuniti di web\",\n    \"app.components.HomePage.community.content\": \"Bincang dengan ahli kumpulan, penyumbang dan pembangun di saluran berbeza.\",\n    \"app.components.HomePage.create\": \"Cipta Jenis Kandungan anda\",\n    \"app.components.HomePage.welcome\": \"Selamat datang!\",\n    \"app.components.HomePage.welcome.again\": \"Selamat datang \",\n    \"app.components.HomePage.welcomeBlock.content\": \"Kami mengalu-alukan kedatangan anda di komuniti. Kami sentiasa mencari penambahbaikan, jadi jangan segan silu untuk mesej kami di \",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Kami harap anda membuat progress pada projek anda... Luangkan masa untuk membaca berita baru kami tentang strapi. Kami memberikan yang terbaik untuk menambah baik produk ini berdasarkan maklum balas anda.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"isu-isu.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" atau berikan \",\n    \"app.components.ImgPreview.hint\": \"Tarik & Lepas fail anda kedalam kawasan ini atau {browse} fail untuk muat naik\",\n    \"app.components.ImgPreview.hint.browse\": \"pilih fail\",\n    \"app.components.InputFile.newFile\": \"Tambah fail baru\",\n    \"app.components.InputFileDetails.open\": \"Buka di tab baru\",\n    \"app.components.InputFileDetails.originalName\": \"Nama asal:\",\n    \"app.components.InputFileDetails.remove\": \"Buang fail ini\",\n    \"app.components.InputFileDetails.size\": \"Saiz:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Ia mungkin mengambil beberapa saat untuk memuat turun dan memasang plugin.\",\n    \"app.components.InstallPluginPage.Download.title\": \"Memuat Turun...\",\n    \"app.components.InstallPluginPage.description\": \"Kembangkan aplikasi anda dengan mudah.\",\n    \"app.components.LeftMenuFooter.help\": \"Bantuan\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Dikuasakan oleh \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Jenis Koleksi\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Konfigurasi\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Umum\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Belum ada plugin yang dipasang\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Plugin-plugin\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Jenis Tunggal\",\n    \"app.components.ListPluginsPage.description\": \"Senarai plugin yang dipasang didalam projek ini.\",\n    \"app.components.ListPluginsPage.head.title\": \"Senarai plugin\",\n    \"app.components.Logout.logout\": \"Log Keluar\",\n    \"app.components.Logout.profile\": \"Profil\",\n    \"app.components.NotFoundPage.back\": \"Kembali ke laman utama\",\n    \"app.components.NotFoundPage.description\": \"Tidak dijumpai\",\n    \"app.components.Official\": \"Rasmi\",\n    \"app.components.Onboarding.label.completed\": \"% siap\",\n    \"app.components.Onboarding.title\": \"Video-video untuk bermula\",\n    \"app.components.PluginCard.Button.label.download\": \"Muat turun\",\n    \"app.components.PluginCard.Button.label.install\": \"Sudah dipasang\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Ciri AutoReload perlu diaktifkan. Sila mulakkan aplikasi anda dengan `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Saya Faham!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Untuk tujuan keselamatan, plugin hanya boleh di muat turun dalam environment 'development'.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Memuat turun adalah mustahil\",\n    \"app.components.PluginCard.compatible\": \"Serasi dengan applikasi anda\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Serasi dengan komuniti\",\n    \"app.components.PluginCard.more-details\": \"Butiran selanjutnya\",\n    \"app.components.listPlugins.button\": \"Tambah Plugin Baru\",\n    \"app.components.listPlugins.title.none\": \"Tiada plugin dipasang\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Satu ralat muncul ketika membuang plugin tersebut\",\n    \"app.containers.App.notification.error.init\": \"Ralat berlaku semasa permintaan API\",\n    \"app.links.configure-view\": \"Susun paparan\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.filters\": \"Tapisan\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"component.Input.error.validation.integer\": \"Nilainya haruslah dalam integer\",\n    \"components.AutoReloadBlocker.description\": \"Jalankan Strapi dengan salah satu arahan berikut:\",\n    \"components.AutoReloadBlocker.header\": \"ciri Reload diperlukan untuk plugin ini.\",\n    \"components.ErrorBoundary.title\": \"Ada sesuatu yang tidak kena...\",\n    \"components.Input.error.attribute.key.taken\": \"Nilai ini sudah wujud\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Tak boleh sama\",\n    \"components.Input.error.attribute.taken\": \"Nama kotak ini sudah wujud\",\n    \"components.Input.error.contentTypeName.taken\": \"Nama ini sudah wujud\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Kata laluan tidak sepadan\",\n    \"components.Input.error.validation.email\": \"Ini bukan email\",\n    \"components.Input.error.validation.json\": \"Ini tak sepadan dengan format JSON\",\n    \"components.Input.error.validation.max\": \"Nilai isinya terlalu tinggi {max}.\",\n    \"components.Input.error.validation.maxLength\": \"Panjang isinya terlalu panjang {max}.\",\n    \"components.Input.error.validation.min\": \"Nilai isinya terlalu rendah {min}.\",\n    \"components.Input.error.validation.minLength\": \"Panjang isinya terlalu pendek {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Tidak boleh lebih tinggi\",\n    \"components.Input.error.validation.regex\": \"Nilai isinya tidak sepadan dengan regex.\",\n    \"components.Input.error.validation.required\": \"Nilai ini adalah wajib.\",\n    \"components.Input.error.validation.unique\": \"Nilai ini telah digunakan.\",\n    \"components.InputSelect.option.placeholder\": \"Pilih disini\",\n    \"components.ListRow.empty\": \"Tiada data untuk pamirkan.\",\n    \"components.OverlayBlocker.description\": \"Anda menggunakan ciri yang memerlukan pelayan untuk dimulakan semula. Sila tunggu sehinggal pelayan habis.\",\n    \"components.OverlayBlocker.description.serverError\": \"Pelayan sepatutnya telah dimulakan semula, sila periksa log anda di terminal.\",\n    \"components.OverlayBlocker.title\": \"Menunggu untuk dimulakan semula...\",\n    \"components.OverlayBlocker.title.serverError\": \"Permulaan semula mengambil masa lebih lama daripada yang dijangkakan\",\n    \"components.PageFooter.select\": \"entri dipaparkan setiap halaman\",\n    \"components.ProductionBlocker.description\": \"Untuk tujuan keslamatan kami perlu menyahkan plugin didalam environment lain.\",\n    \"components.ProductionBlocker.header\": \"Plugin in hanya tersedia dalam pembangunan(development).\",\n    \"components.Search.placeholder\": \"Cari...\",\n    \"components.Wysiwyg.collapse\": \"Tutup\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Tajuk H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Tajuk H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Tajuk H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Tajuk H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Tajuk H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Tajuk H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Tambah tajuk\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"aksara\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Besarkan\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Tarik & Lepas fail, tampal dari clipboard atau {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"pilih\",\n    \"components.popUpWarning.message\": \"Anda yakin untuk memadam?\",\n    \"components.popUpWarning.title\": \"Sila sahkan\",\n    \"form.button.done\": \"Siap\",\n    \"global.prompt.unsaved\": \"Adakah anda pasti untuk meninggalkan halaman ini? Segala perubahan anda akan hilang\",\n    \"notification.contentType.relations.conflict\": \"Jenis kandungan ada hubungan(relations) yang saling bertentangan\",\n    \"notification.error\": \"satu ralat muncul\",\n    \"notification.error.layout\": \"Tidak dapat kesan susunan atur\",\n    \"notification.form.error.fields\": \"Borang mengandungi beberapa kesalahan\",\n    \"notification.form.success.fields\": \"Perubahan disimpan\",\n    \"notification.success.delete\": \"Item telah dipadamkan\",\n    \"request.error.model.unknown\": \"model ini tidak wujud\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, ms as default };\n//# sourceMappingURL=ms.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,KAAK;AAAA,EACL;AAAA,EACA,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,wCAAwC;AAAA,EACxC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,+CAA+C;AAAA,EAC/C,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,+BAA+B;AAAA,EAC/B,+BAA+B;AACnC;", "names": []}