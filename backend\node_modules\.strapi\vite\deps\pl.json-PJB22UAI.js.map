{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/pl.json.mjs"], "sourcesContent": ["var configurations = \"Ustawienia\";\nvar from = \"z\";\nvar pl = {\n    \"attribute.boolean\": \"Typ logiczny\",\n    \"attribute.boolean.description\": \"Tak lub nie, 1 lub 0, true lub false\",\n    \"attribute.component\": \"Komponent\",\n    \"attribute.component.description\": \"Grupa pól do powtarzania lub ponownego używania\",\n    \"attribute.date\": \"Data\",\n    \"attribute.date.description\": \"Wybór daty z godzinami, minutami i sekundami\",\n    \"attribute.datetime\": \"Data i godzina\",\n    \"attribute.dynamiczone\": \"Strefa dynamiczna\",\n    \"attribute.dynamiczone.description\": \"Dynamicznie wybierz komponent podczas edycji treści\",\n    \"attribute.email\": \"Email\",\n    \"attribute.email.description\": \"Pole email ze sprawdzaniem poprawności\",\n    \"attribute.enumeration\": \"Wyliczenie\",\n    \"attribute.enumeration.description\": \"Lista wartości do jednokrotnego wyboru\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"<PERSON> w formacie JSON\",\n    \"attribute.media\": \"Media\",\n    \"attribute.media.description\": \"Pliki takie jak obrazki, filmy, itp.\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"Liczba\",\n    \"attribute.number.description\": \"Liczby (całkowita, zmiennoprzecinkowa, dziesiętna)\",\n    \"attribute.password\": \"Hasło\",\n    \"attribute.password.description\": \"Pole hasła z szyfrowaniem\",\n    \"attribute.relation\": \"Relacja\",\n    \"attribute.relation.description\": \"Odnośnik do innego modelu\",\n    \"attribute.richtext\": \"Tekst sformatowany\",\n    \"attribute.richtext.description\": \"Edytor tekstu z możliwością formatowania\",\n    \"attribute.text\": \"Tekst\",\n    \"attribute.text.description\": \"Krótki lub długi tekst jak tytuł lub opis\",\n    \"attribute.time\": \"Czas\",\n    \"attribute.timestamp\": \"Znacznik czasu\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Unikalny identyfikator\",\n    \"button.attributes.add.another\": \"Dodaj kolejne pole\",\n    \"button.component.add\": \"Dodaj komponent\",\n    \"button.component.create\": \"Nowy komponent\",\n    \"button.model.create\": \"Nowa kolekcja\",\n    \"button.single-types.create\": \"Nowy pojedynczy typ\",\n    \"component.repeatable\": \"(powtarzalne)\",\n    \"components.SelectComponents.displayed-value\": \"{number, plural, =0 {# } one {# } other {# }} wybrano\",\n    \"components.componentSelect.no-component-available\": \"Dodałeś już wszystkie swoje komponenty\",\n    \"components.componentSelect.no-component-available.with-search\": \"Brak elementów pasujących do twojego wyszukiwania\",\n    \"components.componentSelect.value-component\": \"{number} wybrany komponent (wpisz, aby wyszukać komponent)\",\n    \"components.componentSelect.value-components\": \"{number} wybranych komponentów\",\n    configurations: configurations,\n    \"contentType.apiId-plural.description\": \"API ID w liczbie mnogiej\",\n    \"contentType.apiId-plural.label\": \"API ID (liczba mnoga)\",\n    \"contentType.apiId-singular.description\": \"UID jest używane do generowania ścieżek API i tabel/kolekcji w bazie danych\",\n    \"contentType.apiId-singular.label\": \"API ID (liczba pojedyncza)\",\n    \"contentType.collectionName.description\": \"Przydatne, gdy nazwa typu zawartości i nazwa tabeli różnią się\",\n    \"contentType.collectionName.label\": \"Nazwa kolekcji\",\n    \"contentType.displayName.label\": \"Nazwa\",\n    \"contentType.kind.change.warning\": \"Właśnie zmieniłeś rodzaj typu treści: API zostanie zresetowane (ścieżki, kontrolery i usługi zostaną nadpisane).\",\n    \"error.attributeName.reserved-name\": \"Ta nazwa nie może być używana w tym typie treści, ponieważ może uszkodzić inne funkcje\",\n    \"error.contentType.pluralName-used\": \"Ta wartość nie może być taka sama jak pojedyncza\",\n    \"error.contentType.singularName-used\": \"Ta wartość nie może być taka sama jak mnoga\",\n    \"error.contentTypeName.reserved-name\": \"Ta nazwa nie może być używana w tym typie treści, ponieważ może uszkodzić inne funkcje\",\n    \"error.validation.enum-duplicate\": \"Zduplikowane wartości nie są dozwolone\",\n    \"error.validation.enum-empty-string\": \"Puste pola nie są dozwolone\",\n    \"error.validation.enum-number\": \"Wartość nie może zaczynać się liczbą\",\n    \"error.validation.minSupMax\": \"Nie może być wyższy\",\n    \"error.validation.positive\": \"Musi być liczbą dodatnią\",\n    \"error.validation.regex\": \"Regex jest niepoprawny\",\n    \"error.validation.relation.targetAttribute-taken\": \"Ta nazwa już istnieje\",\n    \"form.attribute.component.option.add\": \"Dodaj komponent\",\n    \"form.attribute.component.option.create\": \"Utwórz nowy komponent\",\n    \"form.attribute.component.option.create.description\": \"Komponent jest współużytkowany przez typy i komponenty, będzie dostępny wszędzie.\",\n    \"form.attribute.component.option.repeatable\": \"Powtarzalny komponent\",\n    \"form.attribute.component.option.repeatable.description\": \"Najlepsze dla wielu wystąpień (tablicy) np. składników, metatagów itp.\",\n    \"form.attribute.component.option.reuse-existing\": \"Użyj istniejącego komponentu\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Ponownie użyj utworzonego już komponentu, aby zachować spójność danych między typami treści.\",\n    \"form.attribute.component.option.single\": \"Pojedynczy komponent\",\n    \"form.attribute.component.option.single.description\": \"Najlepsze do grupowania pól takich jak adres, główne informacje itp.\",\n    \"form.attribute.item.customColumnName\": \"Własne nazwy tabel\",\n    \"form.attribute.item.customColumnName.description\": \"Jest to przydatne do zmiany nazwy tabel bazy danych w bardziej wszechstronnym formacie odpowiedzi API\",\n    \"form.attribute.item.date.type.date\": \"data (np: 01/01/{currentYear})\",\n    \"form.attribute.item.date.type.datetime\": \"data i czas (np: 01/01/{currentYear} 00:00 AM)\",\n    \"form.attribute.item.date.type.time\": \"czas (np: 00:00 AM)\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Nazwa pola\",\n    \"form.attribute.item.enumeration.graphql\": \"Nadpisanie nazwy dla GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Pozwalaj na nadpisanie domyślnie wygenerowanej nazwy dla GraphQL.\",\n    \"form.attribute.item.enumeration.placeholder\": \"Przykład:\\nrano\\npołudnie\\nwieczór\",\n    \"form.attribute.item.enumeration.rules\": \"Wartości (każda w osobnej linii)\",\n    \"form.attribute.item.maximum\": \"Maksymalna wartość\",\n    \"form.attribute.item.maximumLength\": \"Maksymalna długość\",\n    \"form.attribute.item.minimum\": \"Minimalna wartość\",\n    \"form.attribute.item.minimumLength\": \"Minimalna długość\",\n    \"form.attribute.item.number.type\": \"Format liczby\",\n    \"form.attribute.item.number.type.biginteger\": \"duża liczba całkowita (np. 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"dziesiętna (np: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"zmiennoprzecinkowa (np: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"całkowita (np: 10)\",\n    \"form.attribute.item.privateField\": \"Pole prywatne\",\n    \"form.attribute.item.privateField.description\": \"To pole nie pojawi się w odpowiedzi API\",\n    \"form.attribute.item.requiredField\": \"Wymagany\",\n    \"form.attribute.item.requiredField.description\": \"Nie będziesz w stanie stworzyć wpisu jeżeli atrybut będzie pusty\",\n    \"form.attribute.item.text.regex\": \"RegExp\",\n    \"form.attribute.item.text.regex.description\": \"Wyrażenie regularne\",\n    \"form.attribute.item.uniqueField\": \"Pole unikalne\",\n    \"form.attribute.item.uniqueField.description\": \"Nie będziesz w stanie stworzyć wpisu jeżeli będzie istniał element z taką samą zawartością\",\n    \"form.attribute.media.allowed-types\": \"Wybierz dozwolone media\",\n    \"form.attribute.media.allowed-types.option-files\": \"Pliki\",\n    \"form.attribute.media.allowed-types.option-images\": \"Obrazki\",\n    \"form.attribute.media.allowed-types.option-videos\": \"Filmy\",\n    \"form.attribute.media.option.multiple\": \"Wiele mediów\",\n    \"form.attribute.media.option.multiple.description\": \"Najlepsze dla slajderów, karuzeli lub pobierania wielu plików\",\n    \"form.attribute.media.option.single\": \"Pojedyncze media\",\n    \"form.attribute.media.option.single.description\": \"Najlepsze dla awatara, zdjęcia profilowego lub okładki\",\n    \"form.attribute.settings.default\": \"Domyślna wartość\",\n    \"form.attribute.text.option.long-text\": \"Długi tekst\",\n    \"form.attribute.text.option.long-text.description\": \"Najlepszy dla opisów, biografii. Dokładne wyszukiwanie jest wyłączone.\",\n    \"form.attribute.text.option.short-text\": \"Krótki tekst\",\n    \"form.attribute.text.option.short-text.description\": \"Najlepszy dla tytułów, nazw, linków (URL). Umożliwia także dokładne wyszukiwanie dla pola.\",\n    \"form.button.add-components-to-dynamiczone\": \"Dodaj komponenty do strefy\",\n    \"form.button.add-field\": \"Dodaj kolejne pole\",\n    \"form.button.add-first-field-to-created-component\": \"Dodaj pierwsze pole do komponentu\",\n    \"form.button.add.field.to.collectionType\": \"Dodaj kolejne pole\",\n    \"form.button.add.field.to.component\": \"Dodaj kolejne pole\",\n    \"form.button.add.field.to.contentType\": \"Dodaj kolejne pole\",\n    \"form.button.add.field.to.singleType\": \"Dodaj kolejne pole\",\n    \"form.button.cancel\": \"Anuluj\",\n    \"form.button.collection-type.description\": \"Najlepsze rozwiązanie dla wielu wystąpień typu artykuły, produkty, komentarze itd.\",\n    \"form.button.collection-type.name\": \"Kolekcja\",\n    \"form.button.configure-component\": \"Skonfiguruj komponent\",\n    \"form.button.configure-view\": \"Skonfiguruj widok\",\n    \"form.button.select-component\": \"Wybierz komponent\",\n    \"form.button.single-type.description\": \"Najlepsze rozwiązanie dla pojedynczych wystąpień typu strona główna, strona o nas itd\",\n    \"form.button.single-type.name\": \"Pojedynczy typ\",\n    from: from,\n    \"menu.section.components.name\": \"Komponenty\",\n    \"menu.section.models.name\": \"Kolekcje\",\n    \"menu.section.single-types.name\": \"Pojedyncze typy\",\n    \"modalForm.attribute.form.base.name.description\": \"Spacja nie jest dozwolona dla nazwy\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"np. slug, seoUrl, canonicalUrl\",\n    \"modalForm.attribute.target-field\": \"Dołączone pole\",\n    \"modalForm.attributes.select-component\": \"Wybierz komponent\",\n    \"modalForm.attributes.select-components\": \"Wybierz komponenty\",\n    \"modalForm.collectionType.header-create\": \"Nowa kolekcja\",\n    \"modalForm.component.header-create\": \"Nowy komponent\",\n    \"modalForm.components.create-component.category.label\": \"Wybierz kategorię lub wprowadź nazwę, aby utworzyć nową\",\n    \"modalForm.components.icon.label\": \"Ikona\",\n    \"modalForm.editCategory.base.name.description\": \"Spacja nie jest dozwolona dla nazwy kategorii\",\n    \"modalForm.header-edit\": \"Edytuj {name}\",\n    \"modalForm.header.categories\": \"Kategorie\",\n    \"modalForm.header.back\": \"Z powrotem\",\n    \"modalForm.singleType.header-create\": \"Nowy pojedynczy typ\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Dodaj nowy komponent do strefy dynamicznej\",\n    \"modalForm.sub-header.attribute.create\": \"Dodaj nowe pole {type}\",\n    \"modalForm.sub-header.attribute.create.step\": \"Dodaj nowy komponent ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Edytuj {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Wybierz pole dla kolekcji\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Wybierz pole dla komponentu\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Wybierz pole dla pojedynczego typu\",\n    \"modelPage.attribute.relation-polymorphic\": \"Relacja\",\n    \"modelPage.attribute.relationWith\": \"Relacja z\",\n    \"notification.error.dynamiczone-min.validation\": \"Co najmniej jeden komponent jest wymagany\",\n    \"notification.info.autoreaload-disable\": \"Funkcja autoReload jest wymagana by użyć tego pluginu. Uruchom serwer używając `strapi develop`\",\n    \"notification.info.creating.notSaved\": \"Zapisz swoją pracę przed utworzeniem nowego typu treści lub komponentu\",\n    \"plugin.description.long\": \"Modeluj strukturę danych swojego API. Twórz atrybuty i relacje w minutę. Pliki są automatycznie tworzone i aktualizowane w twoim projekcie.\",\n    \"plugin.description.short\": \"Modeluj strukturę danych swojego API.\",\n    \"plugin.name\": \"Kreator typu treści\",\n    \"popUpForm.navContainer.advanced\": \"Zaawansowane\",\n    \"popUpForm.navContainer.base\": \"Podstawowe\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"Czy na pewno chcesz anulować swoje zmiany?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Czy na pewno chcesz anulować swoje zmiany? Niektóre komponenty zostały utworzone lub zmodyfikowane ...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"Czy na pewno chcesz usunąć tę kategorię? Wszystkie komponenty również zostaną usunięte.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"Czy na pewno chcesz usunąć ten komponent?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Czy na pewno chcesz usunąć tę kolekcję?\",\n    \"popUpWarning.draft-publish.button.confirm\": \"Tak, wyłącz\",\n    \"popUpWarning.draft-publish.message\": \"Jeśli wyłączysz system szkicu/publikacji, wszystkie twoje szkice zostaną usunięte.\",\n    \"popUpWarning.draft-publish.second-message\": \"Czy na pewno chcesz to wyłączyć?\",\n    \"prompt.unsaved\": \"Jesteś pewny, że chcesz wyjść? Wszystkie twoje zmiany zostaną utracone.\",\n    \"relation.attributeName.placeholder\": \"Np: autor, kategoria, tag\",\n    \"relation.manyToMany\": \"zawiera i należy do wielu\",\n    \"relation.manyToOne\": \"zawiera wiele\",\n    \"relation.manyWay\": \"ma wiele\",\n    \"relation.oneToMany\": \"należy do wielu\",\n    \"relation.oneToOne\": \"zawiera i należy do\",\n    \"relation.oneWay\": \"zawiera\",\n    \"table.button.no-fields\": \"Dodaj nowe pole\",\n    \"table.content.create-first-content-type\": \"Stwórz pierwszy typ treści\",\n    \"table.content.no-fields.collection-type\": \"Dodaj pierwsze pole do tego typu treści\",\n    \"table.content.no-fields.component\": \"Dodaj pierwsze pole do tego komponentu\"\n};\n\nexport { configurations, pl as default, from };\n//# sourceMappingURL=pl.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC;AAAA,EACA,gCAAgC;AAAA,EAChC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qCAAqC;AACzC;", "names": []}