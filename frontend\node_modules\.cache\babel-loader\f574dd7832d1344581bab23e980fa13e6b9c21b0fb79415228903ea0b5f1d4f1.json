{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\dfghj\\\\site\\\\frontend\\\\src\\\\components\\\\ImportateurForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ImportateurForm() {\n  _s();\n  const [importateurs, setImportateurs] = useState([]);\n  const [formData, setFormData] = useState({\n    societe: \"\",\n    pays: \"\",\n    nom_responsable: \"\",\n    prenom_responsable: \"\",\n    telephone_whatsapp: \"\",\n    email: \"\",\n    region: \"\"\n  });\n\n  // Fetch all importateurs\n  useEffect(() => {\n    fetch(\"http://localhost:1337/api/importateurs?populate=*\").then(res => res.json()).then(data => {\n      console.log(\"Importateurs API response:\", data);\n      console.log(\"First item structure:\", data.data && data.data[0]);\n      if (data && data.data && Array.isArray(data.data)) {\n        setImportateurs(data.data);\n      } else {\n        console.warn(\"Unexpected API response structure:\", data);\n        setImportateurs([]);\n      }\n    }).catch(err => {\n      console.error(\"Error fetching importateurs:\", err);\n      setImportateurs([]);\n    });\n  }, []);\n\n  // Handle form input change\n  function handleChange(e) {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  }\n\n  // Submit new importateur\n  function handleSubmit(e) {\n    e.preventDefault();\n    fetch(\"http://localhost:1337/api/importateurs\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        data: formData\n      })\n    }).then(res => res.json()).then(data => {\n      setImportateurs([...importateurs, data.data]);\n      setFormData({\n        societe: \"\",\n        pays: \"\",\n        nom_responsable: \"\",\n        prenom_responsable: \"\",\n        telephone_whatsapp: \"\",\n        email: \"\",\n        region: \"\"\n      });\n    }).catch(err => console.error(err));\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Liste des Importateurs\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: importateurs && importateurs.length > 0 ? importateurs.map(imp => {\n        var _imp$attributes, _imp$data, _imp$data$attributes;\n        console.log(\"Processing importateur:\", imp);\n        // Try different possible data structures\n        const societe = (imp === null || imp === void 0 ? void 0 : (_imp$attributes = imp.attributes) === null || _imp$attributes === void 0 ? void 0 : _imp$attributes.societe) || (imp === null || imp === void 0 ? void 0 : imp.societe) || (imp === null || imp === void 0 ? void 0 : (_imp$data = imp.data) === null || _imp$data === void 0 ? void 0 : (_imp$data$attributes = _imp$data.attributes) === null || _imp$data$attributes === void 0 ? void 0 : _imp$data$attributes.societe) || 'Structure de données inconnue';\n        return /*#__PURE__*/_jsxDEV(\"li\", {\n          children: societe\n        }, imp.id || imp.documentId || Math.random(), false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 15\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Aucun importateur trouv\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Ajouter un Importateur\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"societe\",\n        placeholder: \"Soci\\xE9t\\xE9\",\n        value: formData.societe,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"pays\",\n        placeholder: \"Pays\",\n        value: formData.pays,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"nom_responsable\",\n        placeholder: \"Nom du responsable\",\n        value: formData.nom_responsable,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"prenom_responsable\",\n        placeholder: \"Pr\\xE9nom du responsable\",\n        value: formData.prenom_responsable,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"telephone_whatsapp\",\n        placeholder: \"T\\xE9l\\xE9phone WhatsApp\",\n        value: formData.telephone_whatsapp,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"email\",\n        placeholder: \"Email\",\n        type: \"email\",\n        value: formData.email,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"region\",\n        placeholder: \"R\\xE9gion\",\n        value: formData.region,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Ajouter\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(ImportateurForm, \"rXusMj+woPQUBUSuS4/h9wV5R/4=\");\n_c = ImportateurForm;\nvar _c;\n$RefreshReg$(_c, \"ImportateurForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ImportateurForm", "_s", "importateurs", "setImportateurs", "formData", "setFormData", "societe", "pays", "nom_responsable", "prenom_responsable", "telephone_whatsapp", "email", "region", "fetch", "then", "res", "json", "data", "console", "log", "Array", "isArray", "warn", "catch", "err", "error", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "method", "headers", "body", "JSON", "stringify", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "imp", "_imp$attributes", "_imp$data", "_imp$data$attributes", "attributes", "id", "documentId", "Math", "random", "onSubmit", "placeholder", "onChange", "required", "type", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/dfghj/site/frontend/src/components/ImportateurForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\n\r\nexport default function ImportateurForm() {\r\n  const [importateurs, setImportateurs] = useState([]);\r\n  const [formData, setFormData] = useState({\r\n    societe: \"\",\r\n    pays: \"\",\r\n    nom_responsable: \"\",\r\n    prenom_responsable: \"\",\r\n    telephone_whatsapp: \"\",\r\n    email: \"\",\r\n    region: \"\",\r\n  });\r\n\r\n  // Fetch all importateurs\r\n  useEffect(() => {\r\n    fetch(\"http://localhost:1337/api/importateurs?populate=*\")\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        console.log(\"Importateurs API response:\", data);\r\n        console.log(\"First item structure:\", data.data && data.data[0]);\r\n        if (data && data.data && Array.isArray(data.data)) {\r\n          setImportateurs(data.data);\r\n        } else {\r\n          console.warn(\"Unexpected API response structure:\", data);\r\n          setImportateurs([]);\r\n        }\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Error fetching importateurs:\", err);\r\n        setImportateurs([]);\r\n      });\r\n  }, []);\r\n\r\n  // Handle form input change\r\n  function handleChange(e) {\r\n    setFormData({ ...formData, [e.target.name]: e.target.value });\r\n  }\r\n\r\n  // Submit new importateur\r\n  function handleSubmit(e) {\r\n    e.preventDefault();\r\n\r\n    fetch(\"http://localhost:1337/api/importateurs\", {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      body: JSON.stringify({ data: formData }),\r\n    })\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        setImportateurs([...importateurs, data.data]);\r\n        setFormData({\r\n          societe: \"\",\r\n          pays: \"\",\r\n          nom_responsable: \"\",\r\n          prenom_responsable: \"\",\r\n          telephone_whatsapp: \"\",\r\n          email: \"\",\r\n          region: \"\",\r\n        });\r\n      })\r\n      .catch((err) => console.error(err));\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <h2>Liste des Importateurs</h2>\r\n      <ul>\r\n        {importateurs && importateurs.length > 0 ? (\r\n          importateurs.map((imp) => {\r\n            console.log(\"Processing importateur:\", imp);\r\n            // Try different possible data structures\r\n            const societe = imp?.attributes?.societe ||\r\n                           imp?.societe ||\r\n                           imp?.data?.attributes?.societe ||\r\n                           'Structure de données inconnue';\r\n            return (\r\n              <li key={imp.id || imp.documentId || Math.random()}>\r\n                {societe}\r\n              </li>\r\n            );\r\n          })\r\n        ) : (\r\n          <li>Aucun importateur trouvé</li>\r\n        )}\r\n      </ul>\r\n\r\n      <h3>Ajouter un Importateur</h3>\r\n      <form onSubmit={handleSubmit}>\r\n        <input\r\n          name=\"societe\"\r\n          placeholder=\"Société\"\r\n          value={formData.societe}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"pays\"\r\n          placeholder=\"Pays\"\r\n          value={formData.pays}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"nom_responsable\"\r\n          placeholder=\"Nom du responsable\"\r\n          value={formData.nom_responsable}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"prenom_responsable\"\r\n          placeholder=\"Prénom du responsable\"\r\n          value={formData.prenom_responsable}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"telephone_whatsapp\"\r\n          placeholder=\"Téléphone WhatsApp\"\r\n          value={formData.telephone_whatsapp}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"email\"\r\n          placeholder=\"Email\"\r\n          type=\"email\"\r\n          value={formData.email}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"region\"\r\n          placeholder=\"Région\"\r\n          value={formData.region}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <button type=\"submit\">Ajouter</button>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACxC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,eAAe,EAAE,EAAE;IACnBC,kBAAkB,EAAE,EAAE;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACAf,SAAS,CAAC,MAAM;IACdgB,KAAK,CAAC,mDAAmD,CAAC,CACvDC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MACdC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,IAAI,CAAC;MAC/CC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEF,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC;MAC/D,IAAIA,IAAI,IAAIA,IAAI,CAACA,IAAI,IAAIG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;QACjDd,eAAe,CAACc,IAAI,CAACA,IAAI,CAAC;MAC5B,CAAC,MAAM;QACLC,OAAO,CAACI,IAAI,CAAC,oCAAoC,EAAEL,IAAI,CAAC;QACxDd,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC,CAAC,CACDoB,KAAK,CAAEC,GAAG,IAAK;MACdN,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAED,GAAG,CAAC;MAClDrB,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,SAASuB,YAAYA,CAACC,CAAC,EAAE;IACvBtB,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACuB,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EAC/D;;EAEA;EACA,SAASC,YAAYA,CAACJ,CAAC,EAAE;IACvBA,CAAC,CAACK,cAAc,CAAC,CAAC;IAElBnB,KAAK,CAAC,wCAAwC,EAAE;MAC9CoB,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEpB,IAAI,EAAEb;MAAS,CAAC;IACzC,CAAC,CAAC,CACCU,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MACdd,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAEe,IAAI,CAACA,IAAI,CAAC,CAAC;MAC7CZ,WAAW,CAAC;QACVC,OAAO,EAAE,EAAE;QACXC,IAAI,EAAE,EAAE;QACRC,eAAe,EAAE,EAAE;QACnBC,kBAAkB,EAAE,EAAE;QACtBC,kBAAkB,EAAE,EAAE;QACtBC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,CACDW,KAAK,CAAEC,GAAG,IAAKN,OAAO,CAACO,KAAK,CAACD,GAAG,CAAC,CAAC;EACvC;EAEA,oBACEzB,OAAA;IAAAuC,QAAA,gBACEvC,OAAA;MAAAuC,QAAA,EAAI;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/B3C,OAAA;MAAAuC,QAAA,EACGpC,YAAY,IAAIA,YAAY,CAACyC,MAAM,GAAG,CAAC,GACtCzC,YAAY,CAAC0C,GAAG,CAAEC,GAAG,IAAK;QAAA,IAAAC,eAAA,EAAAC,SAAA,EAAAC,oBAAA;QACxB9B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE0B,GAAG,CAAC;QAC3C;QACA,MAAMvC,OAAO,GAAG,CAAAuC,GAAG,aAAHA,GAAG,wBAAAC,eAAA,GAAHD,GAAG,CAAEI,UAAU,cAAAH,eAAA,uBAAfA,eAAA,CAAiBxC,OAAO,MACzBuC,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEvC,OAAO,MACZuC,GAAG,aAAHA,GAAG,wBAAAE,SAAA,GAAHF,GAAG,CAAE5B,IAAI,cAAA8B,SAAA,wBAAAC,oBAAA,GAATD,SAAA,CAAWE,UAAU,cAAAD,oBAAA,uBAArBA,oBAAA,CAAuB1C,OAAO,KAC9B,+BAA+B;QAC9C,oBACEP,OAAA;UAAAuC,QAAA,EACGhC;QAAO,GADDuC,GAAG,CAACK,EAAE,IAAIL,GAAG,CAACM,UAAU,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE9C,CAAC;MAET,CAAC,CAAC,gBAEF3C,OAAA;QAAAuC,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IACjC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEL3C,OAAA;MAAAuC,QAAA,EAAI;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/B3C,OAAA;MAAMuD,QAAQ,EAAEvB,YAAa;MAAAO,QAAA,gBAC3BvC,OAAA;QACE8B,IAAI,EAAC,SAAS;QACd0B,WAAW,EAAC,eAAS;QACrBzB,KAAK,EAAE1B,QAAQ,CAACE,OAAQ;QACxBkD,QAAQ,EAAE9B,YAAa;QACvB+B,QAAQ;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF3C,OAAA;QACE8B,IAAI,EAAC,MAAM;QACX0B,WAAW,EAAC,MAAM;QAClBzB,KAAK,EAAE1B,QAAQ,CAACG,IAAK;QACrBiD,QAAQ,EAAE9B,YAAa;QACvB+B,QAAQ;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF3C,OAAA;QACE8B,IAAI,EAAC,iBAAiB;QACtB0B,WAAW,EAAC,oBAAoB;QAChCzB,KAAK,EAAE1B,QAAQ,CAACI,eAAgB;QAChCgD,QAAQ,EAAE9B,YAAa;QACvB+B,QAAQ;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF3C,OAAA;QACE8B,IAAI,EAAC,oBAAoB;QACzB0B,WAAW,EAAC,0BAAuB;QACnCzB,KAAK,EAAE1B,QAAQ,CAACK,kBAAmB;QACnC+C,QAAQ,EAAE9B,YAAa;QACvB+B,QAAQ;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF3C,OAAA;QACE8B,IAAI,EAAC,oBAAoB;QACzB0B,WAAW,EAAC,0BAAoB;QAChCzB,KAAK,EAAE1B,QAAQ,CAACM,kBAAmB;QACnC8C,QAAQ,EAAE9B,YAAa;QACvB+B,QAAQ;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF3C,OAAA;QACE8B,IAAI,EAAC,OAAO;QACZ0B,WAAW,EAAC,OAAO;QACnBG,IAAI,EAAC,OAAO;QACZ5B,KAAK,EAAE1B,QAAQ,CAACO,KAAM;QACtB6C,QAAQ,EAAE9B,YAAa;QACvB+B,QAAQ;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF3C,OAAA;QACE8B,IAAI,EAAC,QAAQ;QACb0B,WAAW,EAAC,WAAQ;QACpBzB,KAAK,EAAE1B,QAAQ,CAACQ,MAAO;QACvB4C,QAAQ,EAAE9B,YAAa;QACvB+B,QAAQ;MAAA;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACF3C,OAAA;QAAQ2D,IAAI,EAAC,QAAQ;QAAApB,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACzC,EAAA,CA7IuBD,eAAe;AAAA2D,EAAA,GAAf3D,eAAe;AAAA,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}