var e="Gruppen",n="Sammlungen",t="Seite nicht gefunden",i={"App.schemas.data-loaded":"Die Schemata wurden geladen","ListViewTable.relation-loaded":"Beziehungen wurden geladen","ListViewTable.relation-loading":"Beziehungen laden","ListViewTable.relation-more":"Diese Beziehung enthält mehr Einträge als angezeigt","EditRelations.title":"Beziehungs-Daten","HeaderLayout.button.label-add-entry":"Neuer Eintrag","api.id":"API ID","components.AddFilterCTA.add":"Filter","components.AddFilterCTA.hide":"Filter","components.DragHandle-label":"Ziehen","components.DraggableAttr.edit":"Klicken zum Bearbeiten","components.DraggableCard.delete.field":"<PERSON>ösche {item}","components.DraggableCard.edit.field":"Bearbeite {item}","components.DraggableCard.move.field":"Verschiebe {item}","components.ListViewTable.row-line":"Eintrag Zeile {number}","components.DynamicZone.ComponentPicker-label":"Wähle eine Komponente","components.DynamicZone.add-component":"Füge {componentName} eine Komponente hinzu","components.DynamicZone.delete-label":"Lösche {name}","components.DynamicZone.error-message":"Die Komponente enthält einen oder mehrere Fehler","components.DynamicZone.missing-components":"{number, plura, one {# Komponente} other {# Komponenten}} fehlen","components.DynamicZone.move-down-label":"Verschiebe Komponente nach unten","components.DynamicZone.move-up-label":"Verschiebe Komponente nach oben","components.DynamicZone.pick-compo":"Wähle eine Komponente","components.DynamicZone.required":"Komponente wird benötigt","components.EmptyAttributesBlock.button":"Gehe zu den Einstellungen","components.EmptyAttributesBlock.description":"Du kannst deine Einstellungen ändern","components.FieldItem.linkToComponentLayout":"Layout der Komponente anpassen","components.FieldSelect.label":"Füge Feld hinzu","components.FilterOptions.button.apply":"Anwenden","components.FiltersPickWrapper.PluginHeader.actions.apply":"Anwenden","components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Alle löschen","components.FiltersPickWrapper.PluginHeader.description":"Lege die Bedingungen fest, unter denen die Einträge gefiltert werden sollen","components.FiltersPickWrapper.PluginHeader.title.filter":"Filter","components.FiltersPickWrapper.hide":"Ausblenden","components.LeftMenu.Search.label":"Suche nach einem Inhaltstyp","components.LeftMenu.collection-types":"Sammlungen","components.LeftMenu.single-types":"Einzel-Einträge","components.LimitSelect.itemsPerPage":"Einträge pro Seite","components.NotAllowedInput.text":"Keine Berechtigung dieses Feld anzusehen","components.RepeatableComponent.error-message":"Die Komponente(n) enthält einen/enthalten Fehler","components.Search.placeholder":"Suche nach einem Eintrag....","components.Select.draft-info-title":"Status: Entwurf","components.Select.publish-info-title":"State: Veröffentlicht","components.SettingsViewWrapper.pluginHeader.description.edit-settings":"Anpassen, wie die Bearbeitungsansicht aussieht.","components.SettingsViewWrapper.pluginHeader.description.list-settings":"Einstellungen der Listenansicht anpassen.","components.SettingsViewWrapper.pluginHeader.title":"Ansicht anpassen - {name}","components.TableDelete.delete":"Alle löschen","components.TableDelete.deleteSelected":"Ausgewählte löschen","components.TableDelete.label":"{number, plural, one {# Eintrag} other {# Einträge}} ausgewählt","components.TableEmpty.withFilters":"Es gibt keine {contentType} mit den verwendeten Filtern...","components.TableEmpty.withSearch":"Es gibt keine {contentType}, die der Suche ({search}) entsprechen...","components.TableEmpty.withoutFilter":"Es gibt keine {contentType}...","components.empty-repeatable":"Noch keine Einträge. Nutze den Button unten um einen hinzuzufügen.","components.notification.info.maximum-requirement":"Die maximale Anzahl an Feldern wurde erreicht","components.notification.info.minimum-requirement":"Es wurde ein Feld hinzugefügt um die minimale Anzahl zu erfüllen","components.repeatable.reorder.error":"Während dem Ändern der Reihenfolge der Komponenten ist ein Fehler aufgetreten, bitte versuche es erneut","components.reset-entry":"Eintrag zurücksetzen","components.uid.apply":"apply","components.uid.available":"available","components.uid.regenerate":"regenerate","components.uid.suggested":"suggested","components.uid.unavailable":"unavailable","containers.Edit.Link.Layout":"Layout anpassen","containers.Edit.Link.Model":"Sammlung bearbeiten","containers.Edit.addAnItem":"Füge ein Item hinzu...","containers.Edit.clickToJump":"Klicke, um zu einem Eintrag zu springen","containers.Edit.delete":"Löschen","containers.Edit.delete-entry":"Diesen Eintrag löschen","containers.Edit.editing":"Bearbeite...","containers.Edit.information":"Informationen","containers.Edit.information.by":"Von","containers.Edit.information.created":"Erstellt","containers.Edit.information.draftVersion":"Entwurf","containers.Edit.information.editing":"Bearbeite","containers.Edit.information.lastUpdate":"Letzte Änderung","containers.Edit.information.publishedVersion":"veröffentlichte Version","containers.Edit.pluginHeader.title.new":"Eintrag erstellen","containers.Edit.reset":"Zurücksetzen","containers.Edit.returnList":"Zu Liste zurückkehren","containers.Edit.seeDetails":"Details","containers.Edit.submit":"Speichern","containers.EditSettingsView.modal-form.edit-field":"Feld bearbeiten","containers.EditView.add.new-entry":"Eintrag hinzufügen","containers.EditView.notification.errors":"Das Formular enthält Fehler","containers.Home.introduction":"Um deine Einträge zu verwalten, klicke auf den entsprechenden Link im Menü links. Dieses Plugin ist noch in aktiver Entwicklung und seine Einstellungen können nicht optimal angepasst werden.","containers.Home.pluginHeaderDescription":"Verwalte deine Einträge mithilfe eines mächtigen und wunderschönen Interfaces.","containers.Home.pluginHeaderTitle":"Inhalts-Manager","containers.List.draft":"Entwurf","containers.List.errorFetchRecords":"Fehler","containers.List.published":"Veröffentlicht","containers.list.displayedFields":"Dargestellte Felder","containers.list.items":"{number, plural, one {Eintrag} other {Einträge}}","containers.list.table-headers.publishedAt":"Status","containers.ListSettingsView.modal-form.edit-label":"Beschriftung ändern","containers.SettingPage.add.field":"Ein weiteres Feld hinzufügen","containers.SettingPage.attributes":"Attribut-Felder","containers.SettingPage.attributes.description":"Reihenfolge der Attribute festlegen","containers.SettingPage.editSettings.description":"Ziehe die Felder via Drag & Drop, um das Layout zu erstellen","containers.SettingPage.editSettings.entry.title":"Anzeigefeld","containers.SettingPage.editSettings.entry.title.description":"Anzeigefeld der Einträge wählen","containers.SettingPage.editSettings.relation-field.description":"Setze das dargestellte Feld sowohl in der Bearbeiten-, als auch der Listenansicht","containers.SettingPage.editSettings.title":"Bearbeiten (einstellungen)","containers.SettingPage.layout":"Layout","containers.SettingPage.listSettings.description":"Konfiguriere die Einstellungen für diesen Collection Type","containers.SettingPage.listSettings.title":"Listenansicht (Einstellungen)","containers.SettingPage.pluginHeaderDescription":"Konfiguriere die spezifische Ansicht für diesen Collection Type","containers.SettingPage.settings":"Einstellungen","containers.SettingPage.view":"Ansicht","containers.SettingViewModel.pluginHeader.title":"Inhalts-Manager - {name}","containers.SettingsPage.Block.contentType.description":"Spezifische Einstellungen konfigurieren","containers.SettingsPage.Block.contentType.title":"Sammlungen","containers.SettingsPage.Block.generalSettings.description":"Standardoptionen für Sammlungen konfigurieren","containers.SettingsPage.Block.generalSettings.title":"Generell","containers.SettingsPage.pluginHeaderDescription":"Einstellungen für alle Sammlungen und Gruppen konfigurieren","containers.SettingsView.list.subtitle":"Layout und Darstellung für alle Sammlungen und Gruppen konfigurieren","containers.SettingsView.list.title":"Darstellungsoptionen","edit-settings-view.link-to-ctb.components":"Komponente bearbeiten","edit-settings-view.link-to-ctb.content-types":"Inhalts-Typ bearbeiten","emptyAttributes.button":"Zum Sammlungs-Editor","emptyAttributes.description":"Füge das erste Feld zur Sammlung hinzu","emptyAttributes.title":"Es gibt noch keine Felder","error.attribute.key.taken":"Dieser Wert existiert bereits","error.attribute.sameKeyAndName":"Darf nicht gleich sein","error.attribute.taken":"Dieser Feldname ist bereits vergeben","error.contentTypeName.taken":"Dieser Name existiert bereits","error.model.fetch":"Beim Abruf von model config fetch ist ein Fehler aufgetreten.","error.record.create":"Beim Anlegen eines Dokuments ist ein Fehler aufgetreten.","error.record.delete":"Beim Löschen eines Dokuments ist ein Fehler aufgetreten.","error.record.fetch":"Beim Abruf eines Dokuments ist ein Fehler aufgetreten.","error.record.update":"Beim Aktualisieren eines Dokuments ist ein Fehler aufgetreten.","error.records.count":"Beim Abrufen der Anzahl an Einträgen ist ein Fehler aufgetreten.","error.records.fetch":"Beim Abrufen von Dokumenten ist ein Fehler aufgetreten.","error.schema.generation":"Bei der Generierung des Schemas ist ein Fehler aufgetreten.","error.validation.json":"Dies ist kein JSON","error.validation.max":"Dieser Wert ist zu hoch.","error.validation.maxLength":"Dieser Wert ist zu lang.","error.validation.min":"Dieser Wert ist zu niedrig.","error.validation.minLength":"Dieser Wert ist zu kurz.","error.validation.minSupMax":"Darf nicht höher sein","error.validation.regex":"Dieser Wert entspricht nicht dem RegEx.","error.validation.required":"Dieser Wert ist erforderlich.","form.Input.bulkActions":"Bulk-Bearbeitung aktivieren","form.Input.defaultSort":"Standard-Sortierattribut","form.Input.description":"Beschreibung","form.Input.description.placeholder":"Zeige den Namen im Profil","form.Input.editable":"Editierbares Feld","form.Input.filters":"Filter aktivieren","form.Input.label":"Label","form.Input.label.inputDescription":"Dieser Wert überschreibt das im Kopf der Tabelle angezeigte Label.","form.Input.pageEntries":"Einträge pro Seite","form.Input.pageEntries.inputDescription":"Hinweis: Dieser Wert lässt sich durch die Sammlungs-Einstellungen überschreiben.","form.Input.placeholder":"Platzhalter","form.Input.placeholder.placeholder":"Mein unglaublicher Wert","form.Input.search":"Suche aktivieren","form.Input.search.field":"Suche in diesem Feld aktivieren","form.Input.sort.field":"Sortierung in diesem Feld aktivieren","form.Input.sort.order":"Standard-Reihenfolge","form.Input.wysiwyg":"Als visuellen Editor anzeigen","global.displayedFields":"Angezeigte Felder",groups:e,"groups.numbered":"Gruppen ({number})","header.name":"Inhalt","link-to-ctb":"Modell bearbeiten",models:n,"models.numbered":"Sammlungen ({number})","notification.error.displayedFields":"Du benötigst mindestens ein dargestelltes Feld","notification.error.relationship.fetch":"Beim Abruf von Beziehungen ist ein Fehler aufgetreten.","notification.info.SettingPage.disableSort":"Du musst ein Attribut mit aktivierter Sortierung haben.","notification.info.minimumFields":"Du benötigst mindestens ein dargestelltes Feld","notification.upload.error":"Beim Hochladen deiner Dateien ist ein Fehler aufgetreten",pageNotFound:t,"pages.ListView.header-subtitle":"{number, plural, one {# Eintrag} other {# Einträge}} gefunden","pages.NoContentType.button":"Erstelle deinen ersten Inhalts-Typ","pages.NoContentType.text":"Wenn du noch keinen Inhalt hast, empfehlen wir dir, zuerst einen Inhalts-Typ erstellen.","permissions.not-allowed.create":"Du hast nicht die erforderlichen Berechtigungen, um ein Dokument zu erstellen","permissions.not-allowed.update":"Du hast nicht die erforderlichen Berechtigungen, um dieses Dokument anzuschauen","plugin.description.long":"Greife schnell auf alle Daten in der Datenbank zu und ändere sie.","plugin.description.short":"Greife schnell auf alle Daten in der Datenbank zu und ändere sie.","popover.display-relations.label":"Beziehungen darstellen","success.record.delete":"Gelöscht","success.record.publish":"Veröffentlicht","success.record.save":"Gespeichert","success.record.unpublish":"Veröffentlichung zurückgenommen","utils.data-loaded":"{number, plural, =1 {Der Eintrag wurde} other {Die Einträge wurden}} erfolgreich geladen","popUpWarning.warning.publish-question":"Wollst du diesen Eintrag trotzdem veröffentlichen?","popUpwarning.warning.has-draft-relations.button-confirm":"Ja, veröffentlichen","popUpwarning.warning.has-draft-relations.message":"<b>{count, plural, =0 { von deinen Inhalts-Beziehungen sind} one { von deinen Inhalts-Beziehungen ist} other { von deinen Inhalts-Beziehungen sind}}</b> noch nicht veröffentlicht.<br></br>Das kann zu kaputten Links und Fehlern in deinem Projekt führen."};export{i as default,e as groups,n as models,t as pageNotFound};
