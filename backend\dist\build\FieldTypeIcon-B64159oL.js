import{a7 as f,j as a,a8 as F,a9 as c,aa as l,ab as m,ac as w,ad as s,ae as o,af as R,ag as $,ah as u,ai as e,aj as p,ak as g,al as h,$ as b}from"./strapi-z7ApxZZq.js";const d={biginteger:a.jsx(e,{}),boolean:a.jsx(h,{}),date:a.jsx(s,{}),datetime:a.jsx(s,{}),decimal:a.jsx(e,{}),email:a.jsx(g,{}),enumeration:a.jsx(p,{}),float:a.jsx(e,{}),integer:a.jsx(e,{}),media:a.jsx(u,{}),password:a.jsx($,{}),relation:a.jsx(R,{}),string:a.jsx(o,{}),text:a.jsx(o,{}),richtext:a.jsx(o,{}),time:a.jsx(s,{}),timestamp:a.jsx(s,{}),json:a.jsx(w,{}),uid:a.jsx(m,{}),component:a.jsx(l,{}),dynamiczone:a.jsx(c,{}),blocks:a.jsx(F,{})},C=({type:r,customFieldUid:t})=>{const j=f("FieldTypeIcon",n=>n.customFields.get);if(!r)return null;let i=d[r];if(t){const x=j(t)?.icon;x&&(i=a.jsx(b,{marginRight:3,width:7,height:6,children:a.jsx(x,{})}))}return d[r]?i:null};export{C as F};
