import{aP as k,aQ as E,aR as S,j as e,P as u,c2 as F,w,a as P,bW as C,r as c,c0 as A,c1 as B,B as b,e as g,$ as D,T as p,H as d,M as s,aJ as h,aH as $,aI as N,D as V,bJ as H,c3 as q,k as J}from"./strapi-z7ApxZZq.js";const O=(i,t)=>!i||!t?{}:{[i]:t[i]},Q=i=>(i?.inner||[]).reduce((t,a)=>(a.path&&(t[a.path.split("[").join(".").split("]").join("")]={id:a.message,defaultMessage:a.message,values:O(a?.type,a?.params)}),t),{}),M=k().shape({email:E().email(S.email.id).required(S.required.id)}),Y=J.a`
  color: ${({theme:i})=>i.colors.primary600};
`,_=()=>e.jsx(u.Protect,{permissions:F.settings,children:e.jsx(G,{})}),G=()=>{const{toggleNotification:i}=w(),{formatMessage:t}=P(),{get:a,post:v}=C(),[l,y]=c.useState(""),[I,f]=c.useState(!1),[x,R]=c.useState({}),{data:o,isLoading:j}=A(["email","settings"],async()=>{const n=await a("/email/settings"),{data:{config:r}}=n;return r}),m=B(async n=>{await v("/email/test",n)},{onError(){i({type:"danger",message:t({id:"email.Settings.email.plugin.notification.test.error",defaultMessage:"Failed to send a test mail to {to}"},{to:l})})},onSuccess(){i({type:"success",message:t({id:"email.Settings.email.plugin.notification.test.success",defaultMessage:"Email test succeeded, check the {to} mailbox"},{to:l})})},retry:!1});c.useEffect(()=>{M.validate({email:l},{abortEarly:!1}).then(()=>f(!0)).catch(()=>f(!1))},[l]);const T=n=>{y(()=>n.target.value)},L=async n=>{n.preventDefault();try{await M.validate({email:l},{abortEarly:!1})}catch(r){r instanceof q&&R(Q(r))}m.mutate({to:l})};return j?e.jsx(u.Loading,{}):e.jsxs(u.Main,{labelledBy:"title","aria-busy":j||m.isLoading,children:[e.jsx(u.Title,{children:t({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:t({id:"email.Settings.email.plugin.title",defaultMessage:"Configuration"})})}),e.jsx(b.Header,{id:"title",title:t({id:"email.Settings.email.plugin.title",defaultMessage:"Configuration"}),subtitle:t({id:"email.Settings.email.plugin.subTitle",defaultMessage:"Test the settings for the Email plugin"})}),e.jsx(b.Content,{children:o&&e.jsx("form",{onSubmit:L,children:e.jsxs(g,{direction:"column",alignItems:"stretch",gap:7,children:[e.jsx(D,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:e.jsxs(g,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsxs(g,{direction:"column",alignItems:"stretch",gap:1,children:[e.jsx(p,{variant:"delta",tag:"h2",children:t({id:"email.Settings.email.plugin.title.config",defaultMessage:"Configuration"})}),e.jsx(p,{children:t({id:"email.Settings.email.plugin.text.configuration",defaultMessage:"The plugin is configured through the {file} file, checkout this {link} for the documentation."},{file:"./config/plugins.js",link:e.jsx(Y,{href:"https://docs.strapi.io/developer-docs/latest/plugins/email.html",target:"_blank",rel:"noopener noreferrer",children:t({id:"email.link",defaultMessage:"Link"})})})})]}),e.jsxs(d.Root,{gap:5,children:[e.jsx(d.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(s.Root,{name:"shipper-email",children:[e.jsx(s.Label,{children:t({id:"email.Settings.email.plugin.label.defaultFrom",defaultMessage:"Default sender email"})}),e.jsx(h,{placeholder:t({id:"email.Settings.email.plugin.placeholder.defaultFrom",defaultMessage:"ex: Strapi No-Reply '<'<EMAIL>'>'"}),disabled:!0,value:o.settings.defaultFrom})]})}),e.jsx(d.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(s.Root,{name:"response-email",children:[e.jsx(s.Label,{children:t({id:"email.Settings.email.plugin.label.defaultReplyTo",defaultMessage:"Default response email"})}),e.jsx(h,{placeholder:t({id:"email.Settings.email.plugin.placeholder.defaultReplyTo",defaultMessage:"ex: Strapi '<'<EMAIL>'>'"}),disabled:!0,value:o.settings.defaultReplyTo})]})}),e.jsx(d.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(s.Root,{name:"email-provider",children:[e.jsx(s.Label,{children:t({id:"email.Settings.email.plugin.label.provider",defaultMessage:"Email provider"})}),e.jsx($,{disabled:!0,value:o.provider,children:e.jsx(N,{value:o.provider,children:o.provider})})]})})]})]})}),e.jsxs(g,{alignItems:"stretch",background:"neutral0",direction:"column",gap:4,hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:[e.jsx(p,{variant:"delta",tag:"h2",children:t({id:"email.Settings.email.plugin.title.test",defaultMessage:"Test email delivery"})}),e.jsxs(d.Root,{gap:5,children:[e.jsx(d.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(s.Root,{name:"test-address",error:x.email?.id&&t({id:`email.${x.email?.id}`,defaultMessage:"This is not a valid email"}),children:[e.jsx(s.Label,{children:t({id:"email.Settings.email.plugin.label.testAddress",defaultMessage:"Recipient email"})}),e.jsx(h,{onChange:T,value:l,placeholder:t({id:"email.Settings.email.plugin.placeholder.testAddress",defaultMessage:"ex: <EMAIL>"})})]})}),e.jsx(d.Item,{col:7,s:12,direction:"column",alignItems:"start",children:e.jsx(V,{loading:m.isLoading,disabled:!I,type:"submit",startIcon:e.jsx(H,{}),children:t({id:"email.Settings.email.plugin.button.test-email",defaultMessage:"Send test email"})})})]})]})]})})})]})};export{_ as ProtectedSettingsPage};
