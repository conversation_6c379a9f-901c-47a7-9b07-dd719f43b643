import{j as e,w as v,aA as y,V as M,a as U,r as c,a4 as C,aC as L,aD as S,aE as k,aF as T,e as i,aG as B,$ as d,T as P,M as o,aH as R,aI as _,aJ as O,D as F,aK as w}from"./strapi-z7ApxZZq.js";import{P as A}from"./PrivateRoute-_HMyEha1.js";const I=[{intlLabel:{id:"Usecase.front-end",defaultMessage:"Front-end developer"},value:"front_end_developer"},{intlLabel:{id:"Usecase.back-end",defaultMessage:"Back-end developer"},value:"back_end_developer"},{intlLabel:{id:"Usecase.full-stack",defaultMessage:"Full-stack developer"},value:"full_stack_developer"},{intlLabel:{id:"global.content-manager",defaultMessage:"Content Manager"},value:"content_manager"},{intlLabel:{id:"Usecase.content-creator",defaultMessage:"Content Creator"},value:"content_creator"},{intlLabel:{id:"Usecase.other",defaultMessage:"Other"},value:"other"}],N=()=>{const{toggleNotification:u}=v(),h=y(),g=M(),{formatMessage:a}=U(),[n,p]=c.useState(null),[l,f]=c.useState(""),{firstname:x,email:m}=C("UseCasePage",t=>t.user)??{},{hasAdmin:j}=L.parse(h.search,{ignoreQueryPrefix:!0}),b=n==="other",r=async(t,s)=>{t.preventDefault();try{await fetch("https://analytics.strapi.io/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:m,username:x,firstAdmin:!j,persona:{role:s?void 0:n,otherRole:s?void 0:l}})}),u({type:"success",message:a({id:"Usecase.notification.success.project-created",defaultMessage:"Project has been successfully created"})}),g("/")}catch{}};return e.jsx(S,{children:e.jsxs(k,{labelledBy:"usecase-title",children:[e.jsx(T,{children:e.jsxs("form",{onSubmit:t=>r(t,!1),children:[e.jsxs(i,{direction:"column",paddingBottom:7,children:[e.jsx(B,{}),e.jsx(d,{paddingTop:6,paddingBottom:1,width:"25rem",children:e.jsx(P,{textAlign:"center",variant:"alpha",tag:"h1",id:"usecase-title",children:a({id:"Usecase.title",defaultMessage:"Tell us a bit more about yourself"})})})]}),e.jsxs(i,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsxs(o.Root,{name:"usecase",children:[e.jsx(o.Label,{children:a({id:"Usecase.input.work-type",defaultMessage:"What type of work do you do?"})}),e.jsx(R,{onChange:t=>p(t),value:n,children:I.map(({intlLabel:t,value:s})=>e.jsx(_,{value:s,children:a(t)},s))})]}),b&&e.jsxs(o.Root,{name:"other",children:[e.jsx(o.Label,{children:a({id:"Usecase.other",defaultMessage:"Other"})}),e.jsx(O,{value:l,onChange:t=>f(t.target.value)})]}),e.jsx(F,{type:"submit",size:"L",fullWidth:!0,disabled:!n,children:a({id:"global.finish",defaultMessage:"Finish"})})]})]})}),e.jsx(i,{justifyContent:"center",children:e.jsx(d,{paddingTop:4,children:e.jsx(w,{onClick:t=>r(t,!0),children:a({id:"Usecase.button.skip",defaultMessage:"Skip this question"})})})})]})})},W=()=>e.jsx(A,{children:e.jsx(N,{})});export{W as PrivateUseCasePage,N as UseCasePage,I as options};
