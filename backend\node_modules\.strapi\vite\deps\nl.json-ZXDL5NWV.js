import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-type-builder/dist/admin/translations/nl.json.mjs
var configurations = "configuraties";
var from = "van";
var nl = {
  "attribute.boolean": "Boolean",
  "attribute.boolean.description": "Ja of nee, 1 of 0, waar of onwaar",
  "attribute.component": "Component",
  "attribute.component.description": "<PERSON><PERSON><PERSON> van herbruikbare en herhaalbare velden",
  "attribute.date": "Datum",
  "attribute.date.description": "<PERSON><PERSON> datum<PERSON><PERSON> met uren, minuten en seconden",
  "attribute.datetime": "Datum-tijd",
  "attribute.dynamiczone": "Dynamische zone",
  "attribute.dynamiczone.description": "Kies dynamisch componenten bij het bewerken van content",
  "attribute.email": "E-mail",
  "attribute.email.description": "E-mailveld met formaat validatie",
  "attribute.enumeration": "Opsomming",
  "attribute.enumeration.description": "<PERSON>jst met waarden, kies er een",
  "attribute.json": "JSON",
  "attribute.json.description": "Gegevens in JSON formaat",
  "attribute.media": "Media",
  "attribute.media.description": "Bestanden zoals afbeeldingen, video's, enz",
  "attribute.null": " ",
  "attribute.number": "Getal",
  "attribute.number.description": "Getallen (integer, float, decimal)",
  "attribute.password": "Wachtwoord",
  "attribute.password.description": "Wachtwoordveld met versleuteling",
  "attribute.relation": "Relatie",
  "attribute.relation.description": "Verwijst naar een collectie type",
  "attribute.richtext": "Rijk tekst",
  "attribute.richtext.description": "Een rijk tekst-editor met opmaakopties",
  "attribute.text": "Tekst",
  "attribute.text.description": "Kleine of lange tekst zoals een titel of beschrijving",
  "attribute.time": "Tijd",
  "attribute.timestamp": "Tijdstempel",
  "attribute.uid": "UID",
  "attribute.uid.description": "Unieke identificatie",
  "button.attributes.add.another": "Voeg een veld toe",
  "button.component.add": "Voeg een component toe",
  "button.component.create": "Maak een nieuw component",
  "button.model.create": "Maak een nieuw collectie type",
  "button.single-types.create": "Maak een nieuw enkel type",
  "component.repeatable": "(herhaalbaar)",
  "components.componentSelect.no-component-available": "Alle componenten zijn al toegevoegd",
  "components.componentSelect.no-component-available.with-search": "Er komt geen component overeen met de zoekopdracht",
  "components.componentSelect.value-component": "{number} componenten geselecteerd (typ om een component te zoeken)",
  "components.componentSelect.value-components": "{number} componenten geselecteerd",
  configurations,
  "contentType.collectionName.description": "Handig wanneer de naam van het collectie type en de tabelnaam verschillen",
  "contentType.collectionName.label": "Collectienaam",
  "contentType.displayName.label": "Weergavenaam",
  "contentType.kind.change.warning": "Je hebt zojuist het soort content type gewijzigd. De API wordt gereset (routes, controllers en services worden overschreven).",
  "error.contentTypeName.reserved-name": "Deze naam kan niet worden gebruikt in het project, omdat andere functionaliteiten zou kunnen breken",
  "error.validation.enum-duplicate": "Dubbele waarden zijn niet toegestaan",
  "error.validation.minSupMax": "Kan niet superieur zijn",
  "error.validation.relation.targetAttribute-taken": "Deze naam bestaat al",
  "form.attribute.component.option.add": "Voeg een component toe",
  "form.attribute.component.option.create": "Maak een nieuw component",
  "form.attribute.component.option.create.description": "Een component wordt gedeeld tussen types en componenten, het zal overal beschikbaar zijn.",
  "form.attribute.component.option.repeatable": "Herhaalbaar component",
  "form.attribute.component.option.repeatable.description": "Het beste voor meerdere instanties (array) van ingrediënten, metatags, enz",
  "form.attribute.component.option.reuse-existing": "Gebruik een bestaand component",
  "form.attribute.component.option.reuse-existing.description": "Hergebruik een reeds gemaakt component om de gegevens consistent te houden voor alle Content types.",
  "form.attribute.component.option.single": "Enkel component",
  "form.attribute.component.option.single.description": "het beste voor het groeperen van velden zoals volledig adres, hoofdinformatie, enz",
  "form.attribute.item.customColumnName": "Aangepaste kolom namen",
  "form.attribute.item.customColumnName.description": "Dit is handig om database kolom namen te hernoemen in een meer uitgebreid formaat voor de API responses",
  "form.attribute.item.defineRelation.fieldName": "Veld naam",
  "form.attribute.item.enumeration.graphql": "Naam overschreven voor GraphQL",
  "form.attribute.item.enumeration.graphql.description": "Zorgt ervoor dat je de standaard gegenereerde naam voor GraphQL kan overschrijven",
  "form.attribute.item.enumeration.placeholder": "Bijv.:\nochtend\nmiddag\navond",
  "form.attribute.item.enumeration.rules": "Waardes (één regel per waarde)",
  "form.attribute.item.maximum": "Maximale waarde",
  "form.attribute.item.maximumLength": "Maximale lengte",
  "form.attribute.item.minimum": "Minimale waarde",
  "form.attribute.item.minimumLength": "Minimale lengte",
  "form.attribute.item.number.type": "Nummer formaat",
  "form.attribute.item.number.type.biginteger": "big integer (bijv.: 123456789)",
  "form.attribute.item.number.type.decimal": "decimaal (bijv.: 2.22)",
  "form.attribute.item.number.type.float": "float (bijv.: 3.33333333)",
  "form.attribute.item.number.type.integer": "integer (bijv.: 10)",
  "form.attribute.item.privateField": "Privéveld",
  "form.attribute.item.privateField.description": "Dit veld wordt niet weergegeven in de API response",
  "form.attribute.item.requiredField": "Verplicht veld",
  "form.attribute.item.requiredField.description": "Je kan geen item aanmaken als dit veld leeg is",
  "form.attribute.item.uniqueField": "Uniek veld",
  "form.attribute.item.uniqueField.description": "Je kan geen item aanmaken als er een item is met gelijke inhoud",
  "form.attribute.media.option.multiple": "Meerdere media",
  "form.attribute.media.option.multiple.description": "Het beste voor sliders, carrousels of het downloaden van meerdere bestanden",
  "form.attribute.media.option.single": "Enkel media",
  "form.attribute.media.option.single.description": "het beste voor avatar, profielfoto of omslag",
  "form.attribute.settings.default": "Standaard waarde",
  "form.attribute.text.option.long-text": "Lange tekst",
  "form.attribute.text.option.long-text.description": "Het beste voor beschrijvingen of een biografie. Exact zoeken is uitgeschakeld.",
  "form.attribute.text.option.short-text": "Korte tekst",
  "form.attribute.text.option.short-text.description": "Het beste voor titels, namen, linken (URL). Exact zoeken op het veld is mogelijk.",
  "form.button.add-components-to-dynamiczone": "Voeg componenten toe aan de zone",
  "form.button.add-field": "Voeg een veld toe",
  "form.button.add-first-field-to-created-component": "Voeg een eerste veld toe aan het component",
  "form.button.add.field.to.collectionType": "Voeg een veld toe aan dit collectie type",
  "form.button.add.field.to.component": "Voeg een veld toe aan dit component",
  "form.button.add.field.to.contentType": "Voeg een veld toe aan dit content type",
  "form.button.add.field.to.singleType": "Voeg een veld toe aan dit enkel type",
  "form.button.cancel": "Annuleren",
  "form.button.collection-type.description": "Het beste voor meerdere instanties zoals artikelen, producten, opmerkingen, enz.",
  "form.button.configure-component": "Configureer het component",
  "form.button.configure-view": "Configureer de weergave",
  "form.button.select-component": "Selecteer een component",
  "form.button.single-type.description": "Het beste voor een enkele instantie zoals over ons, homepage, enz.",
  from,
  "modalForm.attribute.form.base.name.description": "Er is geen ruimte toegestaan voor de naam van het attribuut",
  "modalForm.attribute.form.base.name.placeholder": "Bijv.: slug, seoUrl, canonicalUrl",
  "modalForm.attribute.target-field": "Gekoppeld veld",
  "modalForm.attributes.select-component": "Selecteer een component",
  "modalForm.attributes.select-components": "Selecteer de componenten",
  "modalForm.component.header-create": "Maak een component",
  "modalForm.components.create-component.category.label": "Selecteer een categorie of voer een naam in om een nieuwe te maken",
  "modalForm.components.icon.label": "Icoon",
  "modalForm.editCategory.base.name.description": "Er is geen spatie toegestaan in de naam van een categorie",
  "modalForm.header-edit": "Bewerk {name}",
  "modalForm.header.categories": "Categorieën",
  "modalForm.header.back": "Rug",
  "modalForm.singleType.header-create": "Maak een enkel type",
  "modalForm.sub-header.addComponentToDynamicZone": "Voeg een nieuw component toe aan de dynamische zone",
  "modalForm.sub-header.attribute.create": "Voeg een nieuw {type} veld toe",
  "modalForm.sub-header.attribute.create.step": "Nieuw component toevoegen ({step}/2)",
  "modalForm.sub-header.attribute.edit": "Bewerk {name}",
  "modalForm.sub-header.chooseAttribute.collectionType": "Selecteer een veld voor het collectie type",
  "modalForm.sub-header.chooseAttribute.component": "Selecteer een veld voor het component",
  "modalForm.sub-header.chooseAttribute.singleType": "Selecteer een veld voor het enkel type",
  "modelPage.attribute.relation-polymorphic": "Relatie (polymorf)",
  "modelPage.attribute.relationWith": "Relatie met",
  "notification.info.autoreaload-disable": "De autoReload-functie is vereist om deze plug-in te gebruiken. Herstart de server met `strapi develop`",
  "notification.info.creating.notSaved": "Sla het werk op voordat je een nieuw collectie type of component aanmaakt",
  "plugin.description.long": "Modelleer de gegevensstructuur van de API. Maak nieuwe velden en relaties in slechts een minuut. De bestanden worden automatisch aangemaakt en bijgewerkt in jouw project.",
  "plugin.description.short": "Modelleer de gegevensstructuur van de API",
  "popUpForm.navContainer.advanced": "Geavanceerde instellingen",
  "popUpForm.navContainer.base": "Standaard instellingen",
  "popUpWarning.bodyMessage.cancel-modifications": "Weet je zeker dat je de wijzigingen wilt annuleren?",
  "popUpWarning.bodyMessage.cancel-modifications.with-components": "Are you sure you want to cancel your modifications? Some components have been created or modified...",
  "popUpWarning.bodyMessage.category.delete": "Are you sure you want to delete this category? All the components will also be deleted.",
  "popUpWarning.bodyMessage.component.delete": "Are you sure you want to delete this component?",
  "popUpWarning.bodyMessage.contentType.delete": "Weet je zeker dat je dit collectie type wilt verwijderen?",
  "prompt.unsaved": "Weet je zeker dat je wilt stoppen? Al uw wijzigingen gaan verloren.",
  "relation.attributeName.placeholder": "Bijv.: auteur, categorie, tag",
  "relation.manyToMany": "heeft en behoort tot veel",
  "relation.manyToOne": "heeft veel en behoort tot één",
  "relation.manyWay": "heeft veel",
  "relation.oneToMany": "behoort tot vele",
  "relation.oneToOne": "heeft en behoort tot één",
  "relation.oneWay": "heeft één"
};
export {
  configurations,
  nl as default,
  from
};
//# sourceMappingURL=nl.json-ZXDL5NWV.js.map
