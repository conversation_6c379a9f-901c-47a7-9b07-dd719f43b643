{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/components/Widgets.tsx"], "sourcesContent": ["import { Widget, useTracking } from '@strapi/admin/strapi-admin';\nimport { DocumentStatus, RelativeTime } from '@strapi/content-manager/strapi-admin';\nimport { Box, IconButton, Table, Tbody, Td, Tr, Typography } from '@strapi/design-system';\nimport { Pencil } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { StageColumn } from '../routes/content-manager/model/components/TableColumns';\nimport { useGetRecentlyAssignedDocumentsQuery } from '../services/content-manager';\n\nimport type { RecentDocument } from '../../../shared/contracts/homepage';\n\nconst CellTypography = styled(Typography).attrs({ maxWidth: '14.4rem', display: 'block' })`\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n`;\n\nconst RecentDocumentsTable = ({ documents }: { documents: RecentDocument[] }) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const navigate = useNavigate();\n\n  const getEditViewLink = (document: RecentDocument): string => {\n    const isSingleType = document.kind === 'singleType';\n    const kindPath = isSingleType ? 'single-types' : 'collection-types';\n    const queryParams = document.locale ? `?plugins[i18n][locale]=${document.locale}` : '';\n\n    return `/content-manager/${kindPath}/${document.contentTypeUid}${isSingleType ? '' : '/' + document.documentId}${queryParams}`;\n  };\n\n  const handleRowClick = (document: RecentDocument) => () => {\n    trackUsage('willEditEntryFromHome');\n    const link = getEditViewLink(document);\n    navigate(link);\n  };\n\n  return (\n    <Table colCount={6} rowCount={documents?.length ?? 0}>\n      <Tbody>\n        {documents?.map((document) => (\n          <Tr onClick={handleRowClick(document)} cursor=\"pointer\" key={document.documentId}>\n            <Td>\n              <CellTypography title={document.title} variant=\"omega\" textColor=\"neutral800\">\n                {document.title}\n              </CellTypography>\n            </Td>\n            <Td>\n              <CellTypography variant=\"omega\" textColor=\"neutral600\">\n                {document.kind === 'singleType'\n                  ? formatMessage({\n                      id: 'content-manager.widget.last-edited.single-type',\n                      defaultMessage: 'Single-Type',\n                    })\n                  : formatMessage({\n                      id: document.contentTypeDisplayName,\n                      defaultMessage: document.contentTypeDisplayName,\n                    })}\n              </CellTypography>\n            </Td>\n            <Td>\n              <Box display=\"inline-block\">\n                {document.status ? (\n                  <DocumentStatus status={document.status} />\n                ) : (\n                  <Typography textColor=\"neutral600\" aria-hidden>\n                    -\n                  </Typography>\n                )}\n              </Box>\n            </Td>\n            <Td>\n              <Typography textColor=\"neutral600\">\n                <RelativeTime timestamp={new Date(document.updatedAt)} />\n              </Typography>\n            </Td>\n            <Td>\n              <StageColumn strapi_stage={document.strapi_stage} />\n            </Td>\n            <Td onClick={(e) => e.stopPropagation()}>\n              <Box display=\"inline-block\">\n                <IconButton\n                  tag={Link}\n                  to={getEditViewLink(document)}\n                  onClick={() => trackUsage('willEditEntryFromHome')}\n                  label={formatMessage({\n                    id: 'content-manager.actions.edit.label',\n                    defaultMessage: 'Edit',\n                  })}\n                  variant=\"ghost\"\n                >\n                  <Pencil />\n                </IconButton>\n              </Box>\n            </Td>\n          </Tr>\n        ))}\n      </Tbody>\n    </Table>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * AssignedWidget\n * -----------------------------------------------------------------------------------------------*/\n\nconst AssignedWidget = () => {\n  const { formatMessage } = useIntl();\n  const { data, isLoading, error } = useGetRecentlyAssignedDocumentsQuery();\n\n  if (isLoading) {\n    return <Widget.Loading />;\n  }\n\n  if (error || !data) {\n    return <Widget.Error />;\n  }\n\n  if (data.length === 0) {\n    return (\n      <Widget.NoData>\n        {formatMessage({\n          id: 'review-workflows.widget.assigned.no-data',\n          defaultMessage: 'No entries',\n        })}\n      </Widget.NoData>\n    );\n  }\n\n  return <RecentDocumentsTable documents={data} />;\n};\n\nexport { AssignedWidget };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAMA,iBAAiBC,GAAOC,UAAYC,EAAAA,MAAM;EAAEC,UAAU;EAAWC,SAAS;AAAQ,CAAA;;;;;AAMxF,IAAMC,uBAAuB,CAAC,EAAEC,UAAS,MAAmC;AAC1E,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAMC,WAAWC,YAAAA;AAEjB,QAAMC,kBAAkB,CAACC,aAAAA;AACvB,UAAMC,eAAeD,SAASE,SAAS;AACvC,UAAMC,WAAWF,eAAe,iBAAiB;AACjD,UAAMG,cAAcJ,SAASK,SAAS,0BAA0BL,SAASK,MAAM,KAAK;AAEpF,WAAO,oBAAoBF,QAAAA,IAAYH,SAASM,cAAc,GAAGL,eAAe,KAAK,MAAMD,SAASO,UAAU,GAAGH,WAAAA;EACnH;AAEA,QAAMI,iBAAiB,CAACR,aAA6B,MAAA;AACnDL,eAAW,uBAAA;AACX,UAAMc,OAAOV,gBAAgBC,QAAAA;AAC7BH,aAASY,IAAAA;EACX;AAEA,aACEC,wBAACC,OAAAA;IAAMC,UAAU;IAAGC,WAAUrB,uCAAWsB,WAAU;IACjD,cAAAJ,wBAACK,OAAAA;gBACEvB,uCAAWwB,IAAI,CAAChB,iBACfiB,yBAACC,IAAAA;QAAGC,SAASX,eAAeR,QAAAA;QAAWoB,QAAO;;cAC5CV,wBAACW,IAAAA;YACC,cAAAX,wBAACzB,gBAAAA;cAAeqC,OAAOtB,SAASsB;cAAOC,SAAQ;cAAQC,WAAU;cAC9DxB,UAAAA,SAASsB;;;cAGdZ,wBAACW,IAAAA;YACC,cAAAX,wBAACzB,gBAAAA;cAAesC,SAAQ;cAAQC,WAAU;wBACvCxB,SAASE,SAAS,eACfT,cAAc;gBACZgC,IAAI;gBACJC,gBAAgB;cAClB,CAAA,IACAjC,cAAc;gBACZgC,IAAIzB,SAAS2B;gBACbD,gBAAgB1B,SAAS2B;cAC3B,CAAA;;;cAGRjB,wBAACW,IAAAA;YACC,cAAAX,wBAACkB,KAAAA;cAAItC,SAAQ;wBACVU,SAAS6B,aACRnB,wBAACoB,gBAAAA;gBAAeD,QAAQ7B,SAAS6B;uBAEjCnB,wBAACvB,YAAAA;gBAAWqC,WAAU;gBAAaO,eAAW;gBAAC,UAAA;;;;cAMrDrB,wBAACW,IAAAA;YACC,cAAAX,wBAACvB,YAAAA;cAAWqC,WAAU;cACpB,cAAAd,wBAACsB,cAAAA;gBAAaC,WAAW,IAAIC,KAAKlC,SAASmC,SAAS;;;;cAGxDzB,wBAACW,IAAAA;YACC,cAAAX,wBAAC0B,aAAAA;cAAYC,cAAcrC,SAASqC;;;cAEtC3B,wBAACW,IAAAA;YAAGF,SAAS,CAACmB,MAAMA,EAAEC,gBAAe;YACnC,cAAA7B,wBAACkB,KAAAA;cAAItC,SAAQ;cACX,cAAAoB,wBAAC8B,YAAAA;gBACCC,KAAKC;gBACLC,IAAI5C,gBAAgBC,QAAAA;gBACpBmB,SAAS,MAAMxB,WAAW,uBAAA;gBAC1BiD,OAAOnD,cAAc;kBACnBgC,IAAI;kBACJC,gBAAgB;gBAClB,CAAA;gBACAH,SAAQ;gBAER,cAAAb,wBAACmC,eAAAA,CAAAA,CAAAA;;;;;MAlDoD7C,GAAAA,SAASO,UAAU;;;AA2D1F;AAIkG,IAE5FuC,iBAAiB,MAAA;AACrB,QAAM,EAAErD,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEqD,MAAMC,WAAWC,MAAK,IAAKC,qCAAAA;AAEnC,MAAIF,WAAW;AACb,eAAOtC,wBAACyC,OAAOC,SAAO,CAAA,CAAA;EACxB;AAEA,MAAIH,SAAS,CAACF,MAAM;AAClB,eAAOrC,wBAACyC,OAAOE,OAAK,CAAA,CAAA;EACtB;AAEA,MAAIN,KAAKjC,WAAW,GAAG;AACrB,eACEJ,wBAACyC,OAAOG,QAAM;gBACX7D,cAAc;QACbgC,IAAI;QACJC,gBAAgB;MAClB,CAAA;;EAGN;AAEA,aAAOhB,wBAACnB,sBAAAA;IAAqBC,WAAWuD;;AAC1C;", "names": ["CellTypography", "styled", "Typography", "attrs", "max<PERSON><PERSON><PERSON>", "display", "RecentDocumentsTable", "documents", "formatMessage", "useIntl", "trackUsage", "useTracking", "navigate", "useNavigate", "getEditViewLink", "document", "isSingleType", "kind", "<PERSON><PERSON><PERSON>", "queryParams", "locale", "contentTypeUid", "documentId", "handleRowClick", "link", "_jsx", "Table", "col<PERSON>ount", "rowCount", "length", "Tbody", "map", "_jsxs", "Tr", "onClick", "cursor", "Td", "title", "variant", "textColor", "id", "defaultMessage", "contentTypeDisplayName", "Box", "status", "DocumentStatus", "aria-hidden", "RelativeTime", "timestamp", "Date", "updatedAt", "StageColumn", "strapi_stage", "e", "stopPropagation", "IconButton", "tag", "Link", "to", "label", "Pencil", "AssignedWidget", "data", "isLoading", "error", "useGetRecentlyAssignedDocumentsQuery", "Widget", "Loading", "Error", "NoData"]}