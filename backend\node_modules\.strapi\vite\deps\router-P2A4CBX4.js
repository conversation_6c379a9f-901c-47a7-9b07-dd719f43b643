import {
  Route,
  Routes
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/review-workflows/dist/admin/router.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var ProtectedListPage = (0, import_react.lazy)(() => import("./settings-OCJEWVUI.js").then((mod) => ({
  default: mod.ProtectedListPage
})));
var ProtectedEditPage = (0, import_react.lazy)(() => import("./id-REGH7HS3.js").then((mod) => ({
  default: mod.ProtectedEditPage
})));
var routes = [
  {
    path: "/",
    Component: ProtectedListPage
  },
  {
    path: ":id",
    Component: ProtectedEditPage
  }
];
var Router = () => (0, import_jsx_runtime.jsx)(Routes, {
  children: routes.map((route) => (0, import_jsx_runtime.jsx)(Route, {
    ...route
  }, route.path))
});
export {
  Router
};
//# sourceMappingURL=router-P2A4CBX4.js.map
