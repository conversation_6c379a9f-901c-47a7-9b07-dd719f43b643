import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/upload/dist/admin/translations/ru.json.mjs
var ru = {
  "apiError.FileTooBig": "Загруженный файл превышает максимально допустимый размер.",
  "upload.generic-error": "При загрузке файла произошла ошибка.",
  "bulk.select.label": "Выбрать все ресурсы",
  "button.next": "Далее",
  "checkControl.crop-duplicate": "Создать дубликат и кадрировать",
  "checkControl.crop-original": "Кадрирование исходного ресурса",
  "content.isLoading": "Контент загружается.",
  "control-card.add": "Добавить",
  "control-card.cancel": "Отменить",
  "control-card.copy-link": "Скопировать ссылку",
  "control-card.crop": "Кадрирование",
  "control-card.download": "Скачать",
  "control-card.edit": "Редактировать",
  "control-card.replace-media": "Заменить ресурс",
  "control-card.save": "Сохранить",
  "control-card.stop-crop": "Закончить кадрирование",
  "filter.add": "Добавить фильтр",
  "form.button.replace-media": "Заменить медиа",
  "form.input.description.file-alt": "Этот текст будет показан, если ресурс не может быть отображён.",
  "form.input.label.file-alt": "Альтернативный текст",
  "form.input.label.file-caption": "Заголовок",
  "form.input.label.file-name": "Имя файла",
  "form.upload-url.error.url.invalid": "Один URL неверен",
  "form.upload-url.error.url.invalids": "{number} URL-ов неверно",
  "header.actions.add-assets": "Добавить ресурсы",
  "header.actions.add-folder": "Добавить папку",
  "header.actions.add-assets.folder": "папка",
  "header.actions.upload-assets": "Добавить ресурсы",
  "header.actions.upload-new-asset": "Загрузить новый ресурс",
  "header.content.assets-empty": "Нет ресурсов",
  "header.content.assets": "{numberFolders, plural, one {1 папка} other {# паплк}} - {numberAssets, plural, one {1 ресурс} other {# ресурсов}}",
  "input.button.label": "Обзор файлов",
  "input.label": "Перетащите сюда или",
  "input.label-bold": "Перетащите",
  "input.label-normal": "чтобы загрузить или",
  "input.placeholder": "Нажмите, чтобы выбрать ресурс, или перетащите файл в эту область",
  "input.placeholder.icon": "Поместите ресурс в эту зону",
  "input.url.description": "Разделите ссылки переводом строки.",
  "input.url.label": "URL",
  "input.notification.not-supported": "Вы не можете загрузить файл такого типа, принимаются только следующие типы файлов – {fileTypes}",
  "list.assets.title": "Ресурсы ({count})",
  "list.asset.at.finished": "Загрузка ресурсов завершена.",
  "list.assets-empty.search": "Ничего не найдено",
  "list.assets-empty.subtitle": "Добавить первый в список.",
  "list.assets-empty.title": "Пока нет ни одного ресурса",
  "list.assets-empty.title-withSearch": "Нет ресурсов по выбранным фильтрам",
  "list.assets.empty": "Медиабиблиотека пуста",
  "list.assets.empty-upload": "Загрузите свои первые ресурсы...",
  "list.assets.empty.no-permissions": "Нет прав на просмотр",
  "list.assets.loading-asset": "Загрузка предварительного просмотра для медиа: {path}",
  "list.assets.not-supported-content": "Предварительный просмотр недоступен",
  "list.assets.preview-asset": "Предварительный просмотр видео по адресу {path}",
  "list.assets.selected": "{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 asset} other {# assets}}",
  "list-assets-select": "Выбрать {name} ресурс",
  "list.assets.type-not-allowed": "Этот тип файлов не допускается.",
  "list.assets.to-upload": "{number, plural, =0 {Ни одного ресурса не готоово} one {1 ресурс готов} other {# ресурсов готово}} к загрузке",
  "list.folder.edit": "Редактировать папку",
  "list.folder.select": "Выбрать {name} папку",
  "list.folder.subtitle": "{folderCount, plural, =0 {# папка} one {# папка} other {# паплк}}, {filesCount, plural, =0 {# ресурсов} one {# ресурс} other {# ресурсов}}",
  "list.folders.link-label": "Доступ к папке",
  "list.folders.title": "Папки ({count})",
  "mediaLibraryInput.actions.nextSlide": "Следующий слайд",
  "mediaLibraryInput.actions.previousSlide": "Предыдущий слайд",
  "mediaLibraryInput.placeholder": "Нажмите, чтобы добавить ресурс, или перетащите его в эту область",
  "mediaLibraryInput.slideCount": "{n} из {m} слайдов",
  "modal.file-details.date": "Дата",
  "modal.file-details.dimensions": "Размеры",
  "modal.file-details.extension": "Расширение",
  "modal.file-details.size": "Размер",
  "modal.file-details.id": "ID ресурса",
  "modal.folder.elements.count": "{folderCount} папок, {assetCount} ресурсов",
  "modal.header.browse": "Добавить ресурсы",
  "modal.header.file-detail": "Подробно",
  "modal.header.pending-assets": "Ресурсы, ожидающие загрузки",
  "modal.header.select-files": "Выбранные файлы",
  "modal.header.go-back": "Назад",
  "modal.folder.move.title": "Переместить элементы в",
  "modal.nav.browse": "обзор",
  "modal.nav.computer": "с компьютера",
  "modal.nav.selected": "выбрано",
  "modal.nav.url": "с url",
  "modal.remove.success-label": "Элементы были успешно удалены.",
  "modal.move.success-label": "Элементы были успешно перемещены",
  "modal.selected-list.sub-header-subtitle": "Перетащите, чтобы изменить порядок ресурсов в поле",
  "modal.upload-list.footer.button": "Загрузить {number, plural, one {# ресурс} other {# ресурсов}} в бтиблиотеку",
  "modal.upload-list.sub-header-subtitle": "Управление ресурсами перед добавлением в медиабиблиотеку",
  "modal.upload-list.sub-header.button": "Добавить ещё ресурсы",
  "modal.upload.cancelled": "Upload manually aborted.",
  "page.title": "Настройки - Медиабиблиотека",
  "permissions.not-allowed.update": "Вам не разрешено редактировать этот файл.",
  "plugin.description.long": "Управление медиафайлами.",
  "plugin.description.short": "Управление медиафайлами.",
  "plugin.name": "Медиабиблиотека",
  "search.clear.label": "Очистить поиск",
  "search.label": "Искать ресурс",
  "search.placeholder": "Поиск ресурса...",
  "settings.blockTitle": "Управление ресурсами",
  "settings.form.autoOrientation.description": "Автоматически поворачивать изображение в соответствии с EXIF-тегом Orientation",
  "settings.form.autoOrientation.label": "Включить автоопределение ориентации",
  "settings.form.responsiveDimensions.description": "Будут сгенерированы большое, среднее, маленькое изображение",
  "settings.form.responsiveDimensions.label": "Включить адаптивную загрузку",
  "settings.form.sizeOptimization.description": "Включение этой опции приведет к уменьшению размера изображения и незначительному снижению его качества.",
  "settings.form.sizeOptimization.label": "Включить оптимизацию размера (без потери качества)",
  "settings.form.videoPreview.description": "Будет сгенерировано шестисекундное превью (GIF)",
  "settings.form.videoPreview.label": "Превью",
  "settings.header.label": "Медиабиблиотека - Настройки",
  "settings.section.doc.label": "Документ",
  "settings.section.image.label": "Изображение",
  "settings.section.video.label": "Видео",
  "settings.sub-header.label": "Настройка медиабиблиотеки",
  "sort.created_at_asc": "Сначала давно загруженные",
  "sort.created_at_desc": "Сначала недавно загруженные",
  "sort.label": "Сортировка",
  "sort.name_asc": "По алфавиту (А-Я)",
  "sort.name_desc": "По алфавиту обратно (Я-А)",
  "sort.updated_at_asc": "Сначала давно обновлённые",
  "sort.updated_at_desc": "Сначала недавно обновлённые",
  "list.table.header.actions": "действия",
  "list.table.header.preview": "предварительный просмотр",
  "list.table.header.name": "имя",
  "list.table.header.ext": "расширение",
  "list.table.header.size": "размер",
  "list.table.header.createdAt": "создано",
  "list.table.header.updatedAt": "последнее обновление",
  "list.table.header.sort": "Сортировка {label}",
  "list.table.content.empty-label": "Это поле пустое",
  "tabs.title": "Как вы хотите загрузить свои ресурсы?",
  "window.confirm.close-modal.file": "Вы уверены? Изменения будут потеряны.",
  "window.confirm.close-modal.files": "Вы уверены? Некоторые файлы ещё не загружены.",
  "config.back": "Назад",
  "config.subtitle": "Определите параметры просмотра медиабиблиотеки.",
  "config.entries.title": "Записей на странице",
  "config.sort.title": "Порядок сортировки по умолчанию",
  "config.entries.note": "Количество ресурсов, отображаемых по умолчанию в медиабиблиотеке",
  "config.note": "Примечание: Вы можете переопределить это значение в медиабиблиотеке.",
  "config.popUpWarning.warning.updateAllSettings": "Это изменит все ваши настройки",
  "view-switch.list": "Список",
  "view-switch.grid": "Сетка"
};
export {
  ru as default
};
//# sourceMappingURL=ru.json-E3NA2AA2.js.map
