import{r as a,u as j,a as v,b as C,j as r,S as I,c as q,I as T,F as y}from"./strapi-z7ApxZZq.js";const B=({disabled:d,label:g,placeholder:h,trackedEvent:o,trackedEventDetails:m})=>{const t=a.useRef(null),p=a.useRef(null),[{query:S},n]=j(),[s,u]=a.useState(S?._q||""),[l,c]=a.useState(!!s),{formatMessage:f}=v(),{trackUsage:b}=C(),i=()=>c(e=>!e);a.useLayoutEffect(()=>{l&&t.current&&t.current.focus()},[l]);const x=()=>{u(""),n({_q:""},"remove")},R=e=>{e.preventDefault(),s?(o&&b(o,m),n({_q:encodeURIComponent(s),page:1})):(i(),n({_q:""},"remove"))};return l?r.jsx(I,{onSubmit:R,children:r.jsx(q,{ref:t,name:"search",onChange:e=>u(e.target.value),value:s,clearLabel:f({id:"clearLabel",defaultMessage:"Clear"}),onClear:x,placeholder:h,onBlur:e=>{!e.currentTarget.contains(e.relatedTarget)&&e.currentTarget.value===""&&c(!1)},children:g})}):r.jsx(T,{ref:p,disabled:d,label:f({id:"global.search",defaultMessage:"Search"}),onClick:i,children:r.jsx(y,{})})};export{B as S};
