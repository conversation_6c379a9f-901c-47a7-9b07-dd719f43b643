{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\dfghj\\\\site\\\\frontend\\\\src\\\\App.js\";\nimport React from \"react\";\nimport { BrowserRouter as Router, Routes, Route, Link } from \"react-router-dom\";\nimport ImportateurForm from \"./components/ImportateurForm\";\nimport ExportateurForm from \"./components/ExportateurForm\";\nimport OpportuniteForm from \"./components/OpportuniteForm\";\nimport DebugTest from \"./components/DebugTest\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: \"20px\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n        style: {\n          marginBottom: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/importateur\",\n          style: {\n            marginRight: \"10px\"\n          },\n          children: \"\\u2795 Importateur\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/exportateur\",\n          style: {\n            marginRight: \"10px\"\n          },\n          children: \"\\u2795 Exportateur\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/opportunite\",\n          children: \"\\uD83D\\uDCC4 Opportunit\\xE9\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/importateur\",\n          element: /*#__PURE__*/_jsxDEV(ImportateurForm, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/exportateur\",\n          element: /*#__PURE__*/_jsxDEV(ExportateurForm, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/opportunite\",\n          element: /*#__PURE__*/_jsxDEV(OpportuniteForm, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "ImportateurForm", "ExportateurForm", "OpportuniteForm", "DebugTest", "jsxDEV", "_jsxDEV", "App", "children", "style", "padding", "marginBottom", "to", "marginRight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/dfghj/site/frontend/src/App.js"], "sourcesContent": ["import React from \"react\";\nimport { BrowserRouter as Router, Routes, Route, Link } from \"react-router-dom\";\nimport ImportateurForm from \"./components/ImportateurForm\";\nimport ExportateurForm from \"./components/ExportateurForm\";\nimport OpportuniteForm from \"./components/OpportuniteForm\";\nimport DebugTest from \"./components/DebugTest\";\n\nfunction App() {\n  return (\n    <Router>\n      <div style={{ padding: \"20px\" }}>\n        <nav style={{ marginBottom: \"20px\" }}>\n          <Link to=\"/importateur\" style={{ marginRight: \"10px\" }}>➕ Importateur</Link>\n          <Link to=\"/exportateur\" style={{ marginRight: \"10px\" }}>➕ Exportateur</Link>\n          <Link to=\"/opportunite\">📄 Opportunité</Link>\n        </nav>\n\n        <Routes>\n          <Route path=\"/importateur\" element={<ImportateurForm />} />\n          <Route path=\"/exportateur\" element={<ExportateurForm />} />\n          <Route path=\"/opportunite\" element={<OpportuniteForm />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,SAAS,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACT,MAAM;IAAAW,QAAA,eACLF,OAAA;MAAKG,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAC9BF,OAAA;QAAKG,KAAK,EAAE;UAAEE,YAAY,EAAE;QAAO,CAAE;QAAAH,QAAA,gBACnCF,OAAA,CAACN,IAAI;UAACY,EAAE,EAAC,cAAc;UAACH,KAAK,EAAE;YAAEI,WAAW,EAAE;UAAO,CAAE;UAAAL,QAAA,EAAC;QAAa;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5EX,OAAA,CAACN,IAAI;UAACY,EAAE,EAAC,cAAc;UAACH,KAAK,EAAE;YAAEI,WAAW,EAAE;UAAO,CAAE;UAAAL,QAAA,EAAC;QAAa;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5EX,OAAA,CAACN,IAAI;UAACY,EAAE,EAAC,cAAc;UAAAJ,QAAA,EAAC;QAAc;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAENX,OAAA,CAACR,MAAM;QAAAU,QAAA,gBACLF,OAAA,CAACP,KAAK;UAACmB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEb,OAAA,CAACL,eAAe;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DX,OAAA,CAACP,KAAK;UAACmB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEb,OAAA,CAACJ,eAAe;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DX,OAAA,CAACP,KAAK;UAACmB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEb,OAAA,CAACH,eAAe;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACG,EAAA,GAlBQb,GAAG;AAoBZ,eAAeA,GAAG;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}