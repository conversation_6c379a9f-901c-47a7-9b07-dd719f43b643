import {
  SSOProviders
} from "./chunk-TBR2ZHOR.js";
import {
  Login
} from "./chunk-73ABBQBL.js";
import "./chunk-WNXMQTNQ.js";
import "./chunk-VJ3LKUI5.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-7US6K6XC.js";
import "./chunk-EGNP2T5O.js";
import "./chunk-DZIOEGNZ.js";
import "./chunk-L3WQFZOD.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-XVW7MCOW.js";
import "./chunk-2U7FOQNK.js";
import "./chunk-WIFIVZU3.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import "./chunk-FTSHQ5RF.js";
import {
  useGetProvidersQuery
} from "./chunk-RZWN45NY.js";
import "./chunk-FOUXGIF2.js";
import "./chunk-TXOJWASW.js";
import "./chunk-BUMITFEB.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-PW6GS6S3.js";
import {
  Box,
  Divider,
  Flex,
  Typography,
  useIntl
} from "./chunk-Y6YT4U2T.js";
import "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/ee/admin/src/pages/AuthPage/components/Login.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var DividerFull = dt(Divider)`
  flex: 1;
`;
var LoginEE = (loginProps) => {
  const { formatMessage } = useIntl();
  const { isLoading, data: providers = [] } = useGetProvidersQuery(void 0, {
    skip: !window.strapi.features.isEnabled(window.strapi.features.SSO)
  });
  if (!window.strapi.features.isEnabled(window.strapi.features.SSO) || !isLoading && providers.length === 0) {
    return (0, import_jsx_runtime.jsx)(Login, {
      ...loginProps
    });
  }
  return (0, import_jsx_runtime.jsx)(Login, {
    ...loginProps,
    children: (0, import_jsx_runtime.jsx)(Box, {
      paddingTop: 7,
      children: (0, import_jsx_runtime.jsxs)(Flex, {
        direction: "column",
        alignItems: "stretch",
        gap: 7,
        children: [
          (0, import_jsx_runtime.jsxs)(Flex, {
            children: [
              (0, import_jsx_runtime.jsx)(DividerFull, {}),
              (0, import_jsx_runtime.jsx)(Box, {
                paddingLeft: 3,
                paddingRight: 3,
                children: (0, import_jsx_runtime.jsx)(Typography, {
                  variant: "sigma",
                  textColor: "neutral600",
                  children: formatMessage({
                    id: "Auth.login.sso.divider"
                  })
                })
              }),
              (0, import_jsx_runtime.jsx)(DividerFull, {})
            ]
          }),
          (0, import_jsx_runtime.jsx)(SSOProviders, {
            providers,
            displayAllProviders: false
          })
        ]
      })
    })
  });
};
export {
  LoginEE
};
//# sourceMappingURL=Login-3PG3ZDGY.js.map
