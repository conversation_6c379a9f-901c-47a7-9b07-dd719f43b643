{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\dfghj\\\\site\\\\frontend\\\\src\\\\components\\\\ExportateurForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function ExportateurForm() {\n  _s();\n  const [exportateurs, setExportateurs] = useState([]);\n  const [formData, setFormData] = useState({\n    raison_sociale: \"\",\n    nom_contact: \"\",\n    prenom_contact: \"\",\n    matricule_fiscal: \"\",\n    effectif: \"\",\n    forme_juridique: \"\",\n    forme_juridique_autre: \"\",\n    statut: \"\",\n    totalement_exportatrice: false,\n    partiellement_exportatrice: false,\n    adresse: \"\",\n    gouvernorat: \"\",\n    ville: \"\",\n    code_postal: \"\",\n    telephone_siege: \"\",\n    mobile: \"\",\n    email: \"\",\n    secteur_activite: \"\"\n  });\n  useEffect(() => {\n    fetch(\"http://localhost:1337/api/exportateurs?populate=*\").then(res => res.json()).then(data => {\n      console.log(\"Exportateurs API response:\", data);\n      console.log(\"First exportateur structure:\", data.data && data.data[0]);\n      if (data && data.data && Array.isArray(data.data)) {\n        setExportateurs(data.data);\n      } else {\n        console.warn(\"Unexpected API response structure:\", data);\n        setExportateurs([]);\n      }\n    }).catch(err => {\n      console.error(\"Error fetching exportateurs:\", err);\n      setExportateurs([]);\n    });\n  }, []);\n  function handleChange(e) {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === \"checkbox\" ? checked : value\n    });\n  }\n  function handleSubmit(e) {\n    e.preventDefault();\n    fetch(\"http://localhost:1337/api/exportateurs\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\"\n      },\n      body: JSON.stringify({\n        data: formData\n      })\n    }).then(res => res.json()).then(data => {\n      setExportateurs([...exportateurs, data.data]);\n      setFormData({\n        raison_sociale: \"\",\n        nom_contact: \"\",\n        prenom_contact: \"\",\n        matricule_fiscal: \"\",\n        effectif: \"\",\n        forme_juridique: \"\",\n        forme_juridique_autre: \"\",\n        statut: \"\",\n        totalement_exportatrice: false,\n        partiellement_exportatrice: false,\n        adresse: \"\",\n        gouvernorat: \"\",\n        ville: \"\",\n        code_postal: \"\",\n        telephone_siege: \"\",\n        mobile: \"\",\n        email: \"\",\n        secteur_activite: \"\"\n      });\n    }).catch(err => console.error(err));\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Liste des Exportateurs\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      children: exportateurs && exportateurs.length > 0 ? exportateurs.filter(exp => exp !== null && exp !== undefined) // Filtrer les éléments null/undefined\n      .map(exp => {\n        var _exp$attributes, _exp$data, _exp$data$attributes;\n        console.log(\"Processing exportateur:\", exp);\n        // Try different possible data structures\n        const raisonSociale = (exp === null || exp === void 0 ? void 0 : (_exp$attributes = exp.attributes) === null || _exp$attributes === void 0 ? void 0 : _exp$attributes.raison_sociale) || (exp === null || exp === void 0 ? void 0 : exp.raison_sociale) || (exp === null || exp === void 0 ? void 0 : (_exp$data = exp.data) === null || _exp$data === void 0 ? void 0 : (_exp$data$attributes = _exp$data.attributes) === null || _exp$data$attributes === void 0 ? void 0 : _exp$data$attributes.raison_sociale) || 'Structure de données inconnue';\n        return /*#__PURE__*/_jsxDEV(\"li\", {\n          children: raisonSociale\n        }, exp.id || exp.documentId || Math.random(), false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 17\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Aucun exportateur trouv\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Ajouter un Exportateur\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"raison_sociale\",\n        placeholder: \"Raison Sociale\",\n        value: formData.raison_sociale,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"nom_contact\",\n        placeholder: \"Nom du Contact\",\n        value: formData.nom_contact,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"prenom_contact\",\n        placeholder: \"Pr\\xE9nom du Contact\",\n        value: formData.prenom_contact,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"matricule_fiscal\",\n        placeholder: \"Matricule Fiscal\",\n        value: formData.matricule_fiscal,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"effectif\",\n        type: \"number\",\n        placeholder: \"Effectif\",\n        value: formData.effectif,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        name: \"forme_juridique\",\n        value: formData.forme_juridique,\n        onChange: handleChange,\n        required: true,\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"--Forme Juridique--\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"S.A\",\n          children: \"S.A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"S.A.R.L\",\n          children: \"S.A.R.L\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"S.U.A.R.L\",\n          children: \"S.U.A.R.L\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Autre\",\n          children: \"Autre\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), formData.forme_juridique === \"Autre\" && /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"forme_juridique_autre\",\n        placeholder: \"Pr\\xE9cisez la forme juridique\",\n        value: formData.forme_juridique_autre,\n        onChange: handleChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        name: \"statut\",\n        value: formData.statut,\n        onChange: handleChange,\n        required: true,\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"--Statut--\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"R\\xE9sidente\",\n          children: \"R\\xE9sidente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Non r\\xE9sidente\",\n          children: \"Non r\\xE9sidente\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          name: \"totalement_exportatrice\",\n          checked: formData.totalement_exportatrice,\n          onChange: handleChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), \"Totalement Exportatrice\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          name: \"partiellement_exportatrice\",\n          checked: formData.partiellement_exportatrice,\n          onChange: handleChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), \"Partiellement Exportatrice\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"adresse\",\n        placeholder: \"Adresse\",\n        value: formData.adresse,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"gouvernorat\",\n        placeholder: \"Gouvernorat\",\n        value: formData.gouvernorat,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"ville\",\n        placeholder: \"Ville\",\n        value: formData.ville,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"code_postal\",\n        placeholder: \"Code Postal\",\n        value: formData.code_postal,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"telephone_siege\",\n        placeholder: \"T\\xE9l\\xE9phone si\\xE8ge\",\n        value: formData.telephone_siege,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"mobile\",\n        placeholder: \"Mobile\",\n        value: formData.mobile,\n        onChange: handleChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n        name: \"email\",\n        placeholder: \"Email\",\n        type: \"email\",\n        value: formData.email,\n        onChange: handleChange,\n        required: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n        name: \"secteur_activite\",\n        value: formData.secteur_activite,\n        onChange: handleChange,\n        required: true,\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"--Secteur d'activit\\xE9--\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Agro-alimentaire\",\n          children: \"Agro-alimentaire\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Textile\",\n          children: \"Textile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"IME\",\n          children: \"IME\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Service\",\n          children: \"Service\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Artisanat\",\n          children: \"Artisanat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"Divers\",\n          children: \"Divers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        children: \"Ajouter\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n}\n_s(ExportateurForm, \"MuVySSoNkrJkySRdBDenuEamhqE=\");\n_c = ExportateurForm;\nvar _c;\n$RefreshReg$(_c, \"ExportateurForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ExportateurForm", "_s", "exportateurs", "setExportateurs", "formData", "setFormData", "raison_sociale", "nom_contact", "prenom_contact", "matricule_fiscal", "effectif", "forme_juridique", "forme_juridique_autre", "statut", "totalement_exportatrice", "partiellement_exportatrice", "adresse", "gouvernorat", "ville", "code_postal", "telephone_siege", "mobile", "email", "secteur_activite", "fetch", "then", "res", "json", "data", "console", "log", "Array", "isArray", "warn", "catch", "err", "error", "handleChange", "e", "name", "value", "type", "checked", "target", "handleSubmit", "preventDefault", "method", "headers", "body", "JSON", "stringify", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "filter", "exp", "undefined", "map", "_exp$attributes", "_exp$data", "_exp$data$attributes", "raisonSociale", "attributes", "id", "documentId", "Math", "random", "onSubmit", "placeholder", "onChange", "required", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/dfghj/site/frontend/src/components/ExportateurForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\n\r\nexport default function ExportateurForm() {\r\n  const [exportateurs, setExportateurs] = useState([]);\r\n  const [formData, setFormData] = useState({\r\n    raison_sociale: \"\",\r\n    nom_contact: \"\",\r\n    prenom_contact: \"\",\r\n    matricule_fiscal: \"\",\r\n    effectif: \"\",\r\n    forme_juridique: \"\",\r\n    forme_juridique_autre: \"\",\r\n    statut: \"\",\r\n    totalement_exportatrice: false,\r\n    partiellement_exportatrice: false,\r\n    adresse: \"\",\r\n    gouvernorat: \"\",\r\n    ville: \"\",\r\n    code_postal: \"\",\r\n    telephone_siege: \"\",\r\n    mobile: \"\",\r\n    email: \"\",\r\n    secteur_activite: \"\",\r\n  });\r\n\r\n  useEffect(() => {\r\n    fetch(\"http://localhost:1337/api/exportateurs?populate=*\")\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        console.log(\"Exportateurs API response:\", data);\r\n        console.log(\"First exportateur structure:\", data.data && data.data[0]);\r\n        if (data && data.data && Array.isArray(data.data)) {\r\n          setExportateurs(data.data);\r\n        } else {\r\n          console.warn(\"Unexpected API response structure:\", data);\r\n          setExportateurs([]);\r\n        }\r\n      })\r\n      .catch((err) => {\r\n        console.error(\"Error fetching exportateurs:\", err);\r\n        setExportateurs([]);\r\n      });\r\n  }, []);\r\n\r\n  function handleChange(e) {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: type === \"checkbox\" ? checked : value,\r\n    });\r\n  }\r\n\r\n  function handleSubmit(e) {\r\n    e.preventDefault();\r\n\r\n    fetch(\"http://localhost:1337/api/exportateurs\", {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      body: JSON.stringify({ data: formData }),\r\n    })\r\n      .then((res) => res.json())\r\n      .then((data) => {\r\n        setExportateurs([...exportateurs, data.data]);\r\n        setFormData({\r\n          raison_sociale: \"\",\r\n          nom_contact: \"\",\r\n          prenom_contact: \"\",\r\n          matricule_fiscal: \"\",\r\n          effectif: \"\",\r\n          forme_juridique: \"\",\r\n          forme_juridique_autre: \"\",\r\n          statut: \"\",\r\n          totalement_exportatrice: false,\r\n          partiellement_exportatrice: false,\r\n          adresse: \"\",\r\n          gouvernorat: \"\",\r\n          ville: \"\",\r\n          code_postal: \"\",\r\n          telephone_siege: \"\",\r\n          mobile: \"\",\r\n          email: \"\",\r\n          secteur_activite: \"\",\r\n        });\r\n      })\r\n      .catch((err) => console.error(err));\r\n  }\r\n\r\n  return (\r\n    <div>\r\n      <h2>Liste des Exportateurs</h2>\r\n      <ul>\r\n        {exportateurs && exportateurs.length > 0 ? (\r\n          exportateurs\r\n            .filter(exp => exp !== null && exp !== undefined) // Filtrer les éléments null/undefined\r\n            .map((exp) => {\r\n              console.log(\"Processing exportateur:\", exp);\r\n              // Try different possible data structures\r\n              const raisonSociale = exp?.attributes?.raison_sociale ||\r\n                                   exp?.raison_sociale ||\r\n                                   exp?.data?.attributes?.raison_sociale ||\r\n                                   'Structure de données inconnue';\r\n              return (\r\n                <li key={exp.id || exp.documentId || Math.random()}>\r\n                  {raisonSociale}\r\n                </li>\r\n              );\r\n            })\r\n        ) : (\r\n          <li>Aucun exportateur trouvé</li>\r\n        )}\r\n      </ul>\r\n\r\n      <h3>Ajouter un Exportateur</h3>\r\n      <form onSubmit={handleSubmit}>\r\n        <input\r\n          name=\"raison_sociale\"\r\n          placeholder=\"Raison Sociale\"\r\n          value={formData.raison_sociale}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"nom_contact\"\r\n          placeholder=\"Nom du Contact\"\r\n          value={formData.nom_contact}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"prenom_contact\"\r\n          placeholder=\"Prénom du Contact\"\r\n          value={formData.prenom_contact}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"matricule_fiscal\"\r\n          placeholder=\"Matricule Fiscal\"\r\n          value={formData.matricule_fiscal}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"effectif\"\r\n          type=\"number\"\r\n          placeholder=\"Effectif\"\r\n          value={formData.effectif}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <select\r\n          name=\"forme_juridique\"\r\n          value={formData.forme_juridique}\r\n          onChange={handleChange}\r\n          required\r\n        >\r\n          <option value=\"\">--Forme Juridique--</option>\r\n          <option value=\"S.A\">S.A</option>\r\n          <option value=\"S.A.R.L\">S.A.R.L</option>\r\n          <option value=\"S.U.A.R.L\">S.U.A.R.L</option>\r\n          <option value=\"Autre\">Autre</option>\r\n        </select>\r\n        {formData.forme_juridique === \"Autre\" && (\r\n          <input\r\n            name=\"forme_juridique_autre\"\r\n            placeholder=\"Précisez la forme juridique\"\r\n            value={formData.forme_juridique_autre}\r\n            onChange={handleChange}\r\n          />\r\n        )}\r\n        <select\r\n          name=\"statut\"\r\n          value={formData.statut}\r\n          onChange={handleChange}\r\n          required\r\n        >\r\n          <option value=\"\">--Statut--</option>\r\n          <option value=\"Résidente\">Résidente</option>\r\n          <option value=\"Non résidente\">Non résidente</option>\r\n        </select>\r\n        <label>\r\n          <input\r\n            type=\"checkbox\"\r\n            name=\"totalement_exportatrice\"\r\n            checked={formData.totalement_exportatrice}\r\n            onChange={handleChange}\r\n          />\r\n          Totalement Exportatrice\r\n        </label>\r\n        <label>\r\n          <input\r\n            type=\"checkbox\"\r\n            name=\"partiellement_exportatrice\"\r\n            checked={formData.partiellement_exportatrice}\r\n            onChange={handleChange}\r\n          />\r\n          Partiellement Exportatrice\r\n        </label>\r\n        <input\r\n          name=\"adresse\"\r\n          placeholder=\"Adresse\"\r\n          value={formData.adresse}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"gouvernorat\"\r\n          placeholder=\"Gouvernorat\"\r\n          value={formData.gouvernorat}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"ville\"\r\n          placeholder=\"Ville\"\r\n          value={formData.ville}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"code_postal\"\r\n          placeholder=\"Code Postal\"\r\n          value={formData.code_postal}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"telephone_siege\"\r\n          placeholder=\"Téléphone siège\"\r\n          value={formData.telephone_siege}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <input\r\n          name=\"mobile\"\r\n          placeholder=\"Mobile\"\r\n          value={formData.mobile}\r\n          onChange={handleChange}\r\n        />\r\n        <input\r\n          name=\"email\"\r\n          placeholder=\"Email\"\r\n          type=\"email\"\r\n          value={formData.email}\r\n          onChange={handleChange}\r\n          required\r\n        />\r\n        <select\r\n          name=\"secteur_activite\"\r\n          value={formData.secteur_activite}\r\n          onChange={handleChange}\r\n          required\r\n        >\r\n          <option value=\"\">--Secteur d'activité--</option>\r\n          <option value=\"Agro-alimentaire\">Agro-alimentaire</option>\r\n          <option value=\"Textile\">Textile</option>\r\n          <option value=\"IME\">IME</option>\r\n          <option value=\"Service\">Service</option>\r\n          <option value=\"Artisanat\">Artisanat</option>\r\n          <option value=\"Divers\">Divers</option>\r\n        </select>\r\n        <button type=\"submit\">Ajouter</button>\r\n      </form>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,eAAe,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACxC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACvCU,cAAc,EAAE,EAAE;IAClBC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,qBAAqB,EAAE,EAAE;IACzBC,MAAM,EAAE,EAAE;IACVC,uBAAuB,EAAE,KAAK;IAC9BC,0BAA0B,EAAE,KAAK;IACjCC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,gBAAgB,EAAE;EACpB,CAAC,CAAC;EAEF1B,SAAS,CAAC,MAAM;IACd2B,KAAK,CAAC,mDAAmD,CAAC,CACvDC,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MACdC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,IAAI,CAAC;MAC/CC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC;MACtE,IAAIA,IAAI,IAAIA,IAAI,CAACA,IAAI,IAAIG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;QACjDzB,eAAe,CAACyB,IAAI,CAACA,IAAI,CAAC;MAC5B,CAAC,MAAM;QACLC,OAAO,CAACI,IAAI,CAAC,oCAAoC,EAAEL,IAAI,CAAC;QACxDzB,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC,CAAC,CACD+B,KAAK,CAAEC,GAAG,IAAK;MACdN,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAED,GAAG,CAAC;MAClDhC,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAEN,SAASkC,YAAYA,CAACC,CAAC,EAAE;IACvB,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/CtC,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACmC,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC;EACJ;EAEA,SAASI,YAAYA,CAACN,CAAC,EAAE;IACvBA,CAAC,CAACO,cAAc,CAAC,CAAC;IAElBrB,KAAK,CAAC,wCAAwC,EAAE;MAC9CsB,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEtB,IAAI,EAAExB;MAAS,CAAC;IACzC,CAAC,CAAC,CACCqB,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,CAAC,CAAC,CAAC,CACzBF,IAAI,CAAEG,IAAI,IAAK;MACdzB,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAE0B,IAAI,CAACA,IAAI,CAAC,CAAC;MAC7CvB,WAAW,CAAC;QACVC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,EAAE;QACpBC,QAAQ,EAAE,EAAE;QACZC,eAAe,EAAE,EAAE;QACnBC,qBAAqB,EAAE,EAAE;QACzBC,MAAM,EAAE,EAAE;QACVC,uBAAuB,EAAE,KAAK;QAC9BC,0BAA0B,EAAE,KAAK;QACjCC,OAAO,EAAE,EAAE;QACXC,WAAW,EAAE,EAAE;QACfC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,MAAM,EAAE,EAAE;QACVC,KAAK,EAAE,EAAE;QACTC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC,CACDW,KAAK,CAAEC,GAAG,IAAKN,OAAO,CAACO,KAAK,CAACD,GAAG,CAAC,CAAC;EACvC;EAEA,oBACEpC,OAAA;IAAAoD,QAAA,gBACEpD,OAAA;MAAAoD,QAAA,EAAI;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/BxD,OAAA;MAAAoD,QAAA,EACGjD,YAAY,IAAIA,YAAY,CAACsD,MAAM,GAAG,CAAC,GACtCtD,YAAY,CACTuD,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,CAAC,CAAC;MAAA,CACjDC,GAAG,CAAEF,GAAG,IAAK;QAAA,IAAAG,eAAA,EAAAC,SAAA,EAAAC,oBAAA;QACZlC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4B,GAAG,CAAC;QAC3C;QACA,MAAMM,aAAa,GAAG,CAAAN,GAAG,aAAHA,GAAG,wBAAAG,eAAA,GAAHH,GAAG,CAAEO,UAAU,cAAAJ,eAAA,uBAAfA,eAAA,CAAiBvD,cAAc,MAChCoD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEpD,cAAc,MACnBoD,GAAG,aAAHA,GAAG,wBAAAI,SAAA,GAAHJ,GAAG,CAAE9B,IAAI,cAAAkC,SAAA,wBAAAC,oBAAA,GAATD,SAAA,CAAWG,UAAU,cAAAF,oBAAA,uBAArBA,oBAAA,CAAuBzD,cAAc,KACrC,+BAA+B;QACpD,oBACEP,OAAA;UAAAoD,QAAA,EACGa;QAAa,GADPN,GAAG,CAACQ,EAAE,IAAIR,GAAG,CAACS,UAAU,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE9C,CAAC;MAET,CAAC,CAAC,gBAEJxD,OAAA;QAAAoD,QAAA,EAAI;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IACjC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAELxD,OAAA;MAAAoD,QAAA,EAAI;IAAsB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC/BxD,OAAA;MAAMuE,QAAQ,EAAE1B,YAAa;MAAAO,QAAA,gBAC3BpD,OAAA;QACEwC,IAAI,EAAC,gBAAgB;QACrBgC,WAAW,EAAC,gBAAgB;QAC5B/B,KAAK,EAAEpC,QAAQ,CAACE,cAAe;QAC/BkE,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,aAAa;QAClBgC,WAAW,EAAC,gBAAgB;QAC5B/B,KAAK,EAAEpC,QAAQ,CAACG,WAAY;QAC5BiE,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,gBAAgB;QACrBgC,WAAW,EAAC,sBAAmB;QAC/B/B,KAAK,EAAEpC,QAAQ,CAACI,cAAe;QAC/BgE,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,kBAAkB;QACvBgC,WAAW,EAAC,kBAAkB;QAC9B/B,KAAK,EAAEpC,QAAQ,CAACK,gBAAiB;QACjC+D,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,UAAU;QACfE,IAAI,EAAC,QAAQ;QACb8B,WAAW,EAAC,UAAU;QACtB/B,KAAK,EAAEpC,QAAQ,CAACM,QAAS;QACzB8D,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,iBAAiB;QACtBC,KAAK,EAAEpC,QAAQ,CAACO,eAAgB;QAChC6D,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;QAAAtB,QAAA,gBAERpD,OAAA;UAAQyC,KAAK,EAAC,EAAE;UAAAW,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7CxD,OAAA;UAAQyC,KAAK,EAAC,KAAK;UAAAW,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChCxD,OAAA;UAAQyC,KAAK,EAAC,SAAS;UAAAW,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxCxD,OAAA;UAAQyC,KAAK,EAAC,WAAW;UAAAW,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5CxD,OAAA;UAAQyC,KAAK,EAAC,OAAO;UAAAW,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EACRnD,QAAQ,CAACO,eAAe,KAAK,OAAO,iBACnCZ,OAAA;QACEwC,IAAI,EAAC,uBAAuB;QAC5BgC,WAAW,EAAC,gCAA6B;QACzC/B,KAAK,EAAEpC,QAAQ,CAACQ,qBAAsB;QACtC4D,QAAQ,EAAEnC;MAAa;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CACF,eACDxD,OAAA;QACEwC,IAAI,EAAC,QAAQ;QACbC,KAAK,EAAEpC,QAAQ,CAACS,MAAO;QACvB2D,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;QAAAtB,QAAA,gBAERpD,OAAA;UAAQyC,KAAK,EAAC,EAAE;UAAAW,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpCxD,OAAA;UAAQyC,KAAK,EAAC,cAAW;UAAAW,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5CxD,OAAA;UAAQyC,KAAK,EAAC,kBAAe;UAAAW,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACTxD,OAAA;QAAAoD,QAAA,gBACEpD,OAAA;UACE0C,IAAI,EAAC,UAAU;UACfF,IAAI,EAAC,yBAAyB;UAC9BG,OAAO,EAAEtC,QAAQ,CAACU,uBAAwB;UAC1C0D,QAAQ,EAAEnC;QAAa;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,2BAEJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRxD,OAAA;QAAAoD,QAAA,gBACEpD,OAAA;UACE0C,IAAI,EAAC,UAAU;UACfF,IAAI,EAAC,4BAA4B;UACjCG,OAAO,EAAEtC,QAAQ,CAACW,0BAA2B;UAC7CyD,QAAQ,EAAEnC;QAAa;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,8BAEJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRxD,OAAA;QACEwC,IAAI,EAAC,SAAS;QACdgC,WAAW,EAAC,SAAS;QACrB/B,KAAK,EAAEpC,QAAQ,CAACY,OAAQ;QACxBwD,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,aAAa;QAClBgC,WAAW,EAAC,aAAa;QACzB/B,KAAK,EAAEpC,QAAQ,CAACa,WAAY;QAC5BuD,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,OAAO;QACZgC,WAAW,EAAC,OAAO;QACnB/B,KAAK,EAAEpC,QAAQ,CAACc,KAAM;QACtBsD,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,aAAa;QAClBgC,WAAW,EAAC,aAAa;QACzB/B,KAAK,EAAEpC,QAAQ,CAACe,WAAY;QAC5BqD,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,iBAAiB;QACtBgC,WAAW,EAAC,0BAAiB;QAC7B/B,KAAK,EAAEpC,QAAQ,CAACgB,eAAgB;QAChCoD,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,QAAQ;QACbgC,WAAW,EAAC,QAAQ;QACpB/B,KAAK,EAAEpC,QAAQ,CAACiB,MAAO;QACvBmD,QAAQ,EAAEnC;MAAa;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,OAAO;QACZgC,WAAW,EAAC,OAAO;QACnB9B,IAAI,EAAC,OAAO;QACZD,KAAK,EAAEpC,QAAQ,CAACkB,KAAM;QACtBkD,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;MAAA;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACFxD,OAAA;QACEwC,IAAI,EAAC,kBAAkB;QACvBC,KAAK,EAAEpC,QAAQ,CAACmB,gBAAiB;QACjCiD,QAAQ,EAAEnC,YAAa;QACvBoC,QAAQ;QAAAtB,QAAA,gBAERpD,OAAA;UAAQyC,KAAK,EAAC,EAAE;UAAAW,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChDxD,OAAA;UAAQyC,KAAK,EAAC,kBAAkB;UAAAW,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC1DxD,OAAA;UAAQyC,KAAK,EAAC,SAAS;UAAAW,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxCxD,OAAA;UAAQyC,KAAK,EAAC,KAAK;UAAAW,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChCxD,OAAA;UAAQyC,KAAK,EAAC,SAAS;UAAAW,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxCxD,OAAA;UAAQyC,KAAK,EAAC,WAAW;UAAAW,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5CxD,OAAA;UAAQyC,KAAK,EAAC,QAAQ;UAAAW,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eACTxD,OAAA;QAAQ0C,IAAI,EAAC,QAAQ;QAAAU,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACtD,EAAA,CAvQuBD,eAAe;AAAA0E,EAAA,GAAf1E,eAAe;AAAA,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}