{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/hu.json.mjs"], "sourcesContent": ["var groups = \"Csoportok\";\nvar models = \"Gyűjteménytípusok\";\nvar pageNotFound = \"Az oldal nem található\";\nvar hu = {\n    \"App.schemas.data-loaded\": \"A sémák sikeresen betöltve\",\n    \"ListViewTable.relation-loaded\": \"A kapcsolatok betöltődtek\",\n    \"ListViewTable.relation-loading\": \"Relations are loading\",\n    \"ListViewTable.relation-more\": \"This relation contains more entities than displayed\",\n    \"EditRelations.title\": \"Relációs adatok\",\n    \"HeaderLayout.button.label-add-entry\": \"Új bejegyzés létrehozása\",\n    \"api.id\": \"API ID\",\n    \"components.AddFilterCTA.add\": \"Szűrők\",\n    \"components.AddFilterCTA.hide\": \"Szűrők\",\n    \"components.DragHandle-label\": \"Húz\",\n    \"components.DraggableAttr.edit\": \"Kattintson a szerkesztéshez\",\n    \"components.DraggableCard.delete.field\": \"{item} törlése\",\n    \"components.DraggableCard.edit.field\": \"{item} szerkesztése\",\n    \"components.DraggableCard.move.field\": \"{item} mozgatása\",\n    \"components.ListViewTable.row-line\": \"{number}. sor\",\n    \"components.DynamicZone.ComponentPicker-label\": \"Válasszon egy komponenst\",\n    \"components.DynamicZone.add-component\": \"Komponens hozzáadása a {componentName}-hoz\",\n    \"components.DynamicZone.delete-label\": \"{name} törlése\",\n    \"components.DynamicZone.error-message\": \"Az összetevő hibát (hibákat) tartalmaz\",\n    \"components.DynamicZone.missing-components\": \"Hiányzik {number} komponens\",\n    \"components.DynamicZone.move-down-label\": \"Mozgassa a komponenst lefelé\",\n    \"components.DynamicZone.move-up-label\": \"Mozgassa a komponenst felfelé\",\n    \"components.DynamicZone.pick-compo\": \"Válasszon egy komponenst\",\n    \"components.DynamicZone.required\": \"A komponens kötelező\",\n    \"components.EmptyAttributesBlock.button\": \"Ugrás a beállítások oldalra\",\n    \"components.EmptyAttributesBlock.description\": \"Módosíthatja a beállításait\",\n    \"components.FieldItem.linkToComponentLayout\": \"Állítsa be az összetevő elrendezését\",\n    \"components.FieldSelect.label\": \"Mező hozzáadása\",\n    \"components.FilterOptions.button.apply\": \"Alkalmaz\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Alkalmaz\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Mindent kitöröl\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Állítsa be a bejegyzések szűréséhez alkalmazandó feltételeket\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Szűrők\",\n    \"components.FiltersPickWrapper.hide\": \"Elrejt\",\n    \"components.LeftMenu.Search.label\": \"Tartalomtípus keresése\",\n    \"components.LeftMenu.collection-types\": \"Gyűjteménytípusok\",\n    \"components.LeftMenu.single-types\": \"Egyedülálló típusok\",\n    \"components.LimitSelect.itemsPerPage\": \"Oldalankénti elemek száma\",\n    \"components.NotAllowedInput.text\": \"Nincs jogosultság a mező megtekintéséhez\",\n    \"components.RepeatableComponent.error-message\": \"Az összetevő(k) hibát tartalmaz(nak)\",\n    \"components.Search.placeholder\": \"Bejegyzés keresése...\",\n    \"components.Select.draft-info-title\": \"Állapot: Piszkozat\",\n    \"components.Select.publish-info-title\": \"Állapot: Közzétett\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"A szerkesztési nézet megjelenésének testreszabása.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"A listanézet beállításainak megadása.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"A nézet testreszabása - {name}\",\n    \"components.TableDelete.delete\": \"Mindegyik törlése\",\n    \"components.TableDelete.deleteSelected\": \"A kiválasztott elem törlése\",\n    \"components.TableDelete.label\": \"{number} elem kiválasztva\",\n    \"components.TableEmpty.withFilters\": \"Nincsenek {contentType} az alkalmazott szűrőkkel...\",\n    \"components.TableEmpty.withSearch\": \"A megadott kereséssel ({search}) nincs {contentType}...\",\n    \"components.TableEmpty.withoutFilter\": \"Nincs {contentType}...\",\n    \"components.empty-repeatable\": \"Még nincs elem hozzáadva. Kattintson az alábbi gombra a hozzáadásához.\",\n    \"components.notification.info.maximum-requirement\": \"Már elérte a mezők maximális számát\",\n    \"components.notification.info.minimum-requirement\": \"A minimális követelménynek megfelelő mezőt hozzáadtuk\",\n    \"components.repeatable.reorder.error\": \"Hiba történt az mezők átrendezése közben. Kérjük, próbálja újra\",\n    \"components.reset-entry\": \"Bejegyzés visszaállítása\",\n    \"components.uid.apply\": \"alkalmaz\",\n    \"components.uid.available\": \"Elérhető\",\n    \"components.uid.regenerate\": \"Megújít\",\n    \"components.uid.suggested\": \"javasolt\",\n    \"components.uid.unavailable\": \"Nem érhető el\",\n    \"containers.Edit.Link.Layout\": \"Az elrendezés testreszabása\",\n    \"containers.Edit.Link.Model\": \"A gyűjteménytípus szerkesztése\",\n    \"containers.Edit.addAnItem\": \"Elem hozzáadása...\",\n    \"containers.Edit.clickToJump\": \"Kattintson a bejegyzésre ugráshoz\",\n    \"containers.Edit.delete\": \"Törlés\",\n    \"containers.Edit.delete-entry\": \"Bejegyzés törlése\",\n    \"containers.Edit.editing\": \"Szerkesztés...\",\n    \"containers.Edit.information\": \"Információ\",\n    \"containers.Edit.information.by\": \"által\",\n    \"containers.Edit.information.created\": \"Létrehozva\",\n    \"containers.Edit.information.draftVersion\": \"piszkozat\",\n    \"containers.Edit.information.editing\": \"Szerkesztés\",\n    \"containers.Edit.information.lastUpdate\": \"Utolsó frissítés\",\n    \"containers.Edit.information.publishedVersion\": \"közzétett változat\",\n    \"containers.Edit.pluginHeader.title.new\": \"Bejegyzés létrehozása\",\n    \"containers.Edit.reset\": \"Visszaállítás\",\n    \"containers.Edit.returnList\": \"Vissza a listához\",\n    \"containers.Edit.seeDetails\": \"Részletek\",\n    \"containers.Edit.submit\": \"Mentés\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Mező szerkesztése\",\n    \"containers.EditView.add.new-entry\": \"Bejegyzés hozzáadása\",\n    \"containers.EditView.notification.errors\": \"Az űrlap hibás\",\n    \"containers.Home.introduction\": \"A bejegyzések szerkesztéséhez lépjen a bal oldali menüben található hivatkozásra. Ennek a bővítménynek nem módosíthatóak a beállításai, és még mindig aktív fejlesztés alatt áll.\",\n    \"containers.Home.pluginHeaderDescription\": \"Kezelje bejegyzéseit egy sokoldalú és szép felületen keresztül.\",\n    \"containers.Home.pluginHeaderTitle\": \"Tartalomkezelő\",\n    \"containers.List.draft\": \"Piszkozat\",\n    \"containers.List.errorFetchRecords\": \"Hiba\",\n    \"containers.List.published\": \"Közzétett\",\n    \"containers.list.displayedFields\": \"Megjelenített mezők\",\n    \"containers.list.items\": \"{number} elem\",\n    \"containers.list.table-headers.publishedAt\": \"Állapot\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"{fieldName} szerkesztése\",\n    \"containers.SettingPage.add.field\": \"Másik mező beszúrása\",\n    \"containers.SettingPage.attributes\": \"Attribútummezők\",\n    \"containers.SettingPage.attributes.description\": \"Attribútumok sorrendjének meghatározása\",\n    \"containers.SettingPage.editSettings.description\": \"Húzza a megfelelő helyre a mezőket a végleges megjelenítés kialakításához\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Bejegyzés címe\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"A bejegyzés megjelenített mezőjének beállítása\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"A megjelenített mező beállítása szerkesztési és listanézetben\",\n    \"containers.SettingPage.editSettings.title\": \"Nézet szerkesztése (beállítások)\",\n    \"containers.SettingPage.layout\": \"Elrendezés\",\n    \"containers.SettingPage.listSettings.description\": \"Konfigurálja a beállításokat ehhez a gyűjteménytípushoz\",\n    \"containers.SettingPage.listSettings.title\": \"Lista nézet (beállítások)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Gyűjteménytípus speciális beállításainak megadása\",\n    \"containers.SettingPage.settings\": \"Beállítások\",\n    \"containers.SettingPage.view\": \"Nézet\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"Tartalom kezelő - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"A beállítások testreszabása\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Gyűjteménytípusok\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"A gyűjteménytípusok alapértelmezett beállításainak testreszabása\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"Általános\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Az összes gyűjteménytípus és csoport beállításainak testreszabása\",\n    \"containers.SettingsView.list.subtitle\": \"A gyűjteménytípusok és csoportok elrendezésének és megjelenítésének testreszabása\",\n    \"containers.SettingsView.list.title\": \"Megjelenítés beállításai\",\n    \"edit-settings-view.link-to-ctb.components\": \"Komponensek szerkesztése\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"Tartalomtípusok szerkesztése\",\n    \"emptyAttributes.button\": \"Ugrás a gyűjteménytípus-készítőhöz\",\n    \"emptyAttributes.description\": \"Adja hozzá az első mezőt a gyűjtemény típusához\",\n    \"emptyAttributes.title\": \"Még nincsenek mezők\",\n    \"error.attribute.key.taken\": \"Ez az érték már létezik\",\n    \"error.attribute.sameKeyAndName\": \"Nem lehetnek egyenlők\",\n    \"error.attribute.taken\": \"Ez a mezőnév már létezik\",\n    \"error.contentTypeName.taken\": \"Ez a név már létezik\",\n    \"error.model.fetch\": \"Hiba történt a modellek konfigurációjának lekérése során.\",\n    \"error.record.create\": \"Hiba történt a rekord létrehozása közben.\",\n    \"error.record.delete\": \"Hiba történt a rekord törlése közben.\",\n    \"error.record.fetch\": \"Hiba történt a rekord lekérése során.\",\n    \"error.record.update\": \"Hiba történt a rekord frissítése közben.\",\n    \"error.records.count\": \"Hiba történt a rekordok számának lekérése közben.\",\n    \"error.records.fetch\": \"Hiba történt a rekordok lekérése közben.\",\n    \"error.schema.generation\": \"Hiba történt a séma létrehozása során.\",\n    \"error.validation.json\": \"Nem megfelelő JSON formátum\",\n    \"error.validation.max\": \"A megadott érték túl magas.\",\n    \"error.validation.maxLength\": \"A megadott érték túl hosszú.\",\n    \"error.validation.min\": \"A megadott érték túl alacsony.\",\n    \"error.validation.minLength\": \"A megadott érték túl rövid.\",\n    \"error.validation.minSupMax\": \"Nem lehet magasabb\",\n    \"error.validation.regex\": \"A megadott érték nem megfelelő.\",\n    \"error.validation.required\": \"Az érték megadása kötelező.\",\n    \"form.Input.bulkActions\": \"Tömeges művelet engedélyezése\",\n    \"form.Input.defaultSort\": \"Alapértelmezett rendezési attribútum\",\n    \"form.Input.description\": \"Leírás\",\n    \"form.Input.description.placeholder\": \"Megjelenítési név a profilban\",\n    \"form.Input.editable\": \"Szerkeszthető mező\",\n    \"form.Input.filters\": \"Szűrők engedélyezése\",\n    \"form.Input.label\": \"Címke\",\n    \"form.Input.label.inputDescription\": \"Ez az érték felülírja a táblázat fejlécében megjelenő címkét\",\n    \"form.Input.pageEntries\": \"Bejegyzések oldalanként\",\n    \"form.Input.pageEntries.inputDescription\": \"Megjegyzés: Ezt az értéket felülírhatja a Gyűjteménytípus beállításainak oldalán.\",\n    \"form.Input.placeholder\": \"Helykitöltő\",\n    \"form.Input.placeholder.placeholder\": \"Alapérték\",\n    \"form.Input.search\": \"Keresés engedélyezése\",\n    \"form.Input.search.field\": \"Keresés engedélyezése ezen a mezőn\",\n    \"form.Input.sort.field\": \"Rendezés engedélyezése ezen a mezőn\",\n    \"form.Input.sort.order\": \"Alapértelmezett rendezési sorrend\",\n    \"form.Input.wysiwyg\": \"Megjelenítés, mint WYSIWYG\",\n    \"global.displayedFields\": \"Megjelenített mezők\",\n    groups: groups,\n    \"groups.numbered\": \"Csoportok ({number})\",\n    \"header.name\": \"Tartalom\",\n    \"link-to-ctb\": \"Modell szerkesztése\",\n    models: models,\n    \"models.numbered\": \"Gyűjteménytípusok ({number})\",\n    \"notification.error.displayedFields\": \"Legalább egy megjelenített mezőre szükség van\",\n    \"notification.error.relationship.fetch\": \"Hiba történt a kapcsolat lekérése során.\",\n    \"notification.info.SettingPage.disableSort\": \"Legalább egy attribútumnak rendezhetőnek kell lennie\",\n    \"notification.info.minimumFields\": \"Legalább egy mezőt meg kell jeleníteni\",\n    \"notification.upload.error\": \"Hiba történt a fájlok feltöltése közben\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{number, plural, =0 {# bejegyzés} one {# bejegyzés} other {# bejegyzés}} található\",\n    \"pages.NoContentType.button\": \"Tartalomtípus létrehozása\",\n    \"pages.NoContentType.text\": \"Még nincs tartalom, javasoljuk, hogy hozza létre az első tartalomtípust.\",\n    \"permissions.not-allowed.create\": \"Nem hozhat létre dokumentumot\",\n    \"permissions.not-allowed.update\": \"Ezt a dokumentumot nem tekintheti meg\",\n    \"plugin.description.long\": \"Gyors mód az adatbázisban lévő adatok megtekintéséhez, szerkesztéséhez és törléséhez.\",\n    \"plugin.description.short\": \"Gyors mód az adatbázisban lévő adatok megtekintéséhez, szerkesztéséhez és törléséhez.\",\n    \"popover.display-relations.label\": \"Kapcsolatok megjelenítése\",\n    \"select.currently.selected\": \"jelenleg {count} kiválasztva\",\n    \"success.record.delete\": \"Törölve\",\n    \"success.record.publish\": \"Közzétéve\",\n    \"success.record.save\": \"Mentett\",\n    \"success.record.unpublish\": \"Nem közzétett\",\n    \"utils.data-loaded\": \"{number} elem sikeresen betöltődött\",\n    \"apiError.This attribute must be unique\": \"{field} értékének egyedinek kell lennie\",\n    \"popUpWarning.warning.has-draft-relations.title\": \"Megerősítés\",\n    \"popUpWarning.warning.publish-question\": \"Biztosan közzé akarja tenni?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Igen, közzététel\",\n    \"popUpwarning.warning.has-draft-relations.message\": \"<b>{count, plural, one { reláció még nincs } other { reláció még nincs } }</b> publikálva, és nem várt viselkedést okozhat.\"\n};\n\nexport { hu as default, groups, models, pageNotFound };\n//# sourceMappingURL=hu.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,2DAA2D;AAAA,EAC3D,oDAAoD;AACxD;", "names": []}