import{aL as s,j as t,P as i}from"./strapi-z7ApxZZq.js";import{EditView as o}from"./EditView-BmoGNqsN.js";import"./transferTokens-DDbhemls.js";import"./constants-Q2dfXdfa.js";import"./TokenTypeSelect-CcKLfmXH.js";import"./index-C3HeomYJ.js";import"./index-BRVyLNfZ.js";const j=()=>{const e=s(r=>r.admin_app.permissions.settings?.["transfer-tokens"].create);return t.jsx(i.Protect,{permissions:e,children:t.jsx(o,{})})};export{j as ProtectedCreateView};
