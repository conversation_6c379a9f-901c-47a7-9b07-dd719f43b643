import{j as e,$ as k,e as m,d0 as Q,T as F,I as P,bm as Y,f as R,k as _,dc as U,dd as K,a0 as V,de as q,h as $,r as y,u as J,a as N,df as X,dg as Z,b3 as ee,ar as x,cm as S,b7 as te,aJ as se,F as ne,aC as O,w as ae,a7 as re,aM as ie,v as oe,a4 as le,ao as ce,dh as de,di as ue,dj as pe,dk as ge,dl as fe,aw as he,aA as me,bv as ye,P as E,aB as w,B as xe,co as Te,dm as C}from"./strapi-z7ApxZZq.js";import{u as je}from"./useDragLayer-DybN2c0t.js";import{C as Le}from"./CardDragPreview-CL41JsI7.js";import{u as v,a as be}from"./hooks-DCxDPOZs.js";function Se(s,a,n){if(!s||!a||!n)return{display:"none"};const{x:t,y:p}=n;return{transform:`translate(${t}px, ${p}px)`}}const Ce=({renderItem:s})=>{const{itemType:a,isDragging:n,item:t,initialOffset:p,currentOffset:o,mouseOffset:g}=je(r=>({item:r.getItem(),itemType:r.getItemType(),initialOffset:r.getInitialSourceClientOffset(),currentOffset:r.getSourceClientOffset(),isDragging:r.isDragging(),mouseOffset:r.getClientOffset()}));return n?e.jsx(k,{height:"100%",left:0,position:"fixed",pointerEvents:"none",top:0,zIndex:100,width:"100%",children:e.jsx(k,{style:Se(p,o,g),children:s({type:a,item:t})})}):null},Ie=({displayedValue:s})=>e.jsxs(m,{background:"neutral0",borderColor:"neutral200",justifyContent:"space-between",gap:3,padding:3,width:"30rem",children:[e.jsx(ke,{type:"button",children:e.jsxs(m,{gap:6,children:[e.jsx(Me,{alignItems:"center",justifyContent:"center",background:"neutral200",height:"3.2rem",width:"3.2rem",children:e.jsx(Q,{})}),e.jsx(m,{maxWidth:"15rem",children:e.jsx(F,{textColor:"neutral700",ellipsis:!0,children:s})})]})}),e.jsxs(m,{gap:2,children:[e.jsx(P,{withTooltip:!1,label:"",variant:"ghost",children:e.jsx(Y,{})}),e.jsx(P,{withTooltip:!1,label:"",variant:"ghost",children:e.jsx(R,{})})]})]}),Me=_(m)`
  border-radius: 50%;

  svg {
    height: 0.6rem;
    width: 1.1rem;
    > path {
      fill: ${({theme:s})=>s.colors.neutral600};
    }
  }
`,ke=_.button`
  border: none;
  background: transparent;
  display: block;
  width: 100%;
  text-align: unset;
  padding: 0;
`,De=({status:s,displayedValue:a,width:n})=>e.jsx(k,{style:{width:n},children:e.jsxs(m,{paddingTop:2,paddingBottom:2,paddingLeft:2,paddingRight:4,hasRadius:!0,borderWidth:1,background:"neutral0",borderColor:"neutral200",justifyContent:"space-between",gap:4,children:[e.jsxs(U,{gap:1,children:[e.jsx(P,{withTooltip:!1,label:"",variant:"ghost",children:e.jsx(R,{})}),e.jsxs(m,{width:"100%",minWidth:0,justifyContent:"space-between",children:[e.jsx(k,{minWidth:0,paddingTop:1,paddingBottom:1,paddingRight:4,children:e.jsx(K,{href:"",children:e.jsx(F,{textColor:"primary600",ellipsis:!0,children:a})})}),s?e.jsx(V,{status:s}):null]})]}),e.jsx(q,{type:"button",children:e.jsx($,{width:"12px"})})]})}),Ee=()=>{const[s,a]=y.useState(""),[{query:n}]=J(),{formatMessage:t,locale:p}=N(),o=v(c=>c["content-manager"].app.collectionTypeLinks),g=v(c=>c["content-manager"].app.singleTypeLinks),{schemas:r}=X(),{startsWith:i}=Z(p,{sensitivity:"base"}),l=ee(p,{sensitivity:"base"}),T=y.useMemo(()=>[{id:"collectionTypes",title:t({id:x("components.LeftMenu.collection-types"),defaultMessage:"Collection Types"}),searchable:!0,links:o},{id:"singleTypes",title:t({id:x("components.LeftMenu.single-types"),defaultMessage:"Single Types"}),searchable:!0,links:g}].map(c=>({...c,links:c.links.filter(d=>i(d.title,s)).sort((d,j)=>l.compare(d.title,j.title)).map(d=>({...d,title:t({id:d.title,defaultMessage:d.title})}))})),[o,s,g,i,t,l]),u=()=>{a("")},f=({target:{value:c}})=>{a(c)},I=t({id:x("header.name"),defaultMessage:"Content Manager"}),D=c=>{const j=!!r.find(L=>L.uid===c.uid)?.pluginOptions?.i18n?.localized;if(n.plugins&&"i18n"in n.plugins){const{i18n:L,...M}=n.plugins;return j?{i18n:L,...M}:M}return n.plugins};return e.jsxs(S.Main,{"aria-label":I,children:[e.jsx(S.Header,{label:I}),e.jsx(te,{background:"neutral150"}),e.jsx(m,{padding:5,gap:3,direction:"column",alignItems:"stretch",children:e.jsx(se,{startAction:e.jsx(ne,{fill:"neutral500"}),value:s,onChange:f,"aria-label":"Search",placeholder:t({id:"content-manager.components.LeftMenu.Search.label",defaultMessage:"Search for a content type"}),endAction:e.jsx($,{onClick:u,fill:"neutral500",cursor:"pointer"}),size:"S"})}),e.jsx(S.Sections,{children:T.map(c=>e.jsx(S.Section,{label:c.title,children:c.links.map(d=>e.jsx(S.Link,{to:{pathname:d.to,search:O.stringify({...O.parse(d.search??""),plugins:D(d)})},label:d.title},d.uid))},c.id))})]})},{MUTATE_COLLECTION_TYPES_LINKS:we,MUTATE_SINGLE_TYPES_LINKS:Pe}=ue,Oe=()=>{const{toggleNotification:s}=ae(),a=be(),n=re("useContentManagerInitData",u=>u.runHookWaterfall),{notifyStatus:t}=ie(),{formatMessage:p}=N(),{_unstableFormatAPIError:o}=oe(x),g=le("useContentManagerInitData",u=>u.checkUserHasPermissions),r=v(u=>u["content-manager"].app),i=ce(void 0,{refetchOnMountOrArgChange:!0});y.useEffect(()=>{i.data&&t(p({id:x("App.schemas.data-loaded"),defaultMessage:"The schemas have been successfully loaded."}))},[p,i.data,t]),y.useEffect(()=>{i.error&&s({type:"danger",message:o(i.error)})},[o,i.error,s]);const l=de();y.useEffect(()=>{l.error&&s({type:"danger",message:o(l.error)})},[o,l.error,s]);const T=async(u,f,I,D)=>{const{collectionType:c,singleType:d}=f.reduce((h,b)=>(h[b.kind].push(b),h),{collectionType:[],singleType:[]}),j=A(c,"collectionTypes",D),L=A(d,"singleTypes"),M=await Promise.all(j.map(({permissions:h})=>g(h))),z=j.filter((h,b)=>M[b].length>0),B=await Promise.all(L.map(({permissions:h})=>g(h))),W=L.filter((h,b)=>B[b].length>0),{ctLinks:H}=n(we,{ctLinks:z,models:f}),{stLinks:G}=n(Pe,{stLinks:W,models:f});a(pe({authorizedCollectionTypeLinks:H,authorizedSingleTypeLinks:G,components:u,contentTypeSchemas:f,fieldSizes:I}))};return y.useEffect(()=>{i.data&&l.data&&T(i.data.components,i.data.contentTypes,i.data.fieldSizes,l.data)},[i.data,l.data]),{...r}},A=(s,a,n=[])=>s.filter(t=>t.isDisplayed).map(t=>{const p=[{action:"plugin::content-manager.explorer.create",subject:t.uid},{action:"plugin::content-manager.explorer.read",subject:t.uid}],o=[{action:"plugin::content-manager.explorer.read",subject:t.uid}],g=a==="collectionTypes"?p:o,r=n.find(({uid:l})=>l===t.uid);let i=null;if(r){const l={page:1,pageSize:r.settings.pageSize,sort:`${r.settings.defaultSortBy}:${r.settings.defaultSortOrder}`};i=O.stringify(l,{encode:!1})}return{permissions:g,search:i,kind:t.kind,title:t.info.displayName,to:`/content-manager/${t.kind==="collectionType"?ge:fe}/${t.uid}`,uid:t.uid,name:t.uid,isDisplayed:t.isDisplayed}}),_e=()=>{const s=he("/content-manager/:kind/:uid/*"),{isLoading:a,collectionTypeLinks:n,models:t,singleTypeLinks:p}=Oe(),o=[...n,...p].sort((u,f)=>u.title.localeCompare(f.title)),{pathname:g}=me(),{formatMessage:r}=N(),i=ye("Layout",u=>u.startSection),l=y.useRef(i);if(y.useEffect(()=>{l.current&&l.current("contentManager")},[]),a)return e.jsxs(e.Fragment,{children:[e.jsx(E.Title,{children:r({id:x("plugin.name"),defaultMessage:"Content Manager"})}),e.jsx(E.Loading,{})]});const T=t.filter(({isDisplayed:u})=>u);return o.length===0&&T.length>0&&g!=="/content-manager/403"?e.jsx(w,{to:"/403"}):T.length===0&&g!=="/no-content-types"?e.jsx(w,{to:"/no-content-types"}):!s&&o.length>0?e.jsx(w,{to:{pathname:o[0].to,search:o[0].search??""},replace:!0}):e.jsxs(e.Fragment,{children:[e.jsx(E.Title,{children:r({id:x("plugin.name"),defaultMessage:"Content Manager"})}),e.jsxs(xe.Root,{sideNav:e.jsx(Ee,{}),children:[e.jsx(Ce,{renderItem:ve}),e.jsx(Te,{})]})]})};function ve({type:s,item:a}){if(!s||s&&typeof s!="string")return null;const[n]=s.split("_");switch(n){case C.EDIT_FIELD:case C.FIELD:return e.jsx(Le,{label:a.label});case C.COMPONENT:case C.DYNAMIC_ZONE:return e.jsx(Ie,{displayedValue:a.displayedValue});case C.RELATION:return e.jsx(De,{...a});default:return null}}export{_e as Layout};
