import{aL as j,j as s,P as g,a as v,w as A,v as C,aY as E,aZ as F,z as k,B as p,C as O,aP as T,G as q,D as z,E as B,e as H,T as _,H as b,a_ as $,a$ as R,b0 as G,aR as f,aT as U,aU as V,K as D,M as c,b1 as N,b2 as Q}from"./strapi-z7ApxZZq.js";import{u as K}from"./useAdminRoles-DhqKz3-J.js";const W=T().shape({autoRegister:G().required(f.required),defaultRole:R().when("autoRegister",(e,t)=>e?t.required(f.required):t.nullable()),ssoLockedRoles:$().nullable().of(R().when("ssoLockedRoles",(e,t)=>e?t.required(f.required):t.nullable()))}),Y=()=>{const{formatMessage:e}=v(),t=j(l=>l.admin_app.permissions),{toggleNotification:o}=A(),{_unstableFormatAPIError:m,_unstableFormatValidationErrors:h}=C(),{isLoading:i,data:n}=E(),[d,{isLoading:M}]=F(),{isLoading:y,allowedActions:{canUpdate:L,canRead:P}}=k({...t.settings?.sso,readRoles:t.settings?.roles.read??[]}),{roles:S,isLoading:I}=K(void 0,{skip:!P}),w=async(l,u)=>{try{const a=await d(l);if("error"in a){U(a.error)&&a.error.name==="ValidationError"?u.setErrors(h(a.error)):o({type:"danger",message:m(a.error)});return}o({type:"success",message:e({id:"notification.success.saved"})})}catch{o({type:"danger",message:e({id:"notification.error",defaultMessage:"An error occurred, please try again."})})}},x=I||y||i;return s.jsxs(p.Root,{children:[s.jsx(g.Title,{children:e({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"SSO"})}),s.jsx(g.Main,{"aria-busy":M||x,tabIndex:-1,children:s.jsx(O,{method:"PUT",onSubmit:w,validationSchema:W,disabled:!L,initialValues:n||{autoRegister:!1,defaultRole:null,ssoLockedRoles:null},children:({modified:l,isSubmitting:u})=>s.jsxs(s.Fragment,{children:[s.jsx(p.Header,{primaryAction:s.jsx(z,{disabled:!l,loading:u,startIcon:s.jsx(B,{}),type:"submit",children:e({id:"global.save",defaultMessage:"Save"})}),title:e({id:"Settings.sso.title",defaultMessage:"Single Sign-On"}),subtitle:e({id:"Settings.sso.description",defaultMessage:"Configure the settings for the Single Sign-On feature."}),secondaryAction:s.jsx(q,{label:e({id:"components.premiumFeature.title",defaultMessage:"Premium feature"})})}),s.jsx(p.Content,{children:u||x?s.jsx(g.Loading,{}):s.jsxs(H,{direction:"column",alignItems:"stretch",gap:4,background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:[s.jsx(_,{variant:"delta",tag:"h2",children:e({id:"global.settings",defaultMessage:"Settings"})}),s.jsx(b.Root,{gap:4,children:[{hint:e({id:"Settings.sso.form.registration.description",defaultMessage:"Create new user on SSO login if no account exists"}),label:e({id:"Settings.sso.form.registration.label",defaultMessage:"Auto-registration"}),name:"autoRegister",size:6,type:"boolean"},{hint:e({id:"Settings.sso.form.defaultRole.description",defaultMessage:"It will attach the new authenticated user to the selected role"}),label:e({id:"Settings.sso.form.defaultRole.label",defaultMessage:"Default role"}),name:"defaultRole",options:S.map(({id:a,name:r})=>({label:r,value:a.toString()})),placeholder:e({id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"}),size:6,type:"enumeration"},{hint:e({id:"Settings.sso.form.localAuthenticationLock.description",defaultMessage:"Select the roles for which you want to disable the local authentication"}),label:e({id:"Settings.sso.form.localAuthenticationLock.label",defaultMessage:"Local authentication lock-out"}),name:"ssoLockedRoles",options:S.map(({id:a,name:r})=>({label:r,value:a.toString()})),placeholder:e({id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"}),size:6,type:"multi"}].map(({size:a,...r})=>s.jsx(b.Item,{col:a,direction:"column",alignItems:"stretch",children:s.jsx(Z,{...r})},r.name))})]})})]})})})]})},Z=e=>{switch(e.type){case"multi":return s.jsx(J,{...e});default:return s.jsx(V,{...e})}},J=({hint:e,label:t,name:o,options:m,...h})=>{const i=D(o);return s.jsxs(c.Root,{name:o,hint:e,error:i.error,children:[s.jsx(c.Label,{children:t}),s.jsx(N,{onChange:n=>i.onChange("ssoLockedRoles",n),onClear:()=>i.onChange("ssoLockedRoles",[]),value:i.value??[],withTags:!0,...h,children:m.map(({label:n,value:d})=>s.jsx(Q,{value:d,children:n},d))}),s.jsx(c.Hint,{}),s.jsx(c.Error,{})]})},se=()=>{const e=j(t=>t.admin_app.permissions.settings?.sso?.main);return s.jsx(g.Protect,{permissions:e,children:s.jsx(Y,{})})};export{se as ProtectedSSO,Y as SingleSignOnPage};
