{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/nl.json.mjs"], "sourcesContent": ["var configurations = \"configuraties\";\nvar from = \"van\";\nvar nl = {\n    \"attribute.boolean\": \"Boolean\",\n    \"attribute.boolean.description\": \"<PERSON>a of nee, 1 of 0, waar of onwaar\",\n    \"attribute.component\": \"Component\",\n    \"attribute.component.description\": \"<PERSON><PERSON><PERSON> van herbruikbare en herhaalbare velden\",\n    \"attribute.date\": \"Datum\",\n    \"attribute.date.description\": \"<PERSON><PERSON> datum<PERSON> met uren, minuten en seconden\",\n    \"attribute.datetime\": \"Datum-tijd\",\n    \"attribute.dynamiczone\": \"Dynamische zone\",\n    \"attribute.dynamiczone.description\": \"Kies dynamisch componenten bij het bewerken van content\",\n    \"attribute.email\": \"E-mail\",\n    \"attribute.email.description\": \"E-mailveld met formaat validatie\",\n    \"attribute.enumeration\": \"Opsomming\",\n    \"attribute.enumeration.description\": \"Lijst met waarden, kies er een\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"Gegevens in JSON formaat\",\n    \"attribute.media\": \"Media\",\n    \"attribute.media.description\": \"<PERSON><PERSON>en zoals afbee<PERSON>, video's, enz\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"Getal\",\n    \"attribute.number.description\": \"Getallen (integer, float, decimal)\",\n    \"attribute.password\": \"Wachtwoord\",\n    \"attribute.password.description\": \"Wachtwoordveld met versleuteling\",\n    \"attribute.relation\": \"Relatie\",\n    \"attribute.relation.description\": \"Verwijst naar een collectie type\",\n    \"attribute.richtext\": \"Rijk tekst\",\n    \"attribute.richtext.description\": \"Een rijk tekst-editor met opmaakopties\",\n    \"attribute.text\": \"Tekst\",\n    \"attribute.text.description\": \"Kleine of lange tekst zoals een titel of beschrijving\",\n    \"attribute.time\": \"Tijd\",\n    \"attribute.timestamp\": \"Tijdstempel\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Unieke identificatie\",\n    \"button.attributes.add.another\": \"Voeg een veld toe\",\n    \"button.component.add\": \"Voeg een component toe\",\n    \"button.component.create\": \"Maak een nieuw component\",\n    \"button.model.create\": \"Maak een nieuw collectie type\",\n    \"button.single-types.create\": \"Maak een nieuw enkel type\",\n    \"component.repeatable\": \"(herhaalbaar)\",\n    \"components.componentSelect.no-component-available\": \"Alle componenten zijn al toegevoegd\",\n    \"components.componentSelect.no-component-available.with-search\": \"Er komt geen component overeen met de zoekopdracht\",\n    \"components.componentSelect.value-component\": \"{number} componenten geselecteerd (typ om een component te zoeken)\",\n    \"components.componentSelect.value-components\": \"{number} componenten geselecteerd\",\n    configurations: configurations,\n    \"contentType.collectionName.description\": \"Handig wanneer de naam van het collectie type en de tabelnaam verschillen\",\n    \"contentType.collectionName.label\": \"Collectienaam\",\n    \"contentType.displayName.label\": \"Weergavenaam\",\n    \"contentType.kind.change.warning\": \"Je hebt zojuist het soort content type gewijzigd. De API wordt gereset (routes, controllers en services worden overschreven).\",\n    \"error.contentTypeName.reserved-name\": \"Deze naam kan niet worden gebruikt in het project, omdat andere functionaliteiten zou kunnen breken\",\n    \"error.validation.enum-duplicate\": \"Dubbele waarden zijn niet toegestaan\",\n    \"error.validation.minSupMax\": \"Kan niet superieur zijn\",\n    \"error.validation.relation.targetAttribute-taken\": \"Deze naam bestaat al\",\n    \"form.attribute.component.option.add\": \"Voeg een component toe\",\n    \"form.attribute.component.option.create\": \"Maak een nieuw component\",\n    \"form.attribute.component.option.create.description\": \"Een component wordt gedeeld tussen types en componenten, het zal overal beschikbaar zijn.\",\n    \"form.attribute.component.option.repeatable\": \"Herhaalbaar component\",\n    \"form.attribute.component.option.repeatable.description\": \"Het beste voor meerdere instanties (array) van ingrediënten, metatags, enz\",\n    \"form.attribute.component.option.reuse-existing\": \"Gebruik een bestaand component\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Hergebruik een reeds gemaakt component om de gegevens consistent te houden voor alle Content types.\",\n    \"form.attribute.component.option.single\": \"Enkel component\",\n    \"form.attribute.component.option.single.description\": \"het beste voor het groeperen van velden zoals volledig adres, hoofdinformatie, enz\",\n    \"form.attribute.item.customColumnName\": \"Aangepaste kolom namen\",\n    \"form.attribute.item.customColumnName.description\": \"Dit is handig om database kolom namen te hernoemen in een meer uitgebreid formaat voor de API responses\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Veld naam\",\n    \"form.attribute.item.enumeration.graphql\": \"Naam overschreven voor GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Zorgt ervoor dat je de standaard gegenereerde naam voor GraphQL kan overschrijven\",\n    \"form.attribute.item.enumeration.placeholder\": \"Bijv.:\\nochtend\\nmiddag\\navond\",\n    \"form.attribute.item.enumeration.rules\": \"Waardes (één regel per waarde)\",\n    \"form.attribute.item.maximum\": \"Maximale waarde\",\n    \"form.attribute.item.maximumLength\": \"Maximale lengte\",\n    \"form.attribute.item.minimum\": \"Minimale waarde\",\n    \"form.attribute.item.minimumLength\": \"Minimale lengte\",\n    \"form.attribute.item.number.type\": \"Nummer formaat\",\n    \"form.attribute.item.number.type.biginteger\": \"big integer (bijv.: 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"decimaal (bijv.: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"float (bijv.: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"integer (bijv.: 10)\",\n    \"form.attribute.item.privateField\": \"Privéveld\",\n    \"form.attribute.item.privateField.description\": \"Dit veld wordt niet weergegeven in de API response\",\n    \"form.attribute.item.requiredField\": \"Verplicht veld\",\n    \"form.attribute.item.requiredField.description\": \"Je kan geen item aanmaken als dit veld leeg is\",\n    \"form.attribute.item.uniqueField\": \"Uniek veld\",\n    \"form.attribute.item.uniqueField.description\": \"Je kan geen item aanmaken als er een item is met gelijke inhoud\",\n    \"form.attribute.media.option.multiple\": \"Meerdere media\",\n    \"form.attribute.media.option.multiple.description\": \"Het beste voor sliders, carrousels of het downloaden van meerdere bestanden\",\n    \"form.attribute.media.option.single\": \"Enkel media\",\n    \"form.attribute.media.option.single.description\": \"het beste voor avatar, profielfoto of omslag\",\n    \"form.attribute.settings.default\": \"Standaard waarde\",\n    \"form.attribute.text.option.long-text\": \"Lange tekst\",\n    \"form.attribute.text.option.long-text.description\": \"Het beste voor beschrijvingen of een biografie. Exact zoeken is uitgeschakeld.\",\n    \"form.attribute.text.option.short-text\": \"Korte tekst\",\n    \"form.attribute.text.option.short-text.description\": \"Het beste voor titels, namen, linken (URL). Exact zoeken op het veld is mogelijk.\",\n    \"form.button.add-components-to-dynamiczone\": \"Voeg componenten toe aan de zone\",\n    \"form.button.add-field\": \"Voeg een veld toe\",\n    \"form.button.add-first-field-to-created-component\": \"Voeg een eerste veld toe aan het component\",\n    \"form.button.add.field.to.collectionType\": \"Voeg een veld toe aan dit collectie type\",\n    \"form.button.add.field.to.component\": \"Voeg een veld toe aan dit component\",\n    \"form.button.add.field.to.contentType\": \"Voeg een veld toe aan dit content type\",\n    \"form.button.add.field.to.singleType\": \"Voeg een veld toe aan dit enkel type\",\n    \"form.button.cancel\": \"Annuleren\",\n    \"form.button.collection-type.description\": \"Het beste voor meerdere instanties zoals artikelen, producten, opmerkingen, enz.\",\n    \"form.button.configure-component\": \"Configureer het component\",\n    \"form.button.configure-view\": \"Configureer de weergave\",\n    \"form.button.select-component\": \"Selecteer een component\",\n    \"form.button.single-type.description\": \"Het beste voor een enkele instantie zoals over ons, homepage, enz.\",\n    from: from,\n    \"modalForm.attribute.form.base.name.description\": \"Er is geen ruimte toegestaan voor de naam van het attribuut\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"Bijv.: slug, seoUrl, canonicalUrl\",\n    \"modalForm.attribute.target-field\": \"Gekoppeld veld\",\n    \"modalForm.attributes.select-component\": \"Selecteer een component\",\n    \"modalForm.attributes.select-components\": \"Selecteer de componenten\",\n    \"modalForm.component.header-create\": \"Maak een component\",\n    \"modalForm.components.create-component.category.label\": \"Selecteer een categorie of voer een naam in om een nieuwe te maken\",\n    \"modalForm.components.icon.label\": \"Icoon\",\n    \"modalForm.editCategory.base.name.description\": \"Er is geen spatie toegestaan in de naam van een categorie\",\n    \"modalForm.header-edit\": \"Bewerk {name}\",\n    \"modalForm.header.categories\": \"Categorieën\",\n    \"modalForm.header.back\": \"Rug\",\n    \"modalForm.singleType.header-create\": \"Maak een enkel type\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Voeg een nieuw component toe aan de dynamische zone\",\n    \"modalForm.sub-header.attribute.create\": \"Voeg een nieuw {type} veld toe\",\n    \"modalForm.sub-header.attribute.create.step\": \"Nieuw component toevoegen ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Bewerk {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Selecteer een veld voor het collectie type\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Selecteer een veld voor het component\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Selecteer een veld voor het enkel type\",\n    \"modelPage.attribute.relation-polymorphic\": \"Relatie (polymorf)\",\n    \"modelPage.attribute.relationWith\": \"Relatie met\",\n    \"notification.info.autoreaload-disable\": \"De autoReload-functie is vereist om deze plug-in te gebruiken. Herstart de server met `strapi develop`\",\n    \"notification.info.creating.notSaved\": \"Sla het werk op voordat je een nieuw collectie type of component aanmaakt\",\n    \"plugin.description.long\": \"Modelleer de gegevensstructuur van de API. Maak nieuwe velden en relaties in slechts een minuut. De bestanden worden automatisch aangemaakt en bijgewerkt in jouw project.\",\n    \"plugin.description.short\": \"Modelleer de gegevensstructuur van de API\",\n    \"popUpForm.navContainer.advanced\": \"Geavanceerde instellingen\",\n    \"popUpForm.navContainer.base\": \"Standaard instellingen\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"Weet je zeker dat je de wijzigingen wilt annuleren?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Are you sure you want to cancel your modifications? Some components have been created or modified...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"Are you sure you want to delete this category? All the components will also be deleted.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"Are you sure you want to delete this component?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Weet je zeker dat je dit collectie type wilt verwijderen?\",\n    \"prompt.unsaved\": \"Weet je zeker dat je wilt stoppen? Al uw wijzigingen gaan verloren.\",\n    \"relation.attributeName.placeholder\": \"Bijv.: auteur, categorie, tag\",\n    \"relation.manyToMany\": \"heeft en behoort tot veel\",\n    \"relation.manyToOne\": \"heeft veel en behoort tot één\",\n    \"relation.manyWay\": \"heeft veel\",\n    \"relation.oneToMany\": \"behoort tot vele\",\n    \"relation.oneToOne\": \"heeft en behoort tot één\",\n    \"relation.oneWay\": \"heeft één\"\n};\n\nexport { configurations, nl as default, from };\n//# sourceMappingURL=nl.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC;AAAA,EACA,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACvB;", "names": []}