import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/plugin-users-permissions/dist/admin/translations/id.json.mjs
var id = {
  "BoundRoute.title": "Rute terikat ke",
  "EditForm.inputSelect.description.role": "Ini akan melampirkan pengguna baru yang diautentikasi ke peran yang dipilih.",
  "EditForm.inputSelect.label.role": "Peran default untuk pengguna yang diautentikasi",
  "EditForm.inputToggle.description.email": "Larang pengguna membuat beberapa akun menggunakan alamat email yang sama dengan penyedia otentikasi yang berbeda.",
  "EditForm.inputToggle.description.email-confirmation": "Saat diaktifkan (ON), pengguna baru yang terdaftar menerima email konfirmasi.",
  "EditForm.inputToggle.description.email-confirmation-redirection": "Setelah Anda mengkonfirmasi email Anda, pilih ke mana Anda akan diarahkan.",
  "EditForm.inputToggle.description.email-reset-password": "URL halaman setel ulang sandi aplikasi Anda",
  "EditForm.inputToggle.description.sign-up": "Saat dinonaktifkan (OFF), proses pendaftaran dilarang. Tidak ada yang bisa berlangganan lagi tidak peduli penyedia yang digunakan.",
  "EditForm.inputToggle.label.email": "Satu akun per alamat email",
  "EditForm.inputToggle.label.email-confirmation": "Aktifkan konfirmasi email",
  "EditForm.inputToggle.label.email-confirmation-redirection": "URL pengalihan",
  "EditForm.inputToggle.label.email-reset-password": "Atur ulang halaman kata sandi",
  "EditForm.inputToggle.label.sign-up": "Aktifkan pendaftaran",
  "Email.template.email_confirmation": "Konfirmasi alamat email",
  "HeaderNav.link.advancedSettings": "Pengaturan lanjutan",
  "HeaderNav.link.emailTemplates": "Template email",
  "HeaderNav.link.providers": "Penyedia",
  "Plugin.permissions.plugins.description": "Tentukan semua tindakan yang diizinkan untuk plugin {name}.",
  "Plugins.header.description": "Hanya tindakan yang terikat oleh rute yang dicantumkan di bawah.",
  "Plugins.header.title": "Izin",
  "Policies.header.hint": "Pilih tindakan aplikasi atau tindakan plugin dan klik ikon roda gigi untuk menampilkan rute terikat",
  "Policies.header.title": "Pengaturan lanjutan",
  "PopUpForm.Email.email_templates.inputDescription": "Jika Anda tidak yakin bagaimana menggunakan variabel, {link}",
  "PopUpForm.Email.link.documentation": "lihat dokumentasi kami.",
  "PopUpForm.Email.options.from.email.label": "Email pengirim",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Nama pengirim",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "Pesan",
  "PopUpForm.Email.options.object.label": "Subyek",
  "PopUpForm.Email.options.object.placeholder": "Harap konfirmasi alamat email Anda untuk% APP_NAME%",
  "PopUpForm.Email.options.response_email.label": "Email tanggapan",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "Jika dinonaktifkan, pengguna tidak akan dapat menggunakan penyedia ini.",
  "PopUpForm.Providers.enabled.label": "Memungkinkan",
  "PopUpForm.Providers.key.label": "ID Klien",
  "PopUpForm.Providers.key.placeholder": "TEKS",
  "PopUpForm.Providers.redirectURL.front-end.label": "URL pengalihan ke aplikasi front-end Anda",
  "PopUpForm.Providers.redirectURL.label": "URL pengalihan yang akan ditambahkan dalam konfigurasi aplikasi {provider} Anda",
  "PopUpForm.Providers.secret.label": "Client Secret",
  "PopUpForm.Providers.secret.placeholder": "TEKS",
  "PopUpForm.Providers.subdomain.label": "URI Host (Subdomain)",
  "PopUpForm.Providers.subdomain.placeholder": "saya.subdomain.com",
  "PopUpForm.header.edit.email-templates": "Edit Template Email",
  "PopUpForm.header.edit.providers": "Edit Penyedia",
  "Settings.roles.deleted": "Peran dihapus",
  "Settings.roles.edited": "Peran diedit",
  "Settings.section-label": "Plugin Pengguna & Izin",
  "notification.success.submit": "Pengaturan telah diperbarui",
  "plugin.description.long": "Lindungi API Anda dengan proses otentikasi penuh berdasarkan JWT. Plugin ini juga dilengkapi dengan strategi ACL yang memungkinkan Anda untuk mengelola izin di antara grup pengguna.",
  "plugin.description.short": "Lindungi API Anda dengan proses otentikasi penuh berdasarkan JWT",
  "plugin.name": "Plugin Pengguna & Izin",
  "popUpWarning.button.cancel": "Batalkan",
  "popUpWarning.button.confirm": "Konfirmasi",
  "popUpWarning.title": "Harap konfirmasi",
  "popUpWarning.warning.cancel": "Anda yakin ingin membatalkan modifikasi Anda?"
};
export {
  id as default
};
//# sourceMappingURL=id.json-44M764KQ.js.map
