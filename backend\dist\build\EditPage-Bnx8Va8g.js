const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["EventsTable-D2GhW8Zl.js","strapi-z7ApxZZq.js","strapi-D6821bIJ.css","selectors-B6uMLQu7.js","useWebhooks-CzWtIcqU.js"])))=>i.map(i=>d[i]);
import{a as j,j as e,e as g,M as y,d_ as Y,d$ as U,e0 as I,bq as ee,T as x,K as C,e1 as te,k as W,e2 as R,bp as P,e3 as se,ce as T,$ as S,H as b,e4 as re,I as ae,e5 as ne,av as ie,r as w,N as oe,O as le,aK as de,h as N,cy as ce,E as z,bi as ue,_ as he,C as ge,aP as H,B as _,bg as me,D as q,e6 as pe,aU as fe,a_ as O,e7 as be,aQ as E,aw as xe,V as je,w as we,v as ke,P as $,aE as ye,aL as Se,aT as V}from"./strapi-z7ApxZZq.js";import{s as ve}from"./selectors-B6uMLQu7.js";import{u as Ee}from"./useWebhooks-CzWtIcqU.js";const Ce=({children:t})=>{const{formatMessage:s}=j(),r=s({id:"Settings.webhooks.form.events",defaultMessage:"Events"});return e.jsxs(g,{direction:"column",alignItems:"stretch",gap:1,children:[e.jsx(y.Label,{"aria-hidden":!0,children:r}),e.jsx(Me,{"aria-label":r,children:t})]})},Me=W(se)`
  tbody tr:nth-child(odd) {
    background: ${({theme:t})=>t.colors.neutral100};
  }

  thead th span {
    color: ${({theme:t})=>t.colors.neutral500};
  }

  td,
  th {
    padding-block-start: ${({theme:t})=>t.spaces[3]};
    padding-block-end: ${({theme:t})=>t.spaces[3]};
    width: 6%;
    vertical-align: middle;
  }

  tbody tr td:first-child {
    /**
     * Add padding to the start of the first column to avoid the checkbox appearing
     * too close to the edge of the table
     */
    padding-inline-start: ${({theme:t})=>t.spaces[2]};
  }
`,Ie=()=>[{id:"Settings.webhooks.events.create",defaultMessage:"Create"},{id:"Settings.webhooks.events.update",defaultMessage:"Update"},{id:"app.utils.delete",defaultMessage:"Delete"},{id:"app.utils.publish",defaultMessage:"Publish"},{id:"app.utils.unpublish",defaultMessage:"Unpublish"}],Re=({getHeaders:t=Ie})=>{const{formatMessage:s}=j(),r=t();return e.jsx(Y,{children:e.jsxs(U,{children:[e.jsx(I,{children:e.jsx(ee,{children:s({id:"Settings.webhooks.event.select",defaultMessage:"Select event"})})}),r.map(n=>["app.utils.publish","app.utils.unpublish"].includes(n?.id??"")?e.jsx(I,{title:s({id:"Settings.webhooks.event.publish-tooltip",defaultMessage:"This event only exists for content with draft & publish enabled"}),children:e.jsx(x,{variant:"sigma",textColor:"neutral600",children:s(n)})},n.id):e.jsx(I,{children:e.jsx(x,{variant:"sigma",textColor:"neutral600",children:s(n)})},n.id))]})})},Te=({providedEvents:t})=>{const s=t||Ae(),{value:r=[],onChange:n}=C("events"),h="events",i=r,a=[],u=i.reduce((l,d)=>{const o=d.split(".")[0];return l[o]||(l[o]=[]),l[o].push(d),l},{}),f=(l,d)=>{const o=new Set(i);d?o.add(l):o.delete(l),n(h,Array.from(o))},c=(l,d)=>{const o=new Set(i);d?s[l].forEach(m=>{a.includes(m)||o.add(m)}):s[l].forEach(m=>o.delete(m)),n(h,Array.from(o))};return e.jsx(te,{children:Object.entries(s).map(([l,d])=>e.jsx($e,{disabledEvents:a,name:l,events:d,inputValue:u[l],handleSelect:f,handleSelectAll:c},l))})},Ae=()=>({entry:["entry.create","entry.update","entry.delete","entry.publish","entry.unpublish"],media:["media.create","media.update","media.delete"]}),$e=({disabledEvents:t=[],name:s,events:r=[],inputValue:n=[],handleSelect:h,handleSelectAll:i})=>{const{formatMessage:a}=j(),u=r.filter(o=>!t.includes(o)),f=n.length>0,c=n.length===u.length,l=()=>{i(s,!c)},d=5;return e.jsxs(U,{children:[e.jsx(R,{children:e.jsx(P,{"aria-label":a({id:"global.select-all-entries",defaultMessage:"Select all entries"}),name:s,checked:f&&!c?"indeterminate":c,onCheckedChange:l,children:Fe(s)})}),r.map(o=>e.jsx(R,{textAlign:"center",children:e.jsx(g,{width:"100%",justifyContent:"center",children:e.jsx(P,{disabled:t.includes(o),"aria-label":o,name:o,checked:n.includes(o),onCheckedChange:m=>h(o,!!m)})})},o)),r.length<d&&e.jsx(R,{colSpan:d-r.length})]})},Fe=t=>t.replace(/-/g," ").split(" ").map(s=>s.charAt(0).toUpperCase()+s.slice(1).toLowerCase()).join(" "),A={Root:Ce,Headers:Re,Body:Te},Pe=()=>e.jsxs(A.Root,{children:[e.jsx(A.Headers,{}),e.jsx(A.Body,{})]}),He=W(de)`
  cursor: pointer;
`,_e=()=>{const{formatMessage:t}=j(),s=T("HeadersInput",a=>a.addFieldRow),r=T("HeadersInput",a=>a.removeFieldRow),n=T("HeadersInput",a=>a.onChange),{value:h=[]}=C("headers"),i=a=>{h.length===1?n("headers",[{key:"",value:""}]):r("headers",a)};return e.jsxs(g,{direction:"column",alignItems:"stretch",gap:1,children:[e.jsx(y.Label,{children:t({id:"Settings.webhooks.form.headers",defaultMessage:"Headers"})}),e.jsxs(S,{padding:8,background:"neutral100",hasRadius:!0,children:[h.map((a,u)=>e.jsxs(b.Root,{gap:4,padding:2,children:[e.jsx(b.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsx(qe,{name:`headers.${u}.key`,"aria-label":`row ${u+1} key`,label:t({id:"Settings.webhooks.key",defaultMessage:"Key"})})}),e.jsx(b.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(g,{alignItems:"flex-end",gap:2,children:[e.jsx(S,{style:{flex:1},children:e.jsx(re,{name:`headers.${u}.value`,"aria-label":`row ${u+1} value`,label:t({id:"Settings.webhooks.value",defaultMessage:"Value"}),type:"string"})}),e.jsx(ae,{width:"4rem",height:"4rem",onClick:()=>i(u),color:"primary600",label:t({id:"Settings.webhooks.headers.remove",defaultMessage:"Remove header row {number}"},{number:u+1}),type:"button",children:e.jsx(ne,{width:"0.8rem"})})]})})]},`${u}-${JSON.stringify(a.key)}`)),e.jsx(S,{paddingTop:4,children:e.jsx(He,{type:"button",onClick:()=>{s("headers",{key:"",value:""})},startIcon:e.jsx(ie,{}),children:t({id:"Settings.webhooks.create.header",defaultMessage:"Create new header"})})})]})]})},qe=({name:t,label:s,...r})=>{const[n,h]=w.useState([...B]),{value:i}=C("headers"),a=C(t);w.useEffect(()=>{const c=B.filter(l=>!i?.some(d=>d.key!==a.value&&d.key===l));h(c)},[i,a.value]);const u=c=>{a.onChange(t,c)},f=c=>{h(l=>[...l,c]),c&&u(c)};return e.jsxs(y.Root,{name:t,error:a.error,children:[e.jsx(y.Label,{children:s}),e.jsx(oe,{...r,onClear:()=>u(""),onChange:u,onCreateOption:f,placeholder:"",creatable:!0,value:a.value,children:n.map(c=>e.jsx(le,{value:c,children:c},c))}),e.jsx(y.Error,{})]})},B=["A-IM","Accept","Accept-Charset","Accept-Encoding","Accept-Language","Accept-Datetime","Access-Control-Request-Method","Access-Control-Request-Headers","Authorization","Cache-Control","Connection","Content-Length","Content-Type","Cookie","Date","Expect","Forwarded","From","Host","If-Match","If-Modified-Since","If-None-Match","If-Range","If-Unmodified-Since","Max-Forwards","Origin","Pragma","Proxy-Authorization","Range","Referer","TE","User-Agent","Upgrade","Via","Warning"],Oe=({isPending:t,onCancel:s,response:r})=>{const{statusCode:n,message:h}=r??{},{formatMessage:i}=j();return e.jsx(S,{background:"neutral0",padding:5,shadow:"filterShadow",hasRadius:!0,children:e.jsxs(b.Root,{gap:4,style:{alignItems:"center"},children:[e.jsx(b.Item,{col:3,direction:"column",alignItems:"stretch",children:e.jsx(x,{children:i({id:"Settings.webhooks.trigger.test",defaultMessage:"Test-trigger"})})}),e.jsx(b.Item,{col:3,direction:"column",alignItems:"stretch",children:e.jsx(Ve,{isPending:t,statusCode:n})}),e.jsx(b.Item,{col:6,direction:"column",alignItems:"stretch",children:t?e.jsx(g,{justifyContent:"flex-end",children:e.jsx("button",{onClick:s,type:"button",children:e.jsxs(g,{gap:2,alignItems:"center",children:[e.jsx(x,{textColor:"neutral400",children:i({id:"Settings.webhooks.trigger.cancel",defaultMessage:"cancel"})}),e.jsx(N,{fill:"neutral400",height:"1.2rem",width:"1.2rem"})]})})}):e.jsx(Be,{statusCode:n,message:h})})]})})},Ve=({isPending:t,statusCode:s})=>{const{formatMessage:r}=j();return t||!s?e.jsxs(g,{gap:2,alignItems:"center",children:[e.jsx(ce,{height:"1.2rem",width:"1.2rem"}),e.jsx(x,{children:r({id:"Settings.webhooks.trigger.pending",defaultMessage:"pending"})})]}):s>=200&&s<300?e.jsxs(g,{gap:2,alignItems:"center",children:[e.jsx(z,{fill:"success700",height:"1.2rem",width:"1.2rem"}),e.jsx(x,{children:r({id:"Settings.webhooks.trigger.success",defaultMessage:"success"})})]}):s>=300?e.jsxs(g,{gap:2,alignItems:"center",children:[e.jsx(N,{fill:"danger700",height:"1.2rem",width:"1.2rem"}),e.jsxs(x,{children:[r({id:"Settings.error",defaultMessage:"error"})," ",s]})]}):null},Be=({statusCode:t,message:s})=>{const{formatMessage:r}=j();return t?t>=200&&t<300?e.jsx(g,{justifyContent:"flex-end",children:e.jsx(x,{textColor:"neutral600",ellipsis:!0,children:r({id:"Settings.webhooks.trigger.success.label",defaultMessage:"Trigger succeeded"})})}):t>=300?e.jsx(g,{justifyContent:"flex-end",children:e.jsx(g,{maxWidth:"25rem",justifyContent:"flex-end",title:s,children:e.jsx(x,{ellipsis:!0,textColor:"neutral600",children:s})})}):null:null},Le=({handleSubmit:t,triggerWebhook:s,isCreating:r,isTriggering:n,triggerResponse:h,data:i})=>{const{formatMessage:a}=j(),[u,f]=w.useState(!1),c=ue(Pe,async()=>(await he(async()=>{const{EventsTableEE:d}=await import("./EventsTable-D2GhW8Zl.js");return{EventsTableEE:d}},__vite__mapDeps([0,1,2,3,4]))).EventsTableEE),l=d=>Object.keys(d).length?Object.entries(d).map(([o,m])=>({key:o,value:m})):[{key:"",value:""}];return c?e.jsx(ge,{initialValues:{name:i?.name||"",url:i?.url||"",headers:l(i?.headers||{}),events:i?.events||[]},method:r?"POST":"PUT",onSubmit:t,validationSchema:Ne({formatMessage:a}),children:({isSubmitting:d,modified:o})=>e.jsxs(e.Fragment,{children:[e.jsx(_.Header,{primaryAction:e.jsxs(g,{gap:2,children:[e.jsx(q,{onClick:()=>{s(),f(!0)},variant:"tertiary",startIcon:e.jsx(pe,{}),disabled:r||n,children:a({id:"Settings.webhooks.trigger",defaultMessage:"Trigger"})}),e.jsx(q,{startIcon:e.jsx(z,{}),type:"submit",disabled:!o,loading:d,children:a({id:"global.save",defaultMessage:"Save"})})]}),title:r?a({id:"Settings.webhooks.create",defaultMessage:"Create a webhook"}):i?.name,navigationAction:e.jsx(me,{fallback:"../webhooks"})}),e.jsx(_.Content,{children:e.jsxs(g,{direction:"column",alignItems:"stretch",gap:4,children:[u&&e.jsx(Oe,{isPending:n,response:h,onCancel:()=>f(!1)}),e.jsx(S,{background:"neutral0",padding:8,shadow:"filterShadow",hasRadius:!0,children:e.jsxs(g,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsx(b.Root,{gap:6,children:[{label:a({id:"global.name",defaultMessage:"Name"}),name:"name",required:!0,size:6,type:"string"},{label:a({id:"Settings.roles.form.input.url",defaultMessage:"Url"}),name:"url",required:!0,size:12,type:"string"}].map(({size:m,...v})=>e.jsx(b.Item,{col:m,direction:"column",alignItems:"stretch",children:e.jsx(fe,{...v})},v.name))}),e.jsx(_e,{}),e.jsx(c,{})]})})]})})]})}):null},Ue=/(^$)|(^[A-Za-z][_0-9A-Za-z ]*$)/,We=/(^$)|((https?:\/\/.*)(d*)\/?(.*))/,Ne=({formatMessage:t})=>H().shape({name:E().nullable().required(t({id:"Settings.webhooks.validation.name.required",defaultMessage:"Name is required"})).matches(Ue,t({id:"Settings.webhooks.validation.name.regex",defaultMessage:"The name must start with a letter and only contain letters, numbers, spaces and underscores"})),url:E().nullable().required(t({id:"Settings.webhooks.validation.url.required",defaultMessage:"Url is required"})).matches(We,t({id:"Settings.webhooks.validation.url.regex",defaultMessage:"The value must be a valid Url"})),headers:be(s=>{const r=O();if(s.length===1){const{key:n,value:h}=s[0];if(!n&&!h)return r}return r.of(H().shape({key:E().required(t({id:"Settings.webhooks.validation.key",defaultMessage:"Key is required"})).nullable(),value:E().required(t({id:"Settings.webhooks.validation.value",defaultMessage:"Value is required"})).nullable()}))}),events:O()}),L=t=>({...t,headers:t.headers.reduce((s,{key:r,value:n})=>(r!==""&&(s[r]=n),s),{})}),D=()=>{const{formatMessage:t}=j(),r=xe("/settings/webhooks/:id")?.params.id,n=r==="create",h=je(),{toggleNotification:i}=we(),{_unstableFormatAPIError:a,_unstableFormatValidationErrors:u}=ke(),f=w.useCallback(a,[]),[c,l]=w.useState(!1),[d,o]=w.useState(),{isLoading:m,webhooks:v,error:M,createWebhook:K,updateWebhook:G,triggerWebhook:Q}=Ee({id:r},{skip:n});w.useEffect(()=>{M&&i({type:"danger",message:f(M)})},[M,i,f]);const X=async()=>{try{l(!0);const k=await Q(r);if("error"in k){i({type:"danger",message:a(k.error)});return}o(k.data)}catch{i({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred"})})}finally{l(!1)}},Z=async(k,F)=>{try{if(n){const p=await K(L(k));if("error"in p){V(p.error)&&p.error.name==="ValidationError"?F.setErrors(u(p.error)):i({type:"danger",message:a(p.error)});return}i({type:"success",message:t({id:"Settings.webhooks.created"})}),h(`../webhooks/${p.data.id}`,{replace:!0})}else{const p=await G({id:r,...L(k)});if("error"in p){V(p.error)&&p.error.name==="ValidationError"?F.setErrors(u(p.error)):i({type:"danger",message:a(p.error)});return}i({type:"success",message:t({id:"notification.form.success.fields"})})}}catch{i({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred"})})}};if(m)return e.jsx($.Loading,{});const[J]=v??[];return e.jsxs(ye,{children:[e.jsx($.Title,{children:t({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Webhooks"})}),e.jsx(Le,{data:J,handleSubmit:Z,triggerWebhook:X,isCreating:n,isTriggering:c,triggerResponse:d})]})},ze=()=>{const t=Se(ve);return e.jsx($.Protect,{permissions:t.settings?.webhooks.update,children:e.jsx(D,{})})},Qe=Object.freeze(Object.defineProperty({__proto__:null,EditPage:D,ProtectedEditPage:ze},Symbol.toStringTag,{value:"Module"}));export{D as E,A as a,Qe as b};
