const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["ListView-ea6JigI-.js","strapi-z7ApxZZq.js","strapi-D6821bIJ.css","sortable.esm-4pfM8QNQ.js","groupBy--H6Arigv.js","_baseEach-ZnnftuGj.js","sortBy-DX26BOXv.js","_baseMap-BaaWdrf-.js","index-DSQJUh5z.js","index-BRVyLNfZ.js","_arrayIncludesWith-BNzMLSv9.js"])))=>i.map(i=>d[i]);
import{gy as kn,r as H,j as o,T as ge,a6 as Oa,b as Va,a as be,dg as Gb,b3 as Zb,aV as Gn,cm as Bn,b7 as Ha,e as re,D as Ue,cf as zt,ep as Kb,bq as zl,cF as Do,h as fl,aJ as Fn,I as Ua,F as qo,$ as ce,bt as Zn,bu as Br,k as jt,bW as Bl,gz as Jb,fZ as pl,eb as Ee,gA as Yb,o as Ga,n as Vl,gB as Xb,w as Po,gC as Qb,bv as Hl,a7 as Ur,ck as Gl,a4 as ey,c6 as Wa,gD as Le,gE as ml,bx as ty,gF as ny,M as ae,f_ as Zl,ab as ry,ad as Da,ae as hl,gG as ay,gH as iy,af as oy,ag as sy,ai as Dr,ah as xo,ac as gl,aj as bl,ak as uy,a9 as ly,gI as yl,aa as cy,al as dy,a8 as fy,gJ as py,gK as Uo,H as An,s as my,at as hy,aC as gy,a3 as by,av as Hn,bH as Kl,cc as Rn,cP as Nt,aH as pn,aI as _t,gL as Jl,bp as Wo,gM as Yl,dR as Xl,cq as yy,cr as vy,gN as vl,gO as xy,gP as Cy,gQ as _y,gR as jy,gS as wy,gT as Ty,gU as My,gV as ky,gW as Fy,gX as Ay,gY as Ry,gZ as Sy,g_ as $y,g$ as Ny,h0 as Iy,gi as Ly,h1 as Ey,h2 as Oy,gt as Dy,h3 as qy,h4 as Py,h5 as Uy,h6 as Wy,h7 as zy,eQ as By,h8 as Vy,h9 as Hy,ha as Gy,hb as Zy,ek as Ky,hc as Jy,e6 as Yy,hd as Xy,he as Qy,hf as ev,hg as tv,hh as nv,g as rv,c9 as av,hi as Co,hj as Ql,hk as ec,hl as tc,hm as iv,hn as ov,ho as sv,hp as uv,hq as lv,hr as cv,hs as nc,ht as rc,hu as ac,hv as dv,cD as fv,hw as pv,hx as mv,hy as hv,eF as gv,hz as bv,hA as yv,hB as vv,fJ as xv,gs as Cv,hC as _v,hD as jv,hE as wv,hF as Tv,fu as Mv,hG as kv,hH as Fv,hI as Av,ff as Rv,fh as Sv,hJ as $v,hK as Nv,hL as Iv,eO as ic,hM as Lv,hN as Ev,bJ as Ov,hO as Dv,hP as qv,hQ as Pv,hR as Uv,hS as Wv,hT as zv,hU as Bv,hV as Vv,hW as Hv,hX as Gv,hY as Zv,hZ as Kv,h_ as Jv,h$ as Yv,cg as Xv,i0 as Qv,i1 as ex,i2 as tx,E as nx,i3 as rx,i4 as ax,i5 as ix,i6 as ox,i7 as sx,i8 as ux,i9 as lx,ia as cx,ib as dx,ic as fx,id as px,ie as mx,ig as hx,ej as gx,ih as bx,ii as yx,ij as vx,ik as xx,c as Cx,b8 as Za,bm as oc,il as sc,gg as za,eS as _x,bh as uc,eq as jx,eR as wx,im as Tx,c8 as Mx,eN as kx,io as Be,ip as Fx,iq as Ax,d8 as Rx,ir as Sx,is as $x,it as lc,bG as cc,fQ as Nx,gc as Ix,iu as Lx,iv as Ex,iw as Ox,ix as Dx,fH as zo,N as qx,O as Px,iy as dc,dP as fc,iz as Ux,iA as pc,iB as Wx,g8 as zx,g9 as Bx,ga as Vx,iC as Hx,iD as qr,iE as Gx,by as Wt,b0 as Wr,aQ as ke,aR as Oe,iF as _o,aP as Ne,e7 as Zx,a_ as xl,iG as Pr,iH as mc,iI as Kx,iJ as Jx,V as Yx,c4 as Xx,bl as Qx,c3 as e0,fY as t0,bw as n0,P as jo,iK as r0,iL as a0,B as i0,R as o0,Q as qa,_ as s0}from"./strapi-z7ApxZZq.js";import{g as u0}from"./groupBy--H6Arigv.js";import{s as l0}from"./sortBy-DX26BOXv.js";import{f as c0}from"./index-DSQJUh5z.js";import{_ as d0}from"./_arrayIncludesWith-BNzMLSv9.js";const C=a=>`${kn}.${a}`,hc=H.createContext(),Yt=()=>H.useContext(hc),Cl=({status:a})=>{switch(a){case"UNCHANGED":return null;case"CHANGED":return o.jsx(ge,{fontWeight:"semiBold",textColor:"alternative500",children:"M"});case"REMOVED":return o.jsx(ge,{fontWeight:"semiBold",textColor:"danger500",children:"D"});case"NEW":return o.jsx(ge,{fontWeight:"semiBold",textColor:"success500",children:"N"})}},p_=({status:a})=>{switch(a){case"CHANGED":return o.jsx(Oa,{fontWeight:"bold",textColor:"alternative600",backgroundColor:"alternative100",borderColor:"alternative200",children:"Modified"});case"REMOVED":return o.jsx(Oa,{fontWeight:"bold",textColor:"danger600",backgroundColor:"danger100",borderColor:"danger200",children:"Deleted"});case"NEW":return o.jsx(Oa,{fontWeight:"bold",textColor:"success600",backgroundColor:"success100",borderColor:"success200",children:"New"});case"UNCHANGED":default:return o.jsx(Oa,{style:{visibility:"hidden"},fontWeight:"bold",textColor:"warning600",backgroundColor:"warning100",borderColor:"warning200",children:"Unchanged"})}},gc=H.createContext(),pr=()=>H.useContext(gc),f0=()=>{const{componentsGroupedByCategory:a,isInDevelopmentMode:u,sortedContentTypesList:l}=Yt(),{trackUsage:f}=Va(),[d,b]=H.useState(""),{onOpenModalCreateSchema:p}=pr(),{locale:g}=be(),{startsWith:v}=Gb(g,{sensitivity:"base"}),w=Zb(g,{sensitivity:"base"}),j=()=>{f("willCreateContentType"),p({modalType:"contentType",kind:"collectionType",actionType:"create",forTarget:"contentType"})},A=()=>{f("willCreateSingleType"),p({modalType:"contentType",kind:"singleType",actionType:"create",forTarget:"contentType"})},O=()=>{f("willCreateComponent"),p({modalType:"component",kind:null,actionType:"create",forTarget:"component"})},K=Object.entries(a).map(([$,E])=>({name:$,title:Gn($),links:E.map(N=>({name:N.uid,to:`/plugins/${kn}/component-categories/${$}/${N.uid}`,title:N.info.displayName,status:N.status})).sort((N,S)=>w.compare(N.title,S.title))})).sort(($,E)=>w.compare($.title,E.title)),T=l.filter($=>$.visible).map($=>({kind:$.kind,name:$.name,to:$.to,title:$.title,status:$.status}));return{menu:[{name:"models",title:{id:`${C("menu.section.models.name")}`,defaultMessage:"Collection Types"},customLink:u?{id:`${C("button.model.create")}`,defaultMessage:"Create new collection type",onClick:j}:void 0,links:T.filter($=>$.kind==="collectionType")},{name:"singleTypes",title:{id:`${C("menu.section.single-types.name")}`,defaultMessage:"Single Types"},customLink:u?{id:`${C("button.single-types.create")}`,defaultMessage:"Create new single type",onClick:A}:void 0,links:T.filter($=>$.kind==="singleType")},{name:"components",title:{id:`${C("menu.section.components.name")}`,defaultMessage:"Components"},customLink:u?{id:`${C("button.component.create")}`,defaultMessage:"Create a new component",onClick:O}:void 0,links:K}].map($=>{if($.links.some(S=>"links"in S&&Array.isArray(S.links))){let S=0;return{...$,links:$.links.reduce((te,L)=>{const ne="links"in L?L.links.filter(he=>v(he.title,d)):[];return ne.length===0||(S+=ne.length,te.push({...L,links:ne.sort((he,Fe)=>w.compare(he.title,Fe.title))})),te},[]),linksCount:S}}const N=$.links.filter(S=>v(S.title,d)).sort((S,te)=>w.compare(S.title,te.title));return{...$,links:N,linksCount:N.length}}),search:{value:d,onChange:b,clear:()=>b("")}}},p0=jt(Do)`
  transform: scaleX(-1);
`,m0=jt(zt.Item)`
  color: ${({theme:a})=>a.colors.danger600};

  &:hover {
    background: ${({theme:a,disabled:u})=>!u&&a.colors.danger100};
  }
`,h0=()=>{const{menu:a,search:u}=f0(),{saveSchema:l,isModified:f,history:d,isInDevelopmentMode:b}=Yt(),{formatMessage:p}=be(),[g,v]=H.useState(!1),[w,j]=H.useState(!1);H.useEffect(()=>{if(!b)return;const $=E=>{(E.ctrlKey||E.metaKey)&&(E.key==="Enter"?f&&(E.preventDefault(),l()):E.key==="z"&&!E.shiftKey?(E.preventDefault(),d.undo()):(E.key==="y"||E.shiftKey&&E.key==="z"||E.key==="Z")&&(E.preventDefault(),d.redo()))};return document.addEventListener("keydown",$),()=>{document.removeEventListener("keydown",$)}});const A=()=>{j(!0)},O=()=>{v(!1),j(!1),d.discardAllChanges()},K=()=>{d.undo()},T=()=>{d.redo()},U=p({id:C("plugin.name"),defaultMessage:"Content-Type Builder"});return o.jsxs(Bn.Main,{"aria-label":U,children:[o.jsx(Bn.Header,{label:U}),o.jsx(Ha,{background:"neutral150"}),o.jsxs(re,{padding:5,gap:3,direction:"column",alignItems:"stretch",children:[o.jsxs(re,{gap:2,children:[o.jsx(Ue,{flex:1,onClick:$=>{$.preventDefault(),l()},type:"submit",disabled:!f||!b,fullWidth:!0,size:"S",children:p({id:"global.save",defaultMessage:"Save"})}),o.jsxs(zt.Root,{open:g,onOpenChange:v,children:[o.jsxs(zt.Trigger,{size:"S",endIcon:null,paddingTop:"4px",paddingLeft:"7px",paddingRight:"7px",variant:"tertiary",children:[o.jsx(Kb,{fill:"neutral500","aria-hidden":!0,focusable:!1}),o.jsx(zl,{tag:"span",children:p({id:"global.more.actions",defaultMessage:"More actions"})})]}),o.jsxs(zt.Content,{zIndex:1,children:[o.jsx(zt.Item,{disabled:!d.canUndo||!b,onSelect:K,startIcon:o.jsx(p0,{}),children:p({id:"global.last-change.undo",defaultMessage:"Undo last change"})}),o.jsx(zt.Item,{disabled:!d.canRedo||!b,onSelect:T,startIcon:o.jsx(Do,{}),children:p({id:"global.last-change.redo",defaultMessage:"Redo last change"})}),o.jsx(zt.Separator,{}),o.jsx(m0,{disabled:!d.canDiscardAll||!b,onSelect:A,children:o.jsxs(re,{gap:2,children:[o.jsx(fl,{}),o.jsx(ge,{children:p({id:"global.last-changes.discard",defaultMessage:"Discard last changes"})})]})})]})]})]}),o.jsx(Fn,{startAction:o.jsx(qo,{fill:"neutral500"}),value:u.value,onChange:$=>u.onChange($.target.value),"aria-label":"Search",placeholder:p({id:C("search.placeholder"),defaultMessage:"Search"}),endAction:o.jsx(Ua,{onClick:$=>{$.stopPropagation(),$.preventDefault(),u.onChange("")},label:"clear",variant:"ghost",type:"button",style:{padding:0},children:o.jsx(fl,{})}),size:"S"})]}),o.jsx(Bn.Sections,{children:a.map($=>o.jsx(H.Fragment,{children:o.jsx(Bn.Section,{label:p({id:$.title.id,defaultMessage:$.title.defaultMessage}),link:$.customLink&&{label:p({id:$.customLink?.id,defaultMessage:$.customLink?.defaultMessage}),onClik:$.customLink?.onClick},children:$.links.map(E=>{const N=p({id:E.name,defaultMessage:E.title});return"links"in E?o.jsx(Bn.SubSection,{label:E.title,children:E.links.map(S=>{const te=p({id:S.name,defaultMessage:S.title});return o.jsx(Bn.Link,{to:S.to,label:te,endAction:o.jsx(ce,{tag:"span",textAlign:"center",width:"24px",children:o.jsx(Cl,{status:S.status})})},S.name)})},E.name):o.jsx(Bn.Link,{to:E.to,label:N,endAction:o.jsx(ce,{tag:"span",textAlign:"center",width:"24px",children:o.jsx(Cl,{status:E.status})})},E.name)})})},$.name))}),o.jsx(Zn.Root,{open:w,onOpenChange:j,children:o.jsx(Br,{onConfirm:O,children:p({id:C("popUpWarning.discardAll.message"),defaultMessage:"Are you sure you want to discard all changes?"})})})]})},g0="timeout",b0=30*1e3,y0=()=>{const{get:a}=Bl(),u=async l=>{const f=l??Date.now();if(Date.now()-f>b0)throw new Error(g0);try{if((await a("/content-type-builder/update-schema-status"))?.data?.data?.isUpdating===!0)return new Promise(b=>{setTimeout(()=>u(f).then(b),200)})}catch{return new Promise(b=>{setTimeout(()=>u(f).then(b),200)})}};return u},v0=a=>l0(Object.keys(a).map(u=>({visible:a[u].visible,name:u,title:a[u].info.displayName,plugin:a[u].plugin,uid:u,to:`/plugins/${kn}/content-types/${u}`,kind:a[u].kind,restrictRelationsTo:a[u].restrictRelationsTo,status:a[u].status})).filter(u=>u!==null),u=>Jb(u.title)),x0=({components:a,contentTypes:u})=>{const l={newContentTypes:0,editedContentTypes:0,deletedContentTypes:0,newComponents:0,editedComponents:0,deletedComponents:0,newFields:0,editedFields:0,deletedFields:0},f=Object.values(a).filter(b=>["NEW","CHANGED","REMOVED"].includes(b.status)).map(b=>{const p=_l(b),g=p.action;return jl({...b,action:g},l,"component"),p}),d=Object.values(u).filter(b=>["NEW","CHANGED","REMOVED"].includes(b.status)).map(b=>{const p=_l(b),g=p.action;return jl({...b,action:g},l,"contentType"),p});return{requestData:{components:f,contentTypes:d},trackingEventProperties:l}},C0=a=>Object.fromEntries(Object.entries(a).filter(([u,l])=>Array.isArray(l)?l.length>0:typeof l=="object"&&l!==null?Object.keys(l).length>0:l!=null)),_0=a=>"customField"in a?{...a,type:"customField"}:"targetAttribute"in a?{...a,targetAttribute:a.targetAttribute==="-"?null:a.targetAttribute,...a.conditions&&{conditions:a.conditions}}:a,_l=a=>{let u;switch(a.status){case"NEW":u="create";break;case"CHANGED":u="update";break;case"REMOVED":return{action:"delete",uid:a.uid};default:throw new Error("Invalid status")}return{action:u,uid:a.uid,category:"category"in a?a.category:void 0,...pl(a,["info","options","visible","uid","restrictRelationsTo"]),...a.options,...a.info,attributes:a.attributes.map(l=>{let f;switch(l.status){case"NEW":f="create";break;case"REMOVED":return{action:"delete",name:l.name};case"UNCHANGED":case"CHANGED":default:f="update"}return{action:f,name:l.name,properties:C0(pl(_0(l),["status","name"]))}})}},jl=(a,u,l)=>{if(!a||typeof a.action!="string")return;const f=l==="contentType";switch(a.action){case"create":f?u.newContentTypes++:u.newComponents++;break;case"update":f?u.editedContentTypes++:u.editedComponents++;break;case"delete":f?u.deletedContentTypes++:u.deletedComponents++;break}Array.isArray(a.attributes)&&(a.action==="delete"?u.deletedFields+=a.attributes.length:a.attributes.forEach(d=>{if(!(!d||typeof d.status!="string"))switch(d.status){case"NEW":u.newFields++;break;case"CHANGED":u.editedFields++;break;case"REMOVED":u.deletedFields++;break}}))},j0=a=>Object.keys(a).reduce((l,f)=>{const d=Ee(a,[f]),b=w0(d);return b.childComponents.length>0&&l.push(b),l},[]),w0=a=>({component:a.uid,childComponents:a.attributes.filter(u=>{const{type:l}=u;return l==="component"}).map(u=>({component:u.component}))}),T0=a=>{const u=Object.keys(a).reduce((l,f)=>{const d=a?.[f]?.attributes??[],b=M0(d,f);return[...l,...b]},[]);return k0(u)},M0=(a,u)=>a.reduce((l,f)=>{const{type:d}=f;return d==="component"&&l.push({component:f.component,parentCompoUid:u}),l},[]),k0=a=>{const u=new Map;return a.forEach(({component:f,parentCompoUid:d})=>{u.has(f)||u.set(f,new Set),u.get(f).add(d)}),Array.from(u.entries()).map(([f,d])=>({component:f,uidsOfAllParents:Array.from(d)}))},F0=(a,u)=>{const l=Object.keys(a).map(f=>Ee(a,[f,...u],""));return Yb(l)},A0=a=>a["content-type-builder_dataManagerProvider"]||Xb,R0=({children:a})=>{const u=Ga(),l=Vl(A0),{components:f,contentTypes:d,reservedNames:b,initialComponents:p,initialContentTypes:g,isLoading:v}=l.current,{toggleNotification:w}=Po(),{lockAppWithAutoreload:j,unlockAppWithAutoreload:A}=Qb(),{setCurrentStep:O,setStepState:K}=Hl("DataManagerProvider",B=>B),T=y0(),$=Ur("DataManagerProvider",B=>B.getPlugin)("content-type-builder"),E=Gl("DataManagerProvider",B=>B.autoReload),{formatMessage:N}=be(),{trackUsage:S}=Va(),te=ey("DataManagerProvider",B=>B.refetchPermissions),{onCloseModal:L}=pr(),[ne,he]=H.useState(!1),Fe=H.useMemo(()=>!(Wa(f,p)&&Wa(d,g)),[f,d,p,g]),Ae=Bl(),Ve=E,et=H.useRef();et.current=async()=>{try{const[B,je]=await Promise.all([Ae.get("/content-type-builder/schema"),Ae.get("/content-type-builder/reserved-names")]),{components:Lt,contentTypes:Vt}=B.data.data;u(Le.init({components:ml(Lt,Et=>({...Et,status:"UNCHANGED"})),contentTypes:ml(Vt,Et=>({...Et,status:"UNCHANGED"})),reservedNames:je.data})),u(Le.clearHistory())}catch(B){console.error({err:B}),w({type:"danger",message:N({id:"notification.error",defaultMessage:"An error occurred"})})}},H.useEffect(()=>(et.current(),()=>{u(Le.reloadPlugin())}),[]),H.useEffect(()=>{E||w({type:"info",message:N({id:C("notification.info.autoreaload-disable")})})},[E,w,N]);const oe=B=>j0(B),V=B=>T0(B),It=async()=>{await te()},De=async()=>{he(!0);const B=$?.apis?.forms,je=Object.entries(l.current.contentTypes).reduce((Qt,[bt,hn])=>(Qt[bt]=B.mutateContentTypeSchema(hn,g[bt]),Qt),{}),{requestData:Lt,trackingEventProperties:Vt}=x0({components:l.current.components,contentTypes:je}),Et=Object.keys(l.current.contentTypes).length>0;j();try{await Ae.post("/content-type-builder/update-schema",{data:Lt}),Et&&(K("contentTypeBuilder.success",!0),S("didCreateGuidedTourCollectionType"),O(null)),await T(),u(ty.util.invalidateTags(["GuidedTourMeta"])),await et.current(),await It()}catch(Qt){console.error({err:Qt}),w({type:"danger",message:N({id:"notification.error",defaultMessage:"An error occurred"})}),S("didUpdateCTBSchema",{...Vt,success:!1})}finally{he(!1),A(),S("didUpdateCTBSchema",{...Vt,success:!0})}},Bt=H.useMemo(()=>oe(f),[f]),Ke=H.useMemo(()=>V(f),[f]),mn=H.useMemo(()=>F0(f,["category"]),[f]),G=H.useMemo(()=>u0(f,"category"),[f]),fe=H.useMemo(()=>v0(d),[d]),Xt={componentsThatHaveOtherComponentInTheirAttributes:Bt,nestedComponents:Ke,saveSchema:De,reservedNames:b,components:f,contentTypes:d,initialComponents:p,initialContentTypes:g,isSaving:ne,isModified:Fe,isInDevelopmentMode:Ve,allComponentsCategories:mn,componentsGroupedByCategory:G,sortedContentTypesList:fe,isLoading:v,addAttribute(B){u(Le.addAttribute(B))},editAttribute(B){u(Le.editAttribute(B))},addCustomFieldAttribute(B){u(Le.addCustomFieldAttribute(B))},editCustomFieldAttribute(B){u(Le.editCustomFieldAttribute(B))},addCreatedComponentToDynamicZone(B){u(Le.addCreatedComponentToDynamicZone(B))},createSchema(B){u(Le.createSchema(B))},createComponentSchema({data:B,uid:je,componentCategory:Lt}){u(Le.createComponentSchema({data:B,uid:je,componentCategory:Lt}))},changeDynamicZoneComponents({forTarget:B,targetUid:je,dynamicZoneTarget:Lt,newComponents:Vt}){u(Le.changeDynamicZoneComponents({forTarget:B,targetUid:je,dynamicZoneTarget:Lt,newComponents:Vt}))},removeAttribute(B){B.forTarget==="contentType"&&S("willDeleteFieldOfContentType"),u(Le.removeField(B))},removeComponentFromDynamicZone(B){u(Le.removeComponentFromDynamicZone(B))},deleteComponent(B){window.confirm(N({id:C("popUpWarning.bodyMessage.component.delete")}))&&(L(),u(Le.deleteComponent(B)))},deleteContentType(B){window.confirm(N({id:C("popUpWarning.bodyMessage.contentType.delete")}))&&(L(),u(Le.deleteContentType(B)))},updateComponentSchema({data:B,componentUID:je}){u(Le.updateComponentSchema({data:B,uid:je}))},updateComponentUid({componentUID:B,newComponentUID:je}){u(Le.updateComponentUid({uid:B,newComponentUID:je}))},updateSchema(B){u(Le.updateSchema(B))},moveAttribute(B){u(Le.moveAttribute(B))},applyChange(B){u(Le.applyChange(B))},history:{undo(){u(Le.undo())},redo(){u(Le.redo())},discardAllChanges(){u(Le.discardAll())},canUndo:l.past.length>0,canRedo:l.future.length>0,canDiscardAll:Fe}};return o.jsx(hc.Provider,{value:Xt,children:a})},S0=()=>{const{formatMessage:a}=be(),{isModified:u,isSaving:l}=Yt(),f=a({id:C("prompt.unsaved"),defaultMessage:"Are you sure you want to leave? All your modifications will be lost."}),d=ny(b=>b.currentLocation.pathname.startsWith("/plugins/content-type-builder/")&&!b.nextLocation.pathname.startsWith("/plugins/content-type-builder/")&&u);return H.useEffect(()=>{const b=p=>{u&&!l&&(p.preventDefault(),p.returnValue="")};return window.addEventListener("beforeunload",b),()=>window.removeEventListener("beforeunload",b)},[f,u,l]),d.state==="blocked"?o.jsx(Zn.Root,{open:!0,onOpenChange:()=>d.reset(),children:o.jsx(Br,{onConfirm:()=>d.proceed(),children:f})}):null},bc=a=>a.kind==="collectionType"&&(a.restrictRelationsTo===null||Array.isArray(a.restrictRelationsTo)&&a.restrictRelationsTo.length>0),So=(a,u)=>a.find(({name:l})=>l===u),$0=(a,u)=>!a||!u?{}:{[a]:u[a]},N0=a=>(a?.inner||[]).reduce((u,l)=>(l.path&&(u[l.path.split("[").join(".").split("]").join("")]={id:l.message,defaultMessage:l.message,values:$0(l?.type,l?.params)}),u),{}),I0=[{label:"All",children:[{label:"images (JPEG, PNG, GIF, SVG, TIFF, ICO, DVU)",value:"images"},{label:"videos (MPEG, MP4, Quicktime, WMV, AVI, FLV)",value:"videos"},{label:"audios (MP3, WAV, OGG)",value:"audios"},{label:"files (CSV, ZIP, PDF, Excel, JSON, ...)",value:"files"}]}],L0=({intlLabel:a,name:u,onChange:l,value:f=null})=>{const{formatMessage:d}=be(),b=f===null||f?.length===0?d({id:"global.none",defaultMessage:"None"}):[...f].sort().map(g=>Gn(g)).join(", "),p=a.id?d({id:a.id,defaultMessage:a.defaultMessage}):u;return o.jsxs(ae.Root,{name:u,children:[o.jsx(ae.Label,{children:p}),o.jsx(Zl,{customizeContent:()=>b,onChange:g=>{g.length>0?l({target:{name:u,value:g,type:"allowed-types-select"}}):l({target:{name:u,value:null,type:"allowed-types-select"}})},options:I0,value:f||[]})]})},wl={biginteger:Dr,blocks:fy,boolean:dy,collectionType:yl,component:cy,contentType:yl,date:Da,datetime:Da,decimal:Dr,dynamiczone:ly,email:uy,enum:bl,enumeration:bl,file:xo,files:xo,float:Dr,integer:Dr,json:gl,JSON:gl,media:xo,number:Dr,password:sy,relation:oy,richtext:iy,singleType:ay,string:hl,text:hl,time:Da,timestamp:Da,uid:ry},E0=jt(ce)`
  svg {
    height: 100%;
    width: 100%;
  }
`,zr=({type:a,customField:u=null,...l})=>{const f=Ur("AttributeIcon",b=>b.customFields.get);let d=wl[a];if(u){const p=f(u)?.icon;p&&(d=p)}return wl[a]?o.jsx(E0,{width:"3.2rem",height:"3.2rem",shrink:0,...l,"aria-hidden":!0,children:o.jsx(ce,{tag:d})}):null},yc=jt(ce)`
  width: 100%;
  height: 100%;
  border: 1px solid ${({theme:a})=>a.colors.neutral200};
  text-align: left;
  &:hover {
    cursor: pointer;
    background: ${({theme:a})=>a.colors.primary100};
    border: 1px solid ${({theme:a})=>a.colors.primary200};
  }
`,O0=[],D0=()=>o.jsx(re,{grow:1,justifyContent:"flex-end",children:o.jsxs(re,{gap:1,hasRadius:!0,background:"alternative100",padding:"0.2rem 0.4rem",children:[o.jsx(py,{width:"1rem",height:"1rem",fill:"alternative600"}),o.jsx(ge,{textColor:"alternative600",variant:"sigma",children:"New"})]})}),q0=({type:a="text"})=>{const{formatMessage:u}=be(),{onClickSelectField:l}=pr(),f=()=>{l({attributeType:a,step:a==="component"?"1":null})};return o.jsx(yc,{padding:4,tag:"button",hasRadius:!0,type:"button",onClick:f,children:o.jsxs(re,{children:[o.jsx(zr,{type:a}),o.jsxs(ce,{paddingLeft:4,width:"100%",children:[o.jsxs(re,{justifyContent:"space-between",children:[o.jsx(ge,{fontWeight:"bold",textColor:"neutral800",children:u({id:C(`attribute.${a}`),defaultMessage:a})}),O0.includes(a)&&o.jsx(D0,{})]}),o.jsx(re,{children:o.jsx(ge,{variant:"pi",textColor:"neutral600",children:u({id:C(`attribute.${a}.description`),defaultMessage:"A type for modeling data"})})})]})]})})},P0=({attributes:a})=>o.jsx(Uo,{tagName:"button",children:o.jsx(re,{direction:"column",alignItems:"stretch",gap:8,children:a.map((u,l)=>o.jsx(An.Root,{gap:3,children:u.map(f=>o.jsx(An.Item,{col:6,direction:"column",alignItems:"stretch",children:o.jsx(q0,{type:f})},f))},l))})}),U0=({customFieldUid:a,customField:u})=>{const{type:l,intlLabel:f,intlDescription:d}=u,{formatMessage:b}=be(),{onClickSelectCustomField:p}=pr(),g=()=>{p({attributeType:l,customFieldUid:a})};return o.jsx(yc,{padding:4,tag:"button",hasRadius:!0,type:"button",onClick:g,children:o.jsxs(re,{children:[o.jsx(zr,{type:l,customField:a}),o.jsxs(ce,{paddingLeft:4,children:[o.jsx(re,{children:o.jsx(ge,{fontWeight:"bold",textColor:"neutral800",children:b(f)})}),o.jsx(re,{children:o.jsx(ge,{variant:"pi",textColor:"neutral600",children:b(d)})})]})]})})},W0=jt(ce)`
  background: ${({theme:a})=>`linear-gradient(180deg, rgba(234, 234, 239, 0) 0%, ${a.colors.neutral150} 100%)`};
  opacity: 0.33;
`,z0=()=>o.jsx(re,{wrap:"wrap",gap:4,children:[...Array(4)].map((a,u)=>o.jsx(W0,{height:"138px",width:"375px",hasRadius:!0},`empty-card-${u}`))}),B0=()=>{const{formatMessage:a}=be();return o.jsxs(ce,{position:"relative",children:[o.jsx(z0,{}),o.jsx(ce,{position:"absolute",top:6,width:"100%",children:o.jsxs(re,{alignItems:"center",justifyContent:"center",direction:"column",children:[o.jsx(my,{width:"160px",height:"88px"}),o.jsx(ce,{paddingTop:6,paddingBottom:4,children:o.jsxs(ce,{textAlign:"center",children:[o.jsx(ge,{variant:"delta",tag:"p",textColor:"neutral600",children:a({id:C("modalForm.empty.heading"),defaultMessage:"Nothing in here yet."})}),o.jsx(ce,{paddingTop:4,children:o.jsx(ge,{variant:"delta",tag:"p",textColor:"neutral600",children:a({id:C("modalForm.empty.sub-heading"),defaultMessage:"Find what you are looking for through a wide range of extensions."})})})]})}),o.jsx(hy,{tag:by,to:`/marketplace?${gy.stringify({categories:["Custom fields"]})}`,variant:"secondary",startIcon:o.jsx(Hn,{}),children:a({id:C("modalForm.empty.button"),defaultMessage:"Add custom fields"})})]})})]})},V0=()=>{const{formatMessage:a}=be(),u=Ur("CustomFieldsList",d=>d.customFields.getAll),l=Object.entries(u());if(!l.length)return o.jsx(B0,{});const f=l.sort((d,b)=>d[1].name>b[1].name?1:-1);return o.jsx(Uo,{tagName:"button",children:o.jsxs(re,{direction:"column",alignItems:"stretch",gap:3,children:[o.jsx(An.Root,{gap:3,children:f.map(([d,b])=>o.jsx(An.Item,{col:6,direction:"column",alignItems:"stretch",children:o.jsx(U0,{customFieldUid:d,customField:b},d)},d))}),o.jsx(Kl,{href:"https://docs.strapi.io/developer-docs/latest/development/custom-fields.html",isExternal:!0,children:a({id:C("modalForm.tabs.custom.howToLink"),defaultMessage:"How to add custom fields"})})]})})},H0=({attributes:a,forTarget:u,kind:l})=>{const{formatMessage:f}=be(),d=C("modalForm.tabs.default"),b=C("modalForm.tabs.custom"),p=u.includes("component")?"component":l,g=C(`modalForm.sub-header.chooseAttribute.${p}`);return o.jsx(Rn.Body,{children:o.jsxs(Nt.Root,{variant:"simple",defaultValue:"default",children:[o.jsxs(re,{justifyContent:"space-between",children:[o.jsx(ge,{variant:"beta",tag:"h2",children:f({id:g,defaultMessage:"Select a field"})}),o.jsxs(Nt.List,{children:[o.jsx(Nt.Trigger,{value:"default",children:f({id:d,defaultMessage:"Default"})}),o.jsx(Nt.Trigger,{value:"custom",children:f({id:b,defaultMessage:"Custom"})})]})]}),o.jsx(Ha,{marginBottom:6}),o.jsx(Nt.Content,{value:"default",children:o.jsx(P0,{attributes:a})}),o.jsx(Nt.Content,{value:"custom",children:o.jsx(V0,{})})]})})},G0=({intlLabel:a,name:u,options:l,onChange:f,value:d=null})=>{const{formatMessage:b}=be(),p=a.id?b({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):u,g=v=>{let w="";v==="true"&&(w=!0),v==="false"&&(w=!1),f({target:{name:u,value:w,type:"select-default-boolean"}})};return o.jsxs(ae.Root,{name:u,children:[o.jsx(ae.Label,{children:p}),o.jsx(pn,{onChange:g,value:(d===null?"":d).toString(),children:l.map(({metadatas:{intlLabel:v,disabled:w,hidden:j},key:A,value:O})=>o.jsx(_t,{value:O,disabled:w,hidden:j,children:v.defaultMessage},A))})]})},Z0=jt(re)`
  position: relative;
  align-items: stretch;

  label {
    max-width: 50%;
    cursor: pointer;
    user-select: none;
    flex: 1;

    ${Jl()}
  }

  input {
    position: absolute;
    opacity: 0;
  }

  .option {
    height: 100%;
    will-change: transform, opacity;
    background: ${({theme:a})=>a.colors.neutral0};
    border: 1px solid ${({theme:a})=>a.colors.neutral200};
    border-radius: ${({theme:a})=>a.borderRadius};

    .checkmark {
      position: relative;
      display: block;
      will-change: transform;
      background: ${({theme:a})=>a.colors.neutral0};
      width: ${({theme:a})=>a.spaces[5]};
      height: ${({theme:a})=>a.spaces[5]};
      border: solid 1px ${({theme:a})=>a.colors.neutral300};
      border-radius: 50%;

      &:before,
      &:after {
        content: '';
        display: block;
        border-radius: 50%;
        width: ${({theme:a})=>a.spaces[3]};
        height: ${({theme:a})=>a.spaces[3]};
        position: absolute;
        top: 3px;
        left: 3px;
      }

      &:after {
        transform: scale(0);
        transition: inherit;
        will-change: transform;
      }
    }
  }

  .container input:checked ~ div {
    background: ${({theme:a})=>a.colors.primary100};
    color: ${({theme:a})=>a.colors.primary600};
    .checkmark {
      border: solid 1px ${({theme:a})=>a.colors.primary600};
      &::after {
        background: ${({theme:a})=>a.colors.primary600};
        transform: scale(1);
      }
    }
  }
`,Bo=({intlLabel:a,name:u,onChange:l,radios:f=[],value:d})=>{const{formatMessage:b}=be();return o.jsxs(re,{direction:"column",alignItems:"stretch",gap:2,children:[o.jsx(ge,{variant:"pi",fontWeight:"bold",textColor:"neutral800",htmlFor:u,tag:"label",children:b(a)}),o.jsx(Z0,{gap:4,alignItems:"stretch",children:f.map(p=>o.jsxs("label",{htmlFor:p.value.toString(),className:"container",children:[o.jsx("input",{id:p.value.toString(),name:u,className:"option-input",checked:p.value===d,value:p.value,onChange:l,type:"radio"},p.value),o.jsx(ce,{className:"option",padding:4,children:o.jsxs(re,{children:[o.jsx(ce,{paddingRight:4,children:o.jsx("span",{className:"checkmark"})}),o.jsxs(re,{direction:"column",alignItems:"stretch",gap:2,children:[o.jsx(ge,{fontWeight:"bold",children:b(p.title)}),o.jsx(ge,{variant:"pi",textColor:"neutral600",children:b(p.description)})]})]})})]},p.value))})]})},K0=({onChange:a,name:u,intlLabel:l,...f})=>{const d=b=>{const p=b.target.value!=="false";a({target:{name:u,value:p,type:"boolean-radio-group"}})};return o.jsx(Bo,{...f,name:u,onChange:d,intlLabel:l})},J0=({error:a,intlLabel:u,modifiedData:l,name:f,onChange:d,value:b=null})=>{const{formatMessage:p}=be(),g=u.id?p({id:u.id,defaultMessage:u.defaultMessage},{...u.values}):f,v=l.type==="biginteger"?"text":"number",w=!l.type,j=a?p({id:a,defaultMessage:a}):"";return o.jsxs(re,{direction:"column",alignItems:"stretch",gap:2,children:[o.jsx(Wo,{id:f,name:f,onCheckedChange:A=>{d({target:{name:f,value:A?v==="text"?"0":0:null}})},checked:b!==null,children:g}),b!==null&&o.jsx(ce,{paddingLeft:6,style:{maxWidth:"200px"},children:v==="text"?o.jsxs(ae.Root,{error:j,name:f,children:[o.jsx(Fn,{"aria-label":g,disabled:w,onChange:d,value:b===null?"":b}),o.jsx(ae.Error,{})]}):o.jsxs(ae.Root,{error:j,name:f,children:[o.jsx(Yl,{"aria-label":g,disabled:w,onValueChange:A=>{d({target:{name:f,value:A??0,type:v}})},value:b||0}),o.jsx(ae.Error,{})]})})]})},Y0=({onChange:a,...u})=>{const{formatMessage:l}=be(),{toggleNotification:f}=Po(),d=b=>{f({type:"info",message:l({id:C("contentType.kind.change.warning"),defaultMessage:"You just changed the kind of a content type: API will be reset (routes, controllers, and services will be overwritten)."})}),a(b)};return o.jsx(Bo,{...u,onChange:d})},X0=({description:a,disabled:u=!1,intlLabel:l,isCreating:f,name:d,onChange:b,value:p=!1})=>{const{formatMessage:g}=be(),[v,w]=H.useState(!1),j=l.id?g({id:l.id,defaultMessage:l.defaultMessage},{...l.values}):d,A=a?g({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):"",O=()=>{b({target:{name:d,value:!1}}),w(!1)},K=T=>{if(!T&&!f){w(!0);return}b({target:{name:d,value:!!T}})};return o.jsxs(o.Fragment,{children:[o.jsxs(ae.Root,{hint:A,name:d,children:[o.jsx(Wo,{checked:p,disabled:u,onCheckedChange:K,children:j}),o.jsx(ae.Hint,{})]}),o.jsx(Zn.Root,{open:v,onOpenChange:T=>w(T),children:o.jsx(Br,{endAction:o.jsx(Ue,{onClick:O,variant:"danger",width:"100%",justifyContent:"center",children:g({id:C("popUpWarning.draft-publish.button.confirm"),defaultMessage:"Yes, disable"})}),children:g({id:C("popUpWarning.draft-publish.message"),defaultMessage:"If you disable the draft & publish, your drafts will be deleted."})})})]})},Q0=({deleteComponent:a,deleteContentType:u,isAttributeModal:l,isCustomFieldModal:f,isComponentAttribute:d,isComponentToDzModal:b,isContentTypeModal:p,isCreatingComponent:g,isCreatingComponentAttribute:v,isCreatingComponentInDz:w,isCreatingComponentWhileAddingAField:j,isCreatingContentType:A,isCreatingDz:O,isComponentModal:K,isDzAttribute:T,isEditingAttribute:U,isInFirstComponentStep:$,onSubmitAddComponentAttribute:E,onSubmitAddComponentToDz:N,onSubmitCreateContentType:S,onSubmitCreateComponent:te,onSubmitCreateDz:L,onSubmitEditAttribute:ne,onSubmitEditComponent:he,onSubmitEditContentType:Fe,onSubmitEditCustomFieldAttribute:Ae,onSubmitEditDz:Ve,onClickFinish:et})=>{const{formatMessage:oe}=be();return b?w?o.jsx(Ue,{variant:"secondary",type:"submit",onClick:V=>{V.preventDefault(),N(V,!0)},startIcon:o.jsx(Hn,{}),children:oe({id:C("form.button.add-first-field-to-created-component"),defaultMessage:"Add first field to the component"})}):o.jsx(Ue,{variant:"default",type:"submit",onClick:V=>{V.preventDefault(),N(V,!1)},children:oe({id:"global.finish",defaultMessage:"Finish"})}):l&&T&&!O?o.jsx(Ue,{variant:"default",type:"submit",onClick:V=>{V.preventDefault(),et(),Ve(V,!1)},children:oe({id:"global.finish",defaultMessage:"Finish"})}):l&&T&&O?o.jsx(o.Fragment,{children:o.jsx(Ue,{variant:"secondary",type:"submit",onClick:V=>{V.preventDefault(),L(V,!0)},startIcon:o.jsx(Hn,{}),children:oe({id:C("form.button.add-components-to-dynamiczone"),defaultMessage:"Add components to the zone"})})}):l&&d?$?o.jsx(Ue,{variant:"secondary",type:"submit",onClick:V=>{V.preventDefault(),E(V,!0)},children:oe(v?{id:C("form.button.configure-component"),defaultMessage:"Configure the component"}:{id:C("form.button.select-component"),defaultMessage:"Configure the component"})}):o.jsxs(re,{gap:2,children:[o.jsx(Ue,{variant:"secondary",type:"submit",onClick:V=>{V.preventDefault(),E(V,!0)},startIcon:o.jsx(Hn,{}),children:oe(j?{id:C("form.button.add-first-field-to-created-component"),defaultMessage:"Add first field to the component"}:{id:C("form.button.add-field"),defaultMessage:"Add another field"})}),o.jsx(Ue,{variant:"default",type:"button",onClick:V=>{V.preventDefault(),et(),E(V,!1)},children:oe({id:"global.finish",defaultMessage:"Finish"})})]}):l&&!d&&!T?o.jsxs(re,{gap:2,children:[o.jsx(Ue,{type:U?"button":"submit",variant:"secondary",onClick:V=>{V.preventDefault(),ne(V,!0)},startIcon:o.jsx(Hn,{}),children:oe({id:C("form.button.add-field"),defaultMessage:"Add another field"})}),o.jsx(Ue,{type:U?"submit":"button",variant:"default",onClick:V=>{V.preventDefault(),et(),ne(V,!1)},children:oe({id:"global.finish",defaultMessage:"Finish"})})]}):p?o.jsxs(re,{gap:2,children:[!A&&o.jsxs(o.Fragment,{children:[o.jsx(Ue,{type:"button",variant:"danger",onClick:V=>{V.preventDefault(),u()},children:oe({id:"global.delete",defaultMessage:"Delete"})}),o.jsx(Ue,{type:"submit",variant:"default",onClick:V=>{V.preventDefault(),Fe(V,!1)},children:oe({id:"global.finish",defaultMessage:"Finish"})})]}),A&&o.jsx(Ue,{type:"submit",variant:"secondary",onClick:V=>{V.preventDefault(),S(V,!0)},children:oe({id:"global.continue",defaultMessage:"Continue"})})]}):K?o.jsxs(re,{gap:2,children:[!g&&o.jsxs(o.Fragment,{children:[o.jsx(Ue,{type:"button",variant:"danger",onClick:V=>{V.preventDefault(),a()},children:oe({id:"global.delete",defaultMessage:"Delete"})}),o.jsx(Ue,{type:"submit",variant:"default",onClick:V=>{V.preventDefault(),he(V,!1)},children:oe({id:"global.finish",defaultMessage:"Finish"})})]}),g&&o.jsx(Ue,{type:"submit",variant:"secondary",onClick:V=>{V.preventDefault(),te(V,!0)},children:oe({id:"global.continue",defaultMessage:"Continue"})})]}):f?o.jsxs(re,{gap:2,children:[o.jsx(Ue,{type:U?"button":"submit",variant:"secondary",onClick:V=>{V.preventDefault(),Ae(V,!0)},startIcon:o.jsx(Hn,{}),children:oe({id:C("form.button.add-field"),defaultMessage:"Add another field"})}),o.jsx(Ue,{type:U?"submit":"button",variant:"default",onClick:V=>{V.preventDefault(),et(),Ae(V,!1)},children:oe({id:"global.finish",defaultMessage:"Finish"})})]}):null},eC=({actionType:a=null,attributeName:u,attributeType:l,contentTypeKind:f,dynamicZoneTarget:d,forTarget:b,modalType:p=null,targetUid:g,customFieldUid:v=null,showBackLink:w=!1})=>{const{formatMessage:j}=be(),{components:A,contentTypes:O}=Yt(),{onOpenModalAddField:K}=pr();let T="component",U=[];const $=b==="component"?A[g]:O[g],E=$?.info.displayName;if(p==="contentType"&&(T=f),["component"].includes(p||"")&&(T="component"),["component","contentType"].includes(p||"")){let S=C(`modalForm.component.header-${a}`);return p==="contentType"&&(S=C(`modalForm.${f}.header-create`)),a==="edit"&&(S=C("modalForm.header-edit")),o.jsx(Rn.Header,{children:o.jsxs(re,{children:[o.jsx(ce,{children:o.jsx(zr,{type:T})}),o.jsx(ce,{paddingLeft:3,children:o.jsx(Rn.Title,{children:j({id:S},{name:E})})})]})})}return U=[{label:E,info:{category:"category"in $&&$?.category||"",name:$?.info?.displayName}}],p==="chooseAttribute"&&(T=b==="component"?"component":"kind"in $?$.kind:""),p==="addComponentToDynamicZone"&&(T="dynamiczone",U.push({label:d})),(p==="attribute"||p==="customField")&&(T=l,U.push({label:u})),o.jsx(Rn.Header,{children:o.jsxs(re,{gap:3,children:[w&&o.jsx(Kl,{"aria-label":j({id:C("modalForm.header.back"),defaultMessage:"Back"}),startIcon:o.jsx(Xl,{}),onClick:()=>K({forTarget:b,targetUid:g}),href:"#back",isExternal:!1}),o.jsx(zr,{type:T,customField:v}),o.jsx(yy,{label:U.map(({label:S})=>S).join(","),children:U.map(({label:S,info:te},L,ne)=>{if(S=Gn(S),!S)return null;const he=`${S}.${L}`;return te?.category&&(S=`${S} (${Gn(te.category)} - ${Gn(te.name)})`),o.jsx(vy,{isCurrent:L===ne.length-1,children:S},he)})})]})})},tC=({modalType:a,forTarget:u,kind:l,actionType:f,step:d})=>{switch(a){case"chooseAttribute":return C(`modalForm.sub-header.chooseAttribute.${u?.includes("component")?"component":l||"collectionType"}`);case"attribute":return C(`modalForm.sub-header.attribute.${f}${d!=="null"&&d!==null&&f!=="edit"?".step":""}`);case"customField":return C(`modalForm.sub-header.attribute.${f}`);case"addComponentToDynamicZone":return C("modalForm.sub-header.addComponentToDynamicZone");default:return C("configurations")}},nC=({actionType:a,modalType:u,forTarget:l,kind:f,step:d,attributeType:b,attributeName:p,customField:g})=>{const{formatMessage:v}=be(),w=u==="customField"?g?.intlLabel:{id:C(`attribute.${b}`)};return o.jsxs(re,{direction:"column",alignItems:"flex-start",paddingBottom:1,gap:1,children:[o.jsx(ge,{tag:"h2",variant:"beta",children:v({id:tC({actionType:a,forTarget:l,kind:f,step:d,modalType:u}),defaultMessage:"Add new field"},{type:w?Gn(v(w)):"",name:Gn(p),step:d})}),o.jsx(ge,{variant:"pi",textColor:"neutral600",children:v({id:C(`attribute.${b}.description`),defaultMessage:"A type for modeling data"})})]})},vc={alien:xx,apps:vx,archive:yx,arrowDown:bx,arrowLeft:Xl,arrowRight:gx,arrowUp:hx,attachment:mx,bell:px,bold:fx,book:dx,briefcase:cx,brush:Co,bulletList:lx,calendar:ux,car:sx,cast:ox,chartBubble:ix,chartCircle:ax,chartPie:rx,check:nx,clock:tx,cloud:ex,code:Qv,cog:Xv,collapse:Yv,command:Jv,connector:Kv,crop:Zv,crown:Gv,cup:Hv,cursor:Vv,dashboard:Bv,database:zv,discuss:Wv,doctor:Uv,earth:Pv,emotionHappy:qv,emotionUnhappy:Dv,envelop:Ov,exit:Ev,expand:Lv,eye:ic,feather:vl,file:Iv,fileError:Nv,filePdf:$v,filter:Sv,folder:Rv,gate:Av,gift:Fv,globe:kv,grid:Mv,handHeart:Tv,hashtag:wv,headphone:jv,heart:_v,house:Cv,information:xv,italic:vv,key:yv,landscape:bv,layer:gv,layout:hv,lightbulb:mv,link:pv,lock:fv,magic:dv,manyToMany:ac,manyToOne:rc,manyWays:nc,medium:cv,message:lv,microphone:uv,monitor:sv,moon:ov,music:iv,oneToMany:tc,oneToOne:ec,oneWay:Ql,paint:Co,paintBrush:Co,paperPlane:av,pencil:rv,phone:nv,picture:tv,pin:ev,pinMap:Qy,plane:Xy,play:Yy,plus:Hn,priceTag:Jy,puzzle:Ky,question:Zy,quote:Gy,refresh:Do,restaurant:Hy,rocket:Vy,rotate:By,scissors:zy,search:qo,seed:Wy,server:Uy,shield:Py,shirt:qy,shoppingCart:Dy,slideshow:Oy,stack:Ey,star:Ly,store:Iy,strikeThrough:Ny,sun:$y,television:Sy,thumbDown:Ry,thumbUp:Ay,train:Fy,twitter:ky,typhoon:My,underline:Ty,user:wy,volumeMute:jy,volumeUp:_y,walk:Cy,wheelchair:xy,write:vl},rC=jt(re)`
  label {
    ${Jl()}
    border-radius: ${({theme:a})=>a.borderRadius};
    border: 1px solid ${({theme:a})=>a.colors.neutral100};
  }
`,aC=({iconKey:a,name:u,onChange:l,isSelected:f,ariaLabel:d})=>{const b=vc[a];return o.jsx(ae.Root,{name:u,required:!1,children:o.jsxs(ae.Label,{children:[o.jsxs(zl,{children:[d,o.jsx(ae.Input,{type:"radio",checked:f,onChange:l,value:a,"aria-checked":f})]}),o.jsx(Za,{label:a,children:o.jsx(re,{padding:2,cursor:"pointer",hasRadius:!0,background:f?"primary200":void 0,children:o.jsx(b,{width:"2rem",height:"2rem",fill:f?"primary600":"neutral300"})})})]})})},iC=({intlLabel:a,name:u,onChange:l,value:f=""})=>{const{formatMessage:d}=be(),[b,p]=H.useState(!1),[g,v]=H.useState(""),w=Object.keys(vc),[j,A]=H.useState(w),O=H.useRef(null),K=H.useRef(null),T=()=>{p(!b)},U=({target:{value:N}})=>{v(N),A(()=>w.filter(S=>S.toLowerCase().includes(N.toLowerCase())))},$=()=>{T(),v(""),A(w)},E=()=>{l({target:{name:u,value:""}})};return H.useEffect(()=>{b&&K.current?.focus()},[b]),o.jsxs(o.Fragment,{children:[o.jsxs(re,{justifyContent:"space-between",paddingBottom:2,children:[o.jsx(ge,{variant:"pi",fontWeight:"bold",textColor:"neutral800",tag:"label",children:d(a)}),o.jsxs(re,{gap:1,children:[b?o.jsx(Cx,{ref:K,name:"searchbar",placeholder:d({id:C("ComponentIconPicker.search.placeholder"),defaultMessage:"Search for an icon"}),onBlur:()=>{g||T()},onChange:U,value:g,onClear:$,clearLabel:d({id:C("IconPicker.search.clear.label"),defaultMessage:"Clear the icon search"}),children:d({id:C("IconPicker.search.placeholder.label"),defaultMessage:"Search for an icon"})}):o.jsx(Ua,{ref:O,onClick:T,withTooltip:!1,label:d({id:C("IconPicker.search.button.label"),defaultMessage:"Search icon button"}),variant:"ghost",children:o.jsx(qo,{})}),f&&o.jsx(Za,{label:d({id:C("IconPicker.remove.tooltip"),defaultMessage:"Remove the selected icon"}),children:o.jsx(Ua,{onClick:E,withTooltip:!1,label:d({id:C("IconPicker.remove.button"),defaultMessage:"Remove the selected icon"}),variant:"ghost",children:o.jsx(oc,{})})})]})]}),o.jsx(rC,{position:"relative",padding:1,background:"neutral100",hasRadius:!0,wrap:"wrap",gap:2,maxHeight:"126px",overflow:"auto",textAlign:"center",children:j.length>0?j.map(N=>o.jsx(aC,{iconKey:N,name:u,onChange:l,isSelected:N===f,ariaLabel:d({id:C("IconPicker.icon.label"),defaultMessage:"Select {icon} icon"},{icon:N})},N)):o.jsx(ce,{padding:4,grow:2,children:o.jsx(ge,{variant:"delta",textColor:"neutral600",textAlign:"center",children:d({id:C("IconPicker.emptyState.label"),defaultMessage:"No icon found"})})})})]})},oC=({description:a,error:u,intlLabel:l,modifiedData:f,name:d,onChange:b,value:p})=>{const{formatMessage:g}=be(),v=H.useRef(b),w=f?.displayName||"";H.useEffect(()=>{if(w){const K=sc(w);try{const T=za(K,2);v.current({target:{name:d,value:T}})}catch{v.current({target:{name:d,value:K}})}}else v.current({target:{name:d,value:""}})},[w,d]);const j=u?g({id:u,defaultMessage:u}):"",A=a?g({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):"",O=g(l);return o.jsxs(ae.Root,{error:j,hint:A,name:d,children:[o.jsx(ae.Label,{children:O}),o.jsx(Fn,{onChange:b,value:p||""}),o.jsx(ae.Error,{})]})},Tl=a=>{if(a instanceof Date&&Ml(a))return a;if(typeof a=="string"||typeof a=="number"){const u=new Date(a);if(Ml(u))return u}},Ml=a=>!isNaN(a.getTime()),sC=a=>{const[u,l]=a.split(":");return`${u}:${l}`},uC=a=>a.split(":").length===2?`${a}:00.000`:a,lC=a=>{if(a)return a.split(":").length>2?sC(a):a},cC=a=>{if(a)return uC(a)},dC=({value:a})=>lC(a),kl=(a,u,l,f)=>{const d=cC(f);a({target:{name:u,value:d,type:l}})},fC=({autoComplete:a,customInputs:u,description:l,disabled:f,intlLabel:d,labelAction:b,error:p,name:g,onChange:v,onDelete:w,options:j=[],placeholder:A,required:O,step:K,type:T,value:U,isNullable:$,autoFocus:E,attribute:N,attributeName:S,conditionFields:te,...L})=>{const{formatMessage:ne}=be(),he=(G,fe)=>{if(G&&(fe==="minLength"&&fe in G||fe==="maxLength"&&fe in G||fe==="max"&&fe in G||fe==="min"&&fe in G))return G[fe]},{hint:Fe}=pC({description:l,fieldSchema:{minLength:he(N,"minLength"),maxLength:he(N,"maxLength"),max:he(N,"max"),min:he(N,"min")},type:N?.type||T}),[Ae,Ve]=H.useState(!1),et=u?u[T]:null,oe=U??void 0,V=oe??"";function It(G){if(!G)return null;if(typeof G=="string")return ne({id:G,defaultMessage:G});const fe={...G.values};return ne({id:G.id,defaultMessage:G?.defaultMessage??G.id},fe)}const De=It(p)??void 0;if(et)return o.jsx(et,{...L,attribute:N,description:l,hint:Fe,disabled:f,intlLabel:d,labelAction:b,error:De||"",name:g,onChange:v,onDelete:w,options:j,required:O,placeholder:A,type:T,value:oe,autoFocus:E,attributeName:S,conditionFields:te});const Bt=d.id?ne({id:d.id,defaultMessage:d.defaultMessage},{...d.values}):g,Ke=A?ne({id:A.id,defaultMessage:A.defaultMessage},{...A.values}):"",mn=()=>{switch(T){case"json":return o.jsx(kx,{value:oe,disabled:f,onChange:G=>{const fe=N&&"required"in N&&!N?.required&&!G.length?null:G;v({target:{name:g,value:fe}},!1)},minHeight:"25.2rem",maxHeight:"50.4rem"});case"bool":return o.jsx(Mx,{checked:U===null?null:U||!1,disabled:f,offLabel:ne({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"False"}),onLabel:ne({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"True"}),onChange:G=>{v({target:{name:g,value:G.target.checked}})}});case"checkbox":return o.jsx(Wo,{disabled:f,onCheckedChange:G=>{v({target:{name:g,value:G}})},checked:!!oe,children:Bt});case"datetime":{const G=Tl(oe);return o.jsx(Tx,{clearLabel:ne({id:"clearLabel",defaultMessage:"Clear"}),disabled:f,onChange:fe=>{const Xt=fe?fe.toISOString():null;v({target:{name:g,value:Xt,type:T}})},onClear:()=>v({target:{name:g,value:null,type:T}}),placeholder:Ke,value:G})}case"date":{const G=Tl(oe);return o.jsx(wx,{clearLabel:ne({id:"clearLabel",defaultMessage:"Clear"}),disabled:f,onChange:fe=>{v({target:{name:g,value:fe?c0(fe,{representation:"date"}):null,type:T}})},onClear:()=>v({target:{name:g,value:null,type:T}}),placeholder:Ke,value:G})}case"number":return o.jsx(Yl,{disabled:f,onValueChange:G=>{v({target:{name:g,value:G,type:T}})},placeholder:Ke,step:K,value:oe,autoFocus:E});case"email":return o.jsx(Fn,{autoComplete:a,disabled:f,onChange:G=>{v({target:{name:g,value:G.target.value,type:T}})},placeholder:Ke,type:"email",value:V,autoFocus:E});case"timestamp":case"text":case"string":return o.jsx(Fn,{autoComplete:a,disabled:f,onChange:G=>{v({target:{name:g,value:G.target.value,type:T}})},placeholder:Ke,type:"text",value:V,autoFocus:E});case"password":return o.jsx(Fn,{autoComplete:a,disabled:f,endAction:o.jsx("button",{"aria-label":ne({id:"Auth.form.password.show-password",defaultMessage:"Show password"}),onClick:()=>{Ve(G=>!G)},style:{border:"none",padding:0,background:"transparent"},type:"button",children:Ae?o.jsx(ic,{fill:"neutral500"}):o.jsx(jx,{fill:"neutral500"})}),onChange:G=>{v({target:{name:g,value:G.target.value,type:T}})},placeholder:Ke,type:Ae?"text":"password",value:V});case"select":return o.jsx(pn,{disabled:f,onChange:G=>{v({target:{name:g,value:G,type:"select"}})},placeholder:Ke,value:oe,children:j.map(({metadatas:{intlLabel:G,disabled:fe,hidden:Xt},key:B,value:je})=>o.jsx(_t,{value:je,disabled:fe,hidden:Xt,children:ne(G)},B))});case"textarea":return o.jsx(uc,{disabled:f,onChange:G=>v({target:{name:g,value:G.target.value,type:T}}),placeholder:Ke,value:V});case"time":{const G=dC({value:oe});return o.jsx(_x,{clearLabel:ne({id:"clearLabel",defaultMessage:"Clear"}),disabled:f,onChange:fe=>kl(v,g,T,fe),onClear:()=>kl(v,g,T,void 0),value:G})}default:return o.jsx(Fn,{disabled:!0,placeholder:"Not supported",type:"text",value:""})}};return o.jsxs(ae.Root,{error:De,name:g,hint:Fe,required:O,children:[T!=="checkbox"?o.jsx(ae.Label,{action:b,children:Bt}):null,mn(),o.jsx(ae.Error,{}),o.jsx(ae.Hint,{})]})},pC=({description:a,fieldSchema:u,type:l})=>{const{formatMessage:f}=be(),d=()=>a?.id?f({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):"";return{hint:(()=>{const{maximum:p,minimum:g}=hC(u),v=mC({type:l,minimum:g,maximum:p}),w=typeof g=="number",j=typeof p=="number",A=j&&w,O=j||w;return!a?.id&&!O?"":f({id:"content-manager.form.Input.hint.text",defaultMessage:"{min, select, undefined {} other {min. {min}}}{divider}{max, select, undefined {} other {max. {max}}}{unit}{br}{description}"},{min:g,max:p,description:d(),unit:v?.message&&O?f(v.message,v.values):null,divider:A?f({id:"content-manager.form.Input.hint.minMaxDivider",defaultMessage:" / "}):null,br:O?o.jsx("br",{}):null})})()}},mC=({type:a,minimum:u,maximum:l})=>{if(a&&["biginteger","integer","number"].includes(a))return{};const f=Math.max(u||0,l||0);return{message:{id:"content-manager.form.Input.hint.character.unit",defaultMessage:"{maxValue, plural, one { character} other { characters}}"},values:{maxValue:f}}},hC=a=>{if(!a)return{maximum:void 0,minimum:void 0};const{minLength:u,maxLength:l,max:f,min:d}=a;let b,p;const g=Number(d),v=Number(u);Number.isNaN(g)?Number.isNaN(v)||(b=v):b=g;const w=Number(f),j=Number(l);return Number.isNaN(w)?Number.isNaN(j)||(p=j):p=w,{maximum:p,minimum:b}},$o=H.memo(fC,Wa),gC=({oneThatIsCreatingARelationWithAnother:a,target:u})=>{const{contentTypes:l,sortedContentTypesList:f}=Yt(),d=Ga(),b=f.filter(bc),p=l[u];if(!p)return null;const g=({uid:v,plugin:w,title:j,restrictRelationsTo:A})=>()=>{const O=w?`${w}_${j}`:j;d(Be.onChangeRelationTarget({target:{value:v,oneThatIsCreatingARelationWithAnother:a,selectedContentTypeFriendlyName:O,targetContentTypeAllowedRelations:A}}))};return o.jsxs(zt.Root,{children:[o.jsx(bC,{children:`${p.info.displayName} ${p.plugin?`(from: ${p.plugin})`:""}`}),o.jsx(zt.Content,{zIndex:"popover",children:b.map(({uid:v,title:w,restrictRelationsTo:j,plugin:A})=>o.jsxs(zt.Item,{onSelect:g({uid:v,plugin:A,title:w,restrictRelationsTo:j}),children:[w," ",A&&o.jsxs(o.Fragment,{children:["(from: ",A,")"]})]},v))})]})},bC=jt(zt.Trigger)`
  max-width: 16.8rem;
  span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`,Fl=({disabled:a=!1,error:u,header:l,isMain:f=!1,name:d,onChange:b,oneThatIsCreatingARelationWithAnother:p="",target:g,value:v=""})=>o.jsxs(ce,{background:"neutral100",hasRadius:!0,borderColor:"neutral200",children:[o.jsx(re,{paddingTop:f?4:1,paddingBottom:f?3:1,justifyContent:"center",children:f?o.jsx(ge,{variant:"pi",fontWeight:"bold",textColor:"neutral800",children:l}):o.jsx(gC,{target:g,oneThatIsCreatingARelationWithAnother:p})}),o.jsx(Ha,{background:"neutral200"}),o.jsx(ce,{padding:4,children:o.jsx($o,{disabled:a,error:u?.id||null,intlLabel:{id:C("form.attribute.item.defineRelation.fieldName"),defaultMessage:"Field name"},name:d,onChange:b,type:"text",value:v})})]});var yC=Ax,vC=Fx,xC="[object RegExp]";function CC(a){return vC(a)&&yC(a)==xC}var _C=CC,jC=_C,wC=Rx,Al=Sx,Rl=Al&&Al.isRegExp,TC=Rl?wC(Rl):jC,MC=TC,kC=$x,FC=kC("length"),AC=FC,xc="\\ud800-\\udfff",RC="\\u0300-\\u036f",SC="\\ufe20-\\ufe2f",$C="\\u20d0-\\u20ff",NC=RC+SC+$C,IC="\\ufe0e\\ufe0f",LC="["+xc+"]",No="["+NC+"]",Io="\\ud83c[\\udffb-\\udfff]",EC="(?:"+No+"|"+Io+")",Cc="[^"+xc+"]",_c="(?:\\ud83c[\\udde6-\\uddff]){2}",jc="[\\ud800-\\udbff][\\udc00-\\udfff]",OC="\\u200d",wc=EC+"?",Tc="["+IC+"]?",DC="(?:"+OC+"(?:"+[Cc,_c,jc].join("|")+")"+Tc+wc+")*",qC=Tc+wc+DC,PC="(?:"+[Cc+No+"?",No,_c,jc,LC].join("|")+")",Sl=RegExp(Io+"(?="+Io+")|"+PC+qC,"g");function UC(a){for(var u=Sl.lastIndex=0;Sl.test(a);)++u;return u}var WC=UC,zC=AC,BC=lc,VC=WC;function HC(a){return BC(a)?VC(a):zC(a)}var GC=HC,$l=Lx,ZC=Dx,KC=lc,JC=Nx,YC=MC,XC=GC,QC=Ox,e1=Ix,Nl=Ex,t1=30,n1="...",r1=/\w*$/;function a1(a,u){var l=t1,f=n1;if(JC(u)){var d="separator"in u?u.separator:d;l="length"in u?e1(u.length):l,f="omission"in u?$l(u.omission):f}a=Nl(a);var b=a.length;if(KC(a)){var p=QC(a);b=p.length}if(l>=b)return a;var g=l-XC(f);if(g<1)return f;var v=p?ZC(p,0,g).join(""):a.slice(0,g);if(d===void 0)return v+f;if(p&&(g+=v.length-g),YC(d)){if(a.slice(g).search(d)){var w,j=v;for(d.global||(d=RegExp(d.source,Nl(r1.exec(d))+"g")),d.lastIndex=0;w=d.exec(j);)var A=w.index;v=v.slice(0,A===void 0?g:A)}}else if(a.indexOf($l(d),g)!=g){var O=v.lastIndexOf(d);O>-1&&(v=v.slice(0,O))}return v+f}var i1=a1;const Il=cc(i1),o1=jt(ce)`
  position: relative;
  width: 100%;
  &::before {
    content: '';
    position: absolute;
    top: calc(50% - 0px);
    height: 2px;
    width: 100%;
    background-color: ${({theme:a})=>a.colors.primary600};
    z-index: 0;
  }
`,s1=jt(ce)`
  background: ${({theme:a,$isSelected:u})=>a.colors[u?"primary100":"neutral0"]};
  border: 1px solid
    ${({theme:a,$isSelected:u})=>a.colors[u?"primary700":"neutral200"]};
  border-radius: ${({theme:a})=>a.borderRadius};
  z-index: 1;
  flex: 0 0 2.4rem;
  svg {
    width: 2.4rem;
    height: 2.4rem;
    max-width: unset;
    path {
      fill: ${({theme:a,$isSelected:u})=>a.colors[u?"primary700":"neutral500"]};
    }
  }
  cursor: pointer;
  &:disabled {
    cursor: not-allowed;
    background: ${({theme:a})=>a.colors.neutral150};

    svg {
      path {
        fill: ${({theme:a})=>a.colors.neutral300};
      }
    }
  }
  display: flex;
  justify-content: center;
  align-items: center;
`,u1=jt(re)`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
`,l1={oneWay:Ql,oneToOne:ec,oneToMany:tc,manyToOne:rc,manyToMany:ac,manyWay:nc},c1=["oneWay","oneToOne","oneToMany","manyToOne","manyToMany","manyWay"],d1=["oneWay","manyWay"],f1=({naturePickerType:a,oneThatIsCreatingARelationWithAnother:u,relationType:l,target:f,targetUid:d})=>{const b=Ga(),{formatMessage:p}=be(),{contentTypes:g}=Yt(),w=(a==="component"?"component":Ee(g,[d,"kind"],""))==="collectionType"?c1:d1,j=l==="manyToOne",A=Ee(g,[f,"info","displayName"],"unknown"),O=j?A:u,K=j?u:A,T=za(O,l==="manyToMany"?2:1),U=Ee(g,[f,"restrictRelationsTo"],null),$=za(K,["manyToMany","oneToMany","manyToOne","manyWay"].includes(l)?2:1);return l?o.jsxs(re,{style:{flex:1},children:[o.jsx(o1,{children:o.jsx(re,{paddingLeft:9,paddingRight:9,paddingTop:1,justifyContent:"center",children:o.jsx(Uo,{tagName:"button",children:o.jsx(re,{gap:3,children:w.map(E=>{const N=l1[E],S=U===null||U.includes(E);return o.jsx(s1,{tag:"button",$isSelected:l===E,disabled:!S,onClick:()=>{S&&b(Be.onChangeRelationType({target:{oneThatIsCreatingARelationWithAnother:u,value:E}}))},padding:2,type:"button","aria-label":p({id:C(`relation.${E}`)}),"aria-pressed":l===E,"data-relation-type":E,children:o.jsx(N,{"aria-hidden":"true"},E)},E)})})})})}),o.jsxs(u1,{justifyContent:"center",children:[o.jsxs(ge,{children:[Il(T,{length:24})," "]}),o.jsxs(ge,{textColor:"primary600",children:[p({id:C(`relation.${l}`)})," "]}),o.jsx(ge,{children:Il($,{length:24})})]})]}):null},p1=({formErrors:a,mainBoxHeader:u,modifiedData:l,naturePickerType:f,onChange:d,targetUid:b})=>{const p=zo(l.relation,l.targetAttribute);return o.jsxs(re,{style:{position:"relative"},children:[o.jsx(Fl,{isMain:!0,header:u,error:a?.name||null,name:"name",onChange:d,value:l?.name||""}),o.jsx(f1,{naturePickerType:f,oneThatIsCreatingARelationWithAnother:u,relationType:p,target:l.target,targetUid:b}),o.jsx(Fl,{disabled:["oneWay","manyWay"].includes(p),error:a?.targetAttribute||null,name:"targetAttribute",onChange:d,oneThatIsCreatingARelationWithAnother:u,target:l.target,value:l?.targetAttribute||""})]})},m1=({error:a=null,intlLabel:u,name:l,onChange:f,value:d=void 0,isCreating:b,dynamicZoneTarget:p})=>{const{formatMessage:g}=be(),{allComponentsCategories:v}=Yt(),[w,j]=H.useState(v),A=a?g({id:a,defaultMessage:a}):"",O=g(u),K=U=>{f({target:{name:l,value:U,type:"select-category"}})},T=U=>{j($=>[...$,U]),K(U)};return o.jsxs(ae.Root,{error:A,name:l,children:[o.jsx(ae.Label,{children:O}),o.jsx(qx,{disabled:!b&&!p,onChange:K,onCreateOption:T,value:d,creatable:!0,children:w.map(U=>o.jsx(Px,{value:U,children:U},U))}),o.jsx(ae.Error,{})]})},Lo=(a,u)=>u.find(l=>l.component===a),Mc=(a,u,l=0)=>{const f=Lo(a,u);if(!f||!f.childComponents||f.childComponents.length===0)return l;let d=l;return f.childComponents.forEach(b=>{const p=Mc(b.component,u,l+1);p>d&&(d=p)}),d},kc=(a,u)=>{const l=(b,p)=>{const g=[];if(g.push(p),!b.uidsOfAllParents)return g;for(const v of b.uidsOfAllParents){const w=Lo(v,u);w&&g.push(...l(w,p+1))}return g},f=Lo(a,u);return f?Math.max(...l(f,1)):0},h1=({error:a=null,intlLabel:u,isAddingAComponentToAnotherComponent:l,isCreating:f,isCreatingComponentWhileAddingAField:d,componentToCreate:b,name:p,onChange:g,targetUid:v,forTarget:w,value:j})=>{const{formatMessage:A}=be(),O=a?A({id:a,defaultMessage:a}):"",K=A(u),{componentsGroupedByCategory:T,componentsThatHaveOtherComponentInTheirAttributes:U,nestedComponents:$}=Yt(),E=w==="component";let N=Object.entries(T).reduce((S,te)=>{const[L,ne]=te,he=ne.map(Fe=>({uid:Fe.uid,label:Fe.info.displayName,categoryName:L}));return[...S,...he]},[]);return l&&(N=N.filter(({uid:S})=>{const te=Mc(S,U),L=kc(v,$);return te+L<=dc})),E&&(N=N.filter(S=>S.uid!==v)),d&&(N=[{uid:j,label:b?.displayName,categoryName:b?.category}]),o.jsxs(ae.Root,{error:O,name:p,children:[o.jsx(ae.Label,{children:K}),o.jsx(pn,{disabled:d||!f,onChange:S=>{g({target:{name:p,value:S,type:"select-category"}})},value:j||"",children:N.map(S=>o.jsx(_t,{value:S.uid,children:`${S.categoryName} - ${S.label}`},S.uid))}),o.jsx(ae.Error,{})]})},g1=({dynamicZoneTarget:a,intlLabel:u,name:l,onChange:f,value:d,targetUid:b})=>{const{formatMessage:p}=be(),{componentsGroupedByCategory:g,contentTypes:v}=Yt(),w=So(v[b].attributes,a);if(!w)return null;const j="components"in w?w?.components:[],A=Object.keys(g).reduce((T,U)=>{const $=g[U].filter(({uid:E})=>!j.includes(E));return $.length>0&&(T[U]=$),T},{}),O=Object.entries(A).reduce((T,U)=>{const[$,E]=U,N={label:$,children:E.map(({uid:S,info:{displayName:te}})=>({label:te,value:S}))};return T.push(N),T},[]),K=p({id:C("components.SelectComponents.displayed-value"),defaultMessage:"{number, plural, =0 {# components} one {# component} other {# components}} selected"},{number:d?.length??0});return o.jsxs(ae.Root,{name:l,children:[o.jsx(ae.Label,{children:p(u)}),o.jsx(Zl,{id:"select1",customizeContent:()=>K,onChange:T=>{f({target:{name:l,value:T,type:"select-components"}})},options:O,value:d||[]})]})},b1=({intlLabel:a,error:u=void 0,modifiedData:l,name:f,onChange:d,options:b,value:p=""})=>{const{formatMessage:g}=be(),v=g(a),w=u?g({id:u,defaultMessage:u}):"",j=A=>{d({target:{name:f,value:A,type:"select"}}),p&&l.default!==void 0&&l.default!==null&&d({target:{name:"default",value:null}})};return o.jsxs(ae.Root,{error:w,name:f,children:[o.jsx(ae.Label,{children:v}),o.jsx(pn,{onChange:j,value:p||"",children:b.map(({metadatas:{intlLabel:A,disabled:O,hidden:K},key:T,value:U})=>o.jsx(_t,{value:U,disabled:O,hidden:K,children:g({id:A.id,defaultMessage:A.defaultMessage},A.values)},T))}),o.jsx(ae.Error,{})]})},y1=({intlLabel:a,error:u=void 0,modifiedData:l,name:f,onChange:d,options:b,value:p=""})=>{const{formatMessage:g}=be(),v=g(a),w=u?g({id:u,defaultMessage:u}):"",j=A=>{d({target:{name:f,value:A,type:"select"}}),p&&(A==="biginteger"&&p!=="biginteger"&&(l.default!==void 0&&l.default!==null&&d({target:{name:"default",value:null}}),l.max!==void 0&&l.max!==null&&d({target:{name:"max",value:null}}),l.min!==void 0&&l.min!==null&&d({target:{name:"min",value:null}})),typeof A=="string"&&["decimal","float","integer"].includes(A)&&p==="biginteger"&&(l.default!==void 0&&l.default!==null&&d({target:{name:"default",value:null}}),l.max!==void 0&&l.max!==null&&d({target:{name:"max",value:null}}),l.min!==void 0&&l.min!==null&&d({target:{name:"min",value:null}})))};return o.jsxs(ae.Root,{error:w,name:f,children:[o.jsx(ae.Label,{children:v}),o.jsx(pn,{onChange:j,value:p||"",children:b.map(({metadatas:{intlLabel:A,disabled:O,hidden:K},key:T,value:U})=>o.jsx(_t,{value:U,disabled:O,hidden:K,children:g(A)},T))}),o.jsx(ae.Error,{})]})},v1=({description:a=null,error:u=null,intlLabel:l,modifiedData:f,name:d,onChange:b,value:p=null})=>{const{formatMessage:g}=be(),v=H.useRef(b),w=f?.displayName||"";H.useEffect(()=>{w?v.current({target:{name:d,value:sc(w)}}):v.current({target:{name:d,value:""}})},[w,d]);const j=u?g({id:u,defaultMessage:u}):"",A=a?g({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):"",O=g(l);return o.jsxs(ae.Root,{error:j,hint:A,name:d,children:[o.jsx(ae.Label,{children:O}),o.jsx(Fn,{onChange:b,value:p||""}),o.jsx(ae.Error,{}),o.jsx(ae.Hint,{})]})},m_=(a,u)=>{const l=[];return Object.entries(a).forEach(([f,d])=>{if(d.attributes){const b=Array.isArray(d.attributes)?d.attributes.reduce((p,g,v)=>(p[v.toString()]=g,p),{}):d.attributes;Object.entries(b).forEach(([p,g])=>{g.conditions?.visible&&Object.entries(g.conditions.visible).forEach(([,v])=>{const[w]=v;w&&w.var===u&&l.push({contentTypeUid:f,contentType:d.info.displayName,attribute:g.name||p})})})}}),l},x1=(a,u,l)=>{if(!a?.visible)return"";const[[f,d]]=Object.entries(a.visible),[b,p]=d,g=u.find(O=>O.name===b.var),v=g?g.name:b.var,w=f==="=="?"is":"is not",j=String(p);return`If ${v} ${w} ${j}, then ${f==="=="?"Show":"Hide"} ${l}`},C1=(a,u)=>a.filter(l=>{const f=l.type==="boolean"||l.type==="enumeration",d=l.name!==u;return f&&d}).map(l=>({name:l.name,type:l.type,enum:l.type==="enumeration"?l.enum:void 0})),Ll=({form:a,formErrors:u,genericInputProps:l,modifiedData:f,onChange:d})=>{const{formatMessage:b}=be();return o.jsx(o.Fragment,{children:a.map((p,g)=>p.items.length===0?null:o.jsxs(ce,{children:[p.sectionTitle&&o.jsx(ce,{paddingBottom:4,children:o.jsx(ge,{variant:"delta",tag:"h3",children:b(p.sectionTitle)})}),p.intlLabel&&o.jsx(ge,{variant:"pi",textColor:"neutral600",children:b(p.intlLabel)}),o.jsx(An.Root,{gap:4,children:p.items.map((v,w)=>{const j=`${g}.${w}`,A=Ee(f,v.name,void 0),O=Object.keys(u).find(T=>T===v.name),K=O?u[O].id:Ee(u,[...v.name.split(".").filter(T=>T!=="componentToCreate"),"id"],null);if(v.type==="pushRight")return o.jsx(An.Item,{col:v.size||6,direction:"column",alignItems:"stretch",children:o.jsx("div",{})},v.name||j);if(v.type==="condition-form"){const T=Ee(f,v.name),U=l.contentTypeSchema?.attributes||[];if(!l.contentTypeSchema)return console.warn("contentTypeSchema is undefined, skipping condition form"),null;const $=C1(U,f.name),E=b({id:"form.attribute.condition.no-fields",defaultMessage:"No boolean or enumeration fields available to set conditions on."});return o.jsx(An.Item,{col:v.size||12,direction:"column",alignItems:"stretch",children:!T||Object.keys(T).length===0?o.jsxs(ce,{children:[T&&Object.keys(T).length>0&&o.jsx(ge,{variant:"sigma",textColor:"neutral800",marginBottom:2,children:x1(T,$,l.attributeName||f.name)}),o.jsx(Za,{description:E,children:o.jsx(Ue,{marginTop:T&&Object.keys(T).length>0?0:4,fullWidth:!0,variant:"secondary",onClick:()=>{d({target:{name:v.name,value:{visible:{"==":[{var:""},""]}}}})},startIcon:o.jsx("span",{"aria-hidden":!0,children:"＋"}),disabled:$.length===0,children:b({id:"form.attribute.condition.apply",defaultMessage:"Apply condition"})})})]}):o.jsx($o,{...v,...l,error:K,onChange:d,value:A,autoFocus:w===0,attributeName:f.name,conditionFields:$,onDelete:()=>{d({target:{name:v.name}})}})},v.name||j)}return o.jsx(An.Item,{col:v.size||6,direction:"column",alignItems:"stretch",children:o.jsx($o,{...v,...l,error:K,onChange:d,value:A,autoFocus:w===0})},v.name||j)})})]},g))})},_1=({description:a=null,disabled:u=!1,error:l="",intlLabel:f,labelAction:d,name:b,onChange:p,placeholder:g=null,value:v=""})=>{const{formatMessage:w}=be(),j=l?w({id:l,defaultMessage:l}):"",A=a?w({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):"",O=w(f),K=g?w({id:g.id,defaultMessage:g.defaultMessage},{...g.values}):"",T=Array.isArray(v)?v.join(`
`):"",U=$=>{const E=$.target.value.split(`
`);p({target:{name:b,value:E}})};return o.jsxs(ae.Root,{error:j,hint:A,name:b,children:[o.jsx(ae.Label,{action:d,children:O}),o.jsx(uc,{disabled:u,onChange:U,placeholder:K,value:T}),o.jsx(ae.Error,{}),o.jsx(ae.Hint,{})]})},j1=({disabled:a,tooltipMessage:u,onClick:l,marginTop:f=4})=>{const{formatMessage:d}=be(),b=o.jsx(Ue,{marginTop:f,fullWidth:!0,variant:"secondary",disabled:a,onClick:l,startIcon:o.jsx("span",{"aria-hidden":!0,children:"＋"}),children:d({id:"form.attribute.condition.apply",defaultMessage:"Apply condition"})});return u?o.jsx(Za,{description:u,children:b}):b},w1=jt(zr)`
  width: 16px !important;
  height: 16px !important;
  svg {
    width: 16px !important;
    height: 16px !important;
  }
`,T1=a=>{if(!a?.visible)return{dependsOn:"",operator:"is",value:"",action:"show"};const[[u,l]]=Object.entries(a.visible),[f,d]=l;return{dependsOn:f.var,operator:u==="=="?"is":"isNot",value:d,action:u==="=="?"show":"hide"}},M1=a=>{if(!a.dependsOn)return null;const u=fc(),l={dependsOn:a.dependsOn,operator:a.operator,value:a.value};try{return u.validate(l),{visible:{[a.action==="show"?"==":"!="]:[{var:a.dependsOn},a.value]}}}catch{return null}},k1=({name:a,value:u,onChange:l,onDelete:f,attributeName:d,conditionFields:b=[]})=>{const{formatMessage:p}=be(),[g,v]=H.useState(T1(u)),[w,j]=H.useState(!1),A=!!u?.visible;Array.isArray(b)||(b=[]);const O=b.find(L=>L.name===g.dependsOn),K=O?.type==="enumeration",T=L=>{v(L);const ne=fc(),he={dependsOn:L.dependsOn,operator:L.operator,value:L.value};try{ne.validate(he);const Fe=L.action==="show"?"==":"!=",Ae=L.dependsOn?{visible:{[Fe]:[{var:L.dependsOn},L.value]}}:null;Ae&&l({target:{name:a,value:Ae}})}catch{}},U=()=>{const L={dependsOn:"",operator:"is",value:"",action:"show"};v(L),l({target:{name:a,value:M1(L)}})},$=()=>{v({dependsOn:"",operator:"is",value:"",action:"show"}),l({target:{name:a,value:null}}),f(),j(!1)},E=L=>{const ne=L?.toString()||"",Fe=b.find(Ve=>Ve.name===ne)?.type==="enumeration",Ae={...g,dependsOn:ne,value:ne?Fe?"":!1:g.value};T(Ae)},N=L=>{const ne=L?.toString()||"is",he={...g,operator:ne};T(he)},S=L=>{const ne=K?L?.toString():L?.toString()==="true",he={...g,value:ne};T(he)},te=L=>{const ne=L?.toString()||"show",he={...g,action:ne};T(he)};return A?o.jsx(ce,{marginTop:2,children:o.jsxs(ce,{background:"neutral0",hasRadius:!0,borderColor:"neutral200",borderWidth:.5,borderStyle:"solid",children:[o.jsxs(re,{justifyContent:"space-between",alignItems:"center",padding:4,children:[o.jsx(ge,{variant:"sigma",textColor:"neutral800",children:p({id:C("form.attribute.condition.title"),defaultMessage:"Condition for {name}"},{name:o.jsx("strong",{children:d})})}),o.jsxs(Zn.Root,{open:w,onOpenChange:j,children:[o.jsx(Zn.Trigger,{children:o.jsx(Ua,{label:"Delete",children:o.jsx(oc,{})})}),o.jsx(Br,{onConfirm:$,children:p({id:C("popUpWarning.bodyMessage.delete-condition"),defaultMessage:"Are you sure you want to delete this condition?"})})]})]}),o.jsxs(ce,{background:"neutral100",padding:4,children:[o.jsx(ce,{paddingBottom:2,children:o.jsx(ge,{variant:"sigma",textColor:"neutral600",style:{textTransform:"uppercase",letterSpacing:1},children:p({id:C("form.attribute.condition.if"),defaultMessage:"IF"})})}),o.jsxs(re,{gap:4,children:[o.jsx(ce,{minWidth:0,flex:1,children:o.jsx(ae.Root,{name:`${a}.field`,children:o.jsx(pn,{value:g.dependsOn,onChange:E,placeholder:p({id:C("form.attribute.condition.field"),defaultMessage:"field"}),children:b.map(L=>o.jsx(_t,{value:L.name,children:o.jsxs(re,{gap:2,alignItems:"center",children:[o.jsx(w1,{type:L.type}),o.jsx("span",{children:L.name})]})},L.name))})})}),o.jsx(ce,{minWidth:0,flex:1,children:o.jsx(ae.Root,{name:`${a}.operator`,children:o.jsxs(pn,{value:g.operator,onChange:N,disabled:!g.dependsOn,placeholder:p({id:C("form.attribute.condition.operator"),defaultMessage:"condition"}),children:[o.jsx(_t,{value:"is",children:p({id:C("form.attribute.condition.operator.is"),defaultMessage:"is"})}),o.jsx(_t,{value:"isNot",children:p({id:C("form.attribute.condition.operator.isNot"),defaultMessage:"is not"})})]})})}),o.jsx(ce,{minWidth:0,flex:1,children:o.jsx(ae.Root,{name:`${a}.value`,children:o.jsx(pn,{value:g.value?.toString()||"",onChange:S,disabled:!g.dependsOn,placeholder:p({id:C("form.attribute.condition.value"),defaultMessage:"value"}),children:K&&O?.enum?O.enum.map(L=>o.jsx(_t,{value:L,children:L},L)):o.jsxs(o.Fragment,{children:[o.jsx(_t,{value:"true",children:p({id:C("form.attribute.condition.value.true"),defaultMessage:"true"})}),o.jsx(_t,{value:"false",children:p({id:C("form.attribute.condition.value.false"),defaultMessage:"false"})})]})})})})]})]}),o.jsxs(ce,{background:"neutral100",padding:4,children:[o.jsx(ce,{paddingBottom:4,children:o.jsx(ge,{variant:"sigma",textColor:"neutral600",style:{textTransform:"uppercase",letterSpacing:1},children:p({id:C("form.attribute.condition.then"),defaultMessage:"THEN"})})}),o.jsx(ce,{paddingBottom:4,children:o.jsx(ae.Root,{name:`${a}.action`,children:o.jsxs(pn,{value:g.action,onChange:te,placeholder:p({id:C("form.attribute.condition.action"),defaultMessage:"action"}),children:[o.jsxs(_t,{value:"show",children:["Show ",o.jsx("span",{style:{fontWeight:"bold"},children:d||a})]}),o.jsxs(_t,{value:"hide",children:["Hide ",o.jsx("span",{style:{fontWeight:"bold"},children:d||a})]})]})})})]})]})}):o.jsx(ce,{padding:4,margin:4,hasRadius:!0,background:"neutral0",borderColor:"neutral200",children:o.jsx(j1,{onClick:U})})},Jt={name:"name",type:"text",intlLabel:{id:"global.name",defaultMessage:"Name"},description:{id:C("modalForm.attribute.form.base.name.description"),defaultMessage:"No space is allowed for the name of the attribute"}},F1={sections:[{sectionTitle:null,items:[Jt]}]},fr={base(a=""){return[{sectionTitle:null,items:[{name:`${a}displayName`,type:"text",intlLabel:{id:C("contentType.displayName.label"),defaultMessage:"Display Name"}},{name:`${a}category`,type:"select-category",intlLabel:{id:C("modalForm.components.create-component.category.label"),defaultMessage:"Select a category or enter a name to create a new one"}}]},{sectionTitle:null,items:[{name:`${a}icon`,type:"icon-picker",size:12,intlLabel:{id:C("modalForm.components.icon.label"),defaultMessage:"Icon"}}]}]},advanced(){return[]}},Z={default:{name:"default",type:"text",intlLabel:{id:C("form.attribute.settings.default"),defaultMessage:"Default value"}},max:{name:"max",type:"checkbox-with-number-field",intlLabel:{id:C("form.attribute.item.maximum"),defaultMessage:"Maximum value"}},maxLength:{name:"maxLength",type:"checkbox-with-number-field",intlLabel:{id:C("form.attribute.item.maximumLength"),defaultMessage:"Maximum length"}},min:{name:"min",type:"checkbox-with-number-field",intlLabel:{id:C("form.attribute.item.minimum"),defaultMessage:"Minimum value"}},minLength:{name:"minLength",type:"checkbox-with-number-field",intlLabel:{id:C("form.attribute.item.minimumLength"),defaultMessage:"Minimum length"}},private:{name:"private",type:"checkbox",intlLabel:{id:C("form.attribute.item.privateField"),defaultMessage:"Private field"},description:{id:C("form.attribute.item.privateField.description"),defaultMessage:"This field will not show up in the API response"}},regex:{intlLabel:{id:C("form.attribute.item.text.regex"),defaultMessage:"RegExp pattern"},name:"regex",type:"text",description:{id:C("form.attribute.item.text.regex.description"),defaultMessage:"The text of the regular expression"}},required:{name:"required",type:"checkbox",intlLabel:{id:C("form.attribute.item.requiredField"),defaultMessage:"Required field"},description:{id:C("form.attribute.item.requiredField.description"),defaultMessage:"You won't be able to create an entry if this field is empty"}},unique:{name:"unique",type:"checkbox",intlLabel:{id:C("form.attribute.item.uniqueField"),defaultMessage:"Unique field"},description:{id:C("form.attribute.item.uniqueField.description"),defaultMessage:"You won't be able to create an entry if there is an existing entry with identical content"}}},it={sectionTitle:{id:C("form.attribute.condition.title"),defaultMessage:"Condition"},intlLabel:{id:C("form.attribute.condition.description"),defaultMessage:"Toggle field settings depending on the value of another boolean or enumeration field."},items:[{name:"conditions",type:"condition-form",intlLabel:{id:C("form.attribute.condition.label"),defaultMessage:"Conditions"},validations:{required:!0}}]},A1={blocks(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.private]},it]}},boolean(){return{sections:[{sectionTitle:null,items:[{autoFocus:!0,type:"select-default-boolean",intlLabel:{id:C("form.attribute.settings.default"),defaultMessage:"Default value"},name:"default",options:[{value:"true",key:"true",metadatas:{intlLabel:{id:"true",defaultMessage:"true"}}},{value:"",key:"null",metadatas:{intlLabel:{id:"null",defaultMessage:"null"}}},{value:"false",key:"false",metadatas:{intlLabel:{id:"false",defaultMessage:"false"}}}]}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.private]},it]}},component({repeatable:a},u){if(u==="1")return{sections:fr.advanced()};if(a){const l={...Z.min,intlLabel:{id:C("form.attribute.item.minimumComponents"),defaultMessage:"Minimum components"}},f={...Z.max,intlLabel:{id:C("form.attribute.item.maximumComponents"),defaultMessage:"Maximum components"}};return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.private,l,f]},it]}}return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.private]},it]}},date({type:a}){return{sections:[{sectionTitle:null,items:[{...Z.default,type:a||"date",value:null,withDefaultValue:!1,disabled:!a,autoFocus:!1}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.unique,Z.private]},it]}},dynamiczone(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.max,Z.min]},it]}},email(){return{sections:[{sectionTitle:null,items:[{...Z.default,type:"email"}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.unique,Z.maxLength,Z.minLength,Z.private]},it]}},enumeration(a){return{sections:[{sectionTitle:null,items:[{name:"default",type:"select",intlLabel:{id:C("form.attribute.settings.default"),defaultMessage:"Default value"},validations:{},options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"}}},...(a.enum||[]).filter((u,l)=>a.enum.indexOf(u)===l&&u).map(u=>({key:u,value:u,metadatas:{intlLabel:{id:`${u}.no-override`,defaultMessage:u}}}))]},{intlLabel:{id:C("form.attribute.item.enumeration.graphql"),defaultMessage:"Name override for GraphQL"},name:"enumName",type:"text",validations:{},description:{id:C("form.attribute.item.enumeration.graphql.description"),defaultMessage:"Allows you to override the default generated name for GraphQL"}}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.private]},it]}},json(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.private]},it]}},media(){return{sections:[{sectionTitle:null,items:[{intlLabel:{id:C("form.attribute.media.allowed-types"),defaultMessage:"Select allowed types of media"},name:"allowedTypes",type:"allowed-types-select",size:7,value:"",validations:{}}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.private]},it]}},number(a){const u=a.type==="decimal"||a.type==="float"?"any":1;return{sections:[{sectionTitle:null,items:[{autoFocus:!0,name:"default",type:a.type==="biginteger"?"text":"number",step:u,intlLabel:{id:C("form.attribute.settings.default"),defaultMessage:"Default value"},validations:{}}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.unique,Z.max,Z.min,Z.private]},it]}},password(){return{sections:[{sectionTitle:null,items:[Z.default]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.maxLength,Z.minLength,Z.private]},it]}},relation(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.private]},it]}},richtext(){return{sections:[{sectionTitle:null,items:[Z.default]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.maxLength,Z.minLength,Z.private]},it]}},text(){return{sections:[{sectionTitle:null,items:[Z.default,Z.regex]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.unique,Z.maxLength,Z.minLength,Z.private]},it]}},uid(a){return{sections:[{sectionTitle:null,items:[{...Z.default,disabled:!!a.targetField,type:"text"}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[Z.required,Z.maxLength,Z.minLength,Z.private,Z.regex]},it]}}},Eo={intlLabel:{id:"global.type",defaultMessage:"Type"},name:"createComponent",type:"boolean-radio-group",size:12,radios:[{title:{id:C("form.attribute.component.option.create"),defaultMessage:"Create a new component"},description:{id:C("form.attribute.component.option.create.description"),defaultMessage:"A component is shared across types and components, it will be available and accessible everywhere."},value:!0},{title:{id:C("form.attribute.component.option.reuse-existing"),defaultMessage:"Use an existing component"},description:{id:C("form.attribute.component.option.reuse-existing.description"),defaultMessage:"Reuse a component already created to keep your data consistent across content-types."},value:!1}]},R1={component(a,u){if(u==="1"){const l=a.createComponent===!0?fr.base("componentToCreate."):[];return{sections:[{sectionTitle:null,items:[Eo]},...l]}}return{sections:[{sectionTitle:null,items:[Jt,{name:"component",type:"select-component",intlLabel:{id:C("modalForm.attributes.select-component"),defaultMessage:"Select a component"},isMultiple:!1}]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"repeatable",type:"boolean-radio-group",size:12,radios:[{title:{id:C("form.attribute.component.option.repeatable"),defaultMessage:"Repeatable component"},description:{id:C("form.attribute.component.option.repeatable.description"),defaultMessage:"Best for multiple instances (array) of ingredients, meta tags, etc.."},value:!0},{title:{id:C("form.attribute.component.option.single"),defaultMessage:"Single component"},description:{id:C("form.attribute.component.option.single.description"),defaultMessage:"Best for grouping fields like full address, main information, etc..."},value:!1}]}]}]}},date(){return{sections:[{sectionTitle:null,items:[Jt,{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"type",type:"select-date",options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"},hidden:!0}},{key:"date",value:"date",metadatas:{intlLabel:{id:C("form.attribute.item.date.type.date"),defaultMessage:"date (ex: 01/01/{currentYear})",values:{currentYear:new Date().getFullYear()}}}},{key:"datetime",value:"datetime",metadatas:{intlLabel:{id:C("form.attribute.item.date.type.datetime"),defaultMessage:"datetime (ex: 01/01/{currentYear} 00:00 AM)",values:{currentYear:new Date().getFullYear()}}}},{key:"time",value:"time",metadatas:{intlLabel:{id:C("form.attribute.item.date.type.time"),defaultMessage:"time (ex: 00:00 AM)"}}}]}]}]}},enumeration(){return{sections:[{sectionTitle:null,items:[Jt]},{sectionTitle:null,items:[{name:"enum",type:"textarea-enum",size:6,intlLabel:{id:C("form.attribute.item.enumeration.rules"),defaultMessage:"Values (one line per value)"},placeholder:{id:C("form.attribute.item.enumeration.placeholder"),defaultMessage:`Ex:
morning
noon
evening`},validations:{required:!0}}]}]}},media(){return{sections:[{sectionTitle:null,items:[Jt]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"multiple",size:12,type:"boolean-radio-group",radios:[{title:{id:C("form.attribute.media.option.multiple"),defaultMessage:"Multiple media"},description:{id:C("form.attribute.media.option.multiple.description"),defaultMessage:"Best for sliders, carousels or multiple files download"},value:!0},{title:{id:C("form.attribute.media.option.single"),defaultMessage:"Single media"},description:{id:C("form.attribute.media.option.single.description"),defaultMessage:"Best for avatar, profile picture or cover"},value:!1}]}]}]}},number(){return{sections:[{sectionTitle:null,items:[Jt,{intlLabel:{id:C("form.attribute.item.number.type"),defaultMessage:"Number format"},name:"type",type:"select-number",options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"},hidden:!0}},{key:"integer",value:"integer",metadatas:{intlLabel:{id:C("form.attribute.item.number.type.integer"),defaultMessage:"integer (ex: 10)"}}},{key:"biginteger",value:"biginteger",metadatas:{intlLabel:{id:C("form.attribute.item.number.type.biginteger"),defaultMessage:"biginteger (ex: 123456789)"}}},{key:"decimal",value:"decimal",metadatas:{intlLabel:{id:C("form.attribute.item.number.type.decimal"),defaultMessage:"decimal (ex: 2.22)"}}},{key:"float",value:"float",metadatas:{intlLabel:{id:C("form.attribute.item.number.type.float"),defaultMessage:"decimal (ex: 3.3333333)"}}}]}]}]}},relation(){return{sections:[{sectionTitle:null,items:[{intlLabel:{id:"FIXME",defaultMessage:"FIXME"},name:"relation",size:12,type:"relation"}]}]}},string(){return{sections:[{sectionTitle:null,items:[Jt]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"type",size:12,type:"radio-group",radios:[{title:{id:C("form.attribute.text.option.short-text"),defaultMessage:"Sort text"},description:{id:C("form.attribute.text.option.short-text.description"),defaultMessage:"Best for titles, names, links (URL). It also enables exact search on the field."},value:"string"},{title:{id:C("form.attribute.text.option.long-text"),defaultMessage:"Long text"},description:{id:C("form.attribute.text.option.long-text.description"),defaultMessage:"Best for descriptions, biography. Exact search is disabled."},value:"text"}]}]}]}},text(){return{sections:[{sectionTitle:null,items:[Jt]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"type",size:12,type:"radio-group",radios:[{title:{id:C("form.attribute.text.option.short-text"),defaultMessage:"Sort text"},description:{id:C("form.attribute.text.option.short-text.description"),defaultMessage:"Best for titles, names, links (URL). It also enables exact search on the field."},value:"string"},{title:{id:C("form.attribute.text.option.long-text"),defaultMessage:"Long text"},description:{id:C("form.attribute.text.option.long-text.description"),defaultMessage:"Best for descriptions, biography. Exact search is disabled."},value:"text"}]}]}]}},uid(a,u,l){const f=l.filter(({type:d})=>["string","text"].includes(d)).map(({name:d})=>({key:d,value:d,metadatas:{intlLabel:{id:`${d}.no-override`,defaultMessage:d}}}));return{sections:[{sectionTitle:null,items:[{...Jt,placeholder:{id:C("modalForm.attribute.form.base.name.placeholder"),defaultMessage:"e.g. slug, seoUrl, canonicalUrl"}},{intlLabel:{id:C("modalForm.attribute.target-field"),defaultMessage:"Attached field"},name:"targetField",type:"select",options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"global.none",defaultMessage:"None"}}},...f]}]}]}}},El={advanced:A1,base:R1};var wo=Wx,S1=Ux(),$1=pc,N1=1/0,I1=wo&&1/$1(new wo([,-0]))[1]==N1?function(a){return new wo(a)}:S1,L1=I1,E1=zx,O1=Bx,D1=d0,q1=Vx,P1=L1,U1=pc,W1=200;function z1(a,u,l){var f=-1,d=O1,b=a.length,p=!0,g=[],v=g;if(l)p=!1,d=D1;else if(b>=W1){var w=u?null:P1(a);if(w)return U1(w);p=!1,d=q1,v=new E1}else v=u?[]:g;e:for(;++f<b;){var j=a[f],A=u?u(j):j;if(j=l||j!==0?j:0,p&&A===A){for(var O=v.length;O--;)if(v[O]===A)continue e;u&&v.push(A),g.push(j)}else d(v,A,l)||(v!==g&&v.push(A),g.push(j))}return g}var B1=z1,V1=B1;function H1(a){return a&&a.length?V1(a):[]}var G1=H1;const Z1=cc(G1),To=a=>a?Hx(a,{decamelize:!1,lowercase:!1,separator:"_"}):"";var Ba={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */Ba.exports;(function(a,u){(function(){function l(x,M,i){switch(i.length){case 0:return x.call(M);case 1:return x.call(M,i[0]);case 2:return x.call(M,i[0],i[1]);case 3:return x.call(M,i[0],i[1],i[2])}return x.apply(M,i)}function f(x,M,i,q){for(var X=-1,P=x==null?0:x.length;++X<P;){var dt=x[X];M(q,dt,i(dt),x)}return q}function d(x,M){for(var i=-1,q=x==null?0:x.length;++i<q&&M(x[i],i,x)!==!1;);return x}function b(x,M){for(var i=x==null?0:x.length;i--&&M(x[i],i,x)!==!1;);return x}function p(x,M){for(var i=-1,q=x==null?0:x.length;++i<q;)if(!M(x[i],i,x))return!1;return!0}function g(x,M){for(var i=-1,q=x==null?0:x.length,X=0,P=[];++i<q;){var dt=x[i];M(dt,i,x)&&(P[X++]=dt)}return P}function v(x,M){return!!(x!=null&&x.length)&&S(x,M,0)>-1}function w(x,M,i){for(var q=-1,X=x==null?0:x.length;++q<X;)if(i(M,x[q]))return!0;return!1}function j(x,M){for(var i=-1,q=x==null?0:x.length,X=Array(q);++i<q;)X[i]=M(x[i],i,x);return X}function A(x,M){for(var i=-1,q=M.length,X=x.length;++i<q;)x[X+i]=M[i];return x}function O(x,M,i,q){var X=-1,P=x==null?0:x.length;for(q&&P&&(i=x[++X]);++X<P;)i=M(i,x[X],X,x);return i}function K(x,M,i,q){var X=x==null?0:x.length;for(q&&X&&(i=x[--X]);X--;)i=M(i,x[X],X,x);return i}function T(x,M){for(var i=-1,q=x==null?0:x.length;++i<q;)if(M(x[i],i,x))return!0;return!1}function U(x){return x.split("")}function $(x){return x.match(Kc)||[]}function E(x,M,i){var q;return i(x,function(X,P,dt){if(M(X,P,dt))return q=P,!1}),q}function N(x,M,i,q){for(var X=x.length,P=i+(q?1:-1);q?P--:++P<X;)if(M(x[P],P,x))return P;return-1}function S(x,M,i){return M===M?gn(x,M,i):N(x,L,i)}function te(x,M,i,q){for(var X=i-1,P=x.length;++X<P;)if(q(x[X],M))return X;return-1}function L(x){return x!==x}function ne(x,M){var i=x==null?0:x.length;return i?et(x,M)/i:Jn}function he(x){return function(M){return M==null?m:M[x]}}function Fe(x){return function(M){return x==null?m:x[M]}}function Ae(x,M,i,q,X){return X(x,function(P,dt,ji){i=q?(q=!1,P):M(i,P,dt,ji)}),i}function Ve(x,M){var i=x.length;for(x.sort(M);i--;)x[i]=x[i].value;return x}function et(x,M){for(var i,q=-1,X=x.length;++q<X;){var P=M(x[q]);P!==m&&(i=i===m?P:i+P)}return i}function oe(x,M){for(var i=-1,q=Array(x);++i<x;)q[i]=M(i);return q}function V(x,M){return j(M,function(i){return[i,x[i]]})}function It(x){return x&&x.slice(0,Kn(x)+1).replace(hi,"")}function De(x){return function(M){return x(M)}}function Bt(x,M){return j(M,function(i){return x[i]})}function Ke(x,M){return x.has(M)}function mn(x,M){for(var i=-1,q=x.length;++i<q&&S(M,x[i],0)>-1;);return i}function G(x,M){for(var i=x.length;i--&&S(M,x[i],0)>-1;);return i}function fe(x,M){for(var i=x.length,q=0;i--;)x[i]===M&&++q;return q}function Xt(x){return"\\"+$d[x]}function B(x,M){return x==null?m:x[M]}function je(x){return Td.test(x)}function Lt(x){return Md.test(x)}function Vt(x){for(var M,i=[];!(M=x.next()).done;)i.push(M.value);return i}function Et(x){var M=-1,i=Array(x.size);return x.forEach(function(q,X){i[++M]=[X,q]}),i}function Qt(x,M){return function(i){return x(M(i))}}function bt(x,M){for(var i=-1,q=x.length,X=0,P=[];++i<q;){var dt=x[i];dt!==M&&dt!==$n||(x[i]=$n,P[X++]=i)}return P}function hn(x){var M=-1,i=Array(x.size);return x.forEach(function(q){i[++M]=q}),i}function Ka(x){var M=-1,i=Array(x.size);return x.forEach(function(q){i[++M]=[q,q]}),i}function gn(x,M,i){for(var q=i-1,X=x.length;++q<X;)if(x[q]===M)return q;return-1}function mr(x,M,i){for(var q=i+1;q--;)if(x[q]===M)return q;return q}function ct(x){return je(x)?z(x):Ed(x)}function Re(x){return je(x)?ye(x):U(x)}function Kn(x){for(var M=x.length;M--&&Vc.test(x.charAt(M)););return M}function z(x){for(var M=xi.lastIndex=0;xi.test(x);)++M;return M}function ye(x){return x.match(xi)||[]}function Ja(x){return x.match(wd)||[]}var m,Vr="4.17.21",Sn=200,Hr="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",tt="Expected a function",Ya="Invalid `variable` option passed into `_.template`",bn="__lodash_hash_undefined__",Gr=500,$n="__lodash_placeholder__",ot=1,en=2,yt=4,tn=1,Nn=2,st=1,yn=2,hr=4,wt=8,Ye=16,Ot=32,vn=64,Dt=128,In=256,gr=512,Xa=30,Zr="...",Qa=800,Kr=16,br=1,Jr=2,ei=3,nn=1/0,Ht=9007199254740991,ti=17976931348623157e292,Jn=NaN,Tt=4294967295,ni=Tt-1,Q=Tt>>>1,_e=[["ary",Dt],["bind",st],["bindKey",yn],["curry",wt],["curryRight",Ye],["flip",gr],["partial",Ot],["partialRight",vn],["rearg",In]],Se="[object Arguments]",ue="[object Array]",He="[object AsyncFunction]",$e="[object Boolean]",We="[object Date]",ri="[object DOMException]",Yn="[object Error]",Xn="[object Function]",ai="[object GeneratorFunction]",vt="[object Map]",yr="[object Number]",Rc="[object Null]",rn="[object Object]",Vo="[object Promise]",Sc="[object Proxy]",vr="[object RegExp]",qt="[object Set]",xr="[object String]",Yr="[object Symbol]",$c="[object Undefined]",Cr="[object WeakMap]",Nc="[object WeakSet]",_r="[object ArrayBuffer]",Qn="[object DataView]",ii="[object Float32Array]",oi="[object Float64Array]",si="[object Int8Array]",ui="[object Int16Array]",li="[object Int32Array]",ci="[object Uint8Array]",di="[object Uint8ClampedArray]",fi="[object Uint16Array]",pi="[object Uint32Array]",Ic=/\b__p \+= '';/g,Lc=/\b(__p \+=) '' \+/g,Ec=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Ho=/&(?:amp|lt|gt|quot|#39);/g,Go=/[&<>"']/g,Oc=RegExp(Ho.source),Dc=RegExp(Go.source),qc=/<%-([\s\S]+?)%>/g,Pc=/<%([\s\S]+?)%>/g,Zo=/<%=([\s\S]+?)%>/g,Uc=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Wc=/^\w*$/,zc=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,mi=/[\\^$.*+?()[\]{}|]/g,Bc=RegExp(mi.source),hi=/^\s+/,Vc=/\s/,Hc=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Gc=/\{\n\/\* \[wrapped with (.+)\] \*/,Zc=/,? & /,Kc=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Jc=/[()=,{}\[\]\/\s]/,Yc=/\\(\\)?/g,Xc=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Ko=/\w*$/,Qc=/^[-+]0x[0-9a-f]+$/i,ed=/^0b[01]+$/i,td=/^\[object .+?Constructor\]$/,nd=/^0o[0-7]+$/i,rd=/^(?:0|[1-9]\d*)$/,ad=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Xr=/($^)/,id=/['\n\r\u2028\u2029\\]/g,Qr="\\ud800-\\udfff",od="\\u0300-\\u036f",sd="\\ufe20-\\ufe2f",ud="\\u20d0-\\u20ff",Jo=od+sd+ud,Yo="\\u2700-\\u27bf",Xo="a-z\\xdf-\\xf6\\xf8-\\xff",ld="\\xac\\xb1\\xd7\\xf7",cd="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",dd="\\u2000-\\u206f",fd=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Qo="A-Z\\xc0-\\xd6\\xd8-\\xde",es="\\ufe0e\\ufe0f",ts=ld+cd+dd+fd,gi="['’]",pd="["+Qr+"]",ns="["+ts+"]",ea="["+Jo+"]",rs="\\d+",md="["+Yo+"]",as="["+Xo+"]",is="[^"+Qr+ts+rs+Yo+Xo+Qo+"]",bi="\\ud83c[\\udffb-\\udfff]",hd="(?:"+ea+"|"+bi+")",os="[^"+Qr+"]",yi="(?:\\ud83c[\\udde6-\\uddff]){2}",vi="[\\ud800-\\udbff][\\udc00-\\udfff]",er="["+Qo+"]",ss="\\u200d",us="(?:"+as+"|"+is+")",gd="(?:"+er+"|"+is+")",ls="(?:"+gi+"(?:d|ll|m|re|s|t|ve))?",cs="(?:"+gi+"(?:D|LL|M|RE|S|T|VE))?",ds=hd+"?",fs="["+es+"]?",bd="(?:"+ss+"(?:"+[os,yi,vi].join("|")+")"+fs+ds+")*",yd="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",vd="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",ps=fs+ds+bd,xd="(?:"+[md,yi,vi].join("|")+")"+ps,Cd="(?:"+[os+ea+"?",ea,yi,vi,pd].join("|")+")",_d=RegExp(gi,"g"),jd=RegExp(ea,"g"),xi=RegExp(bi+"(?="+bi+")|"+Cd+ps,"g"),wd=RegExp([er+"?"+as+"+"+ls+"(?="+[ns,er,"$"].join("|")+")",gd+"+"+cs+"(?="+[ns,er+us,"$"].join("|")+")",er+"?"+us+"+"+ls,er+"+"+cs,vd,yd,rs,xd].join("|"),"g"),Td=RegExp("["+ss+Qr+Jo+es+"]"),Md=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,kd=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Fd=-1,Te={};Te[ii]=Te[oi]=Te[si]=Te[ui]=Te[li]=Te[ci]=Te[di]=Te[fi]=Te[pi]=!0,Te[Se]=Te[ue]=Te[_r]=Te[$e]=Te[Qn]=Te[We]=Te[Yn]=Te[Xn]=Te[vt]=Te[yr]=Te[rn]=Te[vr]=Te[qt]=Te[xr]=Te[Cr]=!1;var we={};we[Se]=we[ue]=we[_r]=we[Qn]=we[$e]=we[We]=we[ii]=we[oi]=we[si]=we[ui]=we[li]=we[vt]=we[yr]=we[rn]=we[vr]=we[qt]=we[xr]=we[Yr]=we[ci]=we[di]=we[fi]=we[pi]=!0,we[Yn]=we[Xn]=we[Cr]=!1;var Ad={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},Rd={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Sd={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},$d={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Nd=parseFloat,Id=parseInt,ms=typeof qr=="object"&&qr&&qr.Object===Object&&qr,Ld=typeof self=="object"&&self&&self.Object===Object&&self,Xe=ms||Ld||Function("return this")(),Ci=u&&!u.nodeType&&u,Ln=Ci&&!0&&a&&!a.nodeType&&a,hs=Ln&&Ln.exports===Ci,_i=hs&&ms.process,Mt=function(){try{var x=Ln&&Ln.require&&Ln.require("util").types;return x||_i&&_i.binding&&_i.binding("util")}catch{}}(),gs=Mt&&Mt.isArrayBuffer,bs=Mt&&Mt.isDate,ys=Mt&&Mt.isMap,vs=Mt&&Mt.isRegExp,xs=Mt&&Mt.isSet,Cs=Mt&&Mt.isTypedArray,Ed=he("length"),Od=Fe(Ad),Dd=Fe(Rd),qd=Fe(Sd),Pd=function x(M){function i(e){if(qe(e)&&!ie(e)&&!(e instanceof P)){if(e instanceof X)return e;if(Ce.call(e,"__wrapped__"))return hu(e)}return new X(e)}function q(){}function X(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=m}function P(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=Tt,this.__views__=[]}function dt(){var e=new P(this.__wrapped__);return e.__actions__=ft(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=ft(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=ft(this.__views__),e}function ji(){if(this.__filtered__){var e=new P(this);e.__dir__=-1,e.__filtered__=!0}else e=this.clone(),e.__dir__*=-1;return e}function Ud(){var e=this.__wrapped__.value(),t=this.__dir__,n=ie(e),r=t<0,s=n?e.length:0,c=Yf(0,s,this.__views__),h=c.start,y=c.end,_=y-h,F=r?y:h-1,k=this.__iteratees__,R=k.length,D=0,W=nt(_,this.__takeCount__);if(!n||!r&&s==_&&W==_)return Ws(e,this.__actions__);var J=[];e:for(;_--&&D<W;){F+=t;for(var se=-1,Y=e[F];++se<R;){var pe=k[se],me=pe.iteratee,at=pe.type,gt=me(Y);if(at==Jr)Y=gt;else if(!gt){if(at==br)continue e;break e}}J[D++]=Y}return J}function En(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Wd(){this.__data__=Ir?Ir(null):{},this.size=0}function zd(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}function Bd(e){var t=this.__data__;if(Ir){var n=t[e];return n===bn?m:n}return Ce.call(t,e)?t[e]:m}function Vd(e){var t=this.__data__;return Ir?t[e]!==m:Ce.call(t,e)}function Hd(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Ir&&t===m?bn:t,this}function an(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Gd(){this.__data__=[],this.size=0}function Zd(e){var t=this.__data__,n=ta(t,e);return!(n<0)&&(n==t.length-1?t.pop():Aa.call(t,n,1),--this.size,!0)}function Kd(e){var t=this.__data__,n=ta(t,e);return n<0?m:t[n][1]}function Jd(e){return ta(this.__data__,e)>-1}function Yd(e,t){var n=this.__data__,r=ta(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this}function on(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Xd(){this.size=0,this.__data__={hash:new En,map:new($r||an),string:new En}}function Qd(e){var t=pa(this,e).delete(e);return this.size-=t?1:0,t}function ef(e){return pa(this,e).get(e)}function tf(e){return pa(this,e).has(e)}function nf(e,t){var n=pa(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this}function On(e){var t=-1,n=e==null?0:e.length;for(this.__data__=new on;++t<n;)this.add(e[t])}function rf(e){return this.__data__.set(e,bn),this}function af(e){return this.__data__.has(e)}function Pt(e){this.size=(this.__data__=new an(e)).size}function of(){this.__data__=new an,this.size=0}function sf(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}function uf(e){return this.__data__.get(e)}function lf(e){return this.__data__.has(e)}function cf(e,t){var n=this.__data__;if(n instanceof an){var r=n.__data__;if(!$r||r.length<Sn-1)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new on(r)}return n.set(e,t),this.size=n.size,this}function _s(e,t){var n=ie(e),r=!n&&zn(e),s=!n&&!r&&Tn(e),c=!n&&!r&&!s&&dr(e),h=n||r||s||c,y=h?oe(e.length,og):[],_=y.length;for(var F in e)!t&&!Ce.call(e,F)||h&&(F=="length"||s&&(F=="offset"||F=="parent")||c&&(F=="buffer"||F=="byteLength"||F=="byteOffset")||cn(F,_))||y.push(F);return y}function js(e){var t=e.length;return t?e[Ni(0,t-1)]:m}function df(e,t){return ma(ft(e),Dn(t,0,e.length))}function ff(e){return ma(ft(e))}function wi(e,t,n){(n===m||Ut(e[t],n))&&(n!==m||t in e)||sn(e,t,n)}function jr(e,t,n){var r=e[t];Ce.call(e,t)&&Ut(r,n)&&(n!==m||t in e)||sn(e,t,n)}function ta(e,t){for(var n=e.length;n--;)if(Ut(e[n][0],t))return n;return-1}function pf(e,t,n,r){return wn(e,function(s,c,h){t(r,s,n(s),h)}),r}function ws(e,t){return e&&Zt(t,Je(t),e)}function mf(e,t){return e&&Zt(t,mt(t),e)}function sn(e,t,n){t=="__proto__"&&Ra?Ra(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function Ti(e,t){for(var n=-1,r=t.length,s=Ge(r),c=e==null;++n<r;)s[n]=c?m:Xi(e,t[n]);return s}function Dn(e,t,n){return e===e&&(n!==m&&(e=e<=n?e:n),t!==m&&(e=e>=t?e:t)),e}function kt(e,t,n,r,s,c){var h,y=t&ot,_=t&en,F=t&yt;if(n&&(h=s?n(e,r,s,c):n(e)),h!==m)return h;if(!Ie(e))return e;var k=ie(e);if(k){if(h=Qf(e),!y)return ft(e,h)}else{var R=rt(e),D=R==Xn||R==ai;if(Tn(e))return Bs(e,y);if(R==rn||R==Se||D&&!s){if(h=_||D?{}:uu(e),!y)return _?zf(e,mf(h,e)):Wf(e,ws(h,e))}else{if(!we[R])return s?e:{};h=ep(e,R,y)}}c||(c=new Pt);var W=c.get(e);if(W)return W;c.set(e,h),il(e)?e.forEach(function(Y){h.add(kt(Y,t,n,Y,e,c))}):al(e)&&e.forEach(function(Y,pe){h.set(pe,kt(Y,t,n,pe,e,c))});var J=F?_?Bi:zi:_?mt:Je,se=k?m:J(e);return d(se||e,function(Y,pe){se&&(pe=Y,Y=e[pe]),jr(h,pe,kt(Y,t,n,pe,e,c))}),h}function hf(e){var t=Je(e);return function(n){return Ts(n,e,t)}}function Ts(e,t,n){var r=n.length;if(e==null)return!r;for(e=Me(e);r--;){var s=n[r],c=t[s],h=e[s];if(h===m&&!(s in e)||!c(h))return!1}return!0}function Ms(e,t,n){if(typeof e!="function")throw new St(tt);return Er(function(){e.apply(m,n)},t)}function wr(e,t,n,r){var s=-1,c=v,h=!0,y=e.length,_=[],F=t.length;if(!y)return _;n&&(t=j(t,De(n))),r?(c=w,h=!1):t.length>=Sn&&(c=Ke,h=!1,t=new On(t));e:for(;++s<y;){var k=e[s],R=n==null?k:n(k);if(k=r||k!==0?k:0,h&&R===R){for(var D=F;D--;)if(t[D]===R)continue e;_.push(k)}else c(t,R,r)||_.push(k)}return _}function gf(e,t){var n=!0;return wn(e,function(r,s,c){return n=!!t(r,s,c)}),n}function na(e,t,n){for(var r=-1,s=e.length;++r<s;){var c=e[r],h=t(c);if(h!=null&&(y===m?h===h&&!Ct(h):n(h,y)))var y=h,_=c}return _}function bf(e,t,n,r){var s=e.length;for(n=le(n),n<0&&(n=-n>s?0:s+n),r=r===m||r>s?s:le(r),r<0&&(r+=s),r=n>r?0:Nu(r);n<r;)e[n++]=t;return e}function ks(e,t){var n=[];return wn(e,function(r,s,c){t(r,s,c)&&n.push(r)}),n}function Qe(e,t,n,r,s){var c=-1,h=e.length;for(n||(n=np),s||(s=[]);++c<h;){var y=e[c];t>0&&n(y)?t>1?Qe(y,t-1,n,r,s):A(s,y):r||(s[s.length]=y)}return s}function Gt(e,t){return e&&fo(e,t,Je)}function Mi(e,t){return e&&Ju(e,t,Je)}function ra(e,t){return g(t,function(n){return dn(e[n])})}function qn(e,t){t=Cn(t,e);for(var n=0,r=t.length;e!=null&&n<r;)e=e[Kt(t[n++])];return n&&n==r?e:m}function Fs(e,t,n){var r=t(e);return ie(e)?r:A(r,n(e))}function ut(e){return e==null?e===m?$c:Rc:Wn&&Wn in Me(e)?Jf(e):up(e)}function ki(e,t){return e>t}function yf(e,t){return e!=null&&Ce.call(e,t)}function vf(e,t){return e!=null&&t in Me(e)}function xf(e,t,n){return e>=nt(t,n)&&e<Ze(t,n)}function Fi(e,t,n){for(var r=n?w:v,s=e[0].length,c=e.length,h=c,y=Ge(c),_=1/0,F=[];h--;){var k=e[h];h&&t&&(k=j(k,De(t))),_=nt(k.length,_),y[h]=!n&&(t||s>=120&&k.length>=120)?new On(h&&k):m}k=e[0];var R=-1,D=y[0];e:for(;++R<s&&F.length<_;){var W=k[R],J=t?t(W):W;if(W=n||W!==0?W:0,!(D?Ke(D,J):r(F,J,n))){for(h=c;--h;){var se=y[h];if(!(se?Ke(se,J):r(e[h],J,n)))continue e}D&&D.push(J),F.push(W)}}return F}function Cf(e,t,n,r){return Gt(e,function(s,c,h){t(r,n(s),c,h)}),r}function Tr(e,t,n){t=Cn(t,e),e=fu(e,t);var r=e==null?e:e[Kt(At(t))];return r==null?m:l(r,e,n)}function As(e){return qe(e)&&ut(e)==Se}function _f(e){return qe(e)&&ut(e)==_r}function jf(e){return qe(e)&&ut(e)==We}function Mr(e,t,n,r,s){return e===t||(e==null||t==null||!qe(e)&&!qe(t)?e!==e&&t!==t:wf(e,t,n,r,Mr,s))}function wf(e,t,n,r,s,c){var h=ie(e),y=ie(t),_=h?ue:rt(e),F=y?ue:rt(t);_=_==Se?rn:_,F=F==Se?rn:F;var k=_==rn,R=F==rn,D=_==F;if(D&&Tn(e)){if(!Tn(t))return!1;h=!0,k=!1}if(D&&!k)return c||(c=new Pt),h||dr(e)?ou(e,t,n,r,s,c):Zf(e,t,_,n,r,s,c);if(!(n&tn)){var W=k&&Ce.call(e,"__wrapped__"),J=R&&Ce.call(t,"__wrapped__");if(W||J){var se=W?e.value():e,Y=J?t.value():t;return c||(c=new Pt),s(se,Y,n,r,c)}}return!!D&&(c||(c=new Pt),Kf(e,t,n,r,s,c))}function Tf(e){return qe(e)&&rt(e)==vt}function Ai(e,t,n,r){var s=n.length,c=s,h=!r;if(e==null)return!c;for(e=Me(e);s--;){var y=n[s];if(h&&y[2]?y[1]!==e[y[0]]:!(y[0]in e))return!1}for(;++s<c;){y=n[s];var _=y[0],F=e[_],k=y[1];if(h&&y[2]){if(F===m&&!(_ in e))return!1}else{var R=new Pt;if(r)var D=r(F,k,_,e,t,R);if(!(D===m?Mr(k,F,tn|Nn,r,R):D))return!1}}return!0}function Rs(e){return!(!Ie(e)||ap(e))&&(dn(e)?dg:td).test(Un(e))}function Mf(e){return qe(e)&&ut(e)==vr}function kf(e){return qe(e)&&rt(e)==qt}function Ff(e){return qe(e)&&va(e.length)&&!!Te[ut(e)]}function Ss(e){return typeof e=="function"?e:e==null?ht:typeof e=="object"?ie(e)?Is(e[0],e[1]):Ns(e):qu(e)}function Ri(e){if(!Ar(e))return bg(e);var t=[];for(var n in Me(e))Ce.call(e,n)&&n!="constructor"&&t.push(n);return t}function Af(e){if(!Ie(e))return sp(e);var t=Ar(e),n=[];for(var r in e)(r!="constructor"||!t&&Ce.call(e,r))&&n.push(r);return n}function Si(e,t){return e<t}function $s(e,t){var n=-1,r=pt(e)?Ge(e.length):[];return wn(e,function(s,c,h){r[++n]=t(s,c,h)}),r}function Ns(e){var t=Vi(e);return t.length==1&&t[0][2]?cu(t[0][0],t[0][1]):function(n){return n===e||Ai(n,e,t)}}function Is(e,t){return Hi(e)&&lu(t)?cu(Kt(e),t):function(n){var r=Xi(n,e);return r===m&&r===t?Qi(n,e):Mr(t,r,tn|Nn)}}function aa(e,t,n,r,s){e!==t&&fo(t,function(c,h){if(s||(s=new Pt),Ie(c))Rf(e,t,h,n,aa,r,s);else{var y=r?r(Zi(e,h),c,h+"",e,t,s):m;y===m&&(y=c),wi(e,h,y)}},mt)}function Rf(e,t,n,r,s,c,h){var y=Zi(e,n),_=Zi(t,n),F=h.get(_);if(F)return wi(e,n,F),m;var k=c?c(y,_,n+"",e,t,h):m,R=k===m;if(R){var D=ie(_),W=!D&&Tn(_),J=!D&&!W&&dr(_);k=_,D||W||J?ie(y)?k=y:ze(y)?k=ft(y):W?(R=!1,k=Bs(_,!0)):J?(R=!1,k=Vs(_,!0)):k=[]:Rr(_)||zn(_)?(k=y,zn(y)?k=Iu(y):Ie(y)&&!dn(y)||(k=uu(_))):R=!1}R&&(h.set(_,k),s(k,_,r,c,h),h.delete(_)),wi(e,n,k)}function Ls(e,t){var n=e.length;if(n)return t+=t<0?n:0,cn(t,n)?e[t]:m}function Es(e,t,n){t=t.length?j(t,function(s){return ie(s)?function(c){return qn(c,s.length===1?s[0]:s)}:s}):[ht];var r=-1;return t=j(t,De(ee())),Ve($s(e,function(s,c,h){return{criteria:j(t,function(y){return y(s)}),index:++r,value:s}}),function(s,c){return Uf(s,c,n)})}function Sf(e,t){return Os(e,t,function(n,r){return Qi(e,r)})}function Os(e,t,n){for(var r=-1,s=t.length,c={};++r<s;){var h=t[r],y=qn(e,h);n(y,h)&&kr(c,Cn(h,e),y)}return c}function $f(e){return function(t){return qn(t,e)}}function $i(e,t,n,r){var s=r?te:S,c=-1,h=t.length,y=e;for(e===t&&(t=ft(t)),n&&(y=j(e,De(n)));++c<h;)for(var _=0,F=t[c],k=n?n(F):F;(_=s(y,k,_,r))>-1;)y!==e&&Aa.call(y,_,1),Aa.call(e,_,1);return e}function Ds(e,t){for(var n=e?t.length:0,r=n-1;n--;){var s=t[n];if(n==r||s!==c){var c=s;cn(s)?Aa.call(e,s,1):Ei(e,s)}}return e}function Ni(e,t){return e+$a(Gu()*(t-e+1))}function Nf(e,t,n,r){for(var s=-1,c=Ze(Sa((t-e)/(n||1)),0),h=Ge(c);c--;)h[r?c:++s]=e,e+=n;return h}function Ii(e,t){var n="";if(!e||t<1||t>Ht)return n;do t%2&&(n+=e),t=$a(t/2),t&&(e+=e);while(t);return n}function de(e,t){return ho(du(e,t,ht),e+"")}function If(e){return js(ir(e))}function Lf(e,t){var n=ir(e);return ma(n,Dn(t,0,n.length))}function kr(e,t,n,r){if(!Ie(e))return e;t=Cn(t,e);for(var s=-1,c=t.length,h=c-1,y=e;y!=null&&++s<c;){var _=Kt(t[s]),F=n;if(_==="__proto__"||_==="constructor"||_==="prototype")return e;if(s!=h){var k=y[_];F=r?r(k,_,y):m,F===m&&(F=Ie(k)?k:cn(t[s+1])?[]:{})}jr(y,_,F),y=y[_]}return e}function Ef(e){return ma(ir(e))}function Ft(e,t,n){var r=-1,s=e.length;t<0&&(t=-t>s?0:s+t),n=n>s?s:n,n<0&&(n+=s),s=t>n?0:n-t>>>0,t>>>=0;for(var c=Ge(s);++r<s;)c[r]=e[r+t];return c}function Of(e,t){var n;return wn(e,function(r,s,c){return n=t(r,s,c),!n}),!!n}function ia(e,t,n){var r=0,s=e==null?r:e.length;if(typeof t=="number"&&t===t&&s<=Q){for(;r<s;){var c=r+s>>>1,h=e[c];h!==null&&!Ct(h)&&(n?h<=t:h<t)?r=c+1:s=c}return s}return Li(e,t,ht,n)}function Li(e,t,n,r){var s=0,c=e==null?0:e.length;if(c===0)return 0;t=n(t);for(var h=t!==t,y=t===null,_=Ct(t),F=t===m;s<c;){var k=$a((s+c)/2),R=n(e[k]),D=R!==m,W=R===null,J=R===R,se=Ct(R);if(h)var Y=r||J;else Y=F?J&&(r||D):y?J&&D&&(r||!W):_?J&&D&&!W&&(r||!se):!W&&!se&&(r?R<=t:R<t);Y?s=k+1:c=k}return nt(c,ni)}function qs(e,t){for(var n=-1,r=e.length,s=0,c=[];++n<r;){var h=e[n],y=t?t(h):h;if(!n||!Ut(y,_)){var _=y;c[s++]=h===0?0:h}}return c}function Ps(e){return typeof e=="number"?e:Ct(e)?Jn:+e}function xt(e){if(typeof e=="string")return e;if(ie(e))return j(e,xt)+"";if(Ct(e))return Zu?Zu.call(e):"";var t=e+"";return t=="0"&&1/e==-nn?"-0":t}function xn(e,t,n){var r=-1,s=v,c=e.length,h=!0,y=[],_=y;if(n)h=!1,s=w;else if(c>=Sn){var F=t?null:Fg(e);if(F)return hn(F);h=!1,s=Ke,_=new On}else _=t?[]:y;e:for(;++r<c;){var k=e[r],R=t?t(k):k;if(k=n||k!==0?k:0,h&&R===R){for(var D=_.length;D--;)if(_[D]===R)continue e;t&&_.push(R),y.push(k)}else s(_,R,n)||(_!==y&&_.push(R),y.push(k))}return y}function Ei(e,t){return t=Cn(t,e),e=fu(e,t),e==null||delete e[Kt(At(t))]}function Us(e,t,n,r){return kr(e,t,n(qn(e,t)),r)}function oa(e,t,n,r){for(var s=e.length,c=r?s:-1;(r?c--:++c<s)&&t(e[c],c,e););return n?Ft(e,r?0:c,r?c+1:s):Ft(e,r?c+1:0,r?s:c)}function Ws(e,t){var n=e;return n instanceof P&&(n=n.value()),O(t,function(r,s){return s.func.apply(s.thisArg,A([r],s.args))},n)}function Oi(e,t,n){var r=e.length;if(r<2)return r?xn(e[0]):[];for(var s=-1,c=Ge(r);++s<r;)for(var h=e[s],y=-1;++y<r;)y!=s&&(c[s]=wr(c[s]||h,e[y],t,n));return xn(Qe(c,1),t,n)}function zs(e,t,n){for(var r=-1,s=e.length,c=t.length,h={};++r<s;)n(h,e[r],r<c?t[r]:m);return h}function Di(e){return ze(e)?e:[]}function qi(e){return typeof e=="function"?e:ht}function Cn(e,t){return ie(e)?e:Hi(e,t)?[e]:tl(xe(e))}function _n(e,t,n){var r=e.length;return n=n===m?r:n,!t&&n>=r?e:Ft(e,t,n)}function Bs(e,t){if(t)return e.slice();var n=e.length,r=Wu?Wu(n):new e.constructor(n);return e.copy(r),r}function Pi(e){var t=new e.constructor(e.byteLength);return new ka(t).set(new ka(e)),t}function Df(e,t){return new e.constructor(t?Pi(e.buffer):e.buffer,e.byteOffset,e.byteLength)}function qf(e){var t=new e.constructor(e.source,Ko.exec(e));return t.lastIndex=e.lastIndex,t}function Pf(e){return Lr?Me(Lr.call(e)):{}}function Vs(e,t){return new e.constructor(t?Pi(e.buffer):e.buffer,e.byteOffset,e.length)}function Hs(e,t){if(e!==t){var n=e!==m,r=e===null,s=e===e,c=Ct(e),h=t!==m,y=t===null,_=t===t,F=Ct(t);if(!y&&!F&&!c&&e>t||c&&h&&_&&!y&&!F||r&&h&&_||!n&&_||!s)return 1;if(!r&&!c&&!F&&e<t||F&&n&&s&&!r&&!c||y&&n&&s||!h&&s||!_)return-1}return 0}function Uf(e,t,n){for(var r=-1,s=e.criteria,c=t.criteria,h=s.length,y=n.length;++r<h;){var _=Hs(s[r],c[r]);if(_)return r>=y?_:_*(n[r]=="desc"?-1:1)}return e.index-t.index}function Gs(e,t,n,r){for(var s=-1,c=e.length,h=n.length,y=-1,_=t.length,F=Ze(c-h,0),k=Ge(_+F),R=!r;++y<_;)k[y]=t[y];for(;++s<h;)(R||s<c)&&(k[n[s]]=e[s]);for(;F--;)k[y++]=e[s++];return k}function Zs(e,t,n,r){for(var s=-1,c=e.length,h=-1,y=n.length,_=-1,F=t.length,k=Ze(c-y,0),R=Ge(k+F),D=!r;++s<k;)R[s]=e[s];for(var W=s;++_<F;)R[W+_]=t[_];for(;++h<y;)(D||s<c)&&(R[W+n[h]]=e[s++]);return R}function ft(e,t){var n=-1,r=e.length;for(t||(t=Ge(r));++n<r;)t[n]=e[n];return t}function Zt(e,t,n,r){var s=!n;n||(n={});for(var c=-1,h=t.length;++c<h;){var y=t[c],_=r?r(n[y],e[y],y,n,e):m;_===m&&(_=e[y]),s?sn(n,y,_):jr(n,y,_)}return n}function Wf(e,t){return Zt(e,mo(e),t)}function zf(e,t){return Zt(e,Qu(e),t)}function sa(e,t){return function(n,r){var s=ie(n)?f:pf,c=t?t():{};return s(n,e,ee(r,2),c)}}function nr(e){return de(function(t,n){var r=-1,s=n.length,c=s>1?n[s-1]:m,h=s>2?n[2]:m;for(c=e.length>3&&typeof c=="function"?(s--,c):m,h&&lt(n[0],n[1],h)&&(c=s<3?m:c,s=1),t=Me(t);++r<s;){var y=n[r];y&&e(t,y,r,c)}return t})}function Ks(e,t){return function(n,r){if(n==null)return n;if(!pt(n))return e(n,r);for(var s=n.length,c=t?s:-1,h=Me(n);(t?c--:++c<s)&&r(h[c],c,h)!==!1;);return n}}function Js(e){return function(t,n,r){for(var s=-1,c=Me(t),h=r(t),y=h.length;y--;){var _=h[e?y:++s];if(n(c[_],_,c)===!1)break}return t}}function Bf(e,t,n){function r(){return(this&&this!==Xe&&this instanceof r?c:e).apply(s?n:this,arguments)}var s=t&st,c=Fr(e);return r}function Ys(e){return function(t){t=xe(t);var n=je(t)?Re(t):m,r=n?n[0]:t.charAt(0),s=n?_n(n,1).join(""):t.slice(1);return r[e]()+s}}function rr(e){return function(t){return O(Du(Ou(t).replace(_d,"")),e,"")}}function Fr(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=cr(e.prototype),r=e.apply(n,t);return Ie(r)?r:n}}function Vf(e,t,n){function r(){for(var c=arguments.length,h=Ge(c),y=c,_=ar(r);y--;)h[y]=arguments[y];var F=c<3&&h[0]!==_&&h[c-1]!==_?[]:bt(h,_);return c-=F.length,c<n?nu(e,t,ua,r.placeholder,m,h,F,m,m,n-c):l(this&&this!==Xe&&this instanceof r?s:e,this,h)}var s=Fr(e);return r}function Xs(e){return function(t,n,r){var s=Me(t);if(!pt(t)){var c=ee(n,3);t=Je(t),n=function(y){return c(s[y],y,s)}}var h=e(t,n,r);return h>-1?s[c?t[h]:h]:m}}function Qs(e){return ln(function(t){var n=t.length,r=n,s=X.prototype.thru;for(e&&t.reverse();r--;){var c=t[r];if(typeof c!="function")throw new St(tt);if(s&&!h&&fa(c)=="wrapper")var h=new X([],!0)}for(r=h?r:n;++r<n;){c=t[r];var y=fa(c),_=y=="wrapper"?po(c):m;h=_&&Gi(_[0])&&_[1]==(Dt|wt|Ot|In)&&!_[4].length&&_[9]==1?h[fa(_[0])].apply(h,_[3]):c.length==1&&Gi(c)?h[y]():h.thru(c)}return function(){var F=arguments,k=F[0];if(h&&F.length==1&&ie(k))return h.plant(k).value();for(var R=0,D=n?t[R].apply(this,F):k;++R<n;)D=t[R].call(this,D);return D}})}function ua(e,t,n,r,s,c,h,y,_,F){function k(){for(var pe=arguments.length,me=Ge(pe),at=pe;at--;)me[at]=arguments[at];if(J)var gt=ar(k),Mn=fe(me,gt);if(r&&(me=Gs(me,r,s,J)),c&&(me=Zs(me,c,h,J)),pe-=Mn,J&&pe<F)return nu(e,t,ua,k.placeholder,n,me,bt(me,gt),y,_,F-pe);var Pe=D?n:this,$t=W?Pe[e]:e;return pe=me.length,y?me=lp(me,y):se&&pe>1&&me.reverse(),R&&_<pe&&(me.length=_),this&&this!==Xe&&this instanceof k&&($t=Y||Fr($t)),$t.apply(Pe,me)}var R=t&Dt,D=t&st,W=t&yn,J=t&(wt|Ye),se=t&gr,Y=W?m:Fr(e);return k}function eu(e,t){return function(n,r){return Cf(n,e,t(r),{})}}function la(e,t){return function(n,r){var s;if(n===m&&r===m)return t;if(n!==m&&(s=n),r!==m){if(s===m)return r;typeof n=="string"||typeof r=="string"?(n=xt(n),r=xt(r)):(n=Ps(n),r=Ps(r)),s=e(n,r)}return s}}function Ui(e){return ln(function(t){return t=j(t,De(ee())),de(function(n){var r=this;return e(t,function(s){return l(s,r,n)})})})}function ca(e,t){t=t===m?" ":xt(t);var n=t.length;if(n<2)return n?Ii(t,e):t;var r=Ii(t,Sa(e/ct(t)));return je(t)?_n(Re(r),0,e).join(""):r.slice(0,e)}function Hf(e,t,n,r){function s(){for(var y=-1,_=arguments.length,F=-1,k=r.length,R=Ge(k+_),D=this&&this!==Xe&&this instanceof s?h:e;++F<k;)R[F]=r[F];for(;_--;)R[F++]=arguments[++y];return l(D,c?n:this,R)}var c=t&st,h=Fr(e);return s}function tu(e){return function(t,n,r){return r&&typeof r!="number"&&lt(t,n,r)&&(n=r=m),t=fn(t),n===m?(n=t,t=0):n=fn(n),r=r===m?t<n?1:-1:fn(r),Nf(t,n,r,e)}}function da(e){return function(t,n){return typeof t=="string"&&typeof n=="string"||(t=Rt(t),n=Rt(n)),e(t,n)}}function nu(e,t,n,r,s,c,h,y,_,F){var k=t&wt,R=k?h:m,D=k?m:h,W=k?c:m,J=k?m:c;t|=k?Ot:vn,t&=~(k?vn:Ot),t&hr||(t&=-4);var se=[e,t,s,W,R,J,D,y,_,F],Y=n.apply(m,se);return Gi(e)&&el(Y,se),Y.placeholder=r,pu(Y,e,t)}function Wi(e){var t=or[e];return function(n,r){if(n=Rt(n),r=r==null?0:nt(le(r),292),r&&Hu(n)){var s=(xe(n)+"e").split("e");return s=(xe(t(s[0]+"e"+(+s[1]+r)))+"e").split("e"),+(s[0]+"e"+(+s[1]-r))}return t(n)}}function ru(e){return function(t){var n=rt(t);return n==vt?Et(t):n==qt?Ka(t):V(t,e(t))}}function un(e,t,n,r,s,c,h,y){var _=t&yn;if(!_&&typeof e!="function")throw new St(tt);var F=r?r.length:0;if(F||(t&=-97,r=s=m),h=h===m?h:Ze(le(h),0),y=y===m?y:le(y),F-=s?s.length:0,t&vn){var k=r,R=s;r=s=m}var D=_?m:po(e),W=[e,t,n,r,s,k,R,c,h,y];if(D&&op(W,D),e=W[0],t=W[1],n=W[2],r=W[3],s=W[4],y=W[9]=W[9]===m?_?0:e.length:Ze(W[9]-F,0),!y&&t&(wt|Ye)&&(t&=-25),t&&t!=st)J=t==wt||t==Ye?Vf(e,t,y):t!=Ot&&t!=(st|Ot)||s.length?ua.apply(m,W):Hf(e,t,n,r);else var J=Bf(e,t,n);return pu((D?Yu:el)(J,W),e,t)}function au(e,t,n,r){return e===m||Ut(e,sr[n])&&!Ce.call(r,n)?t:e}function iu(e,t,n,r,s,c){return Ie(e)&&Ie(t)&&(c.set(t,e),aa(e,t,m,iu,c),c.delete(t)),e}function Gf(e){return Rr(e)?m:e}function ou(e,t,n,r,s,c){var h=n&tn,y=e.length,_=t.length;if(y!=_&&!(h&&_>y))return!1;var F=c.get(e),k=c.get(t);if(F&&k)return F==t&&k==e;var R=-1,D=!0,W=n&Nn?new On:m;for(c.set(e,t),c.set(t,e);++R<y;){var J=e[R],se=t[R];if(r)var Y=h?r(se,J,R,t,e,c):r(J,se,R,e,t,c);if(Y!==m){if(Y)continue;D=!1;break}if(W){if(!T(t,function(pe,me){if(!Ke(W,me)&&(J===pe||s(J,pe,n,r,c)))return W.push(me)})){D=!1;break}}else if(J!==se&&!s(J,se,n,r,c)){D=!1;break}}return c.delete(e),c.delete(t),D}function Zf(e,t,n,r,s,c,h){switch(n){case Qn:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case _r:return!(e.byteLength!=t.byteLength||!c(new ka(e),new ka(t)));case $e:case We:case yr:return Ut(+e,+t);case Yn:return e.name==t.name&&e.message==t.message;case vr:case xr:return e==t+"";case vt:var y=Et;case qt:var _=r&tn;if(y||(y=hn),e.size!=t.size&&!_)return!1;var F=h.get(e);if(F)return F==t;r|=Nn,h.set(e,t);var k=ou(y(e),y(t),r,s,c,h);return h.delete(e),k;case Yr:if(Lr)return Lr.call(e)==Lr.call(t)}return!1}function Kf(e,t,n,r,s,c){var h=n&tn,y=zi(e),_=y.length;if(_!=zi(t).length&&!h)return!1;for(var F=_;F--;){var k=y[F];if(!(h?k in t:Ce.call(t,k)))return!1}var R=c.get(e),D=c.get(t);if(R&&D)return R==t&&D==e;var W=!0;c.set(e,t),c.set(t,e);for(var J=h;++F<_;){k=y[F];var se=e[k],Y=t[k];if(r)var pe=h?r(Y,se,k,t,e,c):r(se,Y,k,e,t,c);if(!(pe===m?se===Y||s(se,Y,n,r,c):pe)){W=!1;break}J||(J=k=="constructor")}if(W&&!J){var me=e.constructor,at=t.constructor;me!=at&&"constructor"in e&&"constructor"in t&&!(typeof me=="function"&&me instanceof me&&typeof at=="function"&&at instanceof at)&&(W=!1)}return c.delete(e),c.delete(t),W}function ln(e){return ho(du(e,m,yu),e+"")}function zi(e){return Fs(e,Je,mo)}function Bi(e){return Fs(e,mt,Qu)}function fa(e){for(var t=e.name+"",n=lr[t],r=Ce.call(lr,t)?n.length:0;r--;){var s=n[r],c=s.func;if(c==null||c==e)return s.name}return t}function ar(e){return(Ce.call(i,"placeholder")?i:e).placeholder}function ee(){var e=i.iteratee||to;return e=e===to?Ss:e,arguments.length?e(arguments[0],arguments[1]):e}function pa(e,t){var n=e.__data__;return rp(t)?n[typeof t=="string"?"string":"hash"]:n.map}function Vi(e){for(var t=Je(e),n=t.length;n--;){var r=t[n],s=e[r];t[n]=[r,s,lu(s)]}return t}function Pn(e,t){var n=B(e,t);return Rs(n)?n:m}function Jf(e){var t=Ce.call(e,Wn),n=e[Wn];try{e[Wn]=m;var r=!0}catch{}var s=Ta.call(e);return r&&(t?e[Wn]=n:delete e[Wn]),s}function Yf(e,t,n){for(var r=-1,s=n.length;++r<s;){var c=n[r],h=c.size;switch(c.type){case"drop":e+=h;break;case"dropRight":t-=h;break;case"take":t=nt(t,e+h);break;case"takeRight":e=Ze(e,t-h)}}return{start:e,end:t}}function Xf(e){var t=e.match(Gc);return t?t[1].split(Zc):[]}function su(e,t,n){t=Cn(t,e);for(var r=-1,s=t.length,c=!1;++r<s;){var h=Kt(t[r]);if(!(c=e!=null&&n(e,h)))break;e=e[h]}return c||++r!=s?c:(s=e==null?0:e.length,!!s&&va(s)&&cn(h,s)&&(ie(e)||zn(e)))}function Qf(e){var t=e.length,n=new e.constructor(t);return t&&typeof e[0]=="string"&&Ce.call(e,"index")&&(n.index=e.index,n.input=e.input),n}function uu(e){return typeof e.constructor!="function"||Ar(e)?{}:cr(Fa(e))}function ep(e,t,n){var r=e.constructor;switch(t){case _r:return Pi(e);case $e:case We:return new r(+e);case Qn:return Df(e,n);case ii:case oi:case si:case ui:case li:case ci:case di:case fi:case pi:return Vs(e,n);case vt:return new r;case yr:case xr:return new r(e);case vr:return qf(e);case qt:return new r;case Yr:return Pf(e)}}function tp(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(Hc,`{
/* [wrapped with `+t+`] */
`)}function np(e){return ie(e)||zn(e)||!!(Vu&&e&&e[Vu])}function cn(e,t){var n=typeof e;return t=t??Ht,!!t&&(n=="number"||n!="symbol"&&rd.test(e))&&e>-1&&e%1==0&&e<t}function lt(e,t,n){if(!Ie(n))return!1;var r=typeof t;return!!(r=="number"?pt(n)&&cn(t,n.length):r=="string"&&t in n)&&Ut(n[t],e)}function Hi(e,t){if(ie(e))return!1;var n=typeof e;return!(n!="number"&&n!="symbol"&&n!="boolean"&&e!=null&&!Ct(e))||Wc.test(e)||!Uc.test(e)||t!=null&&e in Me(t)}function rp(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Gi(e){var t=fa(e),n=i[t];if(typeof n!="function"||!(t in P.prototype))return!1;if(e===n)return!0;var r=po(n);return!!r&&e===r[0]}function ap(e){return!!Uu&&Uu in e}function Ar(e){var t=e&&e.constructor;return e===(typeof t=="function"&&t.prototype||sr)}function lu(e){return e===e&&!Ie(e)}function cu(e,t){return function(n){return n!=null&&n[e]===t&&(t!==m||e in Me(n))}}function ip(e){var t=ba(e,function(r){return n.size===Gr&&n.clear(),r}),n=t.cache;return t}function op(e,t){var n=e[1],r=t[1],s=n|r,c=s<(st|yn|Dt),h=r==Dt&&n==wt||r==Dt&&n==In&&e[7].length<=t[8]||r==(Dt|In)&&t[7].length<=t[8]&&n==wt;if(!c&&!h)return e;r&st&&(e[2]=t[2],s|=n&st?0:hr);var y=t[3];if(y){var _=e[3];e[3]=_?Gs(_,y,t[4]):y,e[4]=_?bt(e[3],$n):t[4]}return y=t[5],y&&(_=e[5],e[5]=_?Zs(_,y,t[6]):y,e[6]=_?bt(e[5],$n):t[6]),y=t[7],y&&(e[7]=y),r&Dt&&(e[8]=e[8]==null?t[8]:nt(e[8],t[8])),e[9]==null&&(e[9]=t[9]),e[0]=t[0],e[1]=s,e}function sp(e){var t=[];if(e!=null)for(var n in Me(e))t.push(n);return t}function up(e){return Ta.call(e)}function du(e,t,n){return t=Ze(t===m?e.length-1:t,0),function(){for(var r=arguments,s=-1,c=Ze(r.length-t,0),h=Ge(c);++s<c;)h[s]=r[t+s];s=-1;for(var y=Ge(t+1);++s<t;)y[s]=r[s];return y[t]=n(h),l(e,this,y)}}function fu(e,t){return t.length<2?e:qn(e,Ft(t,0,-1))}function lp(e,t){for(var n=e.length,r=nt(t.length,n),s=ft(e);r--;){var c=t[r];e[r]=cn(c,n)?s[c]:m}return e}function Zi(e,t){if((t!=="constructor"||typeof e[t]!="function")&&t!="__proto__")return e[t]}function pu(e,t,n){var r=t+"";return ho(e,tp(r,cp(Xf(r),n)))}function mu(e){var t=0,n=0;return function(){var r=yg(),s=Kr-(r-n);if(n=r,s>0){if(++t>=Qa)return arguments[0]}else t=0;return e.apply(m,arguments)}}function ma(e,t){var n=-1,r=e.length,s=r-1;for(t=t===m?r:t;++n<t;){var c=Ni(n,s),h=e[c];e[c]=e[n],e[n]=h}return e.length=t,e}function Kt(e){if(typeof e=="string"||Ct(e))return e;var t=e+"";return t=="0"&&1/e==-nn?"-0":t}function Un(e){if(e!=null){try{return wa.call(e)}catch{}try{return e+""}catch{}}return""}function cp(e,t){return d(_e,function(n){var r="_."+n[0];t&n[1]&&!v(e,r)&&e.push(r)}),e.sort()}function hu(e){if(e instanceof P)return e.clone();var t=new X(e.__wrapped__,e.__chain__);return t.__actions__=ft(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function dp(e,t,n){t=(n?lt(e,t,n):t===m)?1:Ze(le(t),0);var r=e==null?0:e.length;if(!r||t<1)return[];for(var s=0,c=0,h=Ge(Sa(r/t));s<r;)h[c++]=Ft(e,s,s+=t);return h}function fp(e){for(var t=-1,n=e==null?0:e.length,r=0,s=[];++t<n;){var c=e[t];c&&(s[r++]=c)}return s}function pp(){var e=arguments.length;if(!e)return[];for(var t=Ge(e-1),n=arguments[0],r=e;r--;)t[r-1]=arguments[r];return A(ie(n)?ft(n):[n],Qe(t,1))}function mp(e,t,n){var r=e==null?0:e.length;return r?(t=n||t===m?1:le(t),Ft(e,t<0?0:t,r)):[]}function hp(e,t,n){var r=e==null?0:e.length;return r?(t=n||t===m?1:le(t),t=r-t,Ft(e,0,t<0?0:t)):[]}function gp(e,t){return e&&e.length?oa(e,ee(t,3),!0,!0):[]}function bp(e,t){return e&&e.length?oa(e,ee(t,3),!0):[]}function yp(e,t,n,r){var s=e==null?0:e.length;return s?(n&&typeof n!="number"&&lt(e,t,n)&&(n=0,r=s),bf(e,t,n,r)):[]}function gu(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var s=n==null?0:le(n);return s<0&&(s=Ze(r+s,0)),N(e,ee(t,3),s)}function bu(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var s=r-1;return n!==m&&(s=le(n),s=n<0?Ze(r+s,0):nt(s,r-1)),N(e,ee(t,3),s,!0)}function yu(e){return e!=null&&e.length?Qe(e,1):[]}function vp(e){return e!=null&&e.length?Qe(e,nn):[]}function xp(e,t){return e!=null&&e.length?(t=t===m?1:le(t),Qe(e,t)):[]}function Cp(e){for(var t=-1,n=e==null?0:e.length,r={};++t<n;){var s=e[t];r[s[0]]=s[1]}return r}function vu(e){return e&&e.length?e[0]:m}function _p(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var s=n==null?0:le(n);return s<0&&(s=Ze(r+s,0)),S(e,t,s)}function jp(e){return e!=null&&e.length?Ft(e,0,-1):[]}function wp(e,t){return e==null?"":gg.call(e,t)}function At(e){var t=e==null?0:e.length;return t?e[t-1]:m}function Tp(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var s=r;return n!==m&&(s=le(n),s=s<0?Ze(r+s,0):nt(s,r-1)),t===t?mr(e,t,s):N(e,L,s,!0)}function Mp(e,t){return e&&e.length?Ls(e,le(t)):m}function xu(e,t){return e&&e.length&&t&&t.length?$i(e,t):e}function kp(e,t,n){return e&&e.length&&t&&t.length?$i(e,t,ee(n,2)):e}function Fp(e,t,n){return e&&e.length&&t&&t.length?$i(e,t,m,n):e}function Ap(e,t){var n=[];if(!e||!e.length)return n;var r=-1,s=[],c=e.length;for(t=ee(t,3);++r<c;){var h=e[r];t(h,r,e)&&(n.push(h),s.push(r))}return Ds(e,s),n}function Ki(e){return e==null?e:xg.call(e)}function Rp(e,t,n){var r=e==null?0:e.length;return r?(n&&typeof n!="number"&&lt(e,t,n)?(t=0,n=r):(t=t==null?0:le(t),n=n===m?r:le(n)),Ft(e,t,n)):[]}function Sp(e,t){return ia(e,t)}function $p(e,t,n){return Li(e,t,ee(n,2))}function Np(e,t){var n=e==null?0:e.length;if(n){var r=ia(e,t);if(r<n&&Ut(e[r],t))return r}return-1}function Ip(e,t){return ia(e,t,!0)}function Lp(e,t,n){return Li(e,t,ee(n,2),!0)}function Ep(e,t){if(e!=null&&e.length){var n=ia(e,t,!0)-1;if(Ut(e[n],t))return n}return-1}function Op(e){return e&&e.length?qs(e):[]}function Dp(e,t){return e&&e.length?qs(e,ee(t,2)):[]}function qp(e){var t=e==null?0:e.length;return t?Ft(e,1,t):[]}function Pp(e,t,n){return e&&e.length?(t=n||t===m?1:le(t),Ft(e,0,t<0?0:t)):[]}function Up(e,t,n){var r=e==null?0:e.length;return r?(t=n||t===m?1:le(t),t=r-t,Ft(e,t<0?0:t,r)):[]}function Wp(e,t){return e&&e.length?oa(e,ee(t,3),!1,!0):[]}function zp(e,t){return e&&e.length?oa(e,ee(t,3)):[]}function Bp(e){return e&&e.length?xn(e):[]}function Vp(e,t){return e&&e.length?xn(e,ee(t,2)):[]}function Hp(e,t){return t=typeof t=="function"?t:m,e&&e.length?xn(e,m,t):[]}function Ji(e){if(!e||!e.length)return[];var t=0;return e=g(e,function(n){if(ze(n))return t=Ze(n.length,t),!0}),oe(t,function(n){return j(e,he(n))})}function Cu(e,t){if(!e||!e.length)return[];var n=Ji(e);return t==null?n:j(n,function(r){return l(t,m,r)})}function Gp(e,t){return zs(e||[],t||[],jr)}function Zp(e,t){return zs(e||[],t||[],kr)}function _u(e){var t=i(e);return t.__chain__=!0,t}function Kp(e,t){return t(e),e}function ha(e,t){return t(e)}function Jp(){return _u(this)}function Yp(){return new X(this.value(),this.__chain__)}function Xp(){this.__values__===m&&(this.__values__=$u(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?m:this.__values__[this.__index__++]}}function Qp(){return this}function em(e){for(var t,n=this;n instanceof q;){var r=hu(n);r.__index__=0,r.__values__=m,t?s.__wrapped__=r:t=r;var s=r;n=n.__wrapped__}return s.__wrapped__=e,t}function tm(){var e=this.__wrapped__;if(e instanceof P){var t=e;return this.__actions__.length&&(t=new P(this)),t=t.reverse(),t.__actions__.push({func:ha,args:[Ki],thisArg:m}),new X(t,this.__chain__)}return this.thru(Ki)}function nm(){return Ws(this.__wrapped__,this.__actions__)}function rm(e,t,n){var r=ie(e)?p:gf;return n&&lt(e,t,n)&&(t=m),r(e,ee(t,3))}function am(e,t){return(ie(e)?g:ks)(e,ee(t,3))}function im(e,t){return Qe(ga(e,t),1)}function om(e,t){return Qe(ga(e,t),nn)}function sm(e,t,n){return n=n===m?1:le(n),Qe(ga(e,t),n)}function ju(e,t){return(ie(e)?d:wn)(e,ee(t,3))}function wu(e,t){return(ie(e)?b:Ku)(e,ee(t,3))}function um(e,t,n,r){e=pt(e)?e:ir(e),n=n&&!r?le(n):0;var s=e.length;return n<0&&(n=Ze(s+n,0)),xa(e)?n<=s&&e.indexOf(t,n)>-1:!!s&&S(e,t,n)>-1}function ga(e,t){return(ie(e)?j:$s)(e,ee(t,3))}function lm(e,t,n,r){return e==null?[]:(ie(t)||(t=t==null?[]:[t]),n=r?m:n,ie(n)||(n=n==null?[]:[n]),Es(e,t,n))}function cm(e,t,n){var r=ie(e)?O:Ae,s=arguments.length<3;return r(e,ee(t,4),n,s,wn)}function dm(e,t,n){var r=ie(e)?K:Ae,s=arguments.length<3;return r(e,ee(t,4),n,s,Ku)}function fm(e,t){return(ie(e)?g:ks)(e,ya(ee(t,3)))}function pm(e){return(ie(e)?js:If)(e)}function mm(e,t,n){return t=(n?lt(e,t,n):t===m)?1:le(t),(ie(e)?df:Lf)(e,t)}function hm(e){return(ie(e)?ff:Ef)(e)}function gm(e){if(e==null)return 0;if(pt(e))return xa(e)?ct(e):e.length;var t=rt(e);return t==vt||t==qt?e.size:Ri(e).length}function bm(e,t,n){var r=ie(e)?T:Of;return n&&lt(e,t,n)&&(t=m),r(e,ee(t,3))}function ym(e,t){if(typeof t!="function")throw new St(tt);return e=le(e),function(){if(--e<1)return t.apply(this,arguments)}}function Tu(e,t,n){return t=n?m:t,t=e&&t==null?e.length:t,un(e,Dt,m,m,m,m,t)}function Mu(e,t){var n;if(typeof t!="function")throw new St(tt);return e=le(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=m),n}}function ku(e,t,n){t=n?m:t;var r=un(e,wt,m,m,m,m,m,t);return r.placeholder=ku.placeholder,r}function Fu(e,t,n){t=n?m:t;var r=un(e,Ye,m,m,m,m,m,t);return r.placeholder=Fu.placeholder,r}function Au(e,t,n){function r(Pe){var $t=D,Or=W;return D=W=m,me=Pe,se=e.apply(Or,$t)}function s(Pe){return me=Pe,Y=Er(y,t),at?r(Pe):se}function c(Pe){var $t=Pe-pe,Or=Pe-me,dl=t-$t;return gt?nt(dl,J-Or):dl}function h(Pe){var $t=Pe-pe,Or=Pe-me;return pe===m||$t>=t||$t<0||gt&&Or>=J}function y(){var Pe=La();return h(Pe)?_(Pe):(Y=Er(y,c(Pe)),m)}function _(Pe){return Y=m,Mn&&D?r(Pe):(D=W=m,se)}function F(){Y!==m&&Xu(Y),me=0,D=pe=W=Y=m}function k(){return Y===m?se:_(La())}function R(){var Pe=La(),$t=h(Pe);if(D=arguments,W=this,pe=Pe,$t){if(Y===m)return s(pe);if(gt)return Xu(Y),Y=Er(y,t),r(pe)}return Y===m&&(Y=Er(y,t)),se}var D,W,J,se,Y,pe,me=0,at=!1,gt=!1,Mn=!0;if(typeof e!="function")throw new St(tt);return t=Rt(t)||0,Ie(n)&&(at=!!n.leading,gt="maxWait"in n,J=gt?Ze(Rt(n.maxWait)||0,t):J,Mn="trailing"in n?!!n.trailing:Mn),R.cancel=F,R.flush=k,R}function vm(e){return un(e,gr)}function ba(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new St(tt);var n=function(){var r=arguments,s=t?t.apply(this,r):r[0],c=n.cache;if(c.has(s))return c.get(s);var h=e.apply(this,r);return n.cache=c.set(s,h)||c,h};return n.cache=new(ba.Cache||on),n}function ya(e){if(typeof e!="function")throw new St(tt);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function xm(e){return Mu(2,e)}function Cm(e,t){if(typeof e!="function")throw new St(tt);return t=t===m?t:le(t),de(e,t)}function _m(e,t){if(typeof e!="function")throw new St(tt);return t=t==null?0:Ze(le(t),0),de(function(n){var r=n[t],s=_n(n,0,t);return r&&A(s,r),l(e,this,s)})}function jm(e,t,n){var r=!0,s=!0;if(typeof e!="function")throw new St(tt);return Ie(n)&&(r="leading"in n?!!n.leading:r,s="trailing"in n?!!n.trailing:s),Au(e,t,{leading:r,maxWait:t,trailing:s})}function wm(e){return Tu(e,1)}function Tm(e,t){return bo(qi(t),e)}function Mm(){if(!arguments.length)return[];var e=arguments[0];return ie(e)?e:[e]}function km(e){return kt(e,yt)}function Fm(e,t){return t=typeof t=="function"?t:m,kt(e,yt,t)}function Am(e){return kt(e,ot|yt)}function Rm(e,t){return t=typeof t=="function"?t:m,kt(e,ot|yt,t)}function Sm(e,t){return t==null||Ts(e,t,Je(t))}function Ut(e,t){return e===t||e!==e&&t!==t}function pt(e){return e!=null&&va(e.length)&&!dn(e)}function ze(e){return qe(e)&&pt(e)}function $m(e){return e===!0||e===!1||qe(e)&&ut(e)==$e}function Nm(e){return qe(e)&&e.nodeType===1&&!Rr(e)}function Im(e){if(e==null)return!0;if(pt(e)&&(ie(e)||typeof e=="string"||typeof e.splice=="function"||Tn(e)||dr(e)||zn(e)))return!e.length;var t=rt(e);if(t==vt||t==qt)return!e.size;if(Ar(e))return!Ri(e).length;for(var n in e)if(Ce.call(e,n))return!1;return!0}function Lm(e,t){return Mr(e,t)}function Em(e,t,n){n=typeof n=="function"?n:m;var r=n?n(e,t):m;return r===m?Mr(e,t,m,n):!!r}function Yi(e){if(!qe(e))return!1;var t=ut(e);return t==Yn||t==ri||typeof e.message=="string"&&typeof e.name=="string"&&!Rr(e)}function Om(e){return typeof e=="number"&&Hu(e)}function dn(e){if(!Ie(e))return!1;var t=ut(e);return t==Xn||t==ai||t==He||t==Sc}function Ru(e){return typeof e=="number"&&e==le(e)}function va(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Ht}function Ie(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}function qe(e){return e!=null&&typeof e=="object"}function Dm(e,t){return e===t||Ai(e,t,Vi(t))}function qm(e,t,n){return n=typeof n=="function"?n:m,Ai(e,t,Vi(t),n)}function Pm(e){return Su(e)&&e!=+e}function Um(e){if(Ag(e))throw new oo(Hr);return Rs(e)}function Wm(e){return e===null}function zm(e){return e==null}function Su(e){return typeof e=="number"||qe(e)&&ut(e)==yr}function Rr(e){if(!qe(e)||ut(e)!=rn)return!1;var t=Fa(e);if(t===null)return!0;var n=Ce.call(t,"constructor")&&t.constructor;return typeof n=="function"&&n instanceof n&&wa.call(n)==lg}function Bm(e){return Ru(e)&&e>=-Ht&&e<=Ht}function xa(e){return typeof e=="string"||!ie(e)&&qe(e)&&ut(e)==xr}function Ct(e){return typeof e=="symbol"||qe(e)&&ut(e)==Yr}function Vm(e){return e===m}function Hm(e){return qe(e)&&rt(e)==Cr}function Gm(e){return qe(e)&&ut(e)==Nc}function $u(e){if(!e)return[];if(pt(e))return xa(e)?Re(e):ft(e);if(Sr&&e[Sr])return Vt(e[Sr]());var t=rt(e);return(t==vt?Et:t==qt?hn:ir)(e)}function fn(e){return e?(e=Rt(e),e===nn||e===-nn?(e<0?-1:1)*ti:e===e?e:0):e===0?e:0}function le(e){var t=fn(e),n=t%1;return t===t?n?t-n:t:0}function Nu(e){return e?Dn(le(e),0,Tt):0}function Rt(e){if(typeof e=="number")return e;if(Ct(e))return Jn;if(Ie(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Ie(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=It(e);var n=ed.test(e);return n||nd.test(e)?Id(e.slice(2),n?2:8):Qc.test(e)?Jn:+e}function Iu(e){return Zt(e,mt(e))}function Zm(e){return e?Dn(le(e),-Ht,Ht):e===0?e:0}function xe(e){return e==null?"":xt(e)}function Km(e,t){var n=cr(e);return t==null?n:ws(n,t)}function Jm(e,t){return E(e,ee(t,3),Gt)}function Ym(e,t){return E(e,ee(t,3),Mi)}function Xm(e,t){return e==null?e:fo(e,ee(t,3),mt)}function Qm(e,t){return e==null?e:Ju(e,ee(t,3),mt)}function eh(e,t){return e&&Gt(e,ee(t,3))}function th(e,t){return e&&Mi(e,ee(t,3))}function nh(e){return e==null?[]:ra(e,Je(e))}function rh(e){return e==null?[]:ra(e,mt(e))}function Xi(e,t,n){var r=e==null?m:qn(e,t);return r===m?n:r}function ah(e,t){return e!=null&&su(e,t,yf)}function Qi(e,t){return e!=null&&su(e,t,vf)}function Je(e){return pt(e)?_s(e):Ri(e)}function mt(e){return pt(e)?_s(e,!0):Af(e)}function ih(e,t){var n={};return t=ee(t,3),Gt(e,function(r,s,c){sn(n,t(r,s,c),r)}),n}function oh(e,t){var n={};return t=ee(t,3),Gt(e,function(r,s,c){sn(n,s,t(r,s,c))}),n}function sh(e,t){return Lu(e,ya(ee(t)))}function Lu(e,t){if(e==null)return{};var n=j(Bi(e),function(r){return[r]});return t=ee(t),Os(e,n,function(r,s){return t(r,s[0])})}function uh(e,t,n){t=Cn(t,e);var r=-1,s=t.length;for(s||(s=1,e=m);++r<s;){var c=e==null?m:e[Kt(t[r])];c===m&&(r=s,c=n),e=dn(c)?c.call(e):c}return e}function lh(e,t,n){return e==null?e:kr(e,t,n)}function ch(e,t,n,r){return r=typeof r=="function"?r:m,e==null?e:kr(e,t,n,r)}function dh(e,t,n){var r=ie(e),s=r||Tn(e)||dr(e);if(t=ee(t,4),n==null){var c=e&&e.constructor;n=s?r?new c:[]:Ie(e)&&dn(c)?cr(Fa(e)):{}}return(s?d:Gt)(e,function(h,y,_){return t(n,h,y,_)}),n}function fh(e,t){return e==null||Ei(e,t)}function ph(e,t,n){return e==null?e:Us(e,t,qi(n))}function mh(e,t,n,r){return r=typeof r=="function"?r:m,e==null?e:Us(e,t,qi(n),r)}function ir(e){return e==null?[]:Bt(e,Je(e))}function hh(e){return e==null?[]:Bt(e,mt(e))}function gh(e,t,n){return n===m&&(n=t,t=m),n!==m&&(n=Rt(n),n=n===n?n:0),t!==m&&(t=Rt(t),t=t===t?t:0),Dn(Rt(e),t,n)}function bh(e,t,n){return t=fn(t),n===m?(n=t,t=0):n=fn(n),e=Rt(e),xf(e,t,n)}function yh(e,t,n){if(n&&typeof n!="boolean"&&lt(e,t,n)&&(t=n=m),n===m&&(typeof t=="boolean"?(n=t,t=m):typeof e=="boolean"&&(n=e,e=m)),e===m&&t===m?(e=0,t=1):(e=fn(e),t===m?(t=e,e=0):t=fn(t)),e>t){var r=e;e=t,t=r}if(n||e%1||t%1){var s=Gu();return nt(e+s*(t-e+Nd("1e-"+((s+"").length-1))),t)}return Ni(e,t)}function Eu(e){return vo(xe(e).toLowerCase())}function Ou(e){return e=xe(e),e&&e.replace(ad,Od).replace(jd,"")}function vh(e,t,n){e=xe(e),t=xt(t);var r=e.length;n=n===m?r:Dn(le(n),0,r);var s=n;return n-=t.length,n>=0&&e.slice(n,s)==t}function xh(e){return e=xe(e),e&&Dc.test(e)?e.replace(Go,Dd):e}function Ch(e){return e=xe(e),e&&Bc.test(e)?e.replace(mi,"\\$&"):e}function _h(e,t,n){e=xe(e),t=le(t);var r=t?ct(e):0;if(!t||r>=t)return e;var s=(t-r)/2;return ca($a(s),n)+e+ca(Sa(s),n)}function jh(e,t,n){e=xe(e),t=le(t);var r=t?ct(e):0;return t&&r<t?e+ca(t-r,n):e}function wh(e,t,n){e=xe(e),t=le(t);var r=t?ct(e):0;return t&&r<t?ca(t-r,n)+e:e}function Th(e,t,n){return n||t==null?t=0:t&&(t=+t),vg(xe(e).replace(hi,""),t||0)}function Mh(e,t,n){return t=(n?lt(e,t,n):t===m)?1:le(t),Ii(xe(e),t)}function kh(){var e=arguments,t=xe(e[0]);return e.length<3?t:t.replace(e[1],e[2])}function Fh(e,t,n){return n&&typeof n!="number"&&lt(e,t,n)&&(t=n=m),(n=n===m?Tt:n>>>0)?(e=xe(e),e&&(typeof t=="string"||t!=null&&!yo(t))&&(t=xt(t),!t&&je(e))?_n(Re(e),0,n):e.split(t,n)):[]}function Ah(e,t,n){return e=xe(e),n=n==null?0:Dn(le(n),0,e.length),t=xt(t),e.slice(n,n+t.length)==t}function Rh(e,t,n){var r=i.templateSettings;n&&lt(e,t,n)&&(t=m),e=xe(e),t=Ea({},t,r,au);var s,c,h=Ea({},t.imports,r.imports,au),y=Je(h),_=Bt(h,y),F=0,k=t.interpolate||Xr,R="__p += '",D=so((t.escape||Xr).source+"|"+k.source+"|"+(k===Zo?Xc:Xr).source+"|"+(t.evaluate||Xr).source+"|$","g"),W="//# sourceURL="+(Ce.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Fd+"]")+`
`;e.replace(D,function(Y,pe,me,at,gt,Mn){return me||(me=at),R+=e.slice(F,Mn).replace(id,Xt),pe&&(s=!0,R+=`' +
__e(`+pe+`) +
'`),gt&&(c=!0,R+=`';
`+gt+`;
__p += '`),me&&(R+=`' +
((__t = (`+me+`)) == null ? '' : __t) +
'`),F=Mn+Y.length,Y}),R+=`';
`;var J=Ce.call(t,"variable")&&t.variable;if(J){if(Jc.test(J))throw new oo(Ya)}else R=`with (obj) {
`+R+`
}
`;R=(c?R.replace(Ic,""):R).replace(Lc,"$1").replace(Ec,"$1;"),R="function("+(J||"obj")+`) {
`+(J?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(s?", __e = _.escape":"")+(c?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+R+`return __p
}`;var se=cl(function(){return Pu(y,W+"return "+R).apply(m,_)});if(se.source=R,Yi(se))throw se;return se}function Sh(e){return xe(e).toLowerCase()}function $h(e){return xe(e).toUpperCase()}function Nh(e,t,n){if(e=xe(e),e&&(n||t===m))return It(e);if(!e||!(t=xt(t)))return e;var r=Re(e),s=Re(t);return _n(r,mn(r,s),G(r,s)+1).join("")}function Ih(e,t,n){if(e=xe(e),e&&(n||t===m))return e.slice(0,Kn(e)+1);if(!e||!(t=xt(t)))return e;var r=Re(e);return _n(r,0,G(r,Re(t))+1).join("")}function Lh(e,t,n){if(e=xe(e),e&&(n||t===m))return e.replace(hi,"");if(!e||!(t=xt(t)))return e;var r=Re(e);return _n(r,mn(r,Re(t))).join("")}function Eh(e,t){var n=Xa,r=Zr;if(Ie(t)){var s="separator"in t?t.separator:s;n="length"in t?le(t.length):n,r="omission"in t?xt(t.omission):r}e=xe(e);var c=e.length;if(je(e)){var h=Re(e);c=h.length}if(n>=c)return e;var y=n-ct(r);if(y<1)return r;var _=h?_n(h,0,y).join(""):e.slice(0,y);if(s===m)return _+r;if(h&&(y+=_.length-y),yo(s)){if(e.slice(y).search(s)){var F,k=_;for(s.global||(s=so(s.source,xe(Ko.exec(s))+"g")),s.lastIndex=0;F=s.exec(k);)var R=F.index;_=_.slice(0,R===m?y:R)}}else if(e.indexOf(xt(s),y)!=y){var D=_.lastIndexOf(s);D>-1&&(_=_.slice(0,D))}return _+r}function Oh(e){return e=xe(e),e&&Oc.test(e)?e.replace(Ho,qd):e}function Du(e,t,n){return e=xe(e),t=n?m:t,t===m?Lt(e)?Ja(e):$(e):e.match(t)||[]}function Dh(e){var t=e==null?0:e.length,n=ee();return e=t?j(e,function(r){if(typeof r[1]!="function")throw new St(tt);return[n(r[0]),r[1]]}):[],de(function(r){for(var s=-1;++s<t;){var c=e[s];if(l(c[0],this,r))return l(c[1],this,r)}})}function qh(e){return hf(kt(e,ot))}function eo(e){return function(){return e}}function Ph(e,t){return e==null||e!==e?t:e}function ht(e){return e}function to(e){return Ss(typeof e=="function"?e:kt(e,ot))}function Uh(e){return Ns(kt(e,ot))}function Wh(e,t){return Is(e,kt(t,ot))}function no(e,t,n){var r=Je(t),s=ra(t,r);n!=null||Ie(t)&&(s.length||!r.length)||(n=t,t=e,e=this,s=ra(t,Je(t)));var c=!(Ie(n)&&"chain"in n&&!n.chain),h=dn(e);return d(s,function(y){var _=t[y];e[y]=_,h&&(e.prototype[y]=function(){var F=this.__chain__;if(c||F){var k=e(this.__wrapped__);return(k.__actions__=ft(this.__actions__)).push({func:_,args:arguments,thisArg:e}),k.__chain__=F,k}return _.apply(e,A([this.value()],arguments))})}),e}function zh(){return Xe._===this&&(Xe._=cg),this}function ro(){}function Bh(e){return e=le(e),de(function(t){return Ls(t,e)})}function qu(e){return Hi(e)?he(Kt(e)):$f(e)}function Vh(e){return function(t){return e==null?m:qn(e,t)}}function ao(){return[]}function io(){return!1}function Hh(){return{}}function Gh(){return""}function Zh(){return!0}function Kh(e,t){if(e=le(e),e<1||e>Ht)return[];var n=Tt,r=nt(e,Tt);t=ee(t),e-=Tt;for(var s=oe(r,t);++n<e;)t(n);return s}function Jh(e){return ie(e)?j(e,Kt):Ct(e)?[e]:ft(tl(xe(e)))}function Yh(e){var t=++ug;return xe(e)+t}function Xh(e){return e&&e.length?na(e,ht,ki):m}function Qh(e,t){return e&&e.length?na(e,ee(t,2),ki):m}function eg(e){return ne(e,ht)}function tg(e,t){return ne(e,ee(t,2))}function ng(e){return e&&e.length?na(e,ht,Si):m}function rg(e,t){return e&&e.length?na(e,ee(t,2),Si):m}function ag(e){return e&&e.length?et(e,ht):0}function ig(e,t){return e&&e.length?et(e,ee(t,2)):0}M=M==null?Xe:tr.defaults(Xe.Object(),M,tr.pick(Xe,kd));var Ge=M.Array,Ca=M.Date,oo=M.Error,Pu=M.Function,or=M.Math,Me=M.Object,so=M.RegExp,og=M.String,St=M.TypeError,_a=Ge.prototype,sg=Pu.prototype,sr=Me.prototype,ja=M["__core-js_shared__"],wa=sg.toString,Ce=sr.hasOwnProperty,ug=0,Uu=function(){var e=/[^.]+$/.exec(ja&&ja.keys&&ja.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Ta=sr.toString,lg=wa.call(Me),cg=Xe._,dg=so("^"+wa.call(Ce).replace(mi,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ma=hs?M.Buffer:m,jn=M.Symbol,ka=M.Uint8Array,Wu=Ma?Ma.allocUnsafe:m,Fa=Qt(Me.getPrototypeOf,Me),zu=Me.create,Bu=sr.propertyIsEnumerable,Aa=_a.splice,Vu=jn?jn.isConcatSpreadable:m,Sr=jn?jn.iterator:m,Wn=jn?jn.toStringTag:m,Ra=function(){try{var e=Pn(Me,"defineProperty");return e({},"",{}),e}catch{}}(),fg=M.clearTimeout!==Xe.clearTimeout&&M.clearTimeout,pg=Ca&&Ca.now!==Xe.Date.now&&Ca.now,mg=M.setTimeout!==Xe.setTimeout&&M.setTimeout,Sa=or.ceil,$a=or.floor,uo=Me.getOwnPropertySymbols,hg=Ma?Ma.isBuffer:m,Hu=M.isFinite,gg=_a.join,bg=Qt(Me.keys,Me),Ze=or.max,nt=or.min,yg=Ca.now,vg=M.parseInt,Gu=or.random,xg=_a.reverse,lo=Pn(M,"DataView"),$r=Pn(M,"Map"),co=Pn(M,"Promise"),ur=Pn(M,"Set"),Nr=Pn(M,"WeakMap"),Ir=Pn(Me,"create"),Na=Nr&&new Nr,lr={},Cg=Un(lo),_g=Un($r),jg=Un(co),wg=Un(ur),Tg=Un(Nr),Ia=jn?jn.prototype:m,Lr=Ia?Ia.valueOf:m,Zu=Ia?Ia.toString:m,cr=function(){function e(){}return function(t){if(!Ie(t))return{};if(zu)return zu(t);e.prototype=t;var n=new e;return e.prototype=m,n}}();i.templateSettings={escape:qc,evaluate:Pc,interpolate:Zo,variable:"",imports:{_:i}},i.prototype=q.prototype,i.prototype.constructor=i,X.prototype=cr(q.prototype),X.prototype.constructor=X,P.prototype=cr(q.prototype),P.prototype.constructor=P,En.prototype.clear=Wd,En.prototype.delete=zd,En.prototype.get=Bd,En.prototype.has=Vd,En.prototype.set=Hd,an.prototype.clear=Gd,an.prototype.delete=Zd,an.prototype.get=Kd,an.prototype.has=Jd,an.prototype.set=Yd,on.prototype.clear=Xd,on.prototype.delete=Qd,on.prototype.get=ef,on.prototype.has=tf,on.prototype.set=nf,On.prototype.add=On.prototype.push=rf,On.prototype.has=af,Pt.prototype.clear=of,Pt.prototype.delete=sf,Pt.prototype.get=uf,Pt.prototype.has=lf,Pt.prototype.set=cf;var wn=Ks(Gt),Ku=Ks(Mi,!0),fo=Js(),Ju=Js(!0),Yu=Na?function(e,t){return Na.set(e,t),e}:ht,Mg=Ra?function(e,t){return Ra(e,"toString",{configurable:!0,enumerable:!1,value:eo(t),writable:!0})}:ht,kg=de,Xu=fg||function(e){return Xe.clearTimeout(e)},Fg=ur&&1/hn(new ur([,-0]))[1]==nn?function(e){return new ur(e)}:ro,po=Na?function(e){return Na.get(e)}:ro,mo=uo?function(e){return e==null?[]:(e=Me(e),g(uo(e),function(t){return Bu.call(e,t)}))}:ao,Qu=uo?function(e){for(var t=[];e;)A(t,mo(e)),e=Fa(e);return t}:ao,rt=ut;(lo&&rt(new lo(new ArrayBuffer(1)))!=Qn||$r&&rt(new $r)!=vt||co&&rt(co.resolve())!=Vo||ur&&rt(new ur)!=qt||Nr&&rt(new Nr)!=Cr)&&(rt=function(e){var t=ut(e),n=t==rn?e.constructor:m,r=n?Un(n):"";if(r)switch(r){case Cg:return Qn;case _g:return vt;case jg:return Vo;case wg:return qt;case Tg:return Cr}return t});var Ag=ja?dn:io,el=mu(Yu),Er=mg||function(e,t){return Xe.setTimeout(e,t)},ho=mu(Mg),tl=ip(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(zc,function(n,r,s,c){t.push(s?c.replace(Yc,"$1"):r||n)}),t}),Rg=de(function(e,t){return ze(e)?wr(e,Qe(t,1,ze,!0)):[]}),Sg=de(function(e,t){var n=At(t);return ze(n)&&(n=m),ze(e)?wr(e,Qe(t,1,ze,!0),ee(n,2)):[]}),$g=de(function(e,t){var n=At(t);return ze(n)&&(n=m),ze(e)?wr(e,Qe(t,1,ze,!0),m,n):[]}),Ng=de(function(e){var t=j(e,Di);return t.length&&t[0]===e[0]?Fi(t):[]}),Ig=de(function(e){var t=At(e),n=j(e,Di);return t===At(n)?t=m:n.pop(),n.length&&n[0]===e[0]?Fi(n,ee(t,2)):[]}),Lg=de(function(e){var t=At(e),n=j(e,Di);return t=typeof t=="function"?t:m,t&&n.pop(),n.length&&n[0]===e[0]?Fi(n,m,t):[]}),Eg=de(xu),Og=ln(function(e,t){var n=e==null?0:e.length,r=Ti(e,t);return Ds(e,j(t,function(s){return cn(s,n)?+s:s}).sort(Hs)),r}),Dg=de(function(e){return xn(Qe(e,1,ze,!0))}),qg=de(function(e){var t=At(e);return ze(t)&&(t=m),xn(Qe(e,1,ze,!0),ee(t,2))}),Pg=de(function(e){var t=At(e);return t=typeof t=="function"?t:m,xn(Qe(e,1,ze,!0),m,t)}),Ug=de(function(e,t){return ze(e)?wr(e,t):[]}),Wg=de(function(e){return Oi(g(e,ze))}),zg=de(function(e){var t=At(e);return ze(t)&&(t=m),Oi(g(e,ze),ee(t,2))}),Bg=de(function(e){var t=At(e);return t=typeof t=="function"?t:m,Oi(g(e,ze),m,t)}),Vg=de(Ji),Hg=de(function(e){var t=e.length,n=t>1?e[t-1]:m;return n=typeof n=="function"?(e.pop(),n):m,Cu(e,n)}),Gg=ln(function(e){var t=e.length,n=t?e[0]:0,r=this.__wrapped__,s=function(c){return Ti(c,e)};return!(t>1||this.__actions__.length)&&r instanceof P&&cn(n)?(r=r.slice(n,+n+(t?1:0)),r.__actions__.push({func:ha,args:[s],thisArg:m}),new X(r,this.__chain__).thru(function(c){return t&&!c.length&&c.push(m),c})):this.thru(s)}),Zg=sa(function(e,t,n){Ce.call(e,n)?++e[n]:sn(e,n,1)}),Kg=Xs(gu),Jg=Xs(bu),Yg=sa(function(e,t,n){Ce.call(e,n)?e[n].push(t):sn(e,n,[t])}),Xg=de(function(e,t,n){var r=-1,s=typeof t=="function",c=pt(e)?Ge(e.length):[];return wn(e,function(h){c[++r]=s?l(t,h,n):Tr(h,t,n)}),c}),Qg=sa(function(e,t,n){sn(e,n,t)}),eb=sa(function(e,t,n){e[n?0:1].push(t)},function(){return[[],[]]}),tb=de(function(e,t){if(e==null)return[];var n=t.length;return n>1&&lt(e,t[0],t[1])?t=[]:n>2&&lt(t[0],t[1],t[2])&&(t=[t[0]]),Es(e,Qe(t,1),[])}),La=pg||function(){return Xe.Date.now()},go=de(function(e,t,n){var r=st;if(n.length){var s=bt(n,ar(go));r|=Ot}return un(e,r,t,n,s)}),nl=de(function(e,t,n){var r=st|yn;if(n.length){var s=bt(n,ar(nl));r|=Ot}return un(t,r,e,n,s)}),nb=de(function(e,t){return Ms(e,1,t)}),rb=de(function(e,t,n){return Ms(e,Rt(t)||0,n)});ba.Cache=on;var ab=kg(function(e,t){t=t.length==1&&ie(t[0])?j(t[0],De(ee())):j(Qe(t,1),De(ee()));var n=t.length;return de(function(r){for(var s=-1,c=nt(r.length,n);++s<c;)r[s]=t[s].call(this,r[s]);return l(e,this,r)})}),bo=de(function(e,t){return un(e,Ot,m,t,bt(t,ar(bo)))}),rl=de(function(e,t){return un(e,vn,m,t,bt(t,ar(rl)))}),ib=ln(function(e,t){return un(e,In,m,m,m,t)}),ob=da(ki),sb=da(function(e,t){return e>=t}),zn=As(function(){return arguments}())?As:function(e){return qe(e)&&Ce.call(e,"callee")&&!Bu.call(e,"callee")},ie=Ge.isArray,ub=gs?De(gs):_f,Tn=hg||io,lb=bs?De(bs):jf,al=ys?De(ys):Tf,yo=vs?De(vs):Mf,il=xs?De(xs):kf,dr=Cs?De(Cs):Ff,cb=da(Si),db=da(function(e,t){return e<=t}),fb=nr(function(e,t){if(Ar(t)||pt(t))return Zt(t,Je(t),e),m;for(var n in t)Ce.call(t,n)&&jr(e,n,t[n])}),ol=nr(function(e,t){Zt(t,mt(t),e)}),Ea=nr(function(e,t,n,r){Zt(t,mt(t),e,r)}),pb=nr(function(e,t,n,r){Zt(t,Je(t),e,r)}),mb=ln(Ti),hb=de(function(e,t){e=Me(e);var n=-1,r=t.length,s=r>2?t[2]:m;for(s&&lt(t[0],t[1],s)&&(r=1);++n<r;)for(var c=t[n],h=mt(c),y=-1,_=h.length;++y<_;){var F=h[y],k=e[F];(k===m||Ut(k,sr[F])&&!Ce.call(e,F))&&(e[F]=c[F])}return e}),gb=de(function(e){return e.push(m,iu),l(sl,m,e)}),bb=eu(function(e,t,n){t!=null&&typeof t.toString!="function"&&(t=Ta.call(t)),e[t]=n},eo(ht)),yb=eu(function(e,t,n){t!=null&&typeof t.toString!="function"&&(t=Ta.call(t)),Ce.call(e,t)?e[t].push(n):e[t]=[n]},ee),vb=de(Tr),xb=nr(function(e,t,n){aa(e,t,n)}),sl=nr(function(e,t,n,r){aa(e,t,n,r)}),Cb=ln(function(e,t){var n={};if(e==null)return n;var r=!1;t=j(t,function(c){return c=Cn(c,e),r||(r=c.length>1),c}),Zt(e,Bi(e),n),r&&(n=kt(n,ot|en|yt,Gf));for(var s=t.length;s--;)Ei(n,t[s]);return n}),_b=ln(function(e,t){return e==null?{}:Sf(e,t)}),ul=ru(Je),ll=ru(mt),jb=rr(function(e,t,n){return t=t.toLowerCase(),e+(n?Eu(t):t)}),wb=rr(function(e,t,n){return e+(n?"-":"")+t.toLowerCase()}),Tb=rr(function(e,t,n){return e+(n?" ":"")+t.toLowerCase()}),Mb=Ys("toLowerCase"),kb=rr(function(e,t,n){return e+(n?"_":"")+t.toLowerCase()}),Fb=rr(function(e,t,n){return e+(n?" ":"")+vo(t)}),Ab=rr(function(e,t,n){return e+(n?" ":"")+t.toUpperCase()}),vo=Ys("toUpperCase"),cl=de(function(e,t){try{return l(e,m,t)}catch(n){return Yi(n)?n:new oo(n)}}),Rb=ln(function(e,t){return d(t,function(n){n=Kt(n),sn(e,n,go(e[n],e))}),e}),Sb=Qs(),$b=Qs(!0),Nb=de(function(e,t){return function(n){return Tr(n,e,t)}}),Ib=de(function(e,t){return function(n){return Tr(e,n,t)}}),Lb=Ui(j),Eb=Ui(p),Ob=Ui(T),Db=tu(),qb=tu(!0),Pb=la(function(e,t){return e+t},0),Ub=Wi("ceil"),Wb=la(function(e,t){return e/t},1),zb=Wi("floor"),Bb=la(function(e,t){return e*t},1),Vb=Wi("round"),Hb=la(function(e,t){return e-t},0);return i.after=ym,i.ary=Tu,i.assign=fb,i.assignIn=ol,i.assignInWith=Ea,i.assignWith=pb,i.at=mb,i.before=Mu,i.bind=go,i.bindAll=Rb,i.bindKey=nl,i.castArray=Mm,i.chain=_u,i.chunk=dp,i.compact=fp,i.concat=pp,i.cond=Dh,i.conforms=qh,i.constant=eo,i.countBy=Zg,i.create=Km,i.curry=ku,i.curryRight=Fu,i.debounce=Au,i.defaults=hb,i.defaultsDeep=gb,i.defer=nb,i.delay=rb,i.difference=Rg,i.differenceBy=Sg,i.differenceWith=$g,i.drop=mp,i.dropRight=hp,i.dropRightWhile=gp,i.dropWhile=bp,i.fill=yp,i.filter=am,i.flatMap=im,i.flatMapDeep=om,i.flatMapDepth=sm,i.flatten=yu,i.flattenDeep=vp,i.flattenDepth=xp,i.flip=vm,i.flow=Sb,i.flowRight=$b,i.fromPairs=Cp,i.functions=nh,i.functionsIn=rh,i.groupBy=Yg,i.initial=jp,i.intersection=Ng,i.intersectionBy=Ig,i.intersectionWith=Lg,i.invert=bb,i.invertBy=yb,i.invokeMap=Xg,i.iteratee=to,i.keyBy=Qg,i.keys=Je,i.keysIn=mt,i.map=ga,i.mapKeys=ih,i.mapValues=oh,i.matches=Uh,i.matchesProperty=Wh,i.memoize=ba,i.merge=xb,i.mergeWith=sl,i.method=Nb,i.methodOf=Ib,i.mixin=no,i.negate=ya,i.nthArg=Bh,i.omit=Cb,i.omitBy=sh,i.once=xm,i.orderBy=lm,i.over=Lb,i.overArgs=ab,i.overEvery=Eb,i.overSome=Ob,i.partial=bo,i.partialRight=rl,i.partition=eb,i.pick=_b,i.pickBy=Lu,i.property=qu,i.propertyOf=Vh,i.pull=Eg,i.pullAll=xu,i.pullAllBy=kp,i.pullAllWith=Fp,i.pullAt=Og,i.range=Db,i.rangeRight=qb,i.rearg=ib,i.reject=fm,i.remove=Ap,i.rest=Cm,i.reverse=Ki,i.sampleSize=mm,i.set=lh,i.setWith=ch,i.shuffle=hm,i.slice=Rp,i.sortBy=tb,i.sortedUniq=Op,i.sortedUniqBy=Dp,i.split=Fh,i.spread=_m,i.tail=qp,i.take=Pp,i.takeRight=Up,i.takeRightWhile=Wp,i.takeWhile=zp,i.tap=Kp,i.throttle=jm,i.thru=ha,i.toArray=$u,i.toPairs=ul,i.toPairsIn=ll,i.toPath=Jh,i.toPlainObject=Iu,i.transform=dh,i.unary=wm,i.union=Dg,i.unionBy=qg,i.unionWith=Pg,i.uniq=Bp,i.uniqBy=Vp,i.uniqWith=Hp,i.unset=fh,i.unzip=Ji,i.unzipWith=Cu,i.update=ph,i.updateWith=mh,i.values=ir,i.valuesIn=hh,i.without=Ug,i.words=Du,i.wrap=Tm,i.xor=Wg,i.xorBy=zg,i.xorWith=Bg,i.zip=Vg,i.zipObject=Gp,i.zipObjectDeep=Zp,i.zipWith=Hg,i.entries=ul,i.entriesIn=ll,i.extend=ol,i.extendWith=Ea,no(i,i),i.add=Pb,i.attempt=cl,i.camelCase=jb,i.capitalize=Eu,i.ceil=Ub,i.clamp=gh,i.clone=km,i.cloneDeep=Am,i.cloneDeepWith=Rm,i.cloneWith=Fm,i.conformsTo=Sm,i.deburr=Ou,i.defaultTo=Ph,i.divide=Wb,i.endsWith=vh,i.eq=Ut,i.escape=xh,i.escapeRegExp=Ch,i.every=rm,i.find=Kg,i.findIndex=gu,i.findKey=Jm,i.findLast=Jg,i.findLastIndex=bu,i.findLastKey=Ym,i.floor=zb,i.forEach=ju,i.forEachRight=wu,i.forIn=Xm,i.forInRight=Qm,i.forOwn=eh,i.forOwnRight=th,i.get=Xi,i.gt=ob,i.gte=sb,i.has=ah,i.hasIn=Qi,i.head=vu,i.identity=ht,i.includes=um,i.indexOf=_p,i.inRange=bh,i.invoke=vb,i.isArguments=zn,i.isArray=ie,i.isArrayBuffer=ub,i.isArrayLike=pt,i.isArrayLikeObject=ze,i.isBoolean=$m,i.isBuffer=Tn,i.isDate=lb,i.isElement=Nm,i.isEmpty=Im,i.isEqual=Lm,i.isEqualWith=Em,i.isError=Yi,i.isFinite=Om,i.isFunction=dn,i.isInteger=Ru,i.isLength=va,i.isMap=al,i.isMatch=Dm,i.isMatchWith=qm,i.isNaN=Pm,i.isNative=Um,i.isNil=zm,i.isNull=Wm,i.isNumber=Su,i.isObject=Ie,i.isObjectLike=qe,i.isPlainObject=Rr,i.isRegExp=yo,i.isSafeInteger=Bm,i.isSet=il,i.isString=xa,i.isSymbol=Ct,i.isTypedArray=dr,i.isUndefined=Vm,i.isWeakMap=Hm,i.isWeakSet=Gm,i.join=wp,i.kebabCase=wb,i.last=At,i.lastIndexOf=Tp,i.lowerCase=Tb,i.lowerFirst=Mb,i.lt=cb,i.lte=db,i.max=Xh,i.maxBy=Qh,i.mean=eg,i.meanBy=tg,i.min=ng,i.minBy=rg,i.stubArray=ao,i.stubFalse=io,i.stubObject=Hh,i.stubString=Gh,i.stubTrue=Zh,i.multiply=Bb,i.nth=Mp,i.noConflict=zh,i.noop=ro,i.now=La,i.pad=_h,i.padEnd=jh,i.padStart=wh,i.parseInt=Th,i.random=yh,i.reduce=cm,i.reduceRight=dm,i.repeat=Mh,i.replace=kh,i.result=uh,i.round=Vb,i.runInContext=x,i.sample=pm,i.size=gm,i.snakeCase=kb,i.some=bm,i.sortedIndex=Sp,i.sortedIndexBy=$p,i.sortedIndexOf=Np,i.sortedLastIndex=Ip,i.sortedLastIndexBy=Lp,i.sortedLastIndexOf=Ep,i.startCase=Fb,i.startsWith=Ah,i.subtract=Hb,i.sum=ag,i.sumBy=ig,i.template=Rh,i.times=Kh,i.toFinite=fn,i.toInteger=le,i.toLength=Nu,i.toLower=Sh,i.toNumber=Rt,i.toSafeInteger=Zm,i.toString=xe,i.toUpper=$h,i.trim=Nh,i.trimEnd=Ih,i.trimStart=Lh,i.truncate=Eh,i.unescape=Oh,i.uniqueId=Yh,i.upperCase=Ab,i.upperFirst=vo,i.each=ju,i.eachRight=wu,i.first=vu,no(i,function(){var e={};return Gt(i,function(t,n){Ce.call(i.prototype,n)||(e[n]=t)}),e}(),{chain:!1}),i.VERSION=Vr,d(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){i[e].placeholder=i}),d(["drop","take"],function(e,t){P.prototype[e]=function(n){n=n===m?1:Ze(le(n),0);var r=this.__filtered__&&!t?new P(this):this.clone();return r.__filtered__?r.__takeCount__=nt(n,r.__takeCount__):r.__views__.push({size:nt(n,Tt),type:e+(r.__dir__<0?"Right":"")}),r},P.prototype[e+"Right"]=function(n){return this.reverse()[e](n).reverse()}}),d(["filter","map","takeWhile"],function(e,t){var n=t+1,r=n==br||n==ei;P.prototype[e]=function(s){var c=this.clone();return c.__iteratees__.push({iteratee:ee(s,3),type:n}),c.__filtered__=c.__filtered__||r,c}}),d(["head","last"],function(e,t){var n="take"+(t?"Right":"");P.prototype[e]=function(){return this[n](1).value()[0]}}),d(["initial","tail"],function(e,t){var n="drop"+(t?"":"Right");P.prototype[e]=function(){return this.__filtered__?new P(this):this[n](1)}}),P.prototype.compact=function(){return this.filter(ht)},P.prototype.find=function(e){return this.filter(e).head()},P.prototype.findLast=function(e){return this.reverse().find(e)},P.prototype.invokeMap=de(function(e,t){return typeof e=="function"?new P(this):this.map(function(n){return Tr(n,e,t)})}),P.prototype.reject=function(e){return this.filter(ya(ee(e)))},P.prototype.slice=function(e,t){e=le(e);var n=this;return n.__filtered__&&(e>0||t<0)?new P(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==m&&(t=le(t),n=t<0?n.dropRight(-t):n.take(t-e)),n)},P.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},P.prototype.toArray=function(){return this.take(Tt)},Gt(P.prototype,function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),r=/^(?:head|last)$/.test(t),s=i[r?"take"+(t=="last"?"Right":""):t],c=r||/^find/.test(t);s&&(i.prototype[t]=function(){var h=this.__wrapped__,y=r?[1]:arguments,_=h instanceof P,F=y[0],k=_||ie(h),R=function(pe){var me=s.apply(i,A([pe],y));return r&&D?me[0]:me};k&&n&&typeof F=="function"&&F.length!=1&&(_=k=!1);var D=this.__chain__,W=!!this.__actions__.length,J=c&&!D,se=_&&!W;if(!c&&k){h=se?h:new P(this);var Y=e.apply(h,y);return Y.__actions__.push({func:ha,args:[R],thisArg:m}),new X(Y,D)}return J&&se?e.apply(this,y):(Y=this.thru(R),J?r?Y.value()[0]:Y.value():Y)})}),d(["pop","push","shift","sort","splice","unshift"],function(e){var t=_a[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);i.prototype[e]=function(){var s=arguments;if(r&&!this.__chain__){var c=this.value();return t.apply(ie(c)?c:[],s)}return this[n](function(h){return t.apply(ie(h)?h:[],s)})}}),Gt(P.prototype,function(e,t){var n=i[t];if(n){var r=n.name+"";Ce.call(lr,r)||(lr[r]=[]),lr[r].push({name:t,func:n})}}),lr[ua(m,yn).name]=[{name:"wrapper",func:m}],P.prototype.clone=dt,P.prototype.reverse=ji,P.prototype.value=Ud,i.prototype.at=Gg,i.prototype.chain=Jp,i.prototype.commit=Yp,i.prototype.next=Xp,i.prototype.plant=em,i.prototype.reverse=tm,i.prototype.toJSON=i.prototype.valueOf=i.prototype.value=nm,i.prototype.first=i.prototype.head,Sr&&(i.prototype[Sr]=Qp),i},tr=Pd();Ln?((Ln.exports=tr)._=tr,Ci._=tr):Xe._=tr}).call(qr)})(Ba,Ba.exports);var K1=Ba.exports,Ol=K1.runInContext(),ve=Gx(Ol,Ol);const Fc=/^[A-Za-z][_0-9A-Za-z]*$/,Ac=a=>({name:"attributeNameAlreadyUsed",message:Oe.unique.id,test(u){if(!u)return!1;const l=ve.snakeCase(u);return!a.some(f=>ve.snakeCase(f)===l)}}),Oo=a=>({name:"forbiddenAttributeName",message:C("error.attributeName.reserved-name"),test(u){if(!u)return!1;const l=ve.snakeCase(u);return!a.some(f=>ve.snakeCase(f)===l)}}),I={default:()=>ke().nullable(),max:()=>Wt().integer().nullable(),min:()=>Wt().integer().when("max",(a,u)=>a?u.max(a,C("error.validation.minSupMax")):u).nullable(),maxLength:()=>Wt().integer().positive(C("error.validation.positive")).nullable(),minLength:()=>Wt().integer().min(1).when("maxLength",(a,u)=>a?u.max(a,C("error.validation.minSupMax")):u).nullable(),name(a,u){return ke().test(Ac(a)).test(Oo(u)).matches(Fc,Oe.regex.id).required(Oe.required.id)},required:()=>Wr(),type:()=>ke().required(Oe.required.id),unique:()=>Wr().nullable()},Mo=(a,u)=>({name:I.name(a,u),type:I.type(),default:I.default(),unique:I.unique(),required:I.required(),maxLength:I.maxLength(),minLength:I.minLength(),regex:ke().test({name:"isValidRegExpPattern",message:C("error.validation.regex"),test(f){try{return new RegExp(f||"")!==null}catch{return!1}}}).nullable()}),ko=()=>({name:"isMinSuperiorThanMax",message:C("error.validation.minSupMax"),test(a){if(!a)return!0;const{max:u}=this.parent;return!u||Number.isNaN(_o(a))?!0:_o(u)>=_o(a)}}),Pa={date(a,u){const l={name:I.name(a,u),type:I.type()};return Ne(l)},datetime(a,u){const l={name:I.name(a,u),type:I.type()};return Ne(l)},time(a,u){const l={name:I.name(a,u),type:I.type()};return Ne(l)},default(a,u){const l={name:I.name(a,u),type:I.type()};return Ne(l)},biginteger(a,u){const l={name:I.name(a,u),type:I.type(),default:ke().nullable().matches(/^-?\d*$/),unique:I.unique(),required:I.required(),max:ke().nullable().matches(/^-?\d*$/,Oe.regex.id),min:ke().nullable().test(ko()).matches(/^-?\d*$/,Oe.regex.id)};return Ne(l)},boolean(a,u){const l={name:I.name(a,u),default:Wr().nullable(),required:I.required(),unique:I.unique()};return Ne(l)},component(a,u){const l={name:I.name(a,u),type:I.type(),required:I.required(),max:I.max(),min:I.min(),component:ke().required(Oe.required.id)};return Ne(l)},decimal(a,u){const l={name:I.name(a,u),type:I.type(),default:Wt(),required:I.required(),max:Wt(),min:Wt().test(ko())};return Ne(l)},dynamiczone(a,u){const l={name:I.name(a,u),type:I.type(),required:I.required(),max:I.max(),min:I.min()};return Ne(l)},email(a,u){const l={name:I.name(a,u),type:I.type(),default:ke().email().nullable(),unique:I.unique(),required:I.required(),maxLength:I.maxLength(),minLength:I.minLength()};return Ne(l)},enumeration(a,u){const l=/^[_A-Za-z][_0-9A-Za-z]*$/,f={name:ke().test(Ac(a)).test(Oo(u)).matches(l,Oe.regex.id).required(Oe.required.id),type:I.type(),default:I.default(),unique:I.unique(),required:I.required(),enum:xl().of(ke()).min(1,Oe.min.id).test({name:"areEnumValuesUnique",message:C("error.validation.enum-duplicate"),test(d){return d?!Z1(d.map(To).filter((p,g,v)=>v.indexOf(p)!==g)).length:!1}}).test({name:"doesNotHaveEmptyValues",message:C("error.validation.enum-empty-string"),test:d=>d?!d.map(To).some(b=>b===""):!1}).test({name:"doesMatchRegex",message:C("error.validation.enum-regex"),test:d=>d?d.map(To).every(b=>l.test(b)):!1}),enumName:ke().nullable()};return Ne(f)},float(a,u){const l={name:I.name(a,u),type:I.type(),required:I.required(),default:Wt(),max:Wt(),min:Wt().test(ko())};return Ne(l)},integer(a,u){const l={name:I.name(a,u),type:I.type(),default:Wt().integer(),unique:I.unique(),required:I.required(),max:I.max(),min:I.min()};return Ne(l)},json(a,u){const l={name:I.name(a,u),type:I.type(),required:I.required(),unique:I.unique()};return Ne(l)},media(a,u){const l={name:I.name(a,u),type:I.type(),multiple:Wr(),required:I.required(),allowedTypes:xl().of(ke().oneOf(["images","videos","files","audios"])).min(1).nullable()};return Ne(l)},password(a,u){const l={name:I.name(a,u),type:I.type(),default:I.default(),unique:I.unique(),required:I.required(),maxLength:I.maxLength(),minLength:I.minLength()};return Ne(l)},relation(a,u,l,{initialData:f,modifiedData:d}){const b={name:I.name(a,u),target:ke().required(Oe.required.id),relation:ke().required(),type:ke().required(),targetAttribute:Zx(()=>{const p=zo(d.relation,d.targetAttribute);if(p==="oneWay"||p==="manyWay")return ke().nullable();const g=ke().test(Oo(u)),w=[...l.map(({name:j})=>j),d.name].filter(j=>j!==f.targetAttribute);return g.matches(Fc,Oe.regex.id).test({name:"forbiddenTargetAttributeName",message:C("error.validation.relation.targetAttribute-taken"),test(j){return j?!w.includes(j):!1}}).required(Oe.required.id)})};return Ne(b)},richtext(a,u){const l={name:I.name(a,u),type:I.type(),default:I.default(),unique:I.unique(),required:I.required(),maxLength:I.maxLength(),minLength:I.minLength()};return Ne(l)},blocks(a,u){const l={name:I.name(a,u),type:I.type(),default:I.default(),unique:I.unique(),required:I.required(),maxLength:I.maxLength(),minLength:I.minLength()};return Ne(l)},string(a,u){const l=Mo(a,u);return Ne(l)},text(a,u){const l=Mo(a,u);return Ne(l)},uid(a,u){const l=Mo(a,u);return Ne(l)}},J1=/^[A-Za-z][-_0-9A-Za-z]*$/,Y1=(a,u,l,f,d)=>{const b={displayName:ke().test({name:"nameAlreadyUsed",message:Oe.unique.id,test(p){if(!p)return!1;const g=Pr(p,l),v=ve.snakeCase(g),w=ve.snakeCase(d);return a.every(j=>ve.snakeCase(j)!==v)&&f.every(j=>ve.snakeCase(j)!==w)}}).test({name:"nameNotAllowed",message:C("error.contentTypeName.reserved-name"),test(p){if(!p)return!1;const g=ve.snakeCase(p);return u.every(v=>ve.snakeCase(v)!==g)}}).required(Oe.required.id),category:ke().matches(J1,Oe.regex.id).required(Oe.required.id),icon:ke()};return Ne(b)},Dl={name:"displayName",type:"text",intlLabel:{id:C("contentType.displayName.label"),defaultMessage:"Display name"}},Fo={advanced:{default(){return{sections:[{items:[{intlLabel:{id:C("contentType.draftAndPublish.label"),defaultMessage:"Draft & publish"},description:{id:C("contentType.draftAndPublish.description"),defaultMessage:"Allows writing a draft version of an entry, before it is published"},name:"draftAndPublish",type:"toggle-draft-publish",validations:{}}]}]}}},base:{create(){return{sections:[{sectionTitle:null,items:[Dl,{description:{id:C("contentType.apiId-singular.description"),defaultMessage:"Used to generate the API routes and databases tables/collections"},intlLabel:{id:C("contentType.apiId-singular.label"),defaultMessage:"API ID (Singular)"},name:"singularName",type:"text-singular"},{type:"pushRight",size:6,intlLabel:{id:"",defaultMessage:""},name:"pushRight"},{description:{id:C("contentType.apiId-plural.description"),defaultMessage:"Pluralized API ID"},intlLabel:{id:C("contentType.apiId-plural.label"),defaultMessage:"API ID (Plural)"},name:"pluralName",type:"text-plural"}]}]}},edit(){return{sections:[{sectionTitle:null,items:[Dl,{disabled:!0,description:{id:C("contentType.apiId-singular.description"),defaultMessage:"Used to generate the API routes and databases tables/collections"},intlLabel:{id:C("contentType.apiId-singular.label"),defaultMessage:"API ID (Singular)"},name:"singularName",type:"text"},{type:"pushRight",size:6,intlLabel:{id:"",defaultMessage:""},name:"pushRight"},{disabled:!0,description:{id:C("contentType.apiId-plural.description"),defaultMessage:"Pluralized API ID"},intlLabel:{id:C("contentType.apiId-plural.label"),defaultMessage:"API ID (Plural)"},name:"pluralName",type:"text"},{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"kind",type:"content-type-radio-group",size:12,radios:[{title:{id:C("form.button.collection-type.name"),defaultMessage:"Collection Type"},description:{id:C("form.button.collection-type.description"),defaultMessage:"Best for multiple instances like articles, products, comments, etc."},value:"collectionType"},{title:{id:C("form.button.single-type.name"),defaultMessage:"Single Type"},description:{id:C("form.button.single-type.description"),defaultMessage:"Best for single instance like about us, homepage, etc."},value:"singleType"}]}]}]}}}},X1=({usedContentTypeNames:a=[],reservedModels:u=[],singularNames:l=[],pluralNames:f=[],collectionNames:d=[]})=>{const b={displayName:ke().test({name:"nameAlreadyUsed",message:Oe.unique.id,test(p){if(!p)return!1;const g=mc(p),v=ve.snakeCase(g);return!a.some(w=>ve.snakeCase(w)===v)}}).test({name:"nameNotAllowed",message:C("error.contentTypeName.reserved-name"),test(p){if(!p)return!1;const g=ve.snakeCase(p);return!u.some(v=>ve.snakeCase(v)===g)}}).required(Oe.required.id),pluralName:ke().test({name:"pluralNameAlreadyUsed",message:Oe.unique.id,test(p){if(!p)return!1;const g=ve.snakeCase(p);return!f.some(v=>ve.snakeCase(v)===g)}}).test({name:"pluralNameAlreadyUsedAsSingular",message:C("error.contentType.pluralName-equals-singularName"),test(p){if(!p)return!1;const g=ve.snakeCase(p);return!l.some(v=>ve.snakeCase(v)===g)}}).test({name:"pluralAndSingularAreUnique",message:C("error.contentType.pluralName-used"),test(p,g){return p?ve.snakeCase(g.parent.singularName)!==ve.snakeCase(p):!1}}).test({name:"pluralNameNotAllowed",message:C("error.contentTypeName.reserved-name"),test(p){if(!p)return!1;const g=ve.snakeCase(p);return!u.some(v=>ve.snakeCase(v)===g)}}).test({name:"pluralNameNotAlreadyUsedInCollectionName",message:C("error.contentType.pluralName-equals-collectionName"),test(p){if(!p)return!1;const g=ve.snakeCase(p);return!d.some(v=>ve.snakeCase(v)===g)}}).required(Oe.required.id),singularName:ke().test({name:"singularNameAlreadyUsed",message:Oe.unique.id,test(p){if(!p)return!1;const g=ve.snakeCase(p);return!l.some(v=>ve.snakeCase(v)===g)}}).test({name:"singularNameAlreadyUsedAsPlural",message:C("error.contentType.singularName-equals-pluralName"),test(p){if(!p)return!1;const g=ve.snakeCase(p);return!f.some(v=>ve.snakeCase(v)===g)}}).test({name:"pluralAndSingularAreUnique",message:C("error.contentType.singularName-used"),test(p,g){return p?ve.snakeCase(g.parent.pluralName)!==ve.snakeCase(p):!1}}).test({name:"singularNameNotAllowed",message:C("error.contentTypeName.reserved-name"),test(p){if(!p)return!1;const g=ve.snakeCase(p);return!u.some(v=>ve.snakeCase(v)===g)}}).required(Oe.required.id),draftAndPublish:Wr(),kind:ke().oneOf(["singleType","collectionType"])};return Ne(b)},Ao={advanced:{default(){return{sections:fr.advanced()}}},base:{createComponent(){return{sections:[{sectionTitle:null,items:[Eo]},...fr.base("componentToCreate.")]}},default(){return{sections:[{sectionTitle:null,items:[Eo]},{sectionTitle:null,items:[{type:"pushRight",size:6,intlLabel:{id:"",defaultMessage:""},name:"pushRight"},{name:"components",type:"select-components",intlLabel:{id:C("modalForm.attributes.select-components"),defaultMessage:"Select the components"},isMultiple:!0}]}]}}}},ql=(a,u)=>{a.forEach(l=>{if(!("sectionTitle"in l)){u[0].items?.push(l);return}u.push(l)})},Q1=(a,u)=>`components_${ve.snakeCase(u)}_${za(ve.snakeCase(a))}`,Pl=(a,u)=>a.filter(({name:l})=>l!==u.initialData.name).map(({name:l})=>l),Vn={customField:{schema({schemaAttributes:a,attributeType:u,customFieldValidator:l,reservedNames:f,schemaData:d,ctbFormsAPI:b}){const p=Pl(a,d);let g;return u==="relation"?g=Pa[u](p,f.attributes,[],{initialData:{},modifiedData:{}}):g=Pa[u](p,f.attributes),b.makeCustomFieldValidator(g,l,p,f.attributes,d)},form:{base({customField:a}){const u=[{sectionTitle:null,items:[Jt]}];return a.options?.base&&ql(a.options.base,u),{sections:u}},advanced({customField:a,data:u,step:l,extensions:f,...d}){const b=[{sectionTitle:null,items:[]}],p=f.getAdvancedForm(["attribute",a.type],{data:u,type:a.type,step:l,...d});if(a.options?.advanced&&ql(a.options.advanced,b),p){const g={sectionTitle:{id:C("modalForm.custom-fields.advanced.settings.extended"),defaultMessage:"Extended settings"},items:p};b.push(g)}return{sections:b}}}},attribute:{schema(a,u,l,f,d,b){const p=a?.attributes??[],g=Pl(p,d);try{const v=Pa[u](g,l.attributes,f,d);return b.makeValidator(["attribute",u],v,g,l.attributes,f,d)}catch(v){return console.error("Error yup build schema",v),Pa.default(g,l.attributes)}},form:{advanced({data:a,type:u,step:l,extensions:f,...d}){try{const b=El.advanced[u](a,l).sections,p=f.getAdvancedForm(["attribute",u],{data:a,type:u,step:l,...d});let g=!1;return{sections:b.reduce((w,j)=>(j.sectionTitle===null||g?w.push(j):(w.push({...j,items:[...j.items,...p]}),g=!0),w),[])}}catch(b){return console.error(b),{sections:[]}}},base({data:a,type:u,step:l,attributes:f}){try{return El.base[u](a,l,f)}catch{return F1}}}},contentType:{schema(a,u,l,f,d,b){const p=Object.values(b).map(T=>T.info.singularName),g=Object.values(b).map(T=>T?.info?.pluralName??""),v=u?a.filter(T=>T!==l):a,w=u?p.filter(T=>{const{info:U}=b[l];return U.singularName!==T}):p,j=u?g.filter(T=>{const{info:U}=b[l];return U.pluralName!==T}):g,A=Object.values(b).map(T=>T?.collectionName??""),O=u?A.filter(T=>{const{collectionName:U}=b[l];return T!==U}):A,K=X1({usedContentTypeNames:v,reservedModels:f.models,singularNames:w,pluralNames:j,collectionNames:O});return d.makeValidator(["contentType"],K,v,f.models,w,j)},form:{base({actionType:a}){return a==="create"?Fo.base.create():Fo.base.edit()},advanced({extensions:a}){const u=Fo.advanced.default().sections.map(f=>f.items).flat(),l=a.getAdvancedForm(["contentType"]);return{sections:[{items:[...u,...l]}]}}}},component:{schema(a,u,l,f=!1,d,b,p=null){const g=f?a.filter(A=>A!==p):a,v=Object.values(d).map(A=>A?.collectionName),w=Q1(b,u),j=f?v.filter(A=>A!==w):v;return Y1(g,l.models,u,j,w)},form:{advanced(){return{sections:fr.advanced()}},base(){return{sections:fr.base()}}}},addComponentToDynamicZone:{form:{advanced(){return Ao.advanced.default()},base({data:a}){return a?.createComponent??!1?Ao.base.createComponent():Ao.base.default()}}}},e_=(a,u)=>{const l=a.kind;return l==="singleType"||l===u.kind?!0:(a?.attributes??[]).filter(b=>{if(b.type!=="relation")return!1;const{relation:p,targetAttribute:g}=b,v=zo(p,g);return!["oneWay","manyWay"].includes(v||"")}).length===0},t_=(a="",u,l)=>{const f=["text","boolean","blocks","json","number","email","date","password","media","enumeration","relation","richtext"],d=a==="contentType";if(d)return[[...f.slice(0,-1),"uid",...f.slice(-1)],["component","dynamiczone"]];if(a){const p=kc(u,l)>=dc;if(!d&&!p)return[f,["component"]]}return[f]},Ul=a=>a.reduce((u,l)=>{const f=l.items.reduce((d,b)=>(b.name&&d.push(b.name),d),[]);return[...u,...f]},[]),n_=jt.form`
  overflow: auto;
`,r_=a=>a["content-type-builder_formModal"]||Kx,a_=()=>{const{onCloseModal:a,onNavigateToChooseAttributeModal:u,onNavigateToAddCompoToDZModal:l,onNavigateToCreateComponentStep2:f,actionType:d,attributeName:b,attributeType:p,customFieldUid:g,dynamicZoneTarget:v,forTarget:w,modalType:j,isOpen:A,kind:O,step:K,targetUid:T,showBackLink:U,activeTab:$,setActiveTab:E}=pr(),N=Ur("FormModal",Q=>Q.getPlugin),te=Ur("FormModal",Q=>Q.customFields.get)(g),L=Ga(),{toggleNotification:ne}=Po(),he=Vl(r_,Jx),Fe=Yx(),{trackUsage:Ae}=Va(),{formatMessage:Ve}=be(),oe=N(kn)?.apis.forms,V=oe.components.inputs,{addAttribute:It,editAttribute:De,addCustomFieldAttribute:Bt,addCreatedComponentToDynamicZone:Ke,changeDynamicZoneComponents:mn,contentTypes:G,components:fe,createSchema:Xt,createComponentSchema:B,deleteComponent:je,deleteContentType:Lt,editCustomFieldAttribute:Vt,updateSchema:Et,nestedComponents:Qt,sortedContentTypesList:bt,updateComponentSchema:hn,updateComponentUid:Ka,reservedNames:gn}=Yt(),{componentToCreate:mr,formErrors:ct,initialData:Re,isCreatingComponentWhileAddingAField:Kn,modifiedData:z}=he,ye=w==="component"?fe[T]:G[T],[Ja,m]=H.useState(!1),[Vr,Sn]=H.useState(null),Hr=()=>{if(d!=="edit"||j!=="attribute")return!1;const Q=Re.name,_e=Re.enum,Se=z.enum,He=(ye?.attributes||[]).filter($e=>{if(!$e.conditions)return!1;const We=$e.conditions.visible;if(!We)return!1;const[[,ri]]=Object.entries(We),[Yn,Xn]=ri;return Yn.var!==Q?!1:_e&&Se?_e.filter(vt=>!Se.includes(vt)).includes(Xn):!0});return He.length>0?He:!1};H.useEffect(()=>{if(A){const Q=bt.filter(bc);d==="edit"&&j==="attribute"&&w==="contentType"&&Ae("willEditFieldOfContentType");const _e=So(Ee(ye,"schema.attributes",[]),v)||null;if(j==="contentType"&&d==="create"&&L(Be.setDataToEdit({data:{draftAndPublish:!0}})),j==="contentType"&&d==="edit"&&L(Be.setDataToEdit({data:{displayName:ye.info.displayName,draftAndPublish:ye.options?.draftAndPublish,kind:"kind"in ye&&ye.kind,pluginOptions:ye.pluginOptions,pluralName:"pluralName"in ye.info&&ye.info.pluralName,singularName:"singularName"in ye.info&&ye.info.singularName}})),j==="component"&&d==="edit"&&L(Be.setDataToEdit({data:{displayName:ye.info.displayName,category:"category"in ye&&ye.category,icon:ye.info.icon}})),j==="addComponentToDynamicZone"&&d==="edit"){const Se={..._e,components:[],name:v,createComponent:!1,componentToCreate:{type:"component"}};L(Be.setDynamicZoneDataSchema({attributeToEdit:Se}))}if(p){const ue={...So(Ee(ye,["attributes"],[]),b),name:b};p==="component"&&d==="edit"&&(!("repeatable"in ue)||!ue.repeatable)&&Xx(ue,"repeatable",!1),L(j==="customField"?d==="edit"?Be.setCustomFieldDataSchema({isEditing:!0,modifiedDataToSetForEditing:ue,uid:ye.uid}):Be.setCustomFieldDataSchema({customField:Qx(te,["type","options"]),isEditing:!1,modifiedDataToSetForEditing:ue,uid:ye.uid}):Be.setAttributeDataSchema({attributeType:p,nameToSetForRelation:Ee(Q,["0","title"],"error"),targetUid:Ee(Q,["0","uid"],"error"),isEditing:d==="edit",modifiedDataToSetForEditing:ue,step:K,uid:ye.uid}))}}else L(Be.resetProps())},[d,b,p,v,w,A,j]);const tt=j==="contentType",Ya=j==="component",bn=j==="attribute",Gr=j==="customField",$n=p==="component"&&bn,ot=d==="create",en=Ee(z,"createComponent",!1)||Kn,yt=K==="1",tn=j==="chooseAttribute",Nn=mc(z.displayName||""),st=Ee(ye,["attributes"],null),yn=async()=>{let Q;const _e=en&&K==="1"?Ee(z,"componentToCreate",{}):z;if(tt)Q=Vn.contentType.schema(Object.keys(G),d==="edit",ye?.uid??null,gn,oe,G);else if(Ya)Q=Vn.component.schema(Object.keys(fe),z.category||"",gn,d==="edit",fe,z.displayName||"",ye?.uid??null);else if(Gr)Q=Vn.customField.schema({schemaAttributes:Ee(ye,["attributes"],[]),attributeType:te.type,reservedNames:gn,schemaData:{modifiedData:z,initialData:Re},ctbFormsAPI:oe,customFieldValidator:te.options?.validator});else if($n&&en&&yt)Q=Vn.component.schema(Object.keys(fe),Ee(z,"componentToCreate.category",""),gn,d==="edit",fe,z.componentToCreate.displayName||"");else if(bn&&!yt){const Se=p==="relation"?"relation":z.type;let ue=[];if(Se==="relation"){const He=Ee(z,["target"],null);ue=Ee(G,[He,"attributes"],[]).filter(({name:We})=>d!=="edit"?!0:We!==Re.targetAttribute)}Q=Vn.attribute.schema(ye,Se,gn,ue,{modifiedData:z,initialData:Re},oe)}else if(yt&&en)Q=Vn.component.schema(Object.keys(fe),Ee(z,"componentToCreate.category",""),gn,d==="edit",fe,z.componentToCreate.displayName||"");else return;await Q.validate(_e,{abortEarly:!1})},hr=H.useCallback(({target:{name:Q,value:_e,type:Se,...ue}})=>{const He=["enumName","max","min","maxLength","minLength","regex","default"];let $e;He.includes(Q)&&_e===""?$e=null:Q==="enum"?$e=Array.isArray(_e)?_e:[_e]:$e=_e;const We=Object.assign({},ct);Q==="max"&&delete We.min,Q==="maxLength"&&delete We.minLength,delete We[Q],L(Be.setErrors({errors:We})),L(Be.onChange({keys:Q.split("."),value:$e}))},[L,ct]),wt=async(Q,_e=ot)=>{try{await yn(),L(Be.setErrors({errors:{}})),In(_e);const Se=T;if(tt)if(ot)Xt({data:{kind:O,displayName:z.displayName,draftAndPublish:z.draftAndPublish,pluginOptions:z.pluginOptions,singularName:z.singularName,pluralName:z.pluralName},uid:Nn}),Fe({pathname:`/plugins/${kn}/content-types/${Nn}`}),a();else{const ue=ye;e_(ue,z)?(a(),await Et({uid:ue.uid,data:{displayName:z.displayName,kind:z.kind,draftAndPublish:z.draftAndPublish,pluginOptions:z.pluginOptions}})):ne({type:"danger",message:Ve({id:"notification.contentType.relations.conflict"})});return}else if(j==="component")if(ot){const ue=Pr(z.displayName,z.category),{category:He,...$e}=z;B({data:{displayName:$e.displayName,icon:$e.icon},uid:ue,componentCategory:He}),Fe({pathname:`/plugins/${kn}/component-categories/${He}/${ue}`}),a();return}else{if(hn({data:{icon:z.icon,displayName:z.displayName},componentUID:T}),ye.status==="NEW"){const ue=Pr(z.displayName,z.category);Ka({componentUID:T,newComponentUID:ue}),Fe({pathname:`/plugins/${kn}/component-categories/${z.category}/${ue}`})}a();return}else if(Gr){const ue={attributeToSet:{...z,customField:g},forTarget:w,targetUid:T,name:Re.name};d==="edit"?Vt(ue):Bt(ue),_e?u({forTarget:w,targetUid:Se}):a();return}else if(bn&&!en){if(p==="dynamiczone"){d==="create"?It({attributeToSet:z,forTarget:w,targetUid:T}):De({attributeToSet:z,forTarget:w,targetUid:T,name:Re.name}),ot?(L(Be.resetPropsAndSetTheFormForAddingACompoToADz()),E("basic"),l({dynamicZoneTarget:z.name})):a();return}if(!$n){d==="create"?It({attributeToSet:z,forTarget:w,targetUid:T}):De({attributeToSet:z,forTarget:w,targetUid:T,name:Re.name}),_e?u({forTarget:w,targetUid:Se}):a();return}if(yt){f(),L(Be.resetPropsAndSetFormForAddingAnExistingCompo({uid:ye.uid}));return}if(d==="create")It({attributeToSet:z,forTarget:w,targetUid:T});else{const He={...z};(!("conditions"in z)||z.conditions===void 0)&&(He.conditions=void 0),De({attributeToSet:He,forTarget:w,targetUid:T,name:Re.name})}_e?u({forTarget:w,targetUid:T}):a()}else if(bn&&en){if(yt){Ae("willCreateComponentFromAttributesModal"),L(Be.resetPropsAndSaveCurrentData({uid:ye.uid})),f();return}const{category:ue,...He}=mr,$e=Pr(mr.displayName,ue);B({data:{icon:He.icon,displayName:He.displayName},uid:$e,componentCategory:ue}),It({attributeToSet:z,forTarget:w,targetUid:T}),L(Be.resetProps()),_e?u({forTarget:"component",targetUid:$e}):a();return}else{if(yt)if(en){const{category:ue,type:He,...$e}=z.componentToCreate,We=Pr(z.componentToCreate.displayName,ue);B({data:$e,uid:We,componentCategory:ue}),Ke({forTarget:w,targetUid:T,dynamicZoneTarget:v,componentsToAdd:[We]}),u({forTarget:"component",targetUid:We})}else mn({forTarget:w,targetUid:T,dynamicZoneTarget:v,newComponents:z.components}),a();else console.error("This case is not handled");return}L(Be.resetProps())}catch(Se){if(e0.isError(Se)){const ue=N0(Se);L(Be.setErrors({errors:ue}))}}},Ye=async(Q,_e=ot)=>{if(Q.preventDefault(),Hr()){Sn({e:Q,shouldContinue:_e}),m(!0);return}await wt(Q,_e)},Ot=()=>{window.confirm(Ve({id:"window.confirm.close-modal.file",defaultMessage:"Are you sure? Your changes will be lost."}))&&(a(),L(Be.resetProps()))},vn=()=>{Wa(z,Re)?(a(),L(Be.resetProps())):Ot()},Dt=Q=>{if(Q==="advanced"){if(tt){Ae("didSelectContentTypeSettings");return}w==="contentType"&&Ae("didSelectContentTypeFieldSettings")}},In=Q=>{j==="attribute"&&w==="contentType"&&p!=="dynamiczone"&&Q&&Ae("willAddMoreFieldToContentType")},gr=()=>!!(j==="component"||t0(z,"createComponent")),Xa=t_(w,T,Qt);if(!j)return null;const Zr=Ee(Vn,[j,"form"],{advanced:()=>({sections:[]}),base:()=>({sections:[]})}),Qa=w==="component",Kr={customInputs:{"allowed-types-select":L0,"boolean-radio-group":K0,"checkbox-with-number-field":J0,"icon-picker":iC,"content-type-radio-group":Y0,"radio-group":Bo,relation:p1,"select-category":m1,"select-component":h1,"select-components":g1,"select-default-boolean":G0,"select-number":y1,"select-date":b1,"toggle-draft-publish":X0,"text-plural":oC,"text-singular":v1,"textarea-enum":_1,"condition-form":k1,...V},componentToCreate:mr,dynamicZoneTarget:v,formErrors:ct,isAddingAComponentToAnotherComponent:Qa,isCreatingComponentWhileAddingAField:Kn,mainBoxHeader:Ee(ye,["info","displayName"],""),modifiedData:z,naturePickerType:w,isCreating:ot,targetUid:T,forTarget:w,contentTypeSchema:ye},br=Zr.advanced({data:z,type:p,step:K,actionType:d,attributes:st,extensions:oe,forTarget:w,contentTypeSchema:ye||{},customField:te}).sections,Jr=Zr.base({data:z,type:p,step:K,actionType:d,attributes:st,extensions:oe,forTarget:w,contentTypeSchema:ye||{},customField:te}).sections,ei=Ul(Jr),nn=Ul(br),Ht=Object.keys(ct).some(Q=>ei.includes(Q)),ti=Object.keys(ct).some(Q=>nn.includes(Q)),Jn=Ee(G,[T,"kind"]),Tt=()=>d==="edit"&&st.every(({name:Q})=>Q!==z?.name),ni=()=>{Tt()&&Ae("didEditFieldNameOnContentType")};return o.jsx(Rn.Root,{open:A,onOpenChange:vn,children:o.jsxs(Rn.Content,{children:[o.jsxs(Zn.Root,{open:Ja,onOpenChange:m,children:[o.jsx(Zn.Trigger,{}),o.jsx(Br,{onConfirm:()=>{if(Vr){const{e:Q,shouldContinue:_e}=Vr;m(!1),Sn(null),wt(Q,_e)}},onCancel:()=>{m(!1),Sn(null)},children:(()=>{const Q=Hr();if(!Q)return null;const _e=Q.map(ue=>ue.name).join(", ");if(Re.enum&&z.enum){const ue=Re.enum,He=z.enum,$e=ue.filter(We=>!He.includes(We));return o.jsx(ce,{children:o.jsxs(ge,{children:[Ve({id:"form.attribute.condition.enum-change-warning",defaultMessage:"The following fields have conditions that depend on this field: "}),o.jsx(ge,{fontWeight:"bold",children:_e}),Ve({id:"form.attribute.condition.enum-change-warning-values",defaultMessage:". Changing or removing the enum values "}),o.jsx(ge,{fontWeight:"bold",children:$e.join(", ")}),Ve({id:"form.attribute.condition.enum-change-warning-end",defaultMessage:" will break these conditions. Do you want to proceed?"})]})})}return o.jsx(ce,{children:o.jsxs(ge,{children:[Ve({id:"form.attribute.condition.field-change-warning",defaultMessage:"The following fields have conditions that depend on this field: "}),o.jsx(ge,{fontWeight:"bold",children:_e}),Ve({id:"form.attribute.condition.field-change-warning-end",defaultMessage:". Renaming it will break these conditions. Do you want to proceed?"})]})})})()})]}),o.jsx(eC,{actionType:d,attributeName:b,contentTypeKind:O,dynamicZoneTarget:v,modalType:j,forTarget:w,targetUid:T,attributeType:p,customFieldUid:g,showBackLink:U}),tn&&o.jsx(H0,{attributes:Xa,forTarget:w,kind:Jn||"collectionType"}),!tn&&o.jsxs(n_,{onSubmit:Ye,children:[o.jsx(Rn.Body,{children:o.jsxs(Nt.Root,{variant:"simple",value:$,onValueChange:Q=>{E(Q),Dt(Q)},hasError:Ht?"basic":ti?"advanced":void 0,children:[o.jsxs(re,{justifyContent:"space-between",children:[o.jsx(nC,{actionType:d,forTarget:w,kind:O,step:K,modalType:j,attributeType:p,attributeName:b,customField:te}),o.jsxs(Nt.List,{children:[o.jsx(Nt.Trigger,{value:"basic",children:Ve({id:C("popUpForm.navContainer.base"),defaultMessage:"Basic settings"})}),o.jsx(Nt.Trigger,{value:"advanced",disabled:gr(),children:Ve({id:C("popUpForm.navContainer.advanced"),defaultMessage:"Advanced settings"})})]})]}),o.jsx(Ha,{marginBottom:6}),o.jsx(Nt.Content,{value:"basic",children:o.jsx(re,{direction:"column",alignItems:"stretch",gap:6,children:o.jsx(Ll,{form:Jr,formErrors:ct,genericInputProps:Kr,modifiedData:z,onChange:hr})})}),o.jsx(Nt.Content,{value:"advanced",children:o.jsx(re,{direction:"column",alignItems:"stretch",gap:6,children:o.jsx(Ll,{form:br,formErrors:ct,genericInputProps:Kr,modifiedData:z,onChange:hr})})})]})}),o.jsxs(Rn.Footer,{children:[o.jsx(Ue,{type:"button",variant:"tertiary",onClick:Q=>{Q.preventDefault(),vn()},children:Ve({id:"app.components.Button.cancel",defaultMessage:"Cancel"})}),o.jsx(Q0,{deleteContentType:()=>Lt(T),deleteComponent:()=>je(T),isAttributeModal:j==="attribute",isCustomFieldModal:j==="customField",isComponentToDzModal:j==="addComponentToDynamicZone",isComponentAttribute:p==="component",isComponentModal:j==="component",isContentTypeModal:j==="contentType",isCreatingComponent:d==="create",isCreatingDz:d==="create",isCreatingComponentAttribute:z.createComponent||!1,isCreatingComponentInDz:z.createComponent||!1,isCreatingComponentWhileAddingAField:Kn,isCreatingContentType:d==="create",isEditingAttribute:d==="edit",isDzAttribute:p==="dynamiczone",isInFirstComponentStep:K==="1",onSubmitAddComponentAttribute:Ye,onSubmitAddComponentToDz:Ye,onSubmitCreateComponent:Ye,onSubmitCreateContentType:Ye,onSubmitCreateDz:Ye,onSubmitEditAttribute:Ye,onSubmitEditComponent:Ye,onSubmitEditContentType:Ye,onSubmitEditCustomFieldAttribute:Ye,onSubmitEditDz:Ye,onClickFinish:ni})]})]})]})})},Wl={actionType:null,attributeName:null,attributeType:null,dynamicZoneTarget:null,forTarget:null,modalType:null,isOpen:!0,showBackLink:!1,kind:null,step:null,targetUid:null,customFieldUid:null,activeTab:"basic"},i_=({children:a})=>{const[u,l]=H.useState(Wl),{trackUsage:f}=Va(),d=H.useCallback(({attributeType:N,customFieldUid:S})=>{l(te=>({...te,actionType:"create",modalType:"customField",attributeType:N,customFieldUid:S,activeTab:"basic"}))},[]),b=H.useCallback(({attributeType:N,step:S})=>{u.forTarget==="contentType"&&f("didSelectContentTypeFieldType",{type:N}),l(te=>({...te,actionType:"create",modalType:"attribute",step:S,attributeType:N,showBackLink:!0,activeTab:"basic"}))},[u.forTarget,f]),p=H.useCallback(({dynamicZoneTarget:N,targetUid:S})=>{l(te=>({...te,dynamicZoneTarget:N,targetUid:S,modalType:"addComponentToDynamicZone",forTarget:"contentType",step:"1",actionType:"edit",isOpen:!0}))},[]),g=H.useCallback(({forTarget:N,targetUid:S})=>{l(te=>({...te,actionType:"create",forTarget:N,targetUid:S,modalType:"chooseAttribute",isOpen:!0,showBackLink:!1,activeTab:"basic"}))},[]),v=H.useCallback(N=>{l(S=>({...S,...N,isOpen:!0,activeTab:"basic"}))},[]),w=H.useCallback(({forTarget:N,targetUid:S,attributeName:te,attributeType:L,customFieldUid:ne})=>{l(he=>({...he,modalType:"customField",customFieldUid:ne,actionType:"edit",forTarget:N,targetUid:S,attributeName:te,attributeType:L,isOpen:!0,activeTab:"basic"}))},[]),j=H.useCallback(({forTarget:N,targetUid:S,attributeName:te,attributeType:L,step:ne})=>{l(he=>({...he,modalType:"attribute",actionType:"edit",forTarget:N,targetUid:S,attributeName:te,attributeType:L,step:ne,isOpen:!0}))},[]),A=H.useCallback(({modalType:N,forTarget:S,targetUid:te,kind:L})=>{l(ne=>({...ne,modalType:N,actionType:"edit",forTarget:S,targetUid:te,kind:L,isOpen:!0,activeTab:"basic"}))},[]),O=H.useCallback(()=>{l(Wl)},[]),K=H.useCallback(({forTarget:N,targetUid:S})=>{l(te=>({...te,forTarget:N,targetUid:S,modalType:"chooseAttribute",activeTab:"basic"}))},[]),T=H.useCallback(()=>{l(N=>({...N,attributeType:"component",modalType:"attribute",step:"2",activeTab:"basic"}))},[]),U=H.useCallback(({dynamicZoneTarget:N})=>{l(S=>({...S,dynamicZoneTarget:N,modalType:"addComponentToDynamicZone",actionType:"create",step:"1",attributeType:null,attributeName:null,activeTab:"basic"}))},[]),$=H.useCallback(N=>{l(S=>({...S,activeTab:N}))},[]),E=H.useMemo(()=>({...u,onClickSelectField:b,onClickSelectCustomField:d,onCloseModal:O,onNavigateToChooseAttributeModal:K,onNavigateToAddCompoToDZModal:U,onOpenModalAddComponentsToDZ:p,onNavigateToCreateComponentStep2:T,onOpenModalAddField:g,onOpenModalCreateSchema:v,onOpenModalEditField:j,onOpenModalEditCustomField:w,onOpenModalEditSchema:A,setFormModalNavigationState:l,setActiveTab:$}),[u,b,d,O,K,U,p,T,g,v,j,w,A,$]);return o.jsx(gc.Provider,{value:E,children:a})},o_=()=>{const{formatMessage:a}=be(),u=a({id:C("plugin.name"),defaultMessage:"Content-Type Builder"});return o.jsxs(o.Fragment,{children:[o.jsx(n0.contentTypeBuilder.Introduction,{children:o.jsx(ce,{paddingTop:5})}),o.jsxs(re,{justifyContent:"center",alignItems:"center",height:"100%",direction:"column",children:[o.jsx(ge,{variant:"alpha",children:u}),o.jsx(ge,{variant:"delta",children:a({id:C("table.content.create-first-content-type"),defaultMessage:"Create your first Collection-Type"})})]})]})},Ro=H.lazy(()=>s0(()=>import("./ListView-ea6JigI-.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]))),s_=()=>{const{formatMessage:a}=be(),u=a({id:`${kn}.plugin.name`,defaultMessage:"Content Types Builder"}),l=Hl("App",b=>b.startSection),f=Gl("DataManagerProvider",b=>b.autoReload),d=H.useRef(l);return H.useEffect(()=>{d.current&&d.current("contentTypeBuilder")},[]),o.jsxs(jo.Protect,{permissions:r0.main,children:[o.jsx(jo.Title,{children:u}),o.jsx(a0,{children:o.jsx(i_,{children:o.jsxs(R0,{children:[o.jsx(S0,{}),o.jsxs(o.Fragment,{children:[f&&o.jsx(a_,{}),o.jsx(i0.Root,{sideNav:o.jsx(h0,{}),children:o.jsx(H.Suspense,{fallback:o.jsx(jo.Loading,{}),children:o.jsxs(o0,{children:[o.jsx(qa,{path:"content-types/create-content-type",element:o.jsx(o_,{})}),o.jsx(qa,{path:"content-types/:contentTypeUid",element:o.jsx(Ro,{})}),o.jsx(qa,{path:"component-categories/:categoryUid/:componentUid",element:o.jsx(Ro,{})}),o.jsx(qa,{path:"*",element:o.jsx(Ro,{})})]})})})]})]})})})]})},h_=Object.freeze(Object.defineProperty({__proto__:null,default:s_},Symbol.toStringTag,{value:"Module"}));export{zr as A,vc as C,p_ as S,pr as a,m_ as c,C as g,h_ as i,Yt as u};
