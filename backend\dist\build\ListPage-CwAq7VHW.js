import{a as L,j as e,Y as _,Z as x,T as l,e as I,$ as N,I as $,aL as O,P as m,v as V,w as W,r as M,u as q,z as U,V as H,bV as z,bW as X,B as R,D as J,av as w,W as Q,bn as Y,bo as f,bq as Z,X as G,bX as K,g as ee,bm as se,bs as te,bt as ae,bu as ne,J as oe}from"./strapi-z7ApxZZq.js";import{S as ie}from"./SearchInput-Cmj1ynyp.js";import{u as le}from"./useAdminRoles-DhqKz3-J.js";import{s as re}from"./selectors-B6uMLQu7.js";const de=({id:s,name:n,description:a,usersCount:c,icons:u,rowIndex:j,canUpdate:g,cursor:h})=>{const{formatMessage:r}=L(),[,C]=u,d=r({id:"Roles.RoleRow.user-count",defaultMessage:"{number, plural, =0 {#  user} one {#  user} other {# users}}"},{number:c});return e.jsxs(_,{cursor:h,"aria-rowindex":j,onClick:g?C.onClick:void 0,children:[e.jsx(x,{maxWidth:"13rem",children:e.jsx(l,{ellipsis:!0,textColor:"neutral800",children:n})}),e.jsx(x,{maxWidth:"25rem",children:e.jsx(l,{ellipsis:!0,textColor:"neutral800",children:a})}),e.jsx(x,{children:e.jsx(l,{textColor:"neutral800",children:d})}),e.jsx(x,{children:e.jsx(I,{justifyContent:"flex-end",onClick:o=>o.stopPropagation(),children:u.map((o,p)=>o?e.jsx(N,{paddingLeft:p===0?0:1,children:e.jsx($,{...o,variant:"ghost"})},o.label):null)})})]},s)},ce=()=>{const{formatMessage:s}=L(),n=O(re),{formatAPIError:a}=V(),{toggleNotification:c}=W(),[u,j]=M.useState(!1),[{query:g}]=q(),{isLoading:h,allowedActions:{canCreate:r,canDelete:C,canRead:d,canUpdate:o}}=U(n.settings?.roles),{roles:p,refetch:A}=le({filters:g?._q?{name:{$containsi:g._q}}:void 0},{refetchOnMountOrArgChange:!0,skip:h||!d}),E=H(),[{roleToDelete:y},b]=M.useReducer(ge,ue),{post:k}=X(),P=async()=>{try{b({type:"ON_REMOVE_ROLES"}),await k("/admin/roles/batch-delete",{ids:[y]}),await A(),b({type:"RESET_DATA_TO_DELETE"})}catch(t){oe(t)&&c({type:"danger",message:a(t)})}},T=()=>E("new"),D=()=>j(t=>!t),S=t=>i=>{i.preventDefault(),i.stopPropagation(),t.usersCount?c({type:"info",message:s({id:"Roles.ListPage.notification.delete-not-allowed"})}):(b({type:"SET_ROLE_TO_DELETE",id:t.id}),D())},v=t=>i=>{i.preventDefault(),i.stopPropagation(),E(`duplicate/${t.id}`)},B=p.length+1,F=6;return h?e.jsx(m.Loading,{}):e.jsxs(m.Main,{children:[e.jsx(m.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Roles"})}),e.jsx(R.Header,{primaryAction:r?e.jsx(J,{onClick:T,startIcon:e.jsx(w,{}),size:"S",children:s({id:"Settings.roles.list.button.add",defaultMessage:"Add new role"})}):null,title:s({id:"global.roles",defaultMessage:"roles"}),subtitle:s({id:"Settings.roles.list.description",defaultMessage:"List of roles"})}),d&&e.jsx(R.Action,{startActions:e.jsx(ie,{label:s({id:"app.component.search.label",defaultMessage:"Search for {target}"},{target:s({id:"global.roles",defaultMessage:"roles"})})})}),d&&e.jsx(R.Content,{children:e.jsxs(Q,{colCount:F,rowCount:B,footer:r?e.jsx(te,{cursor:"pointer",onClick:T,icon:e.jsx(w,{}),children:s({id:"Settings.roles.list.button.add",defaultMessage:"Add new role"})}):null,children:[e.jsx(Y,{children:e.jsxs(_,{"aria-rowindex":1,children:[e.jsx(f,{children:e.jsx(l,{variant:"sigma",textColor:"neutral600",children:s({id:"global.name",defaultMessage:"Name"})})}),e.jsx(f,{children:e.jsx(l,{variant:"sigma",textColor:"neutral600",children:s({id:"global.description",defaultMessage:"Description"})})}),e.jsx(f,{children:e.jsx(l,{variant:"sigma",textColor:"neutral600",children:s({id:"global.users",defaultMessage:"Users"})})}),e.jsx(f,{children:e.jsx(Z,{children:s({id:"global.actions",defaultMessage:"Actions"})})})]})}),e.jsx(G,{children:p?.map((t,i)=>e.jsx(de,{cursor:"pointer",id:t.id,name:t.name,description:t.description,usersCount:t.usersCount,icons:[r&&{onClick:v(t),label:s({id:"app.utils.duplicate",defaultMessage:"Duplicate"}),children:e.jsx(K,{})},o&&{onClick:()=>E(t.id.toString()),label:s({id:"app.utils.edit",defaultMessage:"Edit"}),children:e.jsx(ee,{})},C&&{onClick:S(t),label:s({id:"global.delete",defaultMessage:"Delete"}),children:e.jsx(se,{})}].filter(Boolean),rowIndex:i+2,canUpdate:o},t.id))})]})}),e.jsx(ae.Root,{open:u,onOpenChange:D,children:e.jsx(ne,{onConfirm:P})})]})},ue={roleToDelete:null,showModalConfirmButtonLoading:!1,shouldRefetchData:!1},ge=(s,n)=>z(s,a=>{switch(n.type){case"ON_REMOVE_ROLES":{a.showModalConfirmButtonLoading=!0;break}case"ON_REMOVE_ROLES_SUCCEEDED":{a.shouldRefetchData=!0,a.roleToDelete=null;break}case"RESET_DATA_TO_DELETE":{a.shouldRefetchData=!1,a.roleToDelete=null,a.showModalConfirmButtonLoading=!1;break}case"SET_ROLE_TO_DELETE":{a.roleToDelete=n.id;break}default:return a}}),me=()=>{const s=O(n=>n.admin_app.permissions.settings?.roles.read);return e.jsx(m.Protect,{permissions:s,children:e.jsx(ce,{})})};export{ce as ListPage,me as ProtectedListPage};
