{"version": 3, "sources": ["../../../@strapi/upload/dist/admin/translations/pl.json.mjs"], "sourcesContent": ["var pl = {\n    \"bulk.select.label\": \"W<PERSON><PERSON>rz wszystkie\",\n    \"button.next\": \"<PERSON><PERSON>\",\n    \"checkControl.crop-duplicate\": \"Duplikuj i przytnij\",\n    \"checkControl.crop-original\": \"P<PERSON>ytnij\",\n    \"content.isLoading\": \"Ładowanie listy.\",\n    \"control-card.add\": \"Dodaj\",\n    \"control-card.cancel\": \"Anuluj\",\n    \"control-card.copy-link\": \"Kopiuj link\",\n    \"control-card.crop\": \"Przytnij\",\n    \"control-card.download\": \"Pobierz\",\n    \"control-card.edit\": \"Edytuj\",\n    \"control-card.replace-media\": \"Zamień\",\n    \"control-card.save\": \"Zapisz\",\n    \"control-card.stop-crop\": \"Zatrzymaj przycinanie\",\n    \"filter.add\": \"Dodaj filtr\",\n    \"form.button.replace-media\": \"Zamień\",\n    \"form.input.description.file-alt\": \"Ten tekst zostanie wyświetlony, je<PERSON><PERSON> medium nie będzie mogło zostać pokazane.\",\n    \"form.input.label.file-alt\": \"Tekst alternatywny\",\n    \"form.input.label.file-caption\": \"Podpis\",\n    \"form.input.label.file-name\": \"Nazwa pliku\",\n    \"form.upload-url.error.url.invalid\": \"Link URL jest niepoprawny\",\n    \"form.upload-url.error.url.invalids\": \"{number} linków URL jest niepoprawnych\",\n    \"header.actions.add-assets\": \"Dodaj\",\n    \"header.actions.upload-assets\": \"Prześlij zasób\",\n    \"header.content.assets\": \"{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 zasób} other {# zasobów}}\",\n    \"input.button.label\": \"Przeglądaj pliki\",\n    \"input.label\": \"Przeciągnij i upuść tutaj lub\",\n    \"input.label-bold\": \"Przeciągnij & upuść\",\n    \"input.label-normal\": \"do przesłania lub\",\n    \"input.placeholder\": \"Kliknij aby wybrać lub przeciągnij i upuść plik tutaj\",\n    \"input.placeholder.icon\": \"Upuść tutaj\",\n    \"input.url.description\": \"Oddziel swoje linki URL umieszczając je w nowych liniach.\",\n    \"input.url.label\": \"URL\",\n    \"list.asset.at.finished\": \"Załadowano.\",\n    \"list.assets-empty.search\": \"Brak wyników wyszukiwania\",\n    \"list.assets-empty.subtitle\": \"Dodaj jeden do listy.\",\n    \"list.assets-empty.title\": \"Nie ma jeszcze żadnych zasobów\",\n    \"list.assets-empty.title-withSearch\": \"Brak zasobów pasujących do wybranych filtrów\",\n    \"list.assets.empty\": \"Prześlij pierwszy plik...\",\n    \"list.assets.empty.no-permissions\": \"Lista jest pusta.\",\n    \"list.assets.loading-asset\": \"Ładowanie podglądu dla: {path}\",\n    \"list.assets.not-supported-content\": \"Podgląd nie jest dostępny\",\n    \"list.assets.preview-asset\": \"Podgląd dla video: {path}\",\n    \"list.assets.selected\": \"{number, plural, =0 {No asset} other {# zaznaczono}}\",\n    \"list.assets.type-not-allowed\": \"Ten typ pliku jest niedozwolony.\",\n    \"mediaLibraryInput.actions.nextSlide\": \"Następny\",\n    \"mediaLibraryInput.actions.previousSlide\": \"Poprzedni\",\n    \"mediaLibraryInput.placeholder\": \"Kliknij by dodać plik lub przeciągnij i upuść plik w to miejsce\",\n    \"mediaLibraryInput.slideCount\": \"{n} z {m}\",\n    \"modal.file-details.date\": \"Data\",\n    \"modal.file-details.dimensions\": \"Wymiary\",\n    \"modal.file-details.extension\": \"Rozszerzenia\",\n    \"modal.file-details.size\": \"Rozmiar\",\n    \"modal.header.browse\": \"Prześlij media\",\n    \"modal.header.file-detail\": \"Szczegóły\",\n    \"modal.header.pending-assets\": \"Oczekujące\",\n    \"modal.header.select-files\": \"Wybrane pliki\",\n    \"modal.nav.browse\": \"przeglądaj\",\n    \"modal.nav.computer\": \"z komputera\",\n    \"modal.nav.selected\": \"wybrane\",\n    \"modal.nav.url\": \"z linku URL\",\n    \"modal.remove.success-label\": \"Usunięto\",\n    \"modal.selected-list.sub-header-subtitle\": \"Przeciągnij i upuść, aby zmienić kolejność zasobów w polu\",\n    \"modal.upload-list.footer.button\": \"Prześlij\",\n    \"modal.upload-list.sub-header-subtitle\": \"Zarządzaj zasobami przed dodaniem ich do Biblioteki Multimediów\",\n    \"modal.upload-list.sub-header.button\": \"Dodaj więcej\",\n    \"modal.upload.cancelled\": \"Przesyłanie przerwane.\",\n    \"page.title\": \"Ustawienia - Biblioteka Multimediów\",\n    \"permissions.not-allowed.update\": \"Brak dostępu do edycji pliku.\",\n    \"plugin.description.long\": \"Zarządzanie plikami multimedialnymi.\",\n    \"plugin.description.short\": \"Zarządzanie plikami multimedialnymi.\",\n    \"plugin.name\": \"Bilbioteka Multimediów\",\n    \"search.clear.label\": \"Wyczyść\",\n    \"search.label\": \"Szukaj\",\n    \"search.placeholder\": \"Szukaj...\",\n    \"settings.blockTitle\": \"Zarządzanie plikami\",\n    \"settings.form.autoOrientation.description\": \"Automatycznie obróć obraz zgodnie ze znacznikiem orientacji EXIF\",\n    \"settings.form.autoOrientation.label\": \"Włącz automatyczną orientację\",\n    \"settings.form.responsiveDimensions.description\": \"Automatycznie generuje wiele formatów (duży, średni, mały) przesłanego zasobu\",\n    \"settings.form.responsiveDimensions.label\": \"Włącz przesyłanie przyjazne responsywności\",\n    \"settings.form.sizeOptimization.description\": \"Włączenie tej opcji zmniejszy rozmiar obrazka i delikatnie zmniejszy jego jakość.\",\n    \"settings.form.sizeOptimization.label\": \"Włącz optymalizację rozmiaru (bez utraty jakości)\",\n    \"settings.form.videoPreview.description\": \"Wygeneruje sześciosekundowy podgląd wideo (GIF)\",\n    \"settings.form.videoPreview.label\": \"Podgląd\",\n    \"settings.header.label\": \"Biblioteka Multimediów\",\n    \"settings.section.doc.label\": \"Dokument\",\n    \"settings.section.image.label\": \"Obraz\",\n    \"settings.section.video.label\": \"Video\",\n    \"settings.sub-header.label\": \"Skonfiguruj ustawienia biblioteki multimediów\",\n    \"sort.created_at_asc\": \"Najstarsze przesłane\",\n    \"sort.created_at_desc\": \"Ostatnio przesłane\",\n    \"sort.label\": \"Sortuj po\",\n    \"sort.name_asc\": \"Kolejność alfabetyczna (od A do Z)\",\n    \"sort.name_desc\": \"Odwróć kolejność alfabetyczną (od Z do A)\",\n    \"sort.updated_at_asc\": \"Najstarsze aktualizacje\",\n    \"sort.updated_at_desc\": \"Ostatnie aktualizacje\",\n    \"tabs.title\": \"W jaki sposób chcesz przesłać pliki?\",\n    \"window.confirm.close-modal.file\": \"Czy napewno? Twoje zmiany zostaną utracone.\",\n    \"window.confirm.close-modal.files\": \"Czy napewno? Masz pliki, które nie zostały jeszcze przesłane.\"\n};\n\nexport { pl as default };\n//# sourceMappingURL=pl.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,eAAe;AAAA,EACf,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,gCAAgC;AAAA,EAChC,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,8BAA8B;AAAA,EAC9B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,cAAc;AAAA,EACd,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,6CAA6C;AAAA,EAC7C,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,mCAAmC;AAAA,EACnC,oCAAoC;AACxC;", "names": []}