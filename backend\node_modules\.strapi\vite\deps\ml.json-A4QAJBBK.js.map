{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/ml.json.mjs"], "sourcesContent": ["var groups = \"ഗ്രൂപ്പുകൾ\";\nvar models = \"ശേഖരണ തരങ്ങൾ\";\nvar pageNotFound = \"പേജ് കണ്ടെത്തിയില്ല\";\nvar ml = {\n    \"App.schemas.data-loaded\": \"സ്കീമകൾ വിജയകരമായി ലോഡ് ചെയ്തു\",\n    \"ListViewTable.relation-loaded\": \"ബന്ധങ്ങൾ ലോഡ് ചെയ്തു\",\n    \"ListViewTable.relation-loading\": \"ബന്ധങ്ങൾ ലോഡുചെയ്യുന്നു\",\n    \"ListViewTable.relation-more\": \"പ്രദർശിപ്പിച്ചതിനേക്കാൾ കൂടുതൽ എന്റിറ്റികൾ ഈ ബന്ധത്തിൽ അടങ്ങിയിരിക്കുന്നു\",\n    \"EditRelations.title\": \"റിലേഷണൽ ഡാറ്റ\",\n    \"HeaderLayout.button.label-add-entry\": \"പുതിയ എൻട്രി സൃഷ്ടിക്കുക\",\n    \"api.id\": \"API ഐഡി\",\n    \"components.AddFilterCTA.add\": \"ഫിൽട്ടറുകൾ\",\n    \"components.AddFilterCTA.hide\": \"ഫിൽട്ടറുകൾ\",\n    \"components.DragHandle-label\": \"ഡ്രാഗ്\",\n    \"components.DraggableAttr.edit\": \"എഡിറ്റ് ചെയ്യാൻ ക്ലിക്ക് ചെയ്യുക\",\n    \"components.DraggableCard.delete.field\": \"{item} ഇല്ലാതാക്കുക\",\n    \"components.DraggableCard.edit.field\": \"എഡിറ്റ് {ഇനം}\",\n    \"components.DraggableCard.move.field\": \"നീക്കുക {ഇനം}\",\n    \"components.ListViewTable.row-line\": \"ഇനത്തിന്റെ വരി {number}\",\n    \"components.DynamicZone.ComponentPicker-label\": \"ഒരു ഘടകം തിരഞ്ഞെടുക്കുക\",\n    \"components.DynamicZone.add-component\": \"{componentName} എന്നതിലേക്ക് ഒരു ഘടകം ചേർക്കുക\",\n    \"components.DynamicZone.delete-label\": \"{name} ഇല്ലാതാക്കുക\",\n    \"components.DynamicZone.error-message\": \"ഘടകത്തിൽ പിശക്(കൾ) അടങ്ങിയിരിക്കുന്നു\",\n    \"components.DynamicZone.missing-components\": \"അവിടെ {സംഖ്യ, ബഹുവചനം, =0 {നഷ്‌ടമായ # ഘടകങ്ങളുണ്ട്} ഒന്ന് {# നഷ്‌ടമായ ഘടകമാണ്} മറ്റൊന്ന് {# നഷ്‌ടമായ ഘടകങ്ങളാണ്}}\",\n    \"components.DynamicZone.move-down-label\": \"ഘടകം താഴേക്ക് നീക്കുക\",\n    \"components.DynamicZone.move-up-label\": \"ഘടകം മുകളിലേക്ക് നീക്കുക\",\n    \"components.DynamicZone.pick-compo\": \"ഒരു ഘടകം തിരഞ്ഞെടുക്കുക\",\n    \"components.DynamicZone.required\": \"ഘടകം ആവശ്യമാണ്\",\n    \"components.EmptyAttributesBlock.button\": \"ക്രമീകരണ പേജിലേക്ക് പോകുക\",\n    \"components.EmptyAttributesBlock.description\": \"നിങ്ങളുടെ ക്രമീകരണങ്ങൾ നിങ്ങൾക്ക് മാറ്റാവുന്നതാണ്\",\n    \"components.FieldItem.linkToComponentLayout\": \"ഘടകത്തിന്റെ ലേഔട്ട് സജ്ജമാക്കുക\",\n    \"components.FieldSelect.label\": \"ഒരു ഫീൽഡ് ചേർക്കുക\",\n    \"components.FilterOptions.button.apply\": \"പ്രയോഗിക്കുക\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"പ്രയോഗിക്കുക\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"എല്ലാം മായ്‌ക്കുക\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"എൻട്രികൾ ഫിൽട്ടർ ചെയ്യുന്നതിന് ബാധകമാക്കുന്നതിനുള്ള വ്യവസ്ഥകൾ സജ്ജമാക്കുക\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"ഫിൽട്ടറുകൾ\",\n    \"components.FiltersPickWrapper.hide\": \"മറയ്ക്കുക\",\n    \"components.LeftMenu.Search.label\": \"ഒരു ഉള്ളടക്ക തരത്തിനായി തിരയുക\",\n    \"components.LeftMenu.collection-types\": \"ശേഖരണ തരങ്ങൾ\",\n    \"components.LeftMenu.single-types\": \"ഒറ്റ തരങ്ങൾ\",\n    \"components.LimitSelect.itemsPerPage\": \"ഓരോ പേജിനും ഇനങ്ങൾ\",\n    \"components.NotAllowedInput.text\": \"ഈ ഫീൽഡ് കാണുന്നതിന് അനുമതികളൊന്നുമില്ല\",\n    \"components.RepeatableComponent.error-message\": \"ഘടക(ങ്ങളിൽ) പിശക്(കൾ) അടങ്ങിയിരിക്കുന്നു\",\n    \"components.Search.placeholder\": \"ഒരു എൻട്രിക്കായി തിരയുക...\",\n    \"components.Select.draft-info-title\": \"സംസ്ഥാനം: ഡ്രാഫ്റ്റ്\",\n    \"components.Select.publish-info-title\": \"സംസ്ഥാനം: പ്രസിദ്ധീകരിച്ചത്\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"എഡിറ്റ് കാഴ്‌ച എങ്ങനെയായിരിക്കുമെന്ന് ഇഷ്ടാനുസൃതമാക്കുക.\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"ലിസ്റ്റ് കാഴ്ചയുടെ ക്രമീകരണങ്ങൾ നിർവചിക്കുക.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"കാഴ്ച കോൺഫിഗർ ചെയ്യുക - {name}\",\n    \"components.TableDelete.delete\": \"എല്ലാം ഇല്ലാതാക്കുക\",\n    \"components.TableDelete.deleteSelected\": \"തിരഞ്ഞെടുത്ത ഇല്ലാതാക്കുക\",\n    \"components.TableDelete.label\": \"{സംഖ്യ, ബഹുവചനം, ഒരു {# എൻട്രി} മറ്റ് {# എൻട്രികൾ}} തിരഞ്ഞെടുത്തു\",\n    \"components.TableEmpty.withFilters\": \"പ്രയോഗിച്ച ഫിൽട്ടറുകൾക്കൊപ്പം {contentType} ഒന്നുമില്ല...\",\n    \"components.TableEmpty.withSearch\": \"തിരയലുമായി ബന്ധപ്പെട്ട {contentType} ഒന്നുമില്ല ({തിരയൽ})...\",\n    \"components.TableEmpty.withoutFilter\": \"{contentType} ഒന്നുമില്ല...\",\n    \"components.empty-repeatable\": \"ഇതുവരെ എൻട്രി ഒന്നുമില്ല. ഒരെണ്ണം ചേർക്കാൻ ചുവടെയുള്ള ബട്ടണിൽ ക്ലിക്കുചെയ്യുക.\",\n    \"components.notification.info.maximum-requirement\": \"നിങ്ങൾ ഇതിനകം പരമാവധി എണ്ണം ഫീൽഡുകളിൽ എത്തിയിരിക്കുന്നു\",\n    \"components.notification.info.minimum-requirement\": \"കുറഞ്ഞ ആവശ്യകതയുമായി പൊരുത്തപ്പെടുന്നതിന് ഒരു ഫീൽഡ് ചേർത്തു\",\n    \"components.repeatable.reorder.error\": \"നിങ്ങളുടെ ഘടകത്തിന്റെ ഫീൽഡ് പുനഃക്രമീകരിക്കുമ്പോൾ ഒരു പിശക് സംഭവിച്ചു, ദയവായി വീണ്ടും ശ്രമിക്കുക\",\n    \"components.reset-entry\": \"റീസെറ്റ് എൻട്രി\",\n    \"components.uid.apply\": \"പ്രയോഗിക്കുക\",\n    \"components.uid.available\": \"ലഭ്യം\",\n    \"components.uid.regenerate\": \"പുനരുജ്ജീവിപ്പിക്കുക\",\n    \"components.uid.suggested\": \"നിർദ്ദേശിച്ചത്\",\n    \"components.uid.unavailable\": \"ലഭ്യമല്ല\",\n    \"containers.Edit.Link.Layout\": \"ലേഔട്ട് കോൺഫിഗർ ചെയ്യുക\",\n    \"containers.Edit.Link.Model\": \"ശേഖരം-തരം എഡിറ്റ് ചെയ്യുക\",\n    \"containers.Edit.addAnItem\": \"ഒരു ഇനം ചേർക്കുക...\",\n    \"containers.Edit.clickToJump\": \"എൻട്രിയിലേക്ക് പോകുന്നതിന് ക്ലിക്കുചെയ്യുക\",\n    \"containers.Edit.delete\": \"ഇല്ലാതാക്കുക\",\n    \"containers.Edit.delete-entry\": \"ഈ എൻട്രി ഇല്ലാതാക്കുക\",\n    \"containers.Edit.editing\": \"എഡിറ്റിംഗ്...\",\n    \"containers.Edit.information\": \"വിവരങ്ങൾ\",\n    \"containers.Edit.information.by\": \"എഴുതിയത്\",\n    \"containers.Edit.information.created\": \"സൃഷ്ടിച്ചു\",\n    \"containers.Edit.information.draftVersion\": \"ഡ്രാഫ്റ്റ് പതിപ്പ്\",\n    \"containers.Edit.information.editing\": \"എഡിറ്റിംഗ്\",\n    \"containers.Edit.information.lastUpdate\": \"അവസാന അപ്ഡേറ്റ്\",\n    \"containers.Edit.information.publishedVersion\": \"പ്രസിദ്ധീകരിച്ച പതിപ്പ്\",\n    \"containers.Edit.pluginHeader.title.new\": \"ഒരു എൻട്രി സൃഷ്ടിക്കുക\",\n    \"containers.Edit.reset\": \"പുനഃസജ്ജമാക്കുക\",\n    \"containers.Edit.returnList\": \"ലിസ്റ്റിലേക്ക് മടങ്ങുക\",\n    \"containers.Edit.seeDetails\": \"വിശദാംശങ്ങൾ\",\n    \"containers.Edit.submit\": \"സംരക്ഷിക്കുക\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"ഫീൽഡ് എഡിറ്റ് ചെയ്യുക\",\n    \"containers.EditView.add.new-entry\": \"ഒരു എൻട്രി ചേർക്കുക\",\n    \"containers.EditView.notification.errors\": \"ഫോമിൽ ചില പിശകുകൾ അടങ്ങിയിരിക്കുന്നു\",\n    \"containers.Home.introduction\": \"നിങ്ങളുടെ എൻട്രികൾ എഡിറ്റ് ചെയ്യുന്നതിന് ഇടത് മെനുവിലെ നിർദ്ദിഷ്‌ട ലിങ്കിലേക്ക് പോകുക. ഈ പ്ലഗിന്നിന് ക്രമീകരണങ്ങൾ എഡിറ്റ് ചെയ്യാൻ ശരിയായ മാർഗമില്ല, അത് ഇപ്പോഴും സജീവമായ വികസനത്തിലാണ്.\",\n    \"containers.Home.pluginHeaderDescription\": \"ശക്തവും മനോഹരവുമായ ഒരു ഇന്റർഫേസിലൂടെ നിങ്ങളുടെ എൻട്രികൾ കൈകാര്യം ചെയ്യുക.\",\n    \"containers.Home.pluginHeaderTitle\": \"ഉള്ളടക്ക മാനേജർ\",\n    \"containers.List.draft\": \"ഡ്രാഫ്റ്റ്\",\n    \"containers.List.errorFetchRecords\": \"പിശക്\",\n    \"containers.List.published\": \"പ്രസിദ്ധീകരിച്ചു\",\n    \"containers.list.displayedFields\": \"പ്രദർശിപ്പിച്ച ഫീൽഡുകൾ\",\n    \"containers.list.items\": \"{സംഖ്യ, ബഹുവചനം, =0 {ഇനങ്ങൾ} ഒന്ന് {ഇനം} മറ്റൊന്ന് {ഇനങ്ങൾ}}\",\n    \"containers.list.table-headers.publishedAt\": \"സ്റ്റേറ്റ്\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"എഡിറ്റ് {fieldName}\",\n    \"containers.SettingPage.add.field\": \"മറ്റൊരു ഫീൽഡ് ചേർക്കുക\",\n    \"containers.SettingPage.attributes\": \"ആട്രിബ്യൂട്ടുകളുടെ ഫീൽഡുകൾ\",\n    \"containers.SettingPage.attributes.description\": \"ആട്രിബ്യൂട്ടുകളുടെ ക്രമം നിർവചിക്കുക\",\n    \"containers.SettingPage.editSettings.description\": \"ലേഔട്ട് നിർമ്മിക്കുന്നതിന് ഫീൽഡുകൾ വലിച്ചിടുക\",\n    \"containers.SettingPage.editSettings.entry.title\": \"എൻട്രി ശീർഷകം\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"നിങ്ങളുടെ എൻട്രിയുടെ പ്രദർശിപ്പിച്ച ഫീൽഡ് സജ്ജമാക്കുക\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"എഡിറ്റ്, ലിസ്റ്റ് കാഴ്‌ചകളിൽ പ്രദർശിപ്പിച്ച ഫീൽഡ് സജ്ജമാക്കുക\",\n    \"containers.SettingPage.editSettings.title\": \"കാഴ്ച എഡിറ്റ് ചെയ്യുക (ക്രമീകരണങ്ങൾ)\",\n    \"containers.SettingPage.layout\": \"ലേഔട്ട്\",\n    \"containers.SettingPage.listSettings.description\": \"ഈ ശേഖരണ തരത്തിനായുള്ള ഓപ്ഷനുകൾ കോൺഫിഗർ ചെയ്യുക\",\n    \"containers.SettingPage.listSettings.title\": \"ലിസ്റ്റ് കാഴ്ച (ക്രമീകരണങ്ങൾ)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"ഈ ശേഖരണ തരത്തിനായുള്ള നിർദ്ദിഷ്ട ക്രമീകരണങ്ങൾ കോൺഫിഗർ ചെയ്യുക\",\n    \"containers.SettingPage.settings\": \"ക്രമീകരണങ്ങൾ\",\n    \"containers.SettingPage.view\": \"കാണുക\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"ഉള്ളടക്ക മാനേജർ - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"നിർദ്ദിഷ്ട ക്രമീകരണങ്ങൾ കോൺഫിഗർ ചെയ്യുക\",\n    \"containers.SettingsPage.Block.contentType.title\": \"ശേഖരണ തരങ്ങൾ\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"നിങ്ങളുടെ ശേഖരണ തരങ്ങൾക്കായി സ്ഥിരസ്ഥിതി ഓപ്ഷനുകൾ കോൺഫിഗർ ചെയ്യുക\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"പൊതുവായത്\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"നിങ്ങളുടെ എല്ലാ ശേഖരണ തരങ്ങൾക്കും ഗ്രൂപ്പുകൾക്കുമായി ക്രമീകരണങ്ങൾ കോൺഫിഗർ ചെയ്യുക\",\n    \"containers.SettingsView.list.subtitle\": \"നിങ്ങളുടെ ശേഖരണ തരങ്ങളുടെയും ഗ്രൂപ്പുകളുടെയും ലേഔട്ടും പ്രദർശനവും കോൺഫിഗർ ചെയ്യുക\",\n    \"containers.SettingsView.list.title\": \"ഡിസ്‌പ്ലേ കോൺഫിഗറേഷനുകൾ\",\n    \"edit-settings-view.link-to-ctb.components\": \"ഘടകം എഡിറ്റ് ചെയ്യുക\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"ഉള്ളടക്ക തരം എഡിറ്റ് ചെയ്യുക\",\n    \"emptyAttributes.button\": \"ശേഖരണ തരം ബിൽഡറിലേക്ക് പോകുക\",\n    \"emptyAttributes.description\": \"നിങ്ങളുടെ ശേഖരണ തരത്തിലേക്ക് നിങ്ങളുടെ ആദ്യ ഫീൽഡ് ചേർക്കുക\",\n    \"emptyAttributes.title\": \"ഇതുവരെ ഫീൽഡുകളൊന്നുമില്ല\",\n    \"error.attribute.key.taken\": \"ഈ മൂല്യം ഇതിനകം നിലവിലുണ്ട്\",\n    \"error.attribute.sameKeyAndName\": \"തുല്യനാകാൻ കഴിയില്ല\",\n    \"error.attribute.taken\": \"ഈ ഫീൽഡ് നാമം ഇതിനകം നിലവിലുണ്ട്\",\n    \"error.contentTypeName.taken\": \"ഈ പേര് ഇതിനകം നിലവിലുണ്ട്\",\n    \"error.model.fetch\": \"മോഡലുകൾ കോൺഫിഗർ ചെയ്യുന്നതിനിടയിൽ ഒരു പിശക് സംഭവിച്ചു.\",\n    \"error.record.create\": \"റെക്കോർഡ് സൃഷ്ടിക്കുന്നതിനിടയിൽ ഒരു പിശക് സംഭവിച്ചു.\",\n    \"error.record.delete\": \"റെക്കോർഡ് ഇല്ലാതാക്കുമ്പോൾ ഒരു പിശക് സംഭവിച്ചു.\",\n    \"error.record.fetch\": \"റെക്കോർഡ് ലഭ്യമാക്കുമ്പോൾ ഒരു പിശക് സംഭവിച്ചു.\",\n    \"error.record.update\": \"റെക്കോർഡ് അപ്ഡേറ്റ് സമയത്ത് ഒരു പിശക് സംഭവിച്ചു.\",\n    \"error.records.count\": \"എണ്ണം രേഖപ്പെടുത്തുന്ന സമയത്ത് ഒരു പിശക് സംഭവിച്ചു.\",\n    \"error.records.fetch\": \"രേഖകൾ ലഭ്യമാക്കുന്നതിനിടയിൽ ഒരു പിശക് സംഭവിച്ചു.\",\n    \"error.schema.generation\": \"സ്‌കീമ ജനറേഷൻ സമയത്ത് ഒരു പിശക് സംഭവിച്ചു.\",\n    \"error.validation.json\": \"ഇതൊരു JSON അല്ല\",\n    \"error.validation.max\": \"മൂല്യം വളരെ കൂടുതലാണ്.\",\n    \"error.validation.maxLength\": \"മൂല്യം വളരെ ദൈർഘ്യമേറിയതാണ്.\",\n    \"error.validation.min\": \"മൂല്യം വളരെ കുറവാണ്.\",\n    \"error.validation.minLength\": \"മൂല്യം വളരെ ചെറുതാണ്.\",\n    \"error.validation.minSupMax\": \"ഉന്നതമാകാൻ കഴിയില്ല\",\n    \"error.validation.regex\": \"മൂല്യം regex-മായി പൊരുത്തപ്പെടുന്നില്ല.\",\n    \"error.validation.required\": \"ഈ മൂല്യ ഇൻപുട്ട് ആവശ്യമാണ്.\",\n    \"form.Input.bulkActions\": \"ബൾക്ക് പ്രവർത്തനങ്ങൾ പ്രവർത്തനക്ഷമമാക്കുക\",\n    \"form.Input.defaultSort\": \"ഡിഫോൾട്ട് സോർട്ട് ആട്രിബ്യൂട്ട്\",\n    \"form.Input.description\": \"വിവരണം\",\n    \"form.Input.description.placeholder\": \"പ്രൊഫൈലിൽ പേര് പ്രദർശിപ്പിക്കുക\",\n    \"form.Input.editable\": \"എഡിറ്റബിൾ ഫീൽഡ്\",\n    \"form.Input.filters\": \"ഫിൽട്ടറുകൾ പ്രവർത്തനക്ഷമമാക്കുക\",\n    \"form.Input.label\": \"ലേബൽ\",\n    \"form.Input.label.inputDescription\": \"ഈ മൂല്യം പട്ടികയുടെ തലയിൽ പ്രദർശിപ്പിച്ചിരിക്കുന്ന ലേബലിനെ മറികടക്കുന്നു\",\n    \"form.Input.pageEntries\": \"ഓരോ പേജിനും എൻട്രികൾ\",\n    \"form.Input.pageEntries.inputDescription\": \"ശ്രദ്ധിക്കുക: ശേഖരണ തരം ക്രമീകരണ പേജിൽ നിങ്ങൾക്ക് ഈ മൂല്യം അസാധുവാക്കാനാകും.\",\n    \"form.Input.placeholder\": \"പ്ലെയ്‌സ്‌ഹോൾഡർ\",\n    \"form.Input.placeholder.placeholder\": \"എന്റെ ആകർഷണീയമായ മൂല്യം\",\n    \"form.Input.search\": \"തിരയൽ പ്രവർത്തനക്ഷമമാക്കുക\",\n    \"form.Input.search.field\": \"ഈ ഫീൽഡിൽ തിരയൽ പ്രവർത്തനക്ഷമമാക്കുക\",\n    \"form.Input.sort.field\": \"ഈ ഫീൽഡിൽ അടുക്കുന്നത് പ്രവർത്തനക്ഷമമാക്കുക\",\n    \"form.Input.sort.order\": \"ഡിഫോൾട്ട് സോർട്ട് ഓർഡർ\",\n    \"form.Input.wysiwyg\": \"WYSIWYG ആയി പ്രദർശിപ്പിക്കുക\",\n    \"global.displayedFields\": \"പ്രദർശിപ്പിച്ച ഫീൽഡുകൾ\",\n    groups: groups,\n    \"groups.numbered\": \"ഗ്രൂപ്പുകൾ ({നമ്പർ})\",\n    \"header.name\": \"ഉള്ളടക്കം\",\n    \"link-to-ctb\": \"മോഡൽ എഡിറ്റ് ചെയ്യുക\",\n    models: models,\n    \"models.numbered\": \"ശേഖരണ തരങ്ങൾ ({നമ്പർ})\",\n    \"notification.error.displayedFields\": \"നിങ്ങൾക്ക് കുറഞ്ഞത് ഒരു പ്രദർശിപ്പിച്ച ഫീൽഡ് ആവശ്യമാണ്\",\n    \"notification.error.relation.fetch\": \"ബന്ധം കണ്ടെത്തുന്നതിനിടയിൽ ഒരു പിശക് സംഭവിച്ചു.\",\n    \"notification.info.SettingPage.disableSort\": \"അനുവദനീയമായ സോർട്ടിംഗിനൊപ്പം നിങ്ങൾക്ക് ഒരു ആട്രിബ്യൂട്ട് ഉണ്ടായിരിക്കണം\",\n    \"notification.info.minimumFields\": \"നിങ്ങൾക്ക് കുറഞ്ഞത് ഒരു ഫീൽഡെങ്കിലും പ്രദർശിപ്പിക്കേണ്ടതുണ്ട്\",\n    \"notification.upload.error\": \"നിങ്ങളുടെ ഫയലുകൾ അപ്‌ലോഡ് ചെയ്യുമ്പോൾ ഒരു പിശക് സംഭവിച്ചു\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{സംഖ്യ, ബഹുവചനം, =0 {# എൻട്രികൾ} ഒന്ന് {# എൻട്രി} മറ്റ് {# എൻട്രികൾ}} കണ്ടെത്തി\",\n    \"pages.NoContentType.button\": \"നിങ്ങളുടെ ആദ്യ ഉള്ളടക്ക-തരം സൃഷ്ടിക്കുക\",\n    \"pages.NoContentType.text\": \"നിങ്ങൾക്ക് ഇതുവരെ ഒരു ഉള്ളടക്കവും ഇല്ല, നിങ്ങളുടെ ആദ്യ ഉള്ളടക്ക-തരം സൃഷ്ടിക്കാൻ ഞങ്ങൾ ശുപാർശ ചെയ്യുന്നു.\",\n    \"permissions.not-allowed.create\": \"ഒരു പ്രമാണം സൃഷ്ടിക്കാൻ നിങ്ങൾക്ക് അനുവാദമില്ല\",\n    \"permissions.not-allowed.update\": \"നിങ്ങൾക്ക് ഈ പ്രമാണം കാണാൻ അനുവാദമില്ല\",\n    \"plugin.description.long\": \"നിങ്ങളുടെ ഡാറ്റാബേസിലെ ഡാറ്റ കാണാനും എഡിറ്റ് ചെയ്യാനും ഇല്ലാതാക്കാനുമുള്ള ദ്രുത മാർഗം.\",\n    \"plugin.description.short\": \"നിങ്ങളുടെ ഡാറ്റാബേസിലെ ഡാറ്റ കാണാനും എഡിറ്റ് ചെയ്യാനും ഇല്ലാതാക്കാനുമുള്ള ദ്രുത മാർഗം.\",\n    \"popover.display-relations.label\": \"പ്രദർശന ബന്ധങ്ങൾ\",\n    \"success.record.delete\": \"ഇല്ലാതാക്കപ്പെട്ടു\",\n    \"success.record.publish\": \"പ്രസിദ്ധീകരിച്ചത്\",\n    \"success.record.save\": \"സംരക്ഷിച്ചു\",\n    \"success.record.unpublish\": \"പ്രസിദ്ധീകരിക്കാത്തത്\",\n    \"utils.data-loaded\": \"{സംഖ്യ, ബഹുവചനം, =1 {എൻട്രി ഉണ്ട്} മറ്റ് {എൻട്രികൾ ഉണ്ട്}} വിജയകരമായി ലോഡ് ചെയ്തു\",\n    \"apiError. ഈ ആട്രിബ്യൂട്ട് അദ്വിതീയമായിരിക്കണം\": \"{ഫീൽഡ്} അദ്വിതീയമായിരിക്കണം\",\n    \"popUpWarning.warning.publish-question\": \"നിങ്ങൾക്കിത് ഇപ്പോഴും പ്രസിദ്ധീകരിക്കാൻ താൽപ്പര്യമുണ്ടോ?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"അതെ, പ്രസിദ്ധീകരിക്കുക\",\n    \"popUpwarning.warning.has-draft-relations.message\": \"<b>{count, plural, =0 {നിങ്ങളുടെ ഉള്ളടക്ക ബന്ധങ്ങളിൽ} ഒന്ന് {നിങ്ങളുടെ ഉള്ളടക്ക ബന്ധങ്ങളിൽ} മറ്റൊന്നാണ് {നിങ്ങളുടെ ഉള്ളടക്ക ബന്ധങ്ങളിൽ} are}}</b> ഇതുവരെ പ്രസിദ്ധീകരിച്ചിട്ടില്ല.<br></br>ഇത് നിങ്ങളുടെ പ്രോജക്റ്റിൽ തകർന്ന ലിങ്കുകളും പിശകുകളും സൃഷ്ടിച്ചേക്കാം.\"\n};\n\nexport { ml as default, groups, models, pageNotFound };\n//# sourceMappingURL=ml.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,2DAA2D;AAAA,EAC3D,oDAAoD;AACxD;", "names": []}