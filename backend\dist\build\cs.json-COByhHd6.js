var e={"BoundRoute.title":"Spojit adresu s","EditForm.inputSelect.description.role":"Připojí nově autentifikovaného uživatele ke svolené roli.","EditForm.inputSelect.label.role":"Vý<PERSON>zí role pro autentifikovaného uživatele","EditForm.inputToggle.description.email":"Zabránit uživateli vytvářet různé ú<PERSON>ty se stejným e-mailem a jinými poskytovateli autentifikace.","EditForm.inputToggle.description.email-confirmation":"Pokud je tato funkce povolena (ON), nově registrovaní uživatelé dostanou potvrzující e-mail.","EditForm.inputToggle.description.email-confirmation-redirection":"Po potvrzení e-mailu, zvolte kam budete přesměrováni.","EditForm.inputToggle.description.email-reset-password":"<PERSON><PERSON><PERSON> strán<PERSON> obnoven<PERSON> hesla vaš<PERSON> aplik<PERSON>","EditForm.inputToggle.description.sign-up":"Pokud je tato mož<PERSON> (OFF), nen<PERSON> možno projít registrací. Nikdo se již nemůže připojit, bez ohledu jakého použije poskytovatele.","EditForm.inputToggle.label.email":"Jeden účet na e-mail","EditForm.inputToggle.label.email-confirmation":"Povolit potvrzení z e-mailu","EditForm.inputToggle.label.email-confirmation-redirection":"Adresa pro přesměrování","EditForm.inputToggle.label.email-reset-password":"Stránka pro obnovení hesla","EditForm.inputToggle.label.sign-up":"Povolit registrace","HeaderNav.link.advancedSettings":"Pokročilá nastavení","HeaderNav.link.emailTemplates":"E-mailové šablony","HeaderNav.link.providers":"Poskytovatelé","Plugin.permissions.plugins.description":"Nastavit všechny akce pro zásuvný modul {name}.","Plugins.header.description":"Pouze akce spojené s adresou jsou vypsány níže.","Plugins.header.title":"Povolení","Policies.header.hint":"Vyberte akce aplikace, nebo akce zásuvného modulu a klikněte na ikonku ozubeného kolečka pro zobrazení adresy s nimi spojenou.","Policies.header.title":"Pokročilá nastavení","PopUpForm.Email.email_templates.inputDescription":"Pokud si nejste jisti jak používat proměnné, {link}","PopUpForm.Email.options.from.email.label":"Odesilatelův e-mail","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Jméno odesilatele","PopUpForm.Email.options.from.name.placeholder":"Jan Novák","PopUpForm.Email.options.message.label":"Zpráva","PopUpForm.Email.options.object.label":"Předmět","PopUpForm.Email.options.response_email.label":"Response e-mail","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"If disabled, users won't be able to use this provider.","PopUpForm.Providers.enabled.label":"Povolit","PopUpForm.Providers.key.label":"Client ID","PopUpForm.Providers.key.placeholder":"TEXT","PopUpForm.Providers.redirectURL.front-end.label":"Adresa pro přesměrování na vaši front-end aplikaci","PopUpForm.Providers.secret.label":"Client Secret","PopUpForm.Providers.secret.placeholder":"TEXT","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"Upravit e-mailové šablony","notification.success.submit":"Nastavení bylo aktualizování","plugin.description.long":"Chraňte své API pomocí kompletního autentifikačního procesu, založeného na JWT. Tento zásuvný modul obsahuje ACL strategii, která vám umožní spravovat oprávnění mezi skupinami uživatelů.","plugin.description.short":"Chraňte své API pomocí kompletního autentifikačního procesu, založeného na JWT","plugin.name":"Role a oprávnění"};export{e as default};
