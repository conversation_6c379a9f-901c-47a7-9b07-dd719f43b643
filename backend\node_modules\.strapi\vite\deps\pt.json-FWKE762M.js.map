{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/pt.json.mjs"], "sourcesContent": ["var Analytics = \"Estatísticas\";\nvar Email = \"Email\";\nvar Password = \"Palavra-passe\";\nvar Provider = \"Provedor\";\nvar ResetPasswordToken = \"Código de redefinição da palavra-passe\";\nvar Role = \"Função\";\nvar Username = \"Nome de utilizador\";\nvar Users = \"Utilizadores\";\nvar pt = {\n    Analytics: Analytics,\n    \"Auth.form.button.forgot-password\": \"Enviar email\",\n    \"Auth.form.button.login\": \"Entrar\",\n    \"Auth.form.button.register\": \"Pronto para começar\",\n    \"Auth.form.error.blocked\": \"A tua conta foi bloqueada por um administrador.\",\n    \"Auth.form.error.code.provide\": \"O código fornecido está incorreto.\",\n    \"Auth.form.error.confirmed\": \"O email da tua conta não foi confirmado.\",\n    \"Auth.form.error.email.invalid\": \"Este email é inválido.\",\n    \"Auth.form.error.email.provide\": \"Por favor, preenche com o nome de utilizador ou email.\",\n    \"Auth.form.error.email.taken\": \"Este email já está a ser utilizado.\",\n    \"Auth.form.error.invalid\": \"Utilizador ou palavra-passe inválidos.\",\n    \"Auth.form.error.params.provide\": \"Os parâmetros submetidos estão errados.\",\n    \"Auth.form.error.password.format\": \"A sua palavra-passe não pode conter o símbolo `$` mais do que 3 vezes.\",\n    \"Auth.form.error.password.local\": \"Este utilizador nunca definiu uma palavra-passe local, por favor, faz login pelo serviço utilizado durante a criação da conta.\",\n    \"Auth.form.error.password.matching\": \"As palavra-passes não coincidem.\",\n    \"Auth.form.error.password.provide\": \"Por favor, digita a tua palavra-passe.\",\n    \"Auth.form.error.ratelimit\": \"Demasiadas tentativas, por favor, tenta novamente daqui a um minuto.\",\n    \"Auth.form.error.user.not-exist\": \"Este email não existe.\",\n    \"Auth.form.error.username.taken\": \"Este nome de utilizador já está a ser utilizado.\",\n    \"Auth.form.forgot-password.email.label\": \"Introduz o teu email\",\n    \"Auth.form.forgot-password.email.label.success\": \"Email enviado com sucesso\",\n    \"Auth.form.register.news.label\": \"Manter-me atualizado sobre novas funcionalidades e futuras melhorias (ao fazê-lo, estás a aceitar os {terms} e a {policy}).\",\n    \"Auth.form.rememberMe.label\": \"Lembrar-me\",\n    \"Auth.form.username.label\": \"Nome de utilizador\",\n    \"Auth.form.username.placeholder\": \"Kai Doe\",\n    \"Auth.link.forgot-password\": \"Esqueceu-se da palavra-passe?\",\n    \"Auth.link.ready\": \"Pronto para entrar?\",\n    \"Auth.privacy-policy-agreement.policy\": \"política de privacidade\",\n    \"Auth.privacy-policy-agreement.terms\": \"termos de serviço\",\n    \"Content Manager\": \"Gestor de Conteúdo\",\n    \"Content Type Builder\": \"Construtor de Tipos de Conteúdo\",\n    Email: Email,\n    \"Files Upload\": \"Carregamento de Ficheiros\",\n    \"HomePage.head.title\": \"Página principal\",\n    \"HomePage.roadmap\": \"Vê o nosso roadmap\",\n    \"HomePage.welcome.congrats\": \"Parabéns!\",\n    \"HomePage.welcome.congrats.content\": \"Iniciaste sessão como o primeiro administrador. Para descobrires as poderosas funcionalidades do Strapi,\",\n    \"HomePage.welcome.congrats.content.bold\": \"recomendamos que cries o teu primeiro modelo.\",\n    \"New entry\": \"Novo item\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Settings.error\": \"Erro\",\n    \"Settings.global\": \"Definições Globais\",\n    \"Settings.webhooks.create\": \"Criar um webhook\",\n    \"Settings.webhooks.create.header\": \"Criar um novo header\",\n    \"Settings.webhooks.created\": \"Webhook criado\",\n    \"Settings.webhooks.events.create\": \"Ao criar\",\n    \"Settings.webhooks.form.events\": \"Eventos\",\n    \"Settings.webhooks.form.headers\": \"Headers\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.key\": \"Key\",\n    \"Settings.webhooks.list.button.add\": \"Adicionar novo webhook\",\n    \"Settings.webhooks.list.description\": \"Obtém notificações POST de alterações.\",\n    \"Settings.webhooks.list.empty.description\": \"Adiciona o teu primeiro webhook a esta lista.\",\n    \"Settings.webhooks.list.empty.link\": \"Vê a nossa documentação\",\n    \"Settings.webhooks.list.empty.title\": \"Ainda não há nenhum webhook\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhooks\",\n    \"Settings.webhooks.trigger\": \"Executar\",\n    \"Settings.webhooks.trigger.cancel\": \"Cancelar execução\",\n    \"Settings.webhooks.trigger.pending\": \"Pendente…\",\n    \"Settings.webhooks.trigger.save\": \"Por favor, grava antes de executar\",\n    \"Settings.webhooks.trigger.success\": \"Sucesso!\",\n    \"Settings.webhooks.trigger.success.label\": \"Execução com sucesso\",\n    \"Settings.webhooks.trigger.test\": \"Execução de teste\",\n    \"Settings.webhooks.trigger.title\": \"Gravar antes de executar\",\n    \"Settings.webhooks.value\": \"Valor\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Utilizadores & Permissões\",\n    \"app.components.BlockLink.code\": \"Exemplos de código\",\n    \"app.components.Button.cancel\": \"Cancelar\",\n    \"app.components.Button.reset\": \"Restaurar\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Em breve\",\n    \"app.components.DownloadInfo.download\": \"Transferência em progresso...\",\n    \"app.components.DownloadInfo.text\": \"Isto poderá levar alguns minutos. Obrigado pela sua paciência\",\n    \"app.components.EmptyAttributes.title\": \"Ainda não há atributos\",\n    \"app.components.HomePage.button.blog\": \"VÊ MAIS NO BLOG\",\n    \"app.components.HomePage.community\": \"Encontre a comunidade na web\",\n    \"app.components.HomePage.community.content\": \"Conversa com membros da equipa, contribuidores e desenvolvedores através de diferentes canais.\",\n    \"app.components.HomePage.create\": \"Cria o teu primeiro Tipo de Conteúdo\",\n    \"app.components.HomePage.welcome\": \"Bem-vindo(a) a bordo\",\n    \"app.components.HomePage.welcome.again\": \"Bem-vindo(a) \",\n    \"app.components.HomePage.welcomeBlock.content\": \"Estamos felizes em ter-te como um dos membros da comunidade. Estamos constantemente à procura de feedback, por isso sente-te à vontade para nos enviares uma mensagem em privado no \",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Esperamos que estejas a progredir no teu projeto... Sente-te à vontade em ler as nossas últimas publicações sobre o Strapi. Estamos a dar o nosso melhor para melhorar o produto, baseando-nos no teu feedback.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"problemas.\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" ou levante \",\n    \"app.components.ImgPreview.hint\": \"Arraste & solte o seu ficheiro nesta área ou {browse} um ficheiro para o carregar\",\n    \"app.components.ImgPreview.hint.browse\": \"escolha\",\n    \"app.components.InputFile.newFile\": \"Adicionar um novo ficheiro\",\n    \"app.components.InputFileDetails.open\": \"Abrir num novo separador\",\n    \"app.components.InputFileDetails.originalName\": \"Nome original:\",\n    \"app.components.InputFileDetails.remove\": \"Remover este ficheiro\",\n    \"app.components.InputFileDetails.size\": \"Tamanho:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Pode demorar alguns segundos a descarregar e instalar o plugin.\",\n    \"app.components.InstallPluginPage.Download.title\": \"A descarregar...\",\n    \"app.components.InstallPluginPage.description\": \"Estende a tua aplicação sem problemas.\",\n    \"app.components.LeftMenuFooter.help\": \"Ajuda\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Feito com \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Modelos\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Configurações\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Geral\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Nenhuma extensão instalada\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Extensões\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Modelos Únicos\",\n    \"app.components.ListPluginsPage.description\": \"Lista de extensões instaladas no projeto.\",\n    \"app.components.ListPluginsPage.head.title\": \"Lista de extensões\",\n    \"app.components.Logout.logout\": \"Sair\",\n    \"app.components.Logout.profile\": \"Perfil\",\n    \"app.components.NotFoundPage.back\": \"Voltar à página inicial\",\n    \"app.components.NotFoundPage.description\": \"Não encontrado\",\n    \"app.components.Official\": \"Oficial\",\n    \"app.components.Onboarding.label.completed\": \"% completo\",\n    \"app.components.Onboarding.title\": \"Como Começar - Vídeos\",\n    \"app.components.PluginCard.Button.label.download\": \"Transferir\",\n    \"app.components.PluginCard.Button.label.install\": \"Já instalado\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"A funcionalidade autoReload precisa de estar ligada. Por favor, inicia a tua aplicação com `yarn develop`.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Compreendo!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Por questões de segurança, um plugin só pode ser descarregado num ambiente de desenvolvimento.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Impossível descarregar\",\n    \"app.components.PluginCard.compatible\": \"Compatível com a tua aplicação\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Compatível com a comunidade\",\n    \"app.components.PluginCard.more-details\": \"Mais detalhes\",\n    \"app.components.listPlugins.button\": \"Adicionar Nova Extensão\",\n    \"app.components.listPlugins.title.none\": \"Nenhuma extensão instalada\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Ocorreu um erro ao desinstalar a extensão\",\n    \"app.containers.App.notification.error.init\": \"Ocorreu um erro ao efetuar um pedido para a API\",\n    \"app.links.configure-view\": \"Configurar o editor\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.filters\": \"Filtros\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"component.Input.error.validation.integer\": \"Este valor precisa de ser um número inteiro\",\n    \"components.AutoReloadBlocker.description\": \"Inicia o Strapi com um dos seguintes comandos:\",\n    \"components.AutoReloadBlocker.header\": \"A funcionalidade autoReload é necessária para esta extensão.\",\n    \"components.ErrorBoundary.title\": \"Algo correu mal...\",\n    \"components.Input.error.attribute.key.taken\": \"Este valor já existe\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Não pode ser igual\",\n    \"components.Input.error.attribute.taken\": \"Já existe um atributo com este nome\",\n    \"components.Input.error.contentTypeName.taken\": \"Já existe um tipo de conteúdo com este nome\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"As palavra-passes não coincidem\",\n    \"components.Input.error.validation.email\": \"Isto não é um email\",\n    \"components.Input.error.validation.json\": \"Não está em formato JSON\",\n    \"components.Input.error.validation.max\": \"Valor demasiado elevado {max}.\",\n    \"components.Input.error.validation.maxLength\": \"Valor demasiado longo {max}.\",\n    \"components.Input.error.validation.min\": \"Valor demasiado baixo {min}.\",\n    \"components.Input.error.validation.minLength\": \"Valor demasiado curto {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Não pode ser superior\",\n    \"components.Input.error.validation.regex\": \"O valor não corresponde com a expressão regex.\",\n    \"components.Input.error.validation.required\": \"Este valor é obrigatório.\",\n    \"components.Input.error.validation.unique\": \"Este valor tem de ser único, mas já está a ser utilizado.\",\n    \"components.InputSelect.option.placeholder\": \"Escolhe aqui\",\n    \"components.ListRow.empty\": \"Não existem dados para mostrar.\",\n    \"components.OverlayBlocker.description\": \"Estás a usar uma funcionalidade que precisa que o servidor seja reiniciado. Por favor, aguarda até que o servidor esteja totalmente reiniciado.\",\n    \"components.OverlayBlocker.description.serverError\": \"O servidor já deveria ter reiniciado. Por favor verifica os logs no terminal.\",\n    \"components.OverlayBlocker.title\": \"A aguardar pela reinicialização...\",\n    \"components.OverlayBlocker.title.serverError\": \"A reinicialização está a demorar mais do que o esperado\",\n    \"components.PageFooter.select\": \"itens por página\",\n    \"components.ProductionBlocker.description\": \"Por motivos de segurança, temos que desativar esta extensão noutros ambientes.\",\n    \"components.ProductionBlocker.header\": \"Esta extensão está disponível apenas em ambiente de desenvolvimento.\",\n    \"components.Search.placeholder\": \"Procurar...\",\n    \"components.Wysiwyg.collapse\": \"Colapsar\",\n    \"components.Wysiwyg.selectOptions.H1\": \"Título H1\",\n    \"components.Wysiwyg.selectOptions.H2\": \"Título H2\",\n    \"components.Wysiwyg.selectOptions.H3\": \"Título H3\",\n    \"components.Wysiwyg.selectOptions.H4\": \"Título H4\",\n    \"components.Wysiwyg.selectOptions.H5\": \"Título H5\",\n    \"components.Wysiwyg.selectOptions.H6\": \"Título H6\",\n    \"components.Wysiwyg.selectOptions.title\": \"Adicionar título\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"caracteres\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Expandir\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Arrasta e solta ficheiros, cola da área de transferência ou {browse}.\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"selecione-os\",\n    \"components.popUpWarning.message\": \"Tens a certeza que pretendes apagar isto?\",\n    \"components.popUpWarning.title\": \"Por favor confirma\",\n    \"form.button.done\": \"Concluir\",\n    \"global.prompt.unsaved\": \"Tens a certeza que queres sair desta página? Todas as tuas modificações serão perdidas\",\n    \"notification.contentType.relations.conflict\": \"O tipo de conteúdo tem relações que entram em conflito\",\n    \"notification.error\": \"Ocorreu um erro\",\n    \"notification.error.layout\": \"Não foi possível encontrar o layout\",\n    \"notification.form.error.fields\": \"O formulário contém erros\",\n    \"notification.form.success.fields\": \"Alterações gravadas\",\n    \"notification.success.delete\": \"O item foi eliminado\",\n    \"request.error.model.unknown\": \"Este modelo não existe\"\n};\n\nexport { Analytics, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, pt as default };\n//# sourceMappingURL=pt.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,KAAK;AAAA,EACL;AAAA,EACA,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,wCAAwC;AAAA,EACxC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,+CAA+C;AAAA,EAC/C,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,+BAA+B;AAAA,EAC/B,+BAA+B;AACnC;", "names": []}