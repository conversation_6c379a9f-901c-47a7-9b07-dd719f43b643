{"kind": "collectionType", "collectionName": "produit_exportes", "info": {"singularName": "produit-exporte", "pluralName": "produit-exportes", "displayName": "ProduitExporte"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"ngp": {"type": "string", "required": true}, "designation": {"type": "string", "required": true}, "exportateur": {"type": "relation", "relation": "oneToOne", "target": "api::exportateur.exportateur"}}}