import React, { useState, useEffect } from "react";

function DebugTest() {
  const [apiData, setApiData] = useState({
    importateurs: null,
    exportateurs: null,
    opportunites: null,
    loading: true,
    errors: {}
  });

  useEffect(() => {
    const testAPIs = async () => {
      const apis = [
        { name: 'importateurs', url: 'http://localhost:1337/api/importateurs?populate=*' },
        { name: 'exportateurs', url: 'http://localhost:1337/api/exportateurs?populate=*' },
        { name: 'opportunites', url: 'http://localhost:1337/api/opportunites?populate=*' }
      ];

      const results = {};
      const errors = {};

      for (const api of apis) {
        try {
          console.log(`Testing ${api.name} API...`);
          const response = await fetch(api.url);
          const data = await response.json();
          console.log(`${api.name} response:`, data);
          results[api.name] = data;
        } catch (error) {
          console.error(`Error fetching ${api.name}:`, error);
          errors[api.name] = error.message;
        }
      }

      setApiData({
        ...results,
        loading: false,
        errors
      });
    };

    testAPIs();
  }, []);

  if (apiData.loading) {
    return <div>Testing APIs...</div>;
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>API Debug Test</h2>
      
      {Object.keys(apiData.errors).length > 0 && (
        <div style={{ color: 'red', marginBottom: '20px' }}>
          <h3>Errors:</h3>
          {Object.entries(apiData.errors).map(([api, error]) => (
            <div key={api}>{api}: {error}</div>
          ))}
        </div>
      )}

      <div style={{ marginBottom: '20px' }}>
        <h3>Importateurs API Response:</h3>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(apiData.importateurs, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Exportateurs API Response:</h3>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(apiData.exportateurs, null, 2)}
        </pre>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Opportunites API Response:</h3>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(apiData.opportunites, null, 2)}
        </pre>
      </div>
    </div>
  );
}

export default DebugTest;
