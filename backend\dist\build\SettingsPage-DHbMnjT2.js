import{a as x,r as b,j as e,cc as g,D as P,dn as r,av as ae,dp as H,w as M,v as S,dq as se,a4 as O,C as _,aP as te,cP as h,e as T,T as p,b7 as N,$,b0 as ne,aQ as D,dr as z,H as I,ce as w,E as re,aU as ie,K as le,M as v,aH as oe,aI as de,ds as ce,bt as B,I as k,bm as ue,bu as ge,g as me,dt as he,W as fe,bn as xe,Y as R,bo as E,bq as je,X as pe,Z as A,P as L,du as Q,dv as be,z as ye,B as V,as as Me,s as Se}from"./strapi-z7ApxZZq.js";const U=s=>s.name!==void 0,q=({disabled:s,variant:n="default"})=>{const{formatMessage:t}=x(),[a,d]=b.useState(!1);return e.jsxs(g.<PERSON>,{open:a,onOpenChange:d,children:[e.jsx(g.<PERSON><PERSON>,{children:e.jsx(P,{variant:n,disabled:s,startIcon:e.jsx(ae,{}),onClick:()=>d(!0),size:"S",children:t({id:r("Settings.list.actions.add"),defaultMessage:"Add new locale"})})}),e.jsx(ve,{onClose:()=>d(!1)})]})},G=te().shape({code:D().nullable().required({id:"Settings.locales.modal.create.code.error",defaultMessage:"Please select a locale"}),name:D().nullable().max(50,{id:"Settings.locales.modal.create.name.error.min",defaultMessage:"The locale display name can only be less than 50 characters."}).required({id:"Settings.locales.modal.create.name.error.required",defaultMessage:"Please give the locale a display name"}),isDefault:ne()}),Ce={code:"",name:"",isDefault:!1},ve=({onClose:s})=>{const n=H(),{toggleNotification:t}=M(),{_unstableFormatAPIError:a,_unstableFormatValidationErrors:d}=S(),[c]=se(),{formatMessage:o}=x(),i=O("CreateModal",u=>u.refetchPermissions),l=async(u,m)=>{try{const f=await c(u);if("error"in f){U(f.error)&&f.error.name==="ValidationError"?m.setErrors(d(f.error)):t({type:"danger",message:a(f.error)});return}t({type:"success",message:o({id:r("Settings.locales.modal.create.success"),defaultMessage:"Created locale"})}),i(),s()}catch{t({type:"danger",message:o({id:"notification.error",defaultMessage:"An error occurred, please try again"})})}};return e.jsx(g.Content,{children:e.jsxs(_,{method:"POST",initialValues:Ce,validationSchema:G,onSubmit:l,children:[e.jsx(g.Header,{children:e.jsx(g.Title,{children:o({id:r("Settings.list.actions.add"),defaultMessage:"Add new locale"})})}),e.jsx(g.Body,{children:e.jsxs(h.Root,{variant:"simple",defaultValue:"basic",children:[e.jsxs(T,{justifyContent:"space-between",children:[e.jsx(p,{tag:"h2",variant:"beta",id:n,children:o({id:r("Settings.locales.modal.title"),defaultMessage:"Configuration"})}),e.jsxs(h.List,{"aria-labelledby":n,children:[e.jsx(h.Trigger,{value:"basic",children:o({id:r("Settings.locales.modal.base"),defaultMessage:"Basic settings"})}),e.jsx(h.Trigger,{value:"advanced",children:o({id:r("Settings.locales.modal.advanced"),defaultMessage:"Advanced settings"})})]})]}),e.jsx(N,{}),e.jsxs($,{paddingTop:7,paddingBottom:7,children:[e.jsx(h.Content,{value:"basic",children:e.jsx(K,{})}),e.jsx(h.Content,{value:"advanced",children:e.jsx(W,{})})]})]})}),e.jsxs(g.Footer,{children:[e.jsx(g.Close,{children:e.jsx(P,{variant:"tertiary",children:o({id:"app.components.Button.cancel",defaultMessage:"Cancel"})})}),e.jsx(J,{})]})]})})},J=()=>{const{formatMessage:s}=x(),n=w("SubmitButton",a=>a.isSubmitting),t=w("SubmitButton",a=>a.modified);return e.jsx(P,{type:"submit",startIcon:e.jsx(re,{}),disabled:n||!t,children:s({id:"global.save",defaultMessage:"Save"})})},K=({mode:s="create"})=>{const{formatMessage:n}=x(),{toggleNotification:t}=M(),{_unstableFormatAPIError:a}=S(),{data:d,error:c}=z();if(b.useEffect(()=>{c&&t({type:"danger",message:a(c)})},[c,a,t]),!Array.isArray(d))return null;const o=d.map(l=>({label:l.name,value:l.code})),i=[{disabled:s!=="create",label:{id:r("Settings.locales.modal.create.code.label"),defaultMessage:"Locales"},name:"code",options:o,placeholder:{id:"components.placeholder.select",defaultMessage:"Select"},required:!0,size:6,type:"enumeration"},{hint:{id:r("Settings.locales.modal.create.name.label.description"),defaultMessage:"Locale will be displayed under that name in the administration panel"},label:{id:r("Settings.locales.modal.create.name.label"),defaultMessage:"Locale display name"},name:"name",required:!0,size:6,type:"string"}].map(l=>({...l,hint:l.hint?n(l.hint):void 0,label:n(l.label),placeholder:l.placeholder?n(l.placeholder):void 0}));return e.jsx(I.Root,{gap:4,children:i.map(({size:l,...u})=>e.jsx(I.Item,{col:l,direction:"column",alignItems:"stretch",children:e.jsx(X,{...u})},u.name))})},W=({isDefaultLocale:s})=>{const{formatMessage:n}=x(),t=[{disabled:s,hint:{id:r("Settings.locales.modal.advanced.setAsDefault.hint"),defaultMessage:"One default locale is required, change it by selecting another one"},label:{id:r("Settings.locales.modal.advanced.setAsDefault"),defaultMessage:"Set as default locale"},name:"isDefault",size:6,type:"boolean"}].map(a=>({...a,hint:a.hint?n(a.hint):void 0,label:n(a.label)}));return e.jsx(I.Root,{gap:4,children:t.map(({size:a,...d})=>e.jsx(I.Item,{col:a,direction:"column",alignItems:"stretch",children:e.jsx(X,{...d})},d.name))})},X=s=>{switch(s.type){case"enumeration":return e.jsx(Ee,{...s});default:return e.jsx(ie,{...s})}},Ee=({disabled:s,hint:n,label:t,name:a,options:d,placeholder:c,required:o})=>{const{value:i,error:l,onChange:u}=le(a),{data:m=[]}=z(),f=j=>{if(Array.isArray(m)){const F=m.find(C=>C.code===j);u(a,j),u("name",F.name)}else u(a,j)};return e.jsxs(v.Root,{error:l,hint:n,name:a,required:o,children:[e.jsx(v.Label,{children:t}),e.jsx(oe,{disabled:s,onChange:f,placeholder:c,value:i,children:d.map(j=>e.jsx(de,{value:j.value,children:j.label},j.value))}),e.jsx(v.Error,{}),e.jsx(v.Hint,{})]})},Ae=({id:s,name:n})=>{const{formatMessage:t}=x(),{toggleNotification:a}=M(),{_unstableFormatAPIError:d}=S(),[c,o]=b.useState(!1),[i]=ce(),l=async()=>{try{const u=await i(s);if("error"in u){a({type:"danger",message:d(u.error)});return}a({type:"success",message:t({id:r("Settings.locales.modal.delete.success"),defaultMessage:"Deleted locale"})}),o(!1)}catch{a({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred, please try again"})})}};return e.jsxs(B.Root,{open:c,onOpenChange:o,children:[e.jsx(B.Trigger,{children:e.jsx(k,{onClick:()=>o(!0),label:t({id:r("Settings.list.actions.delete"),defaultMessage:"Delete {name} locale"},{name:n}),variant:"ghost",children:e.jsx(ue,{})})}),e.jsx(ge,{onConfirm:l})]})},Le=s=>{const{formatMessage:n}=x(),[t,a]=b.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(k,{onClick:()=>a(!0),label:n({id:r("Settings.list.actions.edit"),defaultMessage:"Edit {name} locale"},{name:s.name}),variant:"ghost",children:e.jsx(me,{})}),e.jsx(Y,{...s,open:t,onOpenChange:a})]})},Y=({id:s,code:n,isDefault:t,name:a,open:d,onOpenChange:c})=>{const{toggleNotification:o}=M(),{_unstableFormatAPIError:i,_unstableFormatValidationErrors:l}=S(),u=O("EditModal",C=>C.refetchPermissions),{formatMessage:m}=x(),f=H(),[j]=he(),F=async({code:C,...Z},ee)=>{try{const y=await j({id:s,...Z});if("error"in y){U(y.error)&&y.error.name==="ValidationError"?ee.setErrors(l(y.error)):o({type:"danger",message:i(y.error)});return}o({type:"success",message:m({id:r("Settings.locales.modal.edit.success"),defaultMessage:"Updated locale"})}),u(),c(!1)}catch{o({type:"danger",message:m({id:"notification.error",defaultMessage:"An error occurred, please try again"})})}};return e.jsx(g.Root,{open:d,onOpenChange:c,children:e.jsx(g.Content,{children:e.jsxs(_,{method:"PUT",onSubmit:F,initialValues:{code:n,name:a,isDefault:t},validationSchema:G,children:[e.jsx(g.Header,{children:e.jsx(g.Title,{children:m({id:r("Settings.list.actions.edit"),defaultMessage:"Edit a locale"},{name:a})})}),e.jsx(g.Body,{children:e.jsxs(h.Root,{variant:"simple",defaultValue:"basic",children:[e.jsxs(T,{justifyContent:"space-between",children:[e.jsx(p,{tag:"h2",variant:"beta",id:f,children:m({id:r("Settings.locales.modal.title"),defaultMessage:"Configuration"})}),e.jsxs(h.List,{"aria-labelledby":f,children:[e.jsx(h.Trigger,{value:"basic",children:m({id:r("Settings.locales.modal.base"),defaultMessage:"Basic settings"})}),e.jsx(h.Trigger,{value:"advanced",children:m({id:r("Settings.locales.modal.advanced"),defaultMessage:"Advanced settings"})})]})]}),e.jsx(N,{}),e.jsxs($,{paddingTop:7,paddingBottom:7,children:[e.jsx(h.Content,{value:"basic",children:e.jsx(K,{mode:"edit"})}),e.jsx(h.Content,{value:"advanced",children:e.jsx(W,{isDefaultLocale:t})})]})]})}),e.jsxs(g.Footer,{children:[e.jsx(g.Close,{children:e.jsx(P,{variant:"tertiary",children:m({id:"app.components.Button.cancel",defaultMessage:"Cancel"})})}),e.jsx(J,{})]})]})})})},Ie=({locales:s=[],canDelete:n,canUpdate:t})=>{const[a,d]=b.useState(),{formatMessage:c}=x(),o=i=>()=>{t&&d(i)};return e.jsxs(fe,{colCount:4,rowCount:s.length+1,children:[e.jsx(xe,{children:e.jsxs(R,{children:[e.jsx(E,{children:e.jsx(p,{variant:"sigma",textColor:"neutral600",children:c({id:r("Settings.locales.row.id"),defaultMessage:"ID"})})}),e.jsx(E,{children:e.jsx(p,{variant:"sigma",textColor:"neutral600",children:c({id:r("Settings.locales.row.displayName"),defaultMessage:"Display name"})})}),e.jsx(E,{children:e.jsx(p,{variant:"sigma",textColor:"neutral600",children:c({id:r("Settings.locales.row.default-locale"),defaultMessage:"Default locale"})})}),e.jsx(E,{children:e.jsx(je,{children:"Actions"})})]})}),e.jsx(pe,{children:s.map(i=>e.jsxs(b.Fragment,{children:[e.jsxs(R,{onClick:o(i.id),style:{cursor:t?"pointer":"default"},children:[e.jsx(A,{children:e.jsx(p,{textColor:"neutral800",children:i.id})}),e.jsx(A,{children:e.jsx(p,{textColor:"neutral800",children:i.name})}),e.jsx(A,{children:e.jsx(p,{textColor:"neutral800",children:i.isDefault?c({id:r("Settings.locales.default"),defaultMessage:"Default"}):null})}),e.jsx(A,{children:e.jsxs(T,{gap:1,justifyContent:"flex-end",onClick:l=>l.stopPropagation(),children:[t&&e.jsx(Le,{...i}),n&&!i.isDefault&&e.jsx(Ae,{...i})]})})]}),e.jsx(Y,{...i,onOpenChange:()=>d(void 0),open:a===i.id})]},i.id))})]})},Pe=()=>{const{formatMessage:s}=x(),{toggleNotification:n}=M(),{_unstableFormatAPIError:t}=S(),{data:a,isLoading:d,error:c}=be(),{isLoading:o,allowedActions:{canUpdate:i,canCreate:l,canDelete:u}}=ye(Q);return b.useEffect(()=>{c&&n({type:"danger",message:t(c)})},[c,t,n]),d||o?e.jsx(L.Loading,{}):c||!Array.isArray(a)?e.jsx(L.Error,{}):e.jsxs(L.Main,{tabIndex:-1,children:[e.jsx(V.Header,{primaryAction:e.jsx(q,{disabled:!l}),title:s({id:r("plugin.name"),defaultMessage:"Internationalization"}),subtitle:s({id:r("Settings.list.description"),defaultMessage:"Configure the settings"})}),e.jsx(V.Content,{children:a.length>0?e.jsx(Ie,{locales:a,canDelete:u,canUpdate:i}):e.jsx(Me,{icon:e.jsx(Se,{width:void 0,height:void 0}),content:s({id:r("Settings.list.empty.title"),defaultMessage:"There are no locales"}),action:e.jsx(q,{disabled:!l,variant:"secondary"})})})]})},Te=()=>e.jsx(L.Protect,{permissions:Q.read,children:e.jsx(Pe,{})});export{Te as ProtectedSettingsPage,Pe as SettingsPage};
