import{bQ as f,a as c,j as e,b as T,V as k,W as b,X as w,Y as E,Z as l,$ as p,a0 as v,T as d,a1 as C,I as D,a3 as L,g as R,k as M}from"./strapi-z7ApxZZq.js";import{W as o}from"./WidgetHelpers-CmZQzLJr.js";const $=f.enhanceEndpoints({addTagTypes:["RecentDocumentList"]}).injectEndpoints({overrideExisting:!0,endpoints:s=>({getRecentDocuments:s.query({query:n=>`/content-manager/homepage/recent-documents?action=${n.action}`,transformResponse:n=>n.data,providesTags:(n,a,{action:i})=>[{type:"RecentDocumentList",id:i}]})})}),{useGetRecentDocumentsQuery:x}=$,u=M(d).attrs({maxWidth:"14.4rem",display:"block"})`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`,h=({documents:s})=>{const{formatMessage:n}=c(),{trackUsage:a}=T(),i=k(),g=t=>{const r=t.kind==="singleType",m=r?"single-types":"collection-types",y=t.locale?`?plugins[i18n][locale]=${t.locale}`:"";return`/content-manager/${m}/${t.contentTypeUid}${r?"":"/"+t.documentId}${y}`},j=t=>()=>{a("willEditEntryFromHome");const r=g(t);i(r)};return e.jsx(b,{colCount:5,rowCount:s?.length??0,children:e.jsx(w,{children:s?.map(t=>e.jsxs(E,{onClick:j(t),cursor:"pointer",children:[e.jsx(l,{children:e.jsx(u,{title:t.title,variant:"omega",textColor:"neutral800",children:t.title})}),e.jsx(l,{children:e.jsx(u,{variant:"omega",textColor:"neutral600",children:t.kind==="singleType"?n({id:"content-manager.widget.last-edited.single-type",defaultMessage:"Single-Type"}):n({id:t.contentTypeDisplayName,defaultMessage:t.contentTypeDisplayName})})}),e.jsx(l,{children:e.jsx(p,{display:"inline-block",children:t.status?e.jsx(v,{status:t.status}):e.jsx(d,{textColor:"neutral600","aria-hidden":!0,children:"-"})})}),e.jsx(l,{children:e.jsx(d,{textColor:"neutral600",children:e.jsx(C,{timestamp:new Date(t.updatedAt)})})}),e.jsx(l,{onClick:r=>r.stopPropagation(),children:e.jsx(p,{display:"inline-block",children:e.jsx(D,{tag:L,to:g(t),onClick:()=>a("willEditEntryFromHome"),label:n({id:"content-manager.actions.edit.label",defaultMessage:"Edit"}),variant:"ghost",children:e.jsx(R,{})})})})]},t.documentId))})})},I=()=>{const{formatMessage:s}=c(),{data:n,isLoading:a,error:i}=x({action:"update"});return a?e.jsx(o.Loading,{}):i||!n?e.jsx(o.Error,{}):n.length===0?e.jsx(o.NoData,{children:s({id:"content-manager.widget.last-edited.no-data",defaultMessage:"No edited entries"})}):e.jsx(h,{documents:n})},P=()=>{const{formatMessage:s}=c(),{data:n,isLoading:a,error:i}=x({action:"publish"});return a?e.jsx(o.Loading,{}):i||!n?e.jsx(o.Error,{}):n.length===0?e.jsx(o.NoData,{children:s({id:"content-manager.widget.last-published.no-data",defaultMessage:"No published entries"})}):e.jsx(h,{documents:n})};export{I as LastEditedWidget,P as LastPublishedWidget};
