import {
  Table,
  useOnce
} from "./chunk-6DVWLYOO.js";
import {
  useDeleteTransferTokenMutation,
  useGetTransferTokensQuery
} from "./chunk-LPUY5VFU.js";
import {
  TRANSFER_TOKEN_TYPE
} from "./chunk-7GAX2FTH.js";
import "./chunk-K4LYEYIN.js";
import "./chunk-XGEFBNZK.js";
import "./chunk-YW3XCEFV.js";
import "./chunk-MBK4V2X7.js";
import {
  useTracking
} from "./chunk-DZIOEGNZ.js";
import "./chunk-L3WQFZOD.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-L32VSWBJ.js";
import {
  useRBAC
} from "./chunk-WIFIVZU3.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import {
  Layouts
} from "./chunk-DETWX3NC.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import {
  ForwardRef$J,
  Page,
  useAPIErrorHandler
} from "./chunk-FTSHQ5RF.js";
import "./chunk-RZWN45NY.js";
import {
  useTypedSelector
} from "./chunk-FOUXGIF2.js";
import {
  require_lib
} from "./chunk-TXOJWASW.js";
import "./chunk-BUMITFEB.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import {
  useNotification
} from "./chunk-PW6GS6S3.js";
import {
  EmptyStateLayout,
  LinkButton,
  useIntl
} from "./chunk-Y6YT4U2T.js";
import {
  Link,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1h
} from "./chunk-QW5ZDDTU.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import "./chunk-3CQBCJ3G.js";
import {
  require_react
} from "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/admin/dist/admin/admin/src/pages/Settings/pages/TransferTokens/ListView.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var qs = __toESM(require_lib(), 1);
var tableHeaders = [
  {
    name: "name",
    label: {
      id: "Settings.tokens.ListView.headers.name",
      defaultMessage: "Name"
    },
    sortable: true
  },
  {
    name: "description",
    label: {
      id: "Settings.tokens.ListView.headers.description",
      defaultMessage: "Description"
    },
    sortable: false
  },
  {
    name: "createdAt",
    label: {
      id: "Settings.tokens.ListView.headers.createdAt",
      defaultMessage: "Created at"
    },
    sortable: false
  },
  {
    name: "lastUsedAt",
    label: {
      id: "Settings.tokens.ListView.headers.lastUsedAt",
      defaultMessage: "Last used"
    },
    sortable: false
  }
];
var ListView = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["transfer-tokens"];
  });
  const { isLoading: isLoadingRBAC, allowedActions: { canCreate, canDelete, canUpdate, canRead } } = useRBAC(permissions);
  const navigate = useNavigate();
  const { trackUsage } = useTracking();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  React.useEffect(() => {
    navigate({
      search: qs.stringify({
        sort: "name:ASC"
      }, {
        encode: false
      })
    });
  }, [
    navigate
  ]);
  useOnce(() => {
    trackUsage("willAccessTokenList", {
      tokenType: TRANSFER_TOKEN_TYPE
    });
  });
  const headers = tableHeaders.map((header) => ({
    ...header,
    label: formatMessage(header.label)
  }));
  const { data: transferTokens = [], isLoading: isLoadingTokens, error } = useGetTransferTokensQuery(void 0, {
    skip: !canRead
  });
  React.useEffect(() => {
    if (transferTokens) {
      trackUsage("didAccessTokenList", {
        number: transferTokens.length,
        tokenType: TRANSFER_TOKEN_TYPE
      });
    }
  }, [
    trackUsage,
    transferTokens
  ]);
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [
    error,
    formatAPIError,
    toggleNotification
  ]);
  const [deleteToken] = useDeleteTransferTokenMutation();
  const handleDelete = async (id) => {
    try {
      const res = await deleteToken(id);
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
      }
    } catch {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occured"
        })
      });
    }
  };
  const isLoading = isLoadingTokens || isLoadingRBAC;
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, {
    children: [
      (0, import_jsx_runtime.jsx)(Page.Title, {
        children: formatMessage({
          id: "Settings.PageTitle",
          defaultMessage: "Settings - {name}"
        }, {
          name: "Transfer Tokens"
        })
      }),
      (0, import_jsx_runtime.jsx)(Layouts.Header, {
        title: formatMessage({
          id: "Settings.transferTokens.title",
          defaultMessage: "Transfer Tokens"
        }),
        subtitle: formatMessage({
          id: "Settings.transferTokens.description",
          defaultMessage: '"List of generated transfer tokens"'
        }),
        primaryAction: canCreate ? (0, import_jsx_runtime.jsx)(LinkButton, {
          role: "button",
          tag: Link,
          "data-testid": "create-transfer-token-button",
          startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
          size: "S",
          onClick: () => trackUsage("willAddTokenFromList", {
            tokenType: TRANSFER_TOKEN_TYPE
          }),
          to: "/settings/transfer-tokens/create",
          children: formatMessage({
            id: "Settings.transferTokens.create",
            defaultMessage: "Create new Transfer Token"
          })
        }) : void 0
      }),
      !canRead ? (0, import_jsx_runtime.jsx)(Page.NoPermissions, {}) : (0, import_jsx_runtime.jsx)(Page.Main, {
        "aria-busy": isLoading,
        children: (0, import_jsx_runtime.jsxs)(Layouts.Content, {
          children: [
            transferTokens.length > 0 && (0, import_jsx_runtime.jsx)(Table, {
              permissions: {
                canRead,
                canDelete,
                canUpdate
              },
              headers,
              isLoading,
              onConfirmDelete: handleDelete,
              tokens: transferTokens,
              tokenType: TRANSFER_TOKEN_TYPE
            }),
            canCreate && transferTokens.length === 0 ? (0, import_jsx_runtime.jsx)(EmptyStateLayout, {
              action: (0, import_jsx_runtime.jsx)(LinkButton, {
                tag: Link,
                variant: "secondary",
                startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1h, {}),
                to: "/settings/transfer-tokens/create",
                children: formatMessage({
                  id: "Settings.transferTokens.addNewToken",
                  defaultMessage: "Add new Transfer Token"
                })
              }),
              icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, {
                width: "16rem"
              }),
              content: formatMessage({
                id: "Settings.transferTokens.addFirstToken",
                defaultMessage: "Add your first Transfer Token"
              })
            }) : null,
            !canCreate && transferTokens.length === 0 ? (0, import_jsx_runtime.jsx)(EmptyStateLayout, {
              icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, {
                width: "16rem"
              }),
              content: formatMessage({
                id: "Settings.transferTokens.emptyStateLayout",
                defaultMessage: "You don’t have any content yet..."
              })
            }) : null
          ]
        })
      })
    ]
  });
};
var ProtectedListView = () => {
  const permissions = useTypedSelector((state) => {
    var _a;
    return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["transfer-tokens"].main;
  });
  return (0, import_jsx_runtime.jsx)(Page.Protect, {
    permissions,
    children: (0, import_jsx_runtime.jsx)(ListView, {})
  });
};
export {
  ListView,
  ProtectedListView
};
//# sourceMappingURL=ListView-QQKDWT7A.js.map
