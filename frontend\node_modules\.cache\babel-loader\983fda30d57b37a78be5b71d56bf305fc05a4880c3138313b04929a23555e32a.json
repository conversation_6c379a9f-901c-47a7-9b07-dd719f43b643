{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\dfghj\\\\site\\\\frontend\\\\src\\\\components\\\\OpportuniteForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction OpportuniteForm() {\n  _s();\n  // États pour les données\n  const [importateurs, setImportateurs] = useState([]);\n  const [exportateurs, setExportateurs] = useState([]);\n  const [matchedExportateurs, setMatchedExportateurs] = useState([]);\n  const [selectedExportateurs, setSelectedExportateurs] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showNewImportateurForm, setShowNewImportateurForm] = useState(false);\n  const [showMatching, setShowMatching] = useState(false);\n  const [showConfirmation, setShowConfirmation] = useState(false);\n\n  // État du formulaire principal\n  const [formData, setFormData] = useState({\n    importateur: \"\",\n    objet: \"\",\n    pays: \"\",\n    pays_autre: \"\",\n    date_debut: \"\",\n    date_fin: \"\",\n    secteurs: []\n  });\n\n  // État du formulaire nouvel importateur\n  const [newImportateur, setNewImportateur] = useState({\n    societe: \"\",\n    pays: \"\",\n    nom_responsable: \"\",\n    prenom_responsable: \"\",\n    telephone_whatsapp: \"\",\n    email: \"\",\n    region: \"\"\n  });\n\n  // Listes des options\n  const secteurs = [\"Agro-alimentaire\", \"Textile\", \"IME\", \"Service\", \"Artisanat\", \"Divers\"];\n  const pays = [\"France\", \"Allemagne\", \"Italie\", \"Espagne\", \"Belgique\", \"Pays-Bas\", \"Royaume-Uni\", \"Suisse\", \"Canada\", \"États-Unis\", \"Maroc\", \"Algérie\", \"Libye\", \"Égypte\", \"Arabie Saoudite\", \"Émirats Arabes Unis\", \"Qatar\", \"Koweït\", \"Turquie\", \"Chine\", \"Japon\", \"Corée du Sud\", \"Inde\", \"Autre\"];\n\n  // Charger les importateurs au démarrage\n  useEffect(() => {\n    Promise.all([fetch(\"http://localhost:1337/api/importateurs?populate=*\").then(r => r.json()), fetch(\"http://localhost:1337/api/exportateurs?populate=*\").then(r => r.json())]).then(([importateursData, exportateursData]) => {\n      console.log(\"Importateurs dans OpportuniteForm:\", importateursData);\n      console.log(\"Premier importateur structure:\", importateursData.data && importateursData.data[0]);\n      console.log(\"Exportateurs dans OpportuniteForm:\", exportateursData);\n      if (importateursData !== null && importateursData !== void 0 && importateursData.data) setImportateurs(importateursData.data);\n      if (exportateursData !== null && exportateursData !== void 0 && exportateursData.data) setExportateurs(exportateursData.data);\n      setLoading(false);\n    }).catch(err => {\n      console.error(\"Erreur lors du chargement:\", err);\n      setLoading(false);\n    });\n  }, []);\n\n  // Fonctions utilitaires\n  const isFormValid = () => {\n    const paysValid = formData.pays && (formData.pays !== \"Autre\" || formData.pays_autre);\n    return formData.importateur && formData.objet && paysValid && formData.date_debut && formData.date_fin && formData.secteurs.length > 0;\n  };\n  const handleMatching = async () => {\n    if (!isFormValid()) return;\n\n    // Si \"autre\" est sélectionné, créer d'abord le nouvel importateur\n    if (formData.importateur === \"autre\") {\n      try {\n        const response = await fetch(\"http://localhost:1337/api/importateurs\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\"\n          },\n          body: JSON.stringify({\n            data: newImportateur\n          })\n        });\n        const newImp = await response.json();\n        setFormData({\n          ...formData,\n          importateur: newImp.data.id\n        });\n        setImportateurs([...importateurs, newImp.data]);\n        setShowNewImportateurForm(false);\n      } catch (error) {\n        console.error(\"Erreur lors de la création de l'importateur:\", error);\n        return;\n      }\n    }\n\n    // Filtrer les exportateurs par secteur\n    console.log(\"Secteurs sélectionnés:\", formData.secteurs);\n    console.log(\"Tous les exportateurs:\", exportateurs);\n    const matched = exportateurs.filter(exp => {\n      var _exp$attributes, _exp$data, _exp$data$attributes;\n      console.log(\"Exportateur en cours:\", exp);\n\n      // Essayer différentes structures de données\n      const expSecteur = (exp === null || exp === void 0 ? void 0 : (_exp$attributes = exp.attributes) === null || _exp$attributes === void 0 ? void 0 : _exp$attributes.secteur_activite) || (exp === null || exp === void 0 ? void 0 : exp.secteur_activite) || (exp === null || exp === void 0 ? void 0 : (_exp$data = exp.data) === null || _exp$data === void 0 ? void 0 : (_exp$data$attributes = _exp$data.attributes) === null || _exp$data$attributes === void 0 ? void 0 : _exp$data$attributes.secteur_activite);\n      console.log(\"Secteur de l'exportateur:\", expSecteur);\n      console.log(\"Secteur inclus?\", formData.secteurs.includes(expSecteur));\n      return formData.secteurs.includes(expSecteur);\n    });\n    console.log(\"Exportateurs correspondants:\", matched);\n    setMatchedExportateurs(matched);\n    setShowMatching(true);\n  };\n  const handleSendOpportunity = () => {\n    setShowConfirmation(true);\n  };\n  const getImportateurName = () => {\n    var _imp$attributes, _imp$data, _imp$data$attributes;\n    if (formData.importateur === \"autre\") {\n      return newImportateur.societe;\n    }\n    const imp = importateurs.find(i => (i.id || i.documentId) === formData.importateur);\n    // Utiliser la même logique flexible\n    return (imp === null || imp === void 0 ? void 0 : (_imp$attributes = imp.attributes) === null || _imp$attributes === void 0 ? void 0 : _imp$attributes.societe) || (imp === null || imp === void 0 ? void 0 : imp.societe) || (imp === null || imp === void 0 ? void 0 : (_imp$data = imp.data) === null || _imp$data === void 0 ? void 0 : (_imp$data$attributes = _imp$data.attributes) === null || _imp$data$attributes === void 0 ? void 0 : _imp$data$attributes.societe) || \"Importateur inconnu\";\n  };\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"Chargement...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 23\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: \"20px\",\n      maxWidth: \"800px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"\\uD83D\\uDD0D Formulaire Opportunit\\xE9 d'Importation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), !showConfirmation ? /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"form\", {\n        style: {\n          marginBottom: \"30px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: \"block\",\n              marginBottom: \"5px\",\n              fontWeight: \"bold\"\n            },\n            children: \"Importateur *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: formData.importateur,\n            onChange: e => {\n              setFormData({\n                ...formData,\n                importateur: e.target.value\n              });\n              setShowNewImportateurForm(e.target.value === \"autre\");\n            },\n            style: {\n              width: \"100%\",\n              padding: \"8px\",\n              marginBottom: \"10px\"\n            },\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"-- S\\xE9lectionner un importateur --\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), importateurs.map(imp => {\n              var _imp$attributes2, _imp$data2, _imp$data2$attributes;\n              // Utiliser la même logique flexible que dans les autres composants\n              const societe = (imp === null || imp === void 0 ? void 0 : (_imp$attributes2 = imp.attributes) === null || _imp$attributes2 === void 0 ? void 0 : _imp$attributes2.societe) || (imp === null || imp === void 0 ? void 0 : imp.societe) || (imp === null || imp === void 0 ? void 0 : (_imp$data2 = imp.data) === null || _imp$data2 === void 0 ? void 0 : (_imp$data2$attributes = _imp$data2.attributes) === null || _imp$data2$attributes === void 0 ? void 0 : _imp$data2$attributes.societe) || 'Nom non disponible';\n              return /*#__PURE__*/_jsxDEV(\"option\", {\n                value: imp.id || imp.documentId,\n                children: societe\n              }, imp.id || imp.documentId, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 21\n              }, this);\n            }), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"autre\",\n              children: \"\\u2795 Autre (Ajouter un nouveau)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this), showNewImportateurForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            border: \"2px solid #007bff\",\n            padding: \"15px\",\n            marginBottom: \"20px\",\n            borderRadius: \"5px\",\n            backgroundColor: \"#f8f9fa\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\u2795 Ajouter un nouvel importateur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"grid\",\n              gridTemplateColumns: \"1fr 1fr\",\n              gap: \"10px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"Soci\\xE9t\\xE9 *\",\n              value: newImportateur.societe,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                societe: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"Pays *\",\n              value: newImportateur.pays,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                pays: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"Nom responsable *\",\n              value: newImportateur.nom_responsable,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                nom_responsable: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"Pr\\xE9nom responsable *\",\n              value: newImportateur.prenom_responsable,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                prenom_responsable: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"T\\xE9l\\xE9phone WhatsApp *\",\n              value: newImportateur.telephone_whatsapp,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                telephone_whatsapp: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"Email *\",\n              type: \"email\",\n              value: newImportateur.email,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                email: e.target.value\n              }),\n              style: {\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              placeholder: \"R\\xE9gion *\",\n              value: newImportateur.region,\n              onChange: e => setNewImportateur({\n                ...newImportateur,\n                region: e.target.value\n              }),\n              style: {\n                padding: \"8px\",\n                gridColumn: \"1 / -1\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: \"block\",\n              marginBottom: \"5px\",\n              fontWeight: \"bold\"\n            },\n            children: \"Objet (Produit recherch\\xE9, sp\\xE9cifications...) *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: formData.objet,\n            onChange: e => setFormData({\n              ...formData,\n              objet: e.target.value\n            }),\n            style: {\n              width: \"100%\",\n              padding: \"8px\",\n              minHeight: \"100px\"\n            },\n            placeholder: \"D\\xE9crivez le produit recherch\\xE9, les sp\\xE9cifications techniques, quantit\\xE9s, etc.\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: \"block\",\n              marginBottom: \"5px\",\n              fontWeight: \"bold\"\n            },\n            children: \"Pays de destination *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: formData.pays,\n            onChange: e => setFormData({\n              ...formData,\n              pays: e.target.value\n            }),\n            style: {\n              width: \"100%\",\n              padding: \"8px\",\n              marginBottom: \"10px\"\n            },\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"-- S\\xE9lectionner un pays --\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this), pays.map(p => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: p,\n              children: p\n            }, p, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), formData.pays === \"Autre\" && /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Pr\\xE9cisez le pays *\",\n            value: formData.pays_autre,\n            onChange: e => setFormData({\n              ...formData,\n              pays_autre: e.target.value\n            }),\n            style: {\n              width: \"100%\",\n              padding: \"8px\",\n              border: \"2px solid #007bff\",\n              borderRadius: \"4px\"\n            },\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"grid\",\n            gridTemplateColumns: \"1fr 1fr\",\n            gap: \"20px\",\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: \"block\",\n                marginBottom: \"5px\",\n                fontWeight: \"bold\"\n              },\n              children: \"Date d\\xE9but *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: formData.date_debut,\n              onChange: e => setFormData({\n                ...formData,\n                date_debut: e.target.value\n              }),\n              style: {\n                width: \"100%\",\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: \"block\",\n                marginBottom: \"5px\",\n                fontWeight: \"bold\"\n              },\n              children: \"Date fin *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              value: formData.date_fin,\n              onChange: e => setFormData({\n                ...formData,\n                date_fin: e.target.value\n              }),\n              style: {\n                width: \"100%\",\n                padding: \"8px\"\n              },\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: \"20px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: \"block\",\n              marginBottom: \"5px\",\n              fontWeight: \"bold\"\n            },\n            children: \"Secteurs d'activit\\xE9 * (s\\xE9lection multiple)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              border: \"1px solid #ccc\",\n              padding: \"10px\",\n              borderRadius: \"4px\",\n              display: \"grid\",\n              gridTemplateColumns: \"repeat(2, 1fr)\",\n              gap: \"10px\"\n            },\n            children: secteurs.map(secteur => /*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: \"flex\",\n                alignItems: \"center\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: formData.secteurs.includes(secteur),\n                onChange: e => {\n                  if (e.target.checked) {\n                    setFormData({\n                      ...formData,\n                      secteurs: [...formData.secteurs, secteur]\n                    });\n                  } else {\n                    setFormData({\n                      ...formData,\n                      secteurs: formData.secteurs.filter(s => s !== secteur)\n                    });\n                  }\n                },\n                style: {\n                  marginRight: \"8px\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 21\n              }, this), secteur]\n            }, secteur, true, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this), formData.secteurs.length === 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n            style: {\n              color: \"red\"\n            },\n            children: \"Veuillez s\\xE9lectionner au moins un secteur\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          onClick: () => handleMatching(),\n          disabled: !isFormValid(),\n          style: {\n            backgroundColor: isFormValid() ? \"#28a745\" : \"#6c757d\",\n            color: \"white\",\n            padding: \"12px 30px\",\n            border: \"none\",\n            borderRadius: \"5px\",\n            fontSize: \"16px\",\n            fontWeight: \"bold\",\n            cursor: isFormValid() ? \"pointer\" : \"not-allowed\",\n            marginRight: \"10px\"\n          },\n          children: \"\\uD83D\\uDD0D MATCHING\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 11\n      }, this), showMatching && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          border: \"2px solid #28a745\",\n          padding: \"20px\",\n          borderRadius: \"5px\",\n          backgroundColor: \"#f8fff9\",\n          marginBottom: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"\\uD83C\\uDFAF Exportateurs correspondants (\", matchedExportateurs.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 15\n        }, this), matchedExportateurs.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"table\", {\n            style: {\n              width: \"100%\",\n              borderCollapse: \"collapse\",\n              marginBottom: \"20px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: {\n                  backgroundColor: \"#e9ecef\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"10px\",\n                    border: \"1px solid #ddd\",\n                    textAlign: \"left\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    onChange: e => {\n                      if (e.target.checked) {\n                        setSelectedExportateurs(matchedExportateurs.map(exp => exp.id));\n                      } else {\n                        setSelectedExportateurs([]);\n                      }\n                    },\n                    style: {\n                      marginRight: \"5px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 27\n                  }, this), \"S\\xE9lectionner\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"10px\",\n                    border: \"1px solid #ddd\",\n                    textAlign: \"left\"\n                  },\n                  children: \"Entreprise\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"10px\",\n                    border: \"1px solid #ddd\",\n                    textAlign: \"left\"\n                  },\n                  children: \"Contact\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"10px\",\n                    border: \"1px solid #ddd\",\n                    textAlign: \"left\"\n                  },\n                  children: \"Secteur\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: \"10px\",\n                    border: \"1px solid #ddd\",\n                    textAlign: \"left\"\n                  },\n                  children: \"Localisation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: matchedExportateurs.map(exp => {\n                var _exp$attributes2, _exp$data2, _exp$data2$attributes, _exp$attributes3, _exp$data3, _exp$data3$attributes, _exp$attributes4, _exp$attributes5, _exp$data4, _exp$data4$attributes, _exp$attributes6, _exp$data5, _exp$data5$attributes, _exp$attributes7, _exp$data6, _exp$data6$attributes, _exp$attributes8, _exp$data7, _exp$data7$attributes, _exp$attributes9, _exp$data8, _exp$data8$attributes, _exp$attributes0, _exp$attributes0$vill, _exp$attributes0$vill2, _exp$attributes0$vill3, _exp$attributes1, _exp$attributes1$vill, _exp$attributes10, _exp$ville, _exp$ville$data, _exp$ville$data$attri, _exp$ville2, _exp$data9, _exp$data9$attributes, _exp$attributes11;\n                console.log(\"Affichage exportateur:\", exp);\n\n                // Utiliser la logique flexible pour accéder aux données\n                const raisonSociale = (exp === null || exp === void 0 ? void 0 : (_exp$attributes2 = exp.attributes) === null || _exp$attributes2 === void 0 ? void 0 : _exp$attributes2.raison_sociale) || (exp === null || exp === void 0 ? void 0 : exp.raison_sociale) || (exp === null || exp === void 0 ? void 0 : (_exp$data2 = exp.data) === null || _exp$data2 === void 0 ? void 0 : (_exp$data2$attributes = _exp$data2.attributes) === null || _exp$data2$attributes === void 0 ? void 0 : _exp$data2$attributes.raison_sociale) || 'Nom non disponible';\n                const email = (exp === null || exp === void 0 ? void 0 : (_exp$attributes3 = exp.attributes) === null || _exp$attributes3 === void 0 ? void 0 : _exp$attributes3.email) || (exp === null || exp === void 0 ? void 0 : exp.email) || (exp === null || exp === void 0 ? void 0 : (_exp$data3 = exp.data) === null || _exp$data3 === void 0 ? void 0 : (_exp$data3$attributes = _exp$data3.attributes) === null || _exp$data3$attributes === void 0 ? void 0 : _exp$data3$attributes.email) || 'Email non disponible';\n                const telephone = (exp === null || exp === void 0 ? void 0 : (_exp$attributes4 = exp.attributes) === null || _exp$attributes4 === void 0 ? void 0 : _exp$attributes4.telephone_siege) || (exp === null || exp === void 0 ? void 0 : exp.telephone_siege) || (exp === null || exp === void 0 ? void 0 : (_exp$attributes5 = exp.attributes) === null || _exp$attributes5 === void 0 ? void 0 : _exp$attributes5.mobile) || (exp === null || exp === void 0 ? void 0 : exp.mobile) || (exp === null || exp === void 0 ? void 0 : (_exp$data4 = exp.data) === null || _exp$data4 === void 0 ? void 0 : (_exp$data4$attributes = _exp$data4.attributes) === null || _exp$data4$attributes === void 0 ? void 0 : _exp$data4$attributes.telephone_siege) || 'Tél. non disponible';\n                const nomContact = (exp === null || exp === void 0 ? void 0 : (_exp$attributes6 = exp.attributes) === null || _exp$attributes6 === void 0 ? void 0 : _exp$attributes6.nom_contact) || (exp === null || exp === void 0 ? void 0 : exp.nom_contact) || (exp === null || exp === void 0 ? void 0 : (_exp$data5 = exp.data) === null || _exp$data5 === void 0 ? void 0 : (_exp$data5$attributes = _exp$data5.attributes) === null || _exp$data5$attributes === void 0 ? void 0 : _exp$data5$attributes.nom_contact) || '';\n                const prenomContact = (exp === null || exp === void 0 ? void 0 : (_exp$attributes7 = exp.attributes) === null || _exp$attributes7 === void 0 ? void 0 : _exp$attributes7.prenom_contact) || (exp === null || exp === void 0 ? void 0 : exp.prenom_contact) || (exp === null || exp === void 0 ? void 0 : (_exp$data6 = exp.data) === null || _exp$data6 === void 0 ? void 0 : (_exp$data6$attributes = _exp$data6.attributes) === null || _exp$data6$attributes === void 0 ? void 0 : _exp$data6$attributes.prenom_contact) || '';\n                const contact = nomContact && prenomContact ? `${prenomContact} ${nomContact}` : nomContact || prenomContact || 'Contact non disponible';\n                const secteur = (exp === null || exp === void 0 ? void 0 : (_exp$attributes8 = exp.attributes) === null || _exp$attributes8 === void 0 ? void 0 : _exp$attributes8.secteur_activite) || (exp === null || exp === void 0 ? void 0 : exp.secteur_activite) || (exp === null || exp === void 0 ? void 0 : (_exp$data7 = exp.data) === null || _exp$data7 === void 0 ? void 0 : (_exp$data7$attributes = _exp$data7.attributes) === null || _exp$data7$attributes === void 0 ? void 0 : _exp$data7$attributes.secteur_activite) || 'Secteur non disponible';\n                const adresse = (exp === null || exp === void 0 ? void 0 : (_exp$attributes9 = exp.attributes) === null || _exp$attributes9 === void 0 ? void 0 : _exp$attributes9.adresse) || (exp === null || exp === void 0 ? void 0 : exp.adresse) || (exp === null || exp === void 0 ? void 0 : (_exp$data8 = exp.data) === null || _exp$data8 === void 0 ? void 0 : (_exp$data8$attributes = _exp$data8.attributes) === null || _exp$data8$attributes === void 0 ? void 0 : _exp$data8$attributes.adresse) || '';\n\n                // Pour la ville, essayer différentes structures\n                const ville = (exp === null || exp === void 0 ? void 0 : (_exp$attributes0 = exp.attributes) === null || _exp$attributes0 === void 0 ? void 0 : (_exp$attributes0$vill = _exp$attributes0.ville) === null || _exp$attributes0$vill === void 0 ? void 0 : (_exp$attributes0$vill2 = _exp$attributes0$vill.data) === null || _exp$attributes0$vill2 === void 0 ? void 0 : (_exp$attributes0$vill3 = _exp$attributes0$vill2.attributes) === null || _exp$attributes0$vill3 === void 0 ? void 0 : _exp$attributes0$vill3.nom) || (exp === null || exp === void 0 ? void 0 : (_exp$attributes1 = exp.attributes) === null || _exp$attributes1 === void 0 ? void 0 : (_exp$attributes1$vill = _exp$attributes1.ville) === null || _exp$attributes1$vill === void 0 ? void 0 : _exp$attributes1$vill.nom) || (exp === null || exp === void 0 ? void 0 : (_exp$attributes10 = exp.attributes) === null || _exp$attributes10 === void 0 ? void 0 : _exp$attributes10.ville) || (exp === null || exp === void 0 ? void 0 : (_exp$ville = exp.ville) === null || _exp$ville === void 0 ? void 0 : (_exp$ville$data = _exp$ville.data) === null || _exp$ville$data === void 0 ? void 0 : (_exp$ville$data$attri = _exp$ville$data.attributes) === null || _exp$ville$data$attri === void 0 ? void 0 : _exp$ville$data$attri.nom) || (exp === null || exp === void 0 ? void 0 : (_exp$ville2 = exp.ville) === null || _exp$ville2 === void 0 ? void 0 : _exp$ville2.nom) || (exp === null || exp === void 0 ? void 0 : exp.ville) || (exp === null || exp === void 0 ? void 0 : (_exp$data9 = exp.data) === null || _exp$data9 === void 0 ? void 0 : (_exp$data9$attributes = _exp$data9.attributes) === null || _exp$data9$attributes === void 0 ? void 0 : _exp$data9$attributes.ville) || '';\n                const localisation = ville && adresse ? `${ville} - ${adresse}` : ville || adresse || 'Localisation non disponible';\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: \"10px\",\n                      border: \"1px solid #ddd\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: selectedExportateurs.includes(exp.id),\n                      onChange: e => {\n                        if (e.target.checked) {\n                          setSelectedExportateurs([...selectedExportateurs, exp.id]);\n                        } else {\n                          setSelectedExportateurs(selectedExportateurs.filter(id => id !== exp.id));\n                        }\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: \"10px\",\n                      border: \"1px solid #ddd\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        style: {\n                          color: \"#007bff\"\n                        },\n                        children: raisonSociale\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 501,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 502,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                        style: {\n                          color: \"#6c757d\"\n                        },\n                        children: (exp === null || exp === void 0 ? void 0 : (_exp$attributes11 = exp.attributes) === null || _exp$attributes11 === void 0 ? void 0 : _exp$attributes11.matricule_fiscal) || (exp === null || exp === void 0 ? void 0 : exp.matricule_fiscal) || 'Matricule N/A'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: \"10px\",\n                      border: \"1px solid #ddd\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontWeight: \"bold\"\n                        },\n                        children: contact\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 510,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: \"12px\",\n                          color: \"#007bff\"\n                        },\n                        children: email\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 511,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          fontSize: \"12px\",\n                          color: \"#28a745\"\n                        },\n                        children: telephone\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 512,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: \"10px\",\n                      border: \"1px solid #ddd\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        backgroundColor: \"#e9ecef\",\n                        padding: \"4px 8px\",\n                        borderRadius: \"12px\",\n                        fontSize: \"12px\",\n                        fontWeight: \"bold\"\n                      },\n                      children: secteur\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 516,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    style: {\n                      padding: \"10px\",\n                      border: \"1px solid #ddd\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: \"12px\"\n                      },\n                      children: localisation\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 29\n                  }, this)]\n                }, exp.id || exp.documentId, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 27\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSendOpportunity,\n            disabled: selectedExportateurs.length === 0,\n            style: {\n              backgroundColor: selectedExportateurs.length > 0 ? \"#007bff\" : \"#6c757d\",\n              color: \"white\",\n              padding: \"12px 30px\",\n              border: \"none\",\n              borderRadius: \"5px\",\n              fontSize: \"16px\",\n              fontWeight: \"bold\",\n              cursor: selectedExportateurs.length > 0 ? \"pointer\" : \"not-allowed\"\n            },\n            children: [\"\\uD83D\\uDCE7 ENVOYER (\", selectedExportateurs.length, \" s\\xE9lectionn\\xE9\", selectedExportateurs.length > 1 ? 's' : '', \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 17\n        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: \"#856404\",\n            backgroundColor: \"#fff3cd\",\n            padding: \"10px\",\n            borderRadius: \"4px\"\n          },\n          children: [\"\\u26A0\\uFE0F Aucun exportateur trouv\\xE9 pour les secteurs s\\xE9lectionn\\xE9s : \", formData.secteurs.join(\", \")]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 555,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: \"#d4edda\",\n        border: \"2px solid #28a745\",\n        borderRadius: \"10px\",\n        padding: \"30px\",\n        textAlign: \"center\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          color: \"#155724\",\n          marginBottom: \"20px\"\n        },\n        children: \"\\uD83C\\uDF89 F\\xE9licitations ! Votre opportunit\\xE9 a bien \\xE9t\\xE9 soumise.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 570,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: \"white\",\n          padding: \"20px\",\n          borderRadius: \"8px\",\n          textAlign: \"left\",\n          marginBottom: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: \"#155724\",\n            marginBottom: \"15px\"\n          },\n          children: \"\\uD83D\\uDCE7 Un email sera envoy\\xE9 aux exportateurs s\\xE9lectionn\\xE9s avec le contenu suivant :\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 581,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: \"#f8f9fa\",\n            padding: \"15px\",\n            borderRadius: \"5px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Objet :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 18\n            }, this), \" Nouvelle Opportunit\\xE9 d'Importation\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Contenu :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 18\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              marginLeft: \"20px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Importateur :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 21\n              }, this), \" \", getImportateurName()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Objet :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 21\n              }, this), \" \", formData.objet]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Pays de destination :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 21\n              }, this), \" \", formData.pays === \"Autre\" ? formData.pays_autre : formData.pays]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"P\\xE9riode :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 21\n              }, this), \" \", formData.date_debut, \" \\u2192 \", formData.date_fin]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Secteurs concern\\xE9s :\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 21\n              }, this), \" \", formData.secteurs.join(\", \")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Exportateurs contact\\xE9s :\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 18\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              marginLeft: \"20px\"\n            },\n            children: selectedExportateurs.map(expId => {\n              var _exp$attributes12, _exp$attributes13;\n              const exp = matchedExportateurs.find(e => e.id === expId);\n              return /*#__PURE__*/_jsxDEV(\"li\", {\n                children: [exp === null || exp === void 0 ? void 0 : (_exp$attributes12 = exp.attributes) === null || _exp$attributes12 === void 0 ? void 0 : _exp$attributes12.email, \" (\", exp === null || exp === void 0 ? void 0 : (_exp$attributes13 = exp.attributes) === null || _exp$attributes13 === void 0 ? void 0 : _exp$attributes13.raison_sociale, \")\"]\n              }, expId, true, {\n                fileName: _jsxFileName,\n                lineNumber: 602,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          setShowConfirmation(false);\n          setShowMatching(false);\n          setSelectedExportateurs([]);\n          setMatchedExportateurs([]);\n          setFormData({\n            importateur: \"\",\n            objet: \"\",\n            pays: \"\",\n            pays_autre: \"\",\n            date_debut: \"\",\n            date_fin: \"\",\n            secteurs: []\n          });\n          setNewImportateur({\n            societe: \"\",\n            pays: \"\",\n            nom_responsable: \"\",\n            prenom_responsable: \"\",\n            telephone_whatsapp: \"\",\n            email: \"\",\n            region: \"\"\n          });\n          setShowNewImportateurForm(false);\n        },\n        style: {\n          backgroundColor: \"#007bff\",\n          color: \"white\",\n          padding: \"12px 30px\",\n          border: \"none\",\n          borderRadius: \"5px\",\n          fontSize: \"16px\",\n          fontWeight: \"bold\",\n          cursor: \"pointer\"\n        },\n        children: \"\\u2728 Nouvelle Opportunit\\xE9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this);\n}\n_s(OpportuniteForm, \"Zyq5a+pmRF0rllIpQWug7zR+WVg=\");\n_c = OpportuniteForm;\nexport default OpportuniteForm;\nvar _c;\n$RefreshReg$(_c, \"OpportuniteForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "OpportuniteForm", "_s", "importateurs", "setImportateurs", "exportateurs", "setExportateurs", "matchedExportateurs", "setMatchedExportateurs", "selectedExportateurs", "setSelectedExportateurs", "loading", "setLoading", "showNewImportateurForm", "setShowNewImportateurForm", "showMatching", "setShowMatching", "showConfirmation", "setShowConfirmation", "formData", "setFormData", "importateur", "objet", "pays", "pays_autre", "date_debut", "date_fin", "secteurs", "newImportateur", "setNewImportateur", "societe", "nom_responsable", "prenom_responsable", "telephone_whatsapp", "email", "region", "Promise", "all", "fetch", "then", "r", "json", "importateursData", "exportateursData", "console", "log", "data", "catch", "err", "error", "isFormValid", "paysValid", "length", "handleMatching", "response", "method", "headers", "body", "JSON", "stringify", "newImp", "id", "matched", "filter", "exp", "_exp$attributes", "_exp$data", "_exp$data$attributes", "expSecteur", "attributes", "secteur_activite", "includes", "handleSendOpportunity", "getImportateurName", "_imp$attributes", "_imp$data", "_imp$data$attributes", "imp", "find", "i", "documentId", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "padding", "max<PERSON><PERSON><PERSON>", "marginBottom", "display", "fontWeight", "value", "onChange", "e", "target", "width", "required", "map", "_imp$attributes2", "_imp$data2", "_imp$data2$attributes", "border", "borderRadius", "backgroundColor", "gridTemplateColumns", "gap", "placeholder", "type", "gridColumn", "minHeight", "p", "secteur", "alignItems", "checked", "s", "marginRight", "color", "onClick", "disabled", "fontSize", "cursor", "borderCollapse", "textAlign", "_exp$attributes2", "_exp$data2", "_exp$data2$attributes", "_exp$attributes3", "_exp$data3", "_exp$data3$attributes", "_exp$attributes4", "_exp$attributes5", "_exp$data4", "_exp$data4$attributes", "_exp$attributes6", "_exp$data5", "_exp$data5$attributes", "_exp$attributes7", "_exp$data6", "_exp$data6$attributes", "_exp$attributes8", "_exp$data7", "_exp$data7$attributes", "_exp$attributes9", "_exp$data8", "_exp$data8$attributes", "_exp$attributes0", "_exp$attributes0$vill", "_exp$attributes0$vill2", "_exp$attributes0$vill3", "_exp$attributes1", "_exp$attributes1$vill", "_exp$attributes10", "_exp$ville", "_exp$ville$data", "_exp$ville$data$attri", "_exp$ville2", "_exp$data9", "_exp$data9$attributes", "_exp$attributes11", "raisonSociale", "raison_sociale", "telephone", "telephone_siege", "mobile", "nomContact", "nom_contact", "prenomContact", "prenom_contact", "contact", "adresse", "ville", "nom", "localisation", "matricule_fiscal", "join", "marginLeft", "expId", "_exp$attributes12", "_exp$attributes13", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Bureau/dfghj/site/frontend/src/components/OpportuniteForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\n\r\nfunction OpportuniteForm() {\r\n  // États pour les données\r\n  const [importateurs, setImportateurs] = useState([]);\r\n  const [exportateurs, setExportateurs] = useState([]);\r\n  const [matchedExportateurs, setMatchedExportateurs] = useState([]);\r\n  const [selectedExportateurs, setSelectedExportateurs] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [showNewImportateurForm, setShowNewImportateurForm] = useState(false);\r\n  const [showMatching, setShowMatching] = useState(false);\r\n  const [showConfirmation, setShowConfirmation] = useState(false);\r\n\r\n  // État du formulaire principal\r\n  const [formData, setFormData] = useState({\r\n    importateur: \"\",\r\n    objet: \"\",\r\n    pays: \"\",\r\n    pays_autre: \"\",\r\n    date_debut: \"\",\r\n    date_fin: \"\",\r\n    secteurs: []\r\n  });\r\n\r\n  // État du formulaire nouvel importateur\r\n  const [newImportateur, setNewImportateur] = useState({\r\n    societe: \"\",\r\n    pays: \"\",\r\n    nom_responsable: \"\",\r\n    prenom_responsable: \"\",\r\n    telephone_whatsapp: \"\",\r\n    email: \"\",\r\n    region: \"\"\r\n  });\r\n\r\n  // Listes des options\r\n  const secteurs = [\r\n    \"Agro-alimentaire\",\r\n    \"Textile\",\r\n    \"IME\",\r\n    \"Service\",\r\n    \"Artisanat\",\r\n    \"Divers\"\r\n  ];\r\n\r\n  const pays = [\r\n    \"France\", \"Allemagne\", \"Italie\", \"Espagne\", \"Belgique\", \"Pays-Bas\",\r\n    \"Royaume-Uni\", \"Suisse\", \"Canada\", \"États-Unis\", \"Maroc\", \"Algérie\",\r\n    \"Libye\", \"Égypte\", \"Arabie Saoudite\", \"Émirats Arabes Unis\", \"Qatar\",\r\n    \"Koweït\", \"Turquie\", \"Chine\", \"Japon\", \"Corée du Sud\", \"Inde\", \"Autre\"\r\n  ];\r\n\r\n  // Charger les importateurs au démarrage\r\n  useEffect(() => {\r\n    Promise.all([\r\n      fetch(\"http://localhost:1337/api/importateurs?populate=*\").then(r => r.json()),\r\n      fetch(\"http://localhost:1337/api/exportateurs?populate=*\").then(r => r.json())\r\n    ])\r\n    .then(([importateursData, exportateursData]) => {\r\n      console.log(\"Importateurs dans OpportuniteForm:\", importateursData);\r\n      console.log(\"Premier importateur structure:\", importateursData.data && importateursData.data[0]);\r\n      console.log(\"Exportateurs dans OpportuniteForm:\", exportateursData);\r\n\r\n      if (importateursData?.data) setImportateurs(importateursData.data);\r\n      if (exportateursData?.data) setExportateurs(exportateursData.data);\r\n\r\n      setLoading(false);\r\n    })\r\n    .catch(err => {\r\n      console.error(\"Erreur lors du chargement:\", err);\r\n      setLoading(false);\r\n    });\r\n  }, []);\r\n\r\n  // Fonctions utilitaires\r\n  const isFormValid = () => {\r\n    const paysValid = formData.pays && (formData.pays !== \"Autre\" || formData.pays_autre);\r\n    return formData.importateur &&\r\n           formData.objet &&\r\n           paysValid &&\r\n           formData.date_debut &&\r\n           formData.date_fin &&\r\n           formData.secteurs.length > 0;\r\n  };\r\n\r\n  const handleMatching = async () => {\r\n    if (!isFormValid()) return;\r\n\r\n    // Si \"autre\" est sélectionné, créer d'abord le nouvel importateur\r\n    if (formData.importateur === \"autre\") {\r\n      try {\r\n        const response = await fetch(\"http://localhost:1337/api/importateurs\", {\r\n          method: \"POST\",\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n          body: JSON.stringify({ data: newImportateur })\r\n        });\r\n        const newImp = await response.json();\r\n        setFormData({...formData, importateur: newImp.data.id});\r\n        setImportateurs([...importateurs, newImp.data]);\r\n        setShowNewImportateurForm(false);\r\n      } catch (error) {\r\n        console.error(\"Erreur lors de la création de l'importateur:\", error);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Filtrer les exportateurs par secteur\r\n    console.log(\"Secteurs sélectionnés:\", formData.secteurs);\r\n    console.log(\"Tous les exportateurs:\", exportateurs);\r\n\r\n    const matched = exportateurs.filter(exp => {\r\n      console.log(\"Exportateur en cours:\", exp);\r\n\r\n      // Essayer différentes structures de données\r\n      const expSecteur = exp?.attributes?.secteur_activite ||\r\n                        exp?.secteur_activite ||\r\n                        exp?.data?.attributes?.secteur_activite;\r\n\r\n      console.log(\"Secteur de l'exportateur:\", expSecteur);\r\n      console.log(\"Secteur inclus?\", formData.secteurs.includes(expSecteur));\r\n\r\n      return formData.secteurs.includes(expSecteur);\r\n    });\r\n\r\n    console.log(\"Exportateurs correspondants:\", matched);\r\n\r\n    setMatchedExportateurs(matched);\r\n    setShowMatching(true);\r\n  };\r\n\r\n  const handleSendOpportunity = () => {\r\n    setShowConfirmation(true);\r\n  };\r\n\r\n  const getImportateurName = () => {\r\n    if (formData.importateur === \"autre\") {\r\n      return newImportateur.societe;\r\n    }\r\n    const imp = importateurs.find(i => (i.id || i.documentId) === formData.importateur);\r\n    // Utiliser la même logique flexible\r\n    return imp?.attributes?.societe ||\r\n           imp?.societe ||\r\n           imp?.data?.attributes?.societe ||\r\n           \"Importateur inconnu\";\r\n  };\r\n\r\n  if (loading) return <div>Chargement...</div>;\r\n\r\n  return (\r\n    <div style={{ padding: \"20px\", maxWidth: \"800px\" }}>\r\n      <h2>🔍 Formulaire Opportunité d'Importation</h2>\r\n\r\n      {!showConfirmation ? (\r\n        <div>\r\n          {/* Formulaire principal */}\r\n          <form style={{ marginBottom: \"30px\" }}>\r\n            {/* Sélection Importateur */}\r\n            <div style={{ marginBottom: \"20px\" }}>\r\n              <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                Importateur *\r\n              </label>\r\n              <select\r\n                value={formData.importateur}\r\n                onChange={(e) => {\r\n                  setFormData({...formData, importateur: e.target.value});\r\n                  setShowNewImportateurForm(e.target.value === \"autre\");\r\n                }}\r\n                style={{ width: \"100%\", padding: \"8px\", marginBottom: \"10px\" }}\r\n                required\r\n              >\r\n                <option value=\"\">-- Sélectionner un importateur --</option>\r\n                {importateurs.map(imp => {\r\n                  // Utiliser la même logique flexible que dans les autres composants\r\n                  const societe = imp?.attributes?.societe ||\r\n                                 imp?.societe ||\r\n                                 imp?.data?.attributes?.societe ||\r\n                                 'Nom non disponible';\r\n                  return (\r\n                    <option key={imp.id || imp.documentId} value={imp.id || imp.documentId}>\r\n                      {societe}\r\n                    </option>\r\n                  );\r\n                })}\r\n                <option value=\"autre\">➕ Autre (Ajouter un nouveau)</option>\r\n              </select>\r\n            </div>\r\n\r\n            {/* Formulaire nouvel importateur (conditionnel) */}\r\n            {showNewImportateurForm && (\r\n              <div style={{\r\n                border: \"2px solid #007bff\",\r\n                padding: \"15px\",\r\n                marginBottom: \"20px\",\r\n                borderRadius: \"5px\",\r\n                backgroundColor: \"#f8f9fa\"\r\n              }}>\r\n                <h4>➕ Ajouter un nouvel importateur</h4>\r\n                <div style={{ display: \"grid\", gridTemplateColumns: \"1fr 1fr\", gap: \"10px\" }}>\r\n                  <input\r\n                    placeholder=\"Société *\"\r\n                    value={newImportateur.societe}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, societe: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Pays *\"\r\n                    value={newImportateur.pays}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, pays: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Nom responsable *\"\r\n                    value={newImportateur.nom_responsable}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, nom_responsable: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Prénom responsable *\"\r\n                    value={newImportateur.prenom_responsable}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, prenom_responsable: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Téléphone WhatsApp *\"\r\n                    value={newImportateur.telephone_whatsapp}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, telephone_whatsapp: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Email *\"\r\n                    type=\"email\"\r\n                    value={newImportateur.email}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, email: e.target.value})}\r\n                    style={{ padding: \"8px\" }}\r\n                    required\r\n                  />\r\n                  <input\r\n                    placeholder=\"Région *\"\r\n                    value={newImportateur.region}\r\n                    onChange={(e) => setNewImportateur({...newImportateur, region: e.target.value})}\r\n                    style={{ padding: \"8px\", gridColumn: \"1 / -1\" }}\r\n                    required\r\n                  />\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Objet */}\r\n            <div style={{ marginBottom: \"20px\" }}>\r\n              <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                Objet (Produit recherché, spécifications...) *\r\n              </label>\r\n              <textarea\r\n                value={formData.objet}\r\n                onChange={(e) => setFormData({...formData, objet: e.target.value})}\r\n                style={{ width: \"100%\", padding: \"8px\", minHeight: \"100px\" }}\r\n                placeholder=\"Décrivez le produit recherché, les spécifications techniques, quantités, etc.\"\r\n                required\r\n              />\r\n            </div>\r\n\r\n            {/* Pays de destination */}\r\n            <div style={{ marginBottom: \"20px\" }}>\r\n              <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                Pays de destination *\r\n              </label>\r\n              <select\r\n                value={formData.pays}\r\n                onChange={(e) => setFormData({...formData, pays: e.target.value})}\r\n                style={{ width: \"100%\", padding: \"8px\", marginBottom: \"10px\" }}\r\n                required\r\n              >\r\n                <option value=\"\">-- Sélectionner un pays --</option>\r\n                {pays.map(p => (\r\n                  <option key={p} value={p}>{p}</option>\r\n                ))}\r\n              </select>\r\n\r\n              {/* Champ texte pour \"Autre\" pays */}\r\n              {formData.pays === \"Autre\" && (\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Précisez le pays *\"\r\n                  value={formData.pays_autre}\r\n                  onChange={(e) => setFormData({...formData, pays_autre: e.target.value})}\r\n                  style={{ width: \"100%\", padding: \"8px\", border: \"2px solid #007bff\", borderRadius: \"4px\" }}\r\n                  required\r\n                />\r\n              )}\r\n            </div>\r\n\r\n            {/* Dates */}\r\n            <div style={{ display: \"grid\", gridTemplateColumns: \"1fr 1fr\", gap: \"20px\", marginBottom: \"20px\" }}>\r\n              <div>\r\n                <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                  Date début *\r\n                </label>\r\n                <input\r\n                  type=\"date\"\r\n                  value={formData.date_debut}\r\n                  onChange={(e) => setFormData({...formData, date_debut: e.target.value})}\r\n                  style={{ width: \"100%\", padding: \"8px\" }}\r\n                  required\r\n                />\r\n              </div>\r\n              <div>\r\n                <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                  Date fin *\r\n                </label>\r\n                <input\r\n                  type=\"date\"\r\n                  value={formData.date_fin}\r\n                  onChange={(e) => setFormData({...formData, date_fin: e.target.value})}\r\n                  style={{ width: \"100%\", padding: \"8px\" }}\r\n                  required\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Secteurs (sélection multiple) */}\r\n            <div style={{ marginBottom: \"20px\" }}>\r\n              <label style={{ display: \"block\", marginBottom: \"5px\", fontWeight: \"bold\" }}>\r\n                Secteurs d'activité * (sélection multiple)\r\n              </label>\r\n              <div style={{\r\n                border: \"1px solid #ccc\",\r\n                padding: \"10px\",\r\n                borderRadius: \"4px\",\r\n                display: \"grid\",\r\n                gridTemplateColumns: \"repeat(2, 1fr)\",\r\n                gap: \"10px\"\r\n              }}>\r\n                {secteurs.map(secteur => (\r\n                  <label key={secteur} style={{ display: \"flex\", alignItems: \"center\" }}>\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={formData.secteurs.includes(secteur)}\r\n                      onChange={(e) => {\r\n                        if (e.target.checked) {\r\n                          setFormData({\r\n                            ...formData,\r\n                            secteurs: [...formData.secteurs, secteur]\r\n                          });\r\n                        } else {\r\n                          setFormData({\r\n                            ...formData,\r\n                            secteurs: formData.secteurs.filter(s => s !== secteur)\r\n                          });\r\n                        }\r\n                      }}\r\n                      style={{ marginRight: \"8px\" }}\r\n                    />\r\n                    {secteur}\r\n                  </label>\r\n                ))}\r\n              </div>\r\n              {formData.secteurs.length === 0 && (\r\n                <small style={{ color: \"red\" }}>Veuillez sélectionner au moins un secteur</small>\r\n              )}\r\n            </div>\r\n\r\n            {/* Bouton MATCHING */}\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => handleMatching()}\r\n              disabled={!isFormValid()}\r\n              style={{\r\n                backgroundColor: isFormValid() ? \"#28a745\" : \"#6c757d\",\r\n                color: \"white\",\r\n                padding: \"12px 30px\",\r\n                border: \"none\",\r\n                borderRadius: \"5px\",\r\n                fontSize: \"16px\",\r\n                fontWeight: \"bold\",\r\n                cursor: isFormValid() ? \"pointer\" : \"not-allowed\",\r\n                marginRight: \"10px\"\r\n              }}\r\n            >\r\n              🔍 MATCHING\r\n            </button>\r\n          </form>\r\n\r\n          {/* Section Matching des Exportateurs */}\r\n          {showMatching && (\r\n            <div style={{\r\n              border: \"2px solid #28a745\",\r\n              padding: \"20px\",\r\n              borderRadius: \"5px\",\r\n              backgroundColor: \"#f8fff9\",\r\n              marginBottom: \"20px\"\r\n            }}>\r\n              <h3>🎯 Exportateurs correspondants ({matchedExportateurs.length})</h3>\r\n\r\n              {matchedExportateurs.length > 0 ? (\r\n                <div>\r\n                  <table style={{ width: \"100%\", borderCollapse: \"collapse\", marginBottom: \"20px\" }}>\r\n                    <thead>\r\n                      <tr style={{ backgroundColor: \"#e9ecef\" }}>\r\n                        <th style={{ padding: \"10px\", border: \"1px solid #ddd\", textAlign: \"left\" }}>\r\n                          <input\r\n                            type=\"checkbox\"\r\n                            onChange={(e) => {\r\n                              if (e.target.checked) {\r\n                                setSelectedExportateurs(matchedExportateurs.map(exp => exp.id));\r\n                              } else {\r\n                                setSelectedExportateurs([]);\r\n                              }\r\n                            }}\r\n                            style={{ marginRight: \"5px\" }}\r\n                          />\r\n                          Sélectionner\r\n                        </th>\r\n                        <th style={{ padding: \"10px\", border: \"1px solid #ddd\", textAlign: \"left\" }}>Entreprise</th>\r\n                        <th style={{ padding: \"10px\", border: \"1px solid #ddd\", textAlign: \"left\" }}>Contact</th>\r\n                        <th style={{ padding: \"10px\", border: \"1px solid #ddd\", textAlign: \"left\" }}>Secteur</th>\r\n                        <th style={{ padding: \"10px\", border: \"1px solid #ddd\", textAlign: \"left\" }}>Localisation</th>\r\n                      </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                      {matchedExportateurs.map(exp => {\r\n                        console.log(\"Affichage exportateur:\", exp);\r\n\r\n                        // Utiliser la logique flexible pour accéder aux données\r\n                        const raisonSociale = exp?.attributes?.raison_sociale ||\r\n                                             exp?.raison_sociale ||\r\n                                             exp?.data?.attributes?.raison_sociale ||\r\n                                             'Nom non disponible';\r\n\r\n                        const email = exp?.attributes?.email ||\r\n                                     exp?.email ||\r\n                                     exp?.data?.attributes?.email ||\r\n                                     'Email non disponible';\r\n\r\n                        const telephone = exp?.attributes?.telephone_siege ||\r\n                                         exp?.telephone_siege ||\r\n                                         exp?.attributes?.mobile ||\r\n                                         exp?.mobile ||\r\n                                         exp?.data?.attributes?.telephone_siege ||\r\n                                         'Tél. non disponible';\r\n\r\n                        const nomContact = exp?.attributes?.nom_contact ||\r\n                                          exp?.nom_contact ||\r\n                                          exp?.data?.attributes?.nom_contact ||\r\n                                          '';\r\n\r\n                        const prenomContact = exp?.attributes?.prenom_contact ||\r\n                                             exp?.prenom_contact ||\r\n                                             exp?.data?.attributes?.prenom_contact ||\r\n                                             '';\r\n\r\n                        const contact = nomContact && prenomContact ?\r\n                                       `${prenomContact} ${nomContact}` :\r\n                                       nomContact || prenomContact || 'Contact non disponible';\r\n\r\n                        const secteur = exp?.attributes?.secteur_activite ||\r\n                                       exp?.secteur_activite ||\r\n                                       exp?.data?.attributes?.secteur_activite ||\r\n                                       'Secteur non disponible';\r\n\r\n                        const adresse = exp?.attributes?.adresse ||\r\n                                       exp?.adresse ||\r\n                                       exp?.data?.attributes?.adresse ||\r\n                                       '';\r\n\r\n                        // Pour la ville, essayer différentes structures\r\n                        const ville = exp?.attributes?.ville?.data?.attributes?.nom ||\r\n                                     exp?.attributes?.ville?.nom ||\r\n                                     exp?.attributes?.ville ||\r\n                                     exp?.ville?.data?.attributes?.nom ||\r\n                                     exp?.ville?.nom ||\r\n                                     exp?.ville ||\r\n                                     exp?.data?.attributes?.ville ||\r\n                                     '';\r\n\r\n                        const localisation = ville && adresse ?\r\n                                           `${ville} - ${adresse}` :\r\n                                           ville || adresse || 'Localisation non disponible';\r\n\r\n                        return (\r\n                          <tr key={exp.id || exp.documentId}>\r\n                            <td style={{ padding: \"10px\", border: \"1px solid #ddd\" }}>\r\n                              <input\r\n                                type=\"checkbox\"\r\n                                checked={selectedExportateurs.includes(exp.id)}\r\n                                onChange={(e) => {\r\n                                  if (e.target.checked) {\r\n                                    setSelectedExportateurs([...selectedExportateurs, exp.id]);\r\n                                  } else {\r\n                                    setSelectedExportateurs(selectedExportateurs.filter(id => id !== exp.id));\r\n                                  }\r\n                                }}\r\n                              />\r\n                            </td>\r\n                            <td style={{ padding: \"10px\", border: \"1px solid #ddd\" }}>\r\n                              <div>\r\n                                <strong style={{ color: \"#007bff\" }}>{raisonSociale}</strong>\r\n                                <br />\r\n                                <small style={{ color: \"#6c757d\" }}>\r\n                                  {exp?.attributes?.matricule_fiscal || exp?.matricule_fiscal || 'Matricule N/A'}\r\n                                </small>\r\n                              </div>\r\n                            </td>\r\n                            <td style={{ padding: \"10px\", border: \"1px solid #ddd\" }}>\r\n                              <div>\r\n                                <div style={{ fontWeight: \"bold\" }}>{contact}</div>\r\n                                <div style={{ fontSize: \"12px\", color: \"#007bff\" }}>{email}</div>\r\n                                <div style={{ fontSize: \"12px\", color: \"#28a745\" }}>{telephone}</div>\r\n                              </div>\r\n                            </td>\r\n                            <td style={{ padding: \"10px\", border: \"1px solid #ddd\" }}>\r\n                              <span style={{\r\n                                backgroundColor: \"#e9ecef\",\r\n                                padding: \"4px 8px\",\r\n                                borderRadius: \"12px\",\r\n                                fontSize: \"12px\",\r\n                                fontWeight: \"bold\"\r\n                              }}>\r\n                                {secteur}\r\n                              </span>\r\n                            </td>\r\n                            <td style={{ padding: \"10px\", border: \"1px solid #ddd\" }}>\r\n                              <div style={{ fontSize: \"12px\" }}>\r\n                                {localisation}\r\n                              </div>\r\n                            </td>\r\n                          </tr>\r\n                        );\r\n                      })}\r\n                    </tbody>\r\n                  </table>\r\n\r\n                  <button\r\n                    onClick={handleSendOpportunity}\r\n                    disabled={selectedExportateurs.length === 0}\r\n                    style={{\r\n                      backgroundColor: selectedExportateurs.length > 0 ? \"#007bff\" : \"#6c757d\",\r\n                      color: \"white\",\r\n                      padding: \"12px 30px\",\r\n                      border: \"none\",\r\n                      borderRadius: \"5px\",\r\n                      fontSize: \"16px\",\r\n                      fontWeight: \"bold\",\r\n                      cursor: selectedExportateurs.length > 0 ? \"pointer\" : \"not-allowed\"\r\n                    }}\r\n                  >\r\n                    📧 ENVOYER ({selectedExportateurs.length} sélectionné{selectedExportateurs.length > 1 ? 's' : ''})\r\n                  </button>\r\n                </div>\r\n              ) : (\r\n                <p style={{ color: \"#856404\", backgroundColor: \"#fff3cd\", padding: \"10px\", borderRadius: \"4px\" }}>\r\n                  ⚠️ Aucun exportateur trouvé pour les secteurs sélectionnés : {formData.secteurs.join(\", \")}\r\n                </p>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      ) : (\r\n        <div style={{\r\n          backgroundColor: \"#d4edda\",\r\n          border: \"2px solid #28a745\",\r\n          borderRadius: \"10px\",\r\n          padding: \"30px\",\r\n          textAlign: \"center\"\r\n        }}>\r\n          <h2 style={{ color: \"#155724\", marginBottom: \"20px\" }}>\r\n            🎉 Félicitations ! Votre opportunité a bien été soumise.\r\n          </h2>\r\n\r\n          <div style={{\r\n            backgroundColor: \"white\",\r\n            padding: \"20px\",\r\n            borderRadius: \"8px\",\r\n            textAlign: \"left\",\r\n            marginBottom: \"20px\"\r\n          }}>\r\n            <h3 style={{ color: \"#155724\", marginBottom: \"15px\" }}>\r\n              📧 Un email sera envoyé aux exportateurs sélectionnés avec le contenu suivant :\r\n            </h3>\r\n\r\n            <div style={{ backgroundColor: \"#f8f9fa\", padding: \"15px\", borderRadius: \"5px\" }}>\r\n              <p><strong>Objet :</strong> Nouvelle Opportunité d'Importation</p>\r\n              <br />\r\n              <p><strong>Contenu :</strong></p>\r\n              <ul style={{ marginLeft: \"20px\" }}>\r\n                <li><strong>Importateur :</strong> {getImportateurName()}</li>\r\n                <li><strong>Objet :</strong> {formData.objet}</li>\r\n                <li><strong>Pays de destination :</strong> {formData.pays === \"Autre\" ? formData.pays_autre : formData.pays}</li>\r\n                <li><strong>Période :</strong> {formData.date_debut} → {formData.date_fin}</li>\r\n                <li><strong>Secteurs concernés :</strong> {formData.secteurs.join(\", \")}</li>\r\n              </ul>\r\n              <br />\r\n              <p><strong>Exportateurs contactés :</strong></p>\r\n              <ul style={{ marginLeft: \"20px\" }}>\r\n                {selectedExportateurs.map(expId => {\r\n                  const exp = matchedExportateurs.find(e => e.id === expId);\r\n                  return (\r\n                    <li key={expId}>\r\n                      {exp?.attributes?.email} ({exp?.attributes?.raison_sociale})\r\n                    </li>\r\n                  );\r\n                })}\r\n              </ul>\r\n            </div>\r\n          </div>\r\n\r\n          <button\r\n            onClick={() => {\r\n              setShowConfirmation(false);\r\n              setShowMatching(false);\r\n              setSelectedExportateurs([]);\r\n              setMatchedExportateurs([]);\r\n              setFormData({\r\n                importateur: \"\",\r\n                objet: \"\",\r\n                pays: \"\",\r\n                pays_autre: \"\",\r\n                date_debut: \"\",\r\n                date_fin: \"\",\r\n                secteurs: []\r\n              });\r\n              setNewImportateur({\r\n                societe: \"\",\r\n                pays: \"\",\r\n                nom_responsable: \"\",\r\n                prenom_responsable: \"\",\r\n                telephone_whatsapp: \"\",\r\n                email: \"\",\r\n                region: \"\"\r\n              });\r\n              setShowNewImportateurForm(false);\r\n            }}\r\n            style={{\r\n              backgroundColor: \"#007bff\",\r\n              color: \"white\",\r\n              padding: \"12px 30px\",\r\n              border: \"none\",\r\n              borderRadius: \"5px\",\r\n              fontSize: \"16px\",\r\n              fontWeight: \"bold\",\r\n              cursor: \"pointer\"\r\n            }}\r\n          >\r\n            ✨ Nouvelle Opportunité\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default OpportuniteForm;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACU,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACY,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACpE,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgB,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACA,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC;IACvCwB,WAAW,EAAE,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC;IACnDiC,OAAO,EAAE,EAAE;IACXP,IAAI,EAAE,EAAE;IACRQ,eAAe,EAAE,EAAE;IACnBC,kBAAkB,EAAE,EAAE;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMR,QAAQ,GAAG,CACf,kBAAkB,EAClB,SAAS,EACT,KAAK,EACL,SAAS,EACT,WAAW,EACX,QAAQ,CACT;EAED,MAAMJ,IAAI,GAAG,CACX,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAClE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EACnE,OAAO,EAAE,QAAQ,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,OAAO,EACpE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,CACvE;;EAED;EACAzB,SAAS,CAAC,MAAM;IACdsC,OAAO,CAACC,GAAG,CAAC,CACVC,KAAK,CAAC,mDAAmD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,EAC9EH,KAAK,CAAC,mDAAmD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAC/E,CAAC,CACDF,IAAI,CAAC,CAAC,CAACG,gBAAgB,EAAEC,gBAAgB,CAAC,KAAK;MAC9CC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEH,gBAAgB,CAAC;MACnEE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEH,gBAAgB,CAACI,IAAI,IAAIJ,gBAAgB,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC;MAChGF,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEF,gBAAgB,CAAC;MAEnE,IAAID,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEI,IAAI,EAAE1C,eAAe,CAACsC,gBAAgB,CAACI,IAAI,CAAC;MAClE,IAAIH,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEG,IAAI,EAAExC,eAAe,CAACqC,gBAAgB,CAACG,IAAI,CAAC;MAElElC,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,CACDmC,KAAK,CAACC,GAAG,IAAI;MACZJ,OAAO,CAACK,KAAK,CAAC,4BAA4B,EAAED,GAAG,CAAC;MAChDpC,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMsC,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,SAAS,GAAGhC,QAAQ,CAACI,IAAI,KAAKJ,QAAQ,CAACI,IAAI,KAAK,OAAO,IAAIJ,QAAQ,CAACK,UAAU,CAAC;IACrF,OAAOL,QAAQ,CAACE,WAAW,IACpBF,QAAQ,CAACG,KAAK,IACd6B,SAAS,IACThC,QAAQ,CAACM,UAAU,IACnBN,QAAQ,CAACO,QAAQ,IACjBP,QAAQ,CAACQ,QAAQ,CAACyB,MAAM,GAAG,CAAC;EACrC,CAAC;EAED,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACH,WAAW,CAAC,CAAC,EAAE;;IAEpB;IACA,IAAI/B,QAAQ,CAACE,WAAW,KAAK,OAAO,EAAE;MACpC,IAAI;QACF,MAAMiC,QAAQ,GAAG,MAAMhB,KAAK,CAAC,wCAAwC,EAAE;UACrEiB,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YAAE,cAAc,EAAE;UAAmB,CAAC;UAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YAAEb,IAAI,EAAElB;UAAe,CAAC;QAC/C,CAAC,CAAC;QACF,MAAMgC,MAAM,GAAG,MAAMN,QAAQ,CAACb,IAAI,CAAC,CAAC;QACpCrB,WAAW,CAAC;UAAC,GAAGD,QAAQ;UAAEE,WAAW,EAAEuC,MAAM,CAACd,IAAI,CAACe;QAAE,CAAC,CAAC;QACvDzD,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAEyD,MAAM,CAACd,IAAI,CAAC,CAAC;QAC/ChC,yBAAyB,CAAC,KAAK,CAAC;MAClC,CAAC,CAAC,OAAOmC,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACpE;MACF;IACF;;IAEA;IACAL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE1B,QAAQ,CAACQ,QAAQ,CAAC;IACxDiB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAExC,YAAY,CAAC;IAEnD,MAAMyD,OAAO,GAAGzD,YAAY,CAAC0D,MAAM,CAACC,GAAG,IAAI;MAAA,IAAAC,eAAA,EAAAC,SAAA,EAAAC,oBAAA;MACzCvB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEmB,GAAG,CAAC;;MAEzC;MACA,MAAMI,UAAU,GAAG,CAAAJ,GAAG,aAAHA,GAAG,wBAAAC,eAAA,GAAHD,GAAG,CAAEK,UAAU,cAAAJ,eAAA,uBAAfA,eAAA,CAAiBK,gBAAgB,MAClCN,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEM,gBAAgB,MACrBN,GAAG,aAAHA,GAAG,wBAAAE,SAAA,GAAHF,GAAG,CAAElB,IAAI,cAAAoB,SAAA,wBAAAC,oBAAA,GAATD,SAAA,CAAWG,UAAU,cAAAF,oBAAA,uBAArBA,oBAAA,CAAuBG,gBAAgB;MAEzD1B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEuB,UAAU,CAAC;MACpDxB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE1B,QAAQ,CAACQ,QAAQ,CAAC4C,QAAQ,CAACH,UAAU,CAAC,CAAC;MAEtE,OAAOjD,QAAQ,CAACQ,QAAQ,CAAC4C,QAAQ,CAACH,UAAU,CAAC;IAC/C,CAAC,CAAC;IAEFxB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEiB,OAAO,CAAC;IAEpDtD,sBAAsB,CAACsD,OAAO,CAAC;IAC/B9C,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMwD,qBAAqB,GAAGA,CAAA,KAAM;IAClCtD,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMuD,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,eAAA,EAAAC,SAAA,EAAAC,oBAAA;IAC/B,IAAIzD,QAAQ,CAACE,WAAW,KAAK,OAAO,EAAE;MACpC,OAAOO,cAAc,CAACE,OAAO;IAC/B;IACA,MAAM+C,GAAG,GAAG1E,YAAY,CAAC2E,IAAI,CAACC,CAAC,IAAI,CAACA,CAAC,CAAClB,EAAE,IAAIkB,CAAC,CAACC,UAAU,MAAM7D,QAAQ,CAACE,WAAW,CAAC;IACnF;IACA,OAAO,CAAAwD,GAAG,aAAHA,GAAG,wBAAAH,eAAA,GAAHG,GAAG,CAAER,UAAU,cAAAK,eAAA,uBAAfA,eAAA,CAAiB5C,OAAO,MACxB+C,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE/C,OAAO,MACZ+C,GAAG,aAAHA,GAAG,wBAAAF,SAAA,GAAHE,GAAG,CAAE/B,IAAI,cAAA6B,SAAA,wBAAAC,oBAAA,GAATD,SAAA,CAAWN,UAAU,cAAAO,oBAAA,uBAArBA,oBAAA,CAAuB9C,OAAO,KAC9B,qBAAqB;EAC9B,CAAC;EAED,IAAInB,OAAO,EAAE,oBAAOX,OAAA;IAAAiF,QAAA,EAAK;EAAa;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;EAE5C,oBACErF,OAAA;IAAKsF,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE;IAAQ,CAAE;IAAAP,QAAA,gBACjDjF,OAAA;MAAAiF,QAAA,EAAI;IAAuC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAE/C,CAACpE,gBAAgB,gBAChBjB,OAAA;MAAAiF,QAAA,gBAEEjF,OAAA;QAAMsF,KAAK,EAAE;UAAEG,YAAY,EAAE;QAAO,CAAE;QAAAR,QAAA,gBAEpCjF,OAAA;UAAKsF,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACnCjF,OAAA;YAAOsF,KAAK,EAAE;cAAEI,OAAO,EAAE,OAAO;cAAED,YAAY,EAAE,KAAK;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAE7E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrF,OAAA;YACE4F,KAAK,EAAEzE,QAAQ,CAACE,WAAY;YAC5BwE,QAAQ,EAAGC,CAAC,IAAK;cACf1E,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEE,WAAW,EAAEyE,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAC;cACvD9E,yBAAyB,CAACgF,CAAC,CAACC,MAAM,CAACH,KAAK,KAAK,OAAO,CAAC;YACvD,CAAE;YACFN,KAAK,EAAE;cAAEU,KAAK,EAAE,MAAM;cAAET,OAAO,EAAE,KAAK;cAAEE,YAAY,EAAE;YAAO,CAAE;YAC/DQ,QAAQ;YAAAhB,QAAA,gBAERjF,OAAA;cAAQ4F,KAAK,EAAC,EAAE;cAAAX,QAAA,EAAC;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAC1DlF,YAAY,CAAC+F,GAAG,CAACrB,GAAG,IAAI;cAAA,IAAAsB,gBAAA,EAAAC,UAAA,EAAAC,qBAAA;cACvB;cACA,MAAMvE,OAAO,GAAG,CAAA+C,GAAG,aAAHA,GAAG,wBAAAsB,gBAAA,GAAHtB,GAAG,CAAER,UAAU,cAAA8B,gBAAA,uBAAfA,gBAAA,CAAiBrE,OAAO,MACzB+C,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE/C,OAAO,MACZ+C,GAAG,aAAHA,GAAG,wBAAAuB,UAAA,GAAHvB,GAAG,CAAE/B,IAAI,cAAAsD,UAAA,wBAAAC,qBAAA,GAATD,UAAA,CAAW/B,UAAU,cAAAgC,qBAAA,uBAArBA,qBAAA,CAAuBvE,OAAO,KAC9B,oBAAoB;cACnC,oBACE9B,OAAA;gBAAuC4F,KAAK,EAAEf,GAAG,CAAChB,EAAE,IAAIgB,GAAG,CAACG,UAAW;gBAAAC,QAAA,EACpEnD;cAAO,GADG+C,GAAG,CAAChB,EAAE,IAAIgB,GAAG,CAACG,UAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE7B,CAAC;YAEb,CAAC,CAAC,eACFrF,OAAA;cAAQ4F,KAAK,EAAC,OAAO;cAAAX,QAAA,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGLxE,sBAAsB,iBACrBb,OAAA;UAAKsF,KAAK,EAAE;YACVgB,MAAM,EAAE,mBAAmB;YAC3Bf,OAAO,EAAE,MAAM;YACfE,YAAY,EAAE,MAAM;YACpBc,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE;UACnB,CAAE;UAAAvB,QAAA,gBACAjF,OAAA;YAAAiF,QAAA,EAAI;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxCrF,OAAA;YAAKsF,KAAK,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEe,mBAAmB,EAAE,SAAS;cAAEC,GAAG,EAAE;YAAO,CAAE;YAAAzB,QAAA,gBAC3EjF,OAAA;cACE2G,WAAW,EAAC,iBAAW;cACvBf,KAAK,EAAEhE,cAAc,CAACE,OAAQ;cAC9B+D,QAAQ,EAAGC,CAAC,IAAKjE,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEE,OAAO,EAAEgE,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACjFN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFrF,OAAA;cACE2G,WAAW,EAAC,QAAQ;cACpBf,KAAK,EAAEhE,cAAc,CAACL,IAAK;cAC3BsE,QAAQ,EAAGC,CAAC,IAAKjE,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEL,IAAI,EAAEuE,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC9EN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFrF,OAAA;cACE2G,WAAW,EAAC,mBAAmB;cAC/Bf,KAAK,EAAEhE,cAAc,CAACG,eAAgB;cACtC8D,QAAQ,EAAGC,CAAC,IAAKjE,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEG,eAAe,EAAE+D,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACzFN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFrF,OAAA;cACE2G,WAAW,EAAC,yBAAsB;cAClCf,KAAK,EAAEhE,cAAc,CAACI,kBAAmB;cACzC6D,QAAQ,EAAGC,CAAC,IAAKjE,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEI,kBAAkB,EAAE8D,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC5FN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFrF,OAAA;cACE2G,WAAW,EAAC,4BAAsB;cAClCf,KAAK,EAAEhE,cAAc,CAACK,kBAAmB;cACzC4D,QAAQ,EAAGC,CAAC,IAAKjE,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEK,kBAAkB,EAAE6D,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC5FN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFrF,OAAA;cACE2G,WAAW,EAAC,SAAS;cACrBC,IAAI,EAAC,OAAO;cACZhB,KAAK,EAAEhE,cAAc,CAACM,KAAM;cAC5B2D,QAAQ,EAAGC,CAAC,IAAKjE,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEM,KAAK,EAAE4D,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAC/EN,KAAK,EAAE;gBAAEC,OAAO,EAAE;cAAM,CAAE;cAC1BU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACFrF,OAAA;cACE2G,WAAW,EAAC,aAAU;cACtBf,KAAK,EAAEhE,cAAc,CAACO,MAAO;cAC7B0D,QAAQ,EAAGC,CAAC,IAAKjE,iBAAiB,CAAC;gBAAC,GAAGD,cAAc;gBAAEO,MAAM,EAAE2D,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cAChFN,KAAK,EAAE;gBAAEC,OAAO,EAAE,KAAK;gBAAEsB,UAAU,EAAE;cAAS,CAAE;cAChDZ,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDrF,OAAA;UAAKsF,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACnCjF,OAAA;YAAOsF,KAAK,EAAE;cAAEI,OAAO,EAAE,OAAO;cAAED,YAAY,EAAE,KAAK;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAE7E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrF,OAAA;YACE4F,KAAK,EAAEzE,QAAQ,CAACG,KAAM;YACtBuE,QAAQ,EAAGC,CAAC,IAAK1E,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEG,KAAK,EAAEwE,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACnEN,KAAK,EAAE;cAAEU,KAAK,EAAE,MAAM;cAAET,OAAO,EAAE,KAAK;cAAEuB,SAAS,EAAE;YAAQ,CAAE;YAC7DH,WAAW,EAAC,2FAA+E;YAC3FV,QAAQ;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNrF,OAAA;UAAKsF,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACnCjF,OAAA;YAAOsF,KAAK,EAAE;cAAEI,OAAO,EAAE,OAAO;cAAED,YAAY,EAAE,KAAK;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAE7E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrF,OAAA;YACE4F,KAAK,EAAEzE,QAAQ,CAACI,IAAK;YACrBsE,QAAQ,EAAGC,CAAC,IAAK1E,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEI,IAAI,EAAEuE,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YAClEN,KAAK,EAAE;cAAEU,KAAK,EAAE,MAAM;cAAET,OAAO,EAAE,KAAK;cAAEE,YAAY,EAAE;YAAO,CAAE;YAC/DQ,QAAQ;YAAAhB,QAAA,gBAERjF,OAAA;cAAQ4F,KAAK,EAAC,EAAE;cAAAX,QAAA,EAAC;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACnD9D,IAAI,CAAC2E,GAAG,CAACa,CAAC,iBACT/G,OAAA;cAAgB4F,KAAK,EAAEmB,CAAE;cAAA9B,QAAA,EAAE8B;YAAC,GAAfA,CAAC;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuB,CACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,EAGRlE,QAAQ,CAACI,IAAI,KAAK,OAAO,iBACxBvB,OAAA;YACE4G,IAAI,EAAC,MAAM;YACXD,WAAW,EAAC,uBAAoB;YAChCf,KAAK,EAAEzE,QAAQ,CAACK,UAAW;YAC3BqE,QAAQ,EAAGC,CAAC,IAAK1E,WAAW,CAAC;cAAC,GAAGD,QAAQ;cAAEK,UAAU,EAAEsE,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACxEN,KAAK,EAAE;cAAEU,KAAK,EAAE,MAAM;cAAET,OAAO,EAAE,KAAK;cAAEe,MAAM,EAAE,mBAAmB;cAAEC,YAAY,EAAE;YAAM,CAAE;YAC3FN,QAAQ;UAAA;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNrF,OAAA;UAAKsF,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEe,mBAAmB,EAAE,SAAS;YAAEC,GAAG,EAAE,MAAM;YAAEjB,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACjGjF,OAAA;YAAAiF,QAAA,gBACEjF,OAAA;cAAOsF,KAAK,EAAE;gBAAEI,OAAO,EAAE,OAAO;gBAAED,YAAY,EAAE,KAAK;gBAAEE,UAAU,EAAE;cAAO,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrF,OAAA;cACE4G,IAAI,EAAC,MAAM;cACXhB,KAAK,EAAEzE,QAAQ,CAACM,UAAW;cAC3BoE,QAAQ,EAAGC,CAAC,IAAK1E,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEM,UAAU,EAAEqE,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACxEN,KAAK,EAAE;gBAAEU,KAAK,EAAE,MAAM;gBAAET,OAAO,EAAE;cAAM,CAAE;cACzCU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrF,OAAA;YAAAiF,QAAA,gBACEjF,OAAA;cAAOsF,KAAK,EAAE;gBAAEI,OAAO,EAAE,OAAO;gBAAED,YAAY,EAAE,KAAK;gBAAEE,UAAU,EAAE;cAAO,CAAE;cAAAV,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRrF,OAAA;cACE4G,IAAI,EAAC,MAAM;cACXhB,KAAK,EAAEzE,QAAQ,CAACO,QAAS;cACzBmE,QAAQ,EAAGC,CAAC,IAAK1E,WAAW,CAAC;gBAAC,GAAGD,QAAQ;gBAAEO,QAAQ,EAAEoE,CAAC,CAACC,MAAM,CAACH;cAAK,CAAC,CAAE;cACtEN,KAAK,EAAE;gBAAEU,KAAK,EAAE,MAAM;gBAAET,OAAO,EAAE;cAAM,CAAE;cACzCU,QAAQ;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrF,OAAA;UAAKsF,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,gBACnCjF,OAAA;YAAOsF,KAAK,EAAE;cAAEI,OAAO,EAAE,OAAO;cAAED,YAAY,EAAE,KAAK;cAAEE,UAAU,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAC;UAE7E;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRrF,OAAA;YAAKsF,KAAK,EAAE;cACVgB,MAAM,EAAE,gBAAgB;cACxBf,OAAO,EAAE,MAAM;cACfgB,YAAY,EAAE,KAAK;cACnBb,OAAO,EAAE,MAAM;cACfe,mBAAmB,EAAE,gBAAgB;cACrCC,GAAG,EAAE;YACP,CAAE;YAAAzB,QAAA,EACCtD,QAAQ,CAACuE,GAAG,CAACc,OAAO,iBACnBhH,OAAA;cAAqBsF,KAAK,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEuB,UAAU,EAAE;cAAS,CAAE;cAAAhC,QAAA,gBACpEjF,OAAA;gBACE4G,IAAI,EAAC,UAAU;gBACfM,OAAO,EAAE/F,QAAQ,CAACQ,QAAQ,CAAC4C,QAAQ,CAACyC,OAAO,CAAE;gBAC7CnB,QAAQ,EAAGC,CAAC,IAAK;kBACf,IAAIA,CAAC,CAACC,MAAM,CAACmB,OAAO,EAAE;oBACpB9F,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXQ,QAAQ,EAAE,CAAC,GAAGR,QAAQ,CAACQ,QAAQ,EAAEqF,OAAO;oBAC1C,CAAC,CAAC;kBACJ,CAAC,MAAM;oBACL5F,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXQ,QAAQ,EAAER,QAAQ,CAACQ,QAAQ,CAACoC,MAAM,CAACoD,CAAC,IAAIA,CAAC,KAAKH,OAAO;oBACvD,CAAC,CAAC;kBACJ;gBACF,CAAE;gBACF1B,KAAK,EAAE;kBAAE8B,WAAW,EAAE;gBAAM;cAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,EACD2B,OAAO;YAAA,GAnBEA,OAAO;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBZ,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACLlE,QAAQ,CAACQ,QAAQ,CAACyB,MAAM,KAAK,CAAC,iBAC7BpD,OAAA;YAAOsF,KAAK,EAAE;cAAE+B,KAAK,EAAE;YAAM,CAAE;YAAApC,QAAA,EAAC;UAAyC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACjF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNrF,OAAA;UACE4G,IAAI,EAAC,QAAQ;UACbU,OAAO,EAAEA,CAAA,KAAMjE,cAAc,CAAC,CAAE;UAChCkE,QAAQ,EAAE,CAACrE,WAAW,CAAC,CAAE;UACzBoC,KAAK,EAAE;YACLkB,eAAe,EAAEtD,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS;YACtDmE,KAAK,EAAE,OAAO;YACd9B,OAAO,EAAE,WAAW;YACpBe,MAAM,EAAE,MAAM;YACdC,YAAY,EAAE,KAAK;YACnBiB,QAAQ,EAAE,MAAM;YAChB7B,UAAU,EAAE,MAAM;YAClB8B,MAAM,EAAEvE,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,aAAa;YACjDkE,WAAW,EAAE;UACf,CAAE;UAAAnC,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGNtE,YAAY,iBACXf,OAAA;QAAKsF,KAAK,EAAE;UACVgB,MAAM,EAAE,mBAAmB;UAC3Bf,OAAO,EAAE,MAAM;UACfgB,YAAY,EAAE,KAAK;UACnBC,eAAe,EAAE,SAAS;UAC1Bf,YAAY,EAAE;QAChB,CAAE;QAAAR,QAAA,gBACAjF,OAAA;UAAAiF,QAAA,GAAI,4CAAgC,EAAC1E,mBAAmB,CAAC6C,MAAM,EAAC,GAAC;QAAA;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAErE9E,mBAAmB,CAAC6C,MAAM,GAAG,CAAC,gBAC7BpD,OAAA;UAAAiF,QAAA,gBACEjF,OAAA;YAAOsF,KAAK,EAAE;cAAEU,KAAK,EAAE,MAAM;cAAE0B,cAAc,EAAE,UAAU;cAAEjC,YAAY,EAAE;YAAO,CAAE;YAAAR,QAAA,gBAChFjF,OAAA;cAAAiF,QAAA,eACEjF,OAAA;gBAAIsF,KAAK,EAAE;kBAAEkB,eAAe,EAAE;gBAAU,CAAE;gBAAAvB,QAAA,gBACxCjF,OAAA;kBAAIsF,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEe,MAAM,EAAE,gBAAgB;oBAAEqB,SAAS,EAAE;kBAAO,CAAE;kBAAA1C,QAAA,gBAC1EjF,OAAA;oBACE4G,IAAI,EAAC,UAAU;oBACff,QAAQ,EAAGC,CAAC,IAAK;sBACf,IAAIA,CAAC,CAACC,MAAM,CAACmB,OAAO,EAAE;wBACpBxG,uBAAuB,CAACH,mBAAmB,CAAC2F,GAAG,CAAClC,GAAG,IAAIA,GAAG,CAACH,EAAE,CAAC,CAAC;sBACjE,CAAC,MAAM;wBACLnD,uBAAuB,CAAC,EAAE,CAAC;sBAC7B;oBACF,CAAE;oBACF4E,KAAK,EAAE;sBAAE8B,WAAW,EAAE;oBAAM;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,mBAEJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLrF,OAAA;kBAAIsF,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEe,MAAM,EAAE,gBAAgB;oBAAEqB,SAAS,EAAE;kBAAO,CAAE;kBAAA1C,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5FrF,OAAA;kBAAIsF,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEe,MAAM,EAAE,gBAAgB;oBAAEqB,SAAS,EAAE;kBAAO,CAAE;kBAAA1C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzFrF,OAAA;kBAAIsF,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEe,MAAM,EAAE,gBAAgB;oBAAEqB,SAAS,EAAE;kBAAO,CAAE;kBAAA1C,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzFrF,OAAA;kBAAIsF,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEe,MAAM,EAAE,gBAAgB;oBAAEqB,SAAS,EAAE;kBAAO,CAAE;kBAAA1C,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5F;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRrF,OAAA;cAAAiF,QAAA,EACG1E,mBAAmB,CAAC2F,GAAG,CAAClC,GAAG,IAAI;gBAAA,IAAA4D,gBAAA,EAAAC,UAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,UAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,UAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,UAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,UAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,UAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,UAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,UAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,UAAA,EAAAC,qBAAA,EAAAC,iBAAA;gBAC9BnH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEmB,GAAG,CAAC;;gBAE1C;gBACA,MAAMgG,aAAa,GAAG,CAAAhG,GAAG,aAAHA,GAAG,wBAAA4D,gBAAA,GAAH5D,GAAG,CAAEK,UAAU,cAAAuD,gBAAA,uBAAfA,gBAAA,CAAiBqC,cAAc,MAChCjG,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEiG,cAAc,MACnBjG,GAAG,aAAHA,GAAG,wBAAA6D,UAAA,GAAH7D,GAAG,CAAElB,IAAI,cAAA+E,UAAA,wBAAAC,qBAAA,GAATD,UAAA,CAAWxD,UAAU,cAAAyD,qBAAA,uBAArBA,qBAAA,CAAuBmC,cAAc,KACrC,oBAAoB;gBAEzC,MAAM/H,KAAK,GAAG,CAAA8B,GAAG,aAAHA,GAAG,wBAAA+D,gBAAA,GAAH/D,GAAG,CAAEK,UAAU,cAAA0D,gBAAA,uBAAfA,gBAAA,CAAiB7F,KAAK,MACvB8B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE9B,KAAK,MACV8B,GAAG,aAAHA,GAAG,wBAAAgE,UAAA,GAAHhE,GAAG,CAAElB,IAAI,cAAAkF,UAAA,wBAAAC,qBAAA,GAATD,UAAA,CAAW3D,UAAU,cAAA4D,qBAAA,uBAArBA,qBAAA,CAAuB/F,KAAK,KAC5B,sBAAsB;gBAEnC,MAAMgI,SAAS,GAAG,CAAAlG,GAAG,aAAHA,GAAG,wBAAAkE,gBAAA,GAAHlE,GAAG,CAAEK,UAAU,cAAA6D,gBAAA,uBAAfA,gBAAA,CAAiBiC,eAAe,MACjCnG,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEmG,eAAe,MACpBnG,GAAG,aAAHA,GAAG,wBAAAmE,gBAAA,GAAHnE,GAAG,CAAEK,UAAU,cAAA8D,gBAAA,uBAAfA,gBAAA,CAAiBiC,MAAM,MACvBpG,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEoG,MAAM,MACXpG,GAAG,aAAHA,GAAG,wBAAAoE,UAAA,GAAHpE,GAAG,CAAElB,IAAI,cAAAsF,UAAA,wBAAAC,qBAAA,GAATD,UAAA,CAAW/D,UAAU,cAAAgE,qBAAA,uBAArBA,qBAAA,CAAuB8B,eAAe,KACtC,qBAAqB;gBAEtC,MAAME,UAAU,GAAG,CAAArG,GAAG,aAAHA,GAAG,wBAAAsE,gBAAA,GAAHtE,GAAG,CAAEK,UAAU,cAAAiE,gBAAA,uBAAfA,gBAAA,CAAiBgC,WAAW,MAC7BtG,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEsG,WAAW,MAChBtG,GAAG,aAAHA,GAAG,wBAAAuE,UAAA,GAAHvE,GAAG,CAAElB,IAAI,cAAAyF,UAAA,wBAAAC,qBAAA,GAATD,UAAA,CAAWlE,UAAU,cAAAmE,qBAAA,uBAArBA,qBAAA,CAAuB8B,WAAW,KAClC,EAAE;gBAEpB,MAAMC,aAAa,GAAG,CAAAvG,GAAG,aAAHA,GAAG,wBAAAyE,gBAAA,GAAHzE,GAAG,CAAEK,UAAU,cAAAoE,gBAAA,uBAAfA,gBAAA,CAAiB+B,cAAc,MAChCxG,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEwG,cAAc,MACnBxG,GAAG,aAAHA,GAAG,wBAAA0E,UAAA,GAAH1E,GAAG,CAAElB,IAAI,cAAA4F,UAAA,wBAAAC,qBAAA,GAATD,UAAA,CAAWrE,UAAU,cAAAsE,qBAAA,uBAArBA,qBAAA,CAAuB6B,cAAc,KACrC,EAAE;gBAEvB,MAAMC,OAAO,GAAGJ,UAAU,IAAIE,aAAa,GAC5B,GAAGA,aAAa,IAAIF,UAAU,EAAE,GAChCA,UAAU,IAAIE,aAAa,IAAI,wBAAwB;gBAEtE,MAAMvD,OAAO,GAAG,CAAAhD,GAAG,aAAHA,GAAG,wBAAA4E,gBAAA,GAAH5E,GAAG,CAAEK,UAAU,cAAAuE,gBAAA,uBAAfA,gBAAA,CAAiBtE,gBAAgB,MAClCN,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEM,gBAAgB,MACrBN,GAAG,aAAHA,GAAG,wBAAA6E,UAAA,GAAH7E,GAAG,CAAElB,IAAI,cAAA+F,UAAA,wBAAAC,qBAAA,GAATD,UAAA,CAAWxE,UAAU,cAAAyE,qBAAA,uBAArBA,qBAAA,CAAuBxE,gBAAgB,KACvC,wBAAwB;gBAEvC,MAAMoG,OAAO,GAAG,CAAA1G,GAAG,aAAHA,GAAG,wBAAA+E,gBAAA,GAAH/E,GAAG,CAAEK,UAAU,cAAA0E,gBAAA,uBAAfA,gBAAA,CAAiB2B,OAAO,MACzB1G,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE0G,OAAO,MACZ1G,GAAG,aAAHA,GAAG,wBAAAgF,UAAA,GAAHhF,GAAG,CAAElB,IAAI,cAAAkG,UAAA,wBAAAC,qBAAA,GAATD,UAAA,CAAW3E,UAAU,cAAA4E,qBAAA,uBAArBA,qBAAA,CAAuByB,OAAO,KAC9B,EAAE;;gBAEjB;gBACA,MAAMC,KAAK,GAAG,CAAA3G,GAAG,aAAHA,GAAG,wBAAAkF,gBAAA,GAAHlF,GAAG,CAAEK,UAAU,cAAA6E,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiByB,KAAK,cAAAxB,qBAAA,wBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBrG,IAAI,cAAAsG,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8B/E,UAAU,cAAAgF,sBAAA,uBAAxCA,sBAAA,CAA0CuB,GAAG,MAC9C5G,GAAG,aAAHA,GAAG,wBAAAsF,gBAAA,GAAHtF,GAAG,CAAEK,UAAU,cAAAiF,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAiBqB,KAAK,cAAApB,qBAAA,uBAAtBA,qBAAA,CAAwBqB,GAAG,MAC3B5G,GAAG,aAAHA,GAAG,wBAAAwF,iBAAA,GAAHxF,GAAG,CAAEK,UAAU,cAAAmF,iBAAA,uBAAfA,iBAAA,CAAiBmB,KAAK,MACtB3G,GAAG,aAAHA,GAAG,wBAAAyF,UAAA,GAAHzF,GAAG,CAAE2G,KAAK,cAAAlB,UAAA,wBAAAC,eAAA,GAAVD,UAAA,CAAY3G,IAAI,cAAA4G,eAAA,wBAAAC,qBAAA,GAAhBD,eAAA,CAAkBrF,UAAU,cAAAsF,qBAAA,uBAA5BA,qBAAA,CAA8BiB,GAAG,MACjC5G,GAAG,aAAHA,GAAG,wBAAA4F,WAAA,GAAH5F,GAAG,CAAE2G,KAAK,cAAAf,WAAA,uBAAVA,WAAA,CAAYgB,GAAG,MACf5G,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE2G,KAAK,MACV3G,GAAG,aAAHA,GAAG,wBAAA6F,UAAA,GAAH7F,GAAG,CAAElB,IAAI,cAAA+G,UAAA,wBAAAC,qBAAA,GAATD,UAAA,CAAWxF,UAAU,cAAAyF,qBAAA,uBAArBA,qBAAA,CAAuBa,KAAK,KAC5B,EAAE;gBAEf,MAAME,YAAY,GAAGF,KAAK,IAAID,OAAO,GAClB,GAAGC,KAAK,MAAMD,OAAO,EAAE,GACvBC,KAAK,IAAID,OAAO,IAAI,6BAA6B;gBAEpE,oBACE1K,OAAA;kBAAAiF,QAAA,gBACEjF,OAAA;oBAAIsF,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEe,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvDjF,OAAA;sBACE4G,IAAI,EAAC,UAAU;sBACfM,OAAO,EAAEzG,oBAAoB,CAAC8D,QAAQ,CAACP,GAAG,CAACH,EAAE,CAAE;sBAC/CgC,QAAQ,EAAGC,CAAC,IAAK;wBACf,IAAIA,CAAC,CAACC,MAAM,CAACmB,OAAO,EAAE;0BACpBxG,uBAAuB,CAAC,CAAC,GAAGD,oBAAoB,EAAEuD,GAAG,CAACH,EAAE,CAAC,CAAC;wBAC5D,CAAC,MAAM;0BACLnD,uBAAuB,CAACD,oBAAoB,CAACsD,MAAM,CAACF,EAAE,IAAIA,EAAE,KAAKG,GAAG,CAACH,EAAE,CAAC,CAAC;wBAC3E;sBACF;oBAAE;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC,eACLrF,OAAA;oBAAIsF,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEe,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvDjF,OAAA;sBAAAiF,QAAA,gBACEjF,OAAA;wBAAQsF,KAAK,EAAE;0BAAE+B,KAAK,EAAE;wBAAU,CAAE;wBAAApC,QAAA,EAAE+E;sBAAa;wBAAA9E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAS,CAAC,eAC7DrF,OAAA;wBAAAkF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACNrF,OAAA;wBAAOsF,KAAK,EAAE;0BAAE+B,KAAK,EAAE;wBAAU,CAAE;wBAAApC,QAAA,EAChC,CAAAjB,GAAG,aAAHA,GAAG,wBAAA+F,iBAAA,GAAH/F,GAAG,CAAEK,UAAU,cAAA0F,iBAAA,uBAAfA,iBAAA,CAAiBe,gBAAgB,MAAI9G,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE8G,gBAAgB,KAAI;sBAAe;wBAAA5F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLrF,OAAA;oBAAIsF,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEe,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvDjF,OAAA;sBAAAiF,QAAA,gBACEjF,OAAA;wBAAKsF,KAAK,EAAE;0BAAEK,UAAU,EAAE;wBAAO,CAAE;wBAAAV,QAAA,EAAEwF;sBAAO;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACnDrF,OAAA;wBAAKsF,KAAK,EAAE;0BAAEkC,QAAQ,EAAE,MAAM;0BAAEH,KAAK,EAAE;wBAAU,CAAE;wBAAApC,QAAA,EAAE/C;sBAAK;wBAAAgD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjErF,OAAA;wBAAKsF,KAAK,EAAE;0BAAEkC,QAAQ,EAAE,MAAM;0BAAEH,KAAK,EAAE;wBAAU,CAAE;wBAAApC,QAAA,EAAEiF;sBAAS;wBAAAhF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLrF,OAAA;oBAAIsF,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEe,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvDjF,OAAA;sBAAMsF,KAAK,EAAE;wBACXkB,eAAe,EAAE,SAAS;wBAC1BjB,OAAO,EAAE,SAAS;wBAClBgB,YAAY,EAAE,MAAM;wBACpBiB,QAAQ,EAAE,MAAM;wBAChB7B,UAAU,EAAE;sBACd,CAAE;sBAAAV,QAAA,EACC+B;oBAAO;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLrF,OAAA;oBAAIsF,KAAK,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEe,MAAM,EAAE;oBAAiB,CAAE;oBAAArB,QAAA,eACvDjF,OAAA;sBAAKsF,KAAK,EAAE;wBAAEkC,QAAQ,EAAE;sBAAO,CAAE;sBAAAvC,QAAA,EAC9B4F;oBAAY;sBAAA3F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GA7CErB,GAAG,CAACH,EAAE,IAAIG,GAAG,CAACgB,UAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8C7B,CAAC;cAET,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAERrF,OAAA;YACEsH,OAAO,EAAE9C,qBAAsB;YAC/B+C,QAAQ,EAAE9G,oBAAoB,CAAC2C,MAAM,KAAK,CAAE;YAC5CkC,KAAK,EAAE;cACLkB,eAAe,EAAE/F,oBAAoB,CAAC2C,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,SAAS;cACxEiE,KAAK,EAAE,OAAO;cACd9B,OAAO,EAAE,WAAW;cACpBe,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBiB,QAAQ,EAAE,MAAM;cAChB7B,UAAU,EAAE,MAAM;cAClB8B,MAAM,EAAEhH,oBAAoB,CAAC2C,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG;YACxD,CAAE;YAAA6B,QAAA,GACH,wBACa,EAACxE,oBAAoB,CAAC2C,MAAM,EAAC,oBAAY,EAAC3C,oBAAoB,CAAC2C,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,GACnG;UAAA;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENrF,OAAA;UAAGsF,KAAK,EAAE;YAAE+B,KAAK,EAAE,SAAS;YAAEb,eAAe,EAAE,SAAS;YAAEjB,OAAO,EAAE,MAAM;YAAEgB,YAAY,EAAE;UAAM,CAAE;UAAAtB,QAAA,GAAC,kFACnC,EAAC9D,QAAQ,CAACQ,QAAQ,CAACoJ,IAAI,CAAC,IAAI,CAAC;QAAA;UAAA7F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CACJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,gBAENrF,OAAA;MAAKsF,KAAK,EAAE;QACVkB,eAAe,EAAE,SAAS;QAC1BF,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBhB,OAAO,EAAE,MAAM;QACfoC,SAAS,EAAE;MACb,CAAE;MAAA1C,QAAA,gBACAjF,OAAA;QAAIsF,KAAK,EAAE;UAAE+B,KAAK,EAAE,SAAS;UAAE5B,YAAY,EAAE;QAAO,CAAE;QAAAR,QAAA,EAAC;MAEvD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELrF,OAAA;QAAKsF,KAAK,EAAE;UACVkB,eAAe,EAAE,OAAO;UACxBjB,OAAO,EAAE,MAAM;UACfgB,YAAY,EAAE,KAAK;UACnBoB,SAAS,EAAE,MAAM;UACjBlC,YAAY,EAAE;QAChB,CAAE;QAAAR,QAAA,gBACAjF,OAAA;UAAIsF,KAAK,EAAE;YAAE+B,KAAK,EAAE,SAAS;YAAE5B,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELrF,OAAA;UAAKsF,KAAK,EAAE;YAAEkB,eAAe,EAAE,SAAS;YAAEjB,OAAO,EAAE,MAAM;YAAEgB,YAAY,EAAE;UAAM,CAAE;UAAAtB,QAAA,gBAC/EjF,OAAA;YAAAiF,QAAA,gBAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,0CAAmC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClErF,OAAA;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNrF,OAAA;YAAAiF,QAAA,eAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjCrF,OAAA;YAAIsF,KAAK,EAAE;cAAE0F,UAAU,EAAE;YAAO,CAAE;YAAA/F,QAAA,gBAChCjF,OAAA;cAAAiF,QAAA,gBAAIjF,OAAA;gBAAAiF,QAAA,EAAQ;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACZ,kBAAkB,CAAC,CAAC;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9DrF,OAAA;cAAAiF,QAAA,gBAAIjF,OAAA;gBAAAiF,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAClE,QAAQ,CAACG,KAAK;YAAA;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClDrF,OAAA;cAAAiF,QAAA,gBAAIjF,OAAA;gBAAAiF,QAAA,EAAQ;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAClE,QAAQ,CAACI,IAAI,KAAK,OAAO,GAAGJ,QAAQ,CAACK,UAAU,GAAGL,QAAQ,CAACI,IAAI;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjHrF,OAAA;cAAAiF,QAAA,gBAAIjF,OAAA;gBAAAiF,QAAA,EAAQ;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAClE,QAAQ,CAACM,UAAU,EAAC,UAAG,EAACN,QAAQ,CAACO,QAAQ;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/ErF,OAAA;cAAAiF,QAAA,gBAAIjF,OAAA;gBAAAiF,QAAA,EAAQ;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAClE,QAAQ,CAACQ,QAAQ,CAACoJ,IAAI,CAAC,IAAI,CAAC;YAAA;cAAA7F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACLrF,OAAA;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNrF,OAAA;YAAAiF,QAAA,eAAGjF,OAAA;cAAAiF,QAAA,EAAQ;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChDrF,OAAA;YAAIsF,KAAK,EAAE;cAAE0F,UAAU,EAAE;YAAO,CAAE;YAAA/F,QAAA,EAC/BxE,oBAAoB,CAACyF,GAAG,CAAC+E,KAAK,IAAI;cAAA,IAAAC,iBAAA,EAAAC,iBAAA;cACjC,MAAMnH,GAAG,GAAGzD,mBAAmB,CAACuE,IAAI,CAACgB,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAKoH,KAAK,CAAC;cACzD,oBACEjL,OAAA;gBAAAiF,QAAA,GACGjB,GAAG,aAAHA,GAAG,wBAAAkH,iBAAA,GAAHlH,GAAG,CAAEK,UAAU,cAAA6G,iBAAA,uBAAfA,iBAAA,CAAiBhJ,KAAK,EAAC,IAAE,EAAC8B,GAAG,aAAHA,GAAG,wBAAAmH,iBAAA,GAAHnH,GAAG,CAAEK,UAAU,cAAA8G,iBAAA,uBAAfA,iBAAA,CAAiBlB,cAAc,EAAC,GAC7D;cAAA,GAFSgB,KAAK;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CAAC;YAET,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrF,OAAA;QACEsH,OAAO,EAAEA,CAAA,KAAM;UACbpG,mBAAmB,CAAC,KAAK,CAAC;UAC1BF,eAAe,CAAC,KAAK,CAAC;UACtBN,uBAAuB,CAAC,EAAE,CAAC;UAC3BF,sBAAsB,CAAC,EAAE,CAAC;UAC1BY,WAAW,CAAC;YACVC,WAAW,EAAE,EAAE;YACfC,KAAK,EAAE,EAAE;YACTC,IAAI,EAAE,EAAE;YACRC,UAAU,EAAE,EAAE;YACdC,UAAU,EAAE,EAAE;YACdC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE;UACZ,CAAC,CAAC;UACFE,iBAAiB,CAAC;YAChBC,OAAO,EAAE,EAAE;YACXP,IAAI,EAAE,EAAE;YACRQ,eAAe,EAAE,EAAE;YACnBC,kBAAkB,EAAE,EAAE;YACtBC,kBAAkB,EAAE,EAAE;YACtBC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE;UACV,CAAC,CAAC;UACFrB,yBAAyB,CAAC,KAAK,CAAC;QAClC,CAAE;QACFwE,KAAK,EAAE;UACLkB,eAAe,EAAE,SAAS;UAC1Ba,KAAK,EAAE,OAAO;UACd9B,OAAO,EAAE,WAAW;UACpBe,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBiB,QAAQ,EAAE,MAAM;UAChB7B,UAAU,EAAE,MAAM;UAClB8B,MAAM,EAAE;QACV,CAAE;QAAAxC,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACnF,EAAA,CA3oBQD,eAAe;AAAAmL,EAAA,GAAfnL,eAAe;AA6oBxB,eAAeA,eAAe;AAAC,IAAAmL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}