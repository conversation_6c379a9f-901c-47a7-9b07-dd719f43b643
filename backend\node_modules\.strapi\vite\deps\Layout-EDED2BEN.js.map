{"version": 3, "sources": ["../../../@strapi/admin/admin/src/hooks/useSettingsMenu.ts", "../../../@strapi/admin/admin/src/pages/Settings/components/SettingsNav.tsx", "../../../@strapi/admin/admin/src/pages/Settings/Layout.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport sortBy from 'lodash/sortBy';\nimport { useSelector } from 'react-redux';\n\nimport { SETTINGS_LINKS_CE, SettingsMenuLink } from '../constants';\nimport { useAppInfo } from '../features/AppInfo';\nimport { useAuth } from '../features/Auth';\nimport { useStrapiApp } from '../features/StrapiApp';\nimport { selectAdminPermissions } from '../selectors';\nimport { PermissionMap } from '../types/permissions';\n\nimport { useEnterprise } from './useEnterprise';\n\nimport type {\n  StrapiAppSetting,\n  StrapiAppSettingLink as IStrapiAppSettingLink,\n} from '../core/apis/router';\n\nconst formatLinks = (menu: SettingsMenuSection[]): SettingsMenuSectionWithDisplayedLinks[] =>\n  menu.map((menuSection) => {\n    const formattedLinks = menuSection.links.map((link) => ({\n      ...link,\n      isDisplayed: false,\n    }));\n\n    return { ...menuSection, links: formattedLinks };\n  });\n\ninterface SettingsMenuLinkWithPermissions extends SettingsMenuLink {\n  permissions: IStrapiAppSettingLink['permissions'];\n  hasNotification?: boolean;\n}\n\ninterface StrapiAppSettingsLink extends IStrapiAppSettingLink {\n  licenseOnly?: never;\n  hasNotification?: never;\n}\n\ninterface SettingsMenuSection extends Omit<StrapiAppSetting, 'links'> {\n  links: Array<SettingsMenuLinkWithPermissions | StrapiAppSettingsLink>;\n}\n\ninterface SettingsMenuLinkWithPermissionsAndDisplayed extends SettingsMenuLinkWithPermissions {\n  isDisplayed: boolean;\n}\n\ninterface StrapiAppSettingLinkWithDisplayed extends StrapiAppSettingsLink {\n  isDisplayed: boolean;\n}\n\ninterface SettingsMenuSectionWithDisplayedLinks extends Omit<SettingsMenuSection, 'links'> {\n  links: Array<SettingsMenuLinkWithPermissionsAndDisplayed | StrapiAppSettingLinkWithDisplayed>;\n}\n\ntype SettingsMenu = SettingsMenuSectionWithDisplayedLinks[];\n\nconst useSettingsMenu = (): {\n  isLoading: boolean;\n  menu: SettingsMenu;\n} => {\n  const [{ isLoading, menu }, setData] = React.useState<{\n    isLoading: boolean;\n    menu: SettingsMenu;\n  }>({\n    isLoading: true,\n    menu: [],\n  });\n  const checkUserHasPermission = useAuth(\n    'useSettingsMenu',\n    (state) => state.checkUserHasPermissions\n  );\n  const shouldUpdateStrapi = useAppInfo('useSettingsMenu', (state) => state.shouldUpdateStrapi);\n  const settings = useStrapiApp('useSettingsMenu', (state) => state.settings);\n  const permissions = useSelector(selectAdminPermissions);\n\n  /**\n   * memoize the return value of this function to avoid re-computing it on every render\n   * because it's used in an effect it ends up re-running recursively.\n   */\n  const ceLinks = React.useMemo(() => SETTINGS_LINKS_CE(), []);\n\n  const { admin: adminLinks, global: globalLinks } = useEnterprise(\n    ceLinks,\n    async () => (await import('../../../ee/admin/src/constants')).SETTINGS_LINKS_EE(),\n    {\n      combine(ceLinks, eeLinks) {\n        return {\n          admin: [...eeLinks.admin, ...ceLinks.admin],\n          global: [...ceLinks.global, ...eeLinks.global],\n        };\n      },\n      defaultValue: {\n        admin: [],\n        global: [],\n      },\n    }\n  );\n\n  const addPermissions = React.useCallback(\n    (link: SettingsMenuLink) => {\n      if (!link.id) {\n        throw new Error('The settings menu item must have an id attribute.');\n      }\n\n      return {\n        ...link,\n        permissions: permissions.settings?.[link.id as keyof PermissionMap['settings']]?.main ?? [],\n      } satisfies SettingsMenuLinkWithPermissions;\n    },\n    [permissions.settings]\n  );\n\n  React.useEffect(() => {\n    const getData = async () => {\n      interface MenuLinkPermission {\n        hasPermission: boolean;\n        sectionIndex: number;\n        linkIndex: number;\n      }\n\n      const buildMenuPermissions = (sections: SettingsMenuSectionWithDisplayedLinks[]) =>\n        Promise.all(\n          sections.reduce<Promise<MenuLinkPermission>[]>((acc, section, sectionIndex) => {\n            const linksWithPermissions = section.links.map(async (link, linkIndex) => ({\n              hasPermission: (await checkUserHasPermission(link.permissions)).length > 0,\n              sectionIndex,\n              linkIndex,\n            }));\n\n            return [...acc, ...linksWithPermissions];\n          }, [])\n        );\n\n      const menuPermissions = await buildMenuPermissions(sections);\n\n      setData((prev) => {\n        return {\n          ...prev,\n          isLoading: false,\n          menu: sections.map((section, sectionIndex) => ({\n            ...section,\n            links: section.links.map((link, linkIndex) => {\n              const permission = menuPermissions.find(\n                (permission) =>\n                  permission.sectionIndex === sectionIndex && permission.linkIndex === linkIndex\n              );\n\n              return {\n                ...link,\n                isDisplayed: Boolean(permission?.hasPermission),\n              };\n            }),\n          })),\n        };\n      });\n    };\n\n    const { global, ...otherSections } = settings;\n    const sections = formatLinks([\n      {\n        ...global,\n        links: sortBy([...global.links, ...globalLinks.map(addPermissions)], (link) => link.id).map(\n          (link) => ({\n            ...link,\n            hasNotification: link.id === '000-application-infos' && shouldUpdateStrapi,\n          })\n        ),\n      },\n      {\n        id: 'permissions',\n        intlLabel: { id: 'Settings.permissions', defaultMessage: 'Administration Panel' },\n        links: adminLinks.map(addPermissions),\n      },\n      ...Object.values(otherSections),\n    ]);\n\n    getData();\n  }, [\n    adminLinks,\n    globalLinks,\n    settings,\n    shouldUpdateStrapi,\n    addPermissions,\n    checkUserHasPermission,\n  ]);\n\n  return {\n    isLoading,\n    menu: menu.map((menuItem) => ({\n      ...menuItem,\n      links: menuItem.links.filter((link) => link.isDisplayed),\n    })),\n  };\n};\n\nexport { useSettingsMenu };\nexport type { SettingsMenu };\n", "import { Badge, Divider } from '@strapi/design-system';\nimport { Lightning } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useLocation } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { SubNav } from '../../../components/SubNav';\nimport { useTracking } from '../../../features/Tracking';\nimport { SettingsMenu } from '../../../hooks/useSettingsMenu';\n\ninterface SettingsNavProps {\n  menu: SettingsMenu;\n}\n\nconst StyledBadge = styled(Badge)`\n  border-radius: 50%;\n  padding: ${({ theme }) => theme.spaces[2]};\n  height: 2rem;\n`;\n\nconst SettingsNav = ({ menu }: SettingsNavProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { pathname } = useLocation();\n\n  const filteredMenu = menu.filter(\n    (section) => !section.links.every((link) => link.isDisplayed === false)\n  );\n\n  const sections = filteredMenu.map((section) => {\n    return {\n      ...section,\n      title: section.intlLabel,\n      links: section.links.map((link) => {\n        return {\n          ...link,\n          title: link.intlLabel,\n          name: link.id,\n        };\n      }),\n    };\n  });\n\n  const label = formatMessage({\n    id: 'global.settings',\n    defaultMessage: 'Settings',\n  });\n\n  const handleClickOnLink = (destination: string) => () => {\n    trackUsage('willNavigate', { from: pathname, to: destination });\n  };\n\n  return (\n    <SubNav.Main aria-label={label}>\n      <SubNav.Header label={label} />\n      <Divider background=\"neutral150\" marginBottom={5} />\n      <SubNav.Sections>\n        {sections.map((section) => (\n          <SubNav.Section key={section.id} label={formatMessage(section.intlLabel)}>\n            {section.links.map((link) => {\n              return (\n                <SubNav.Link\n                  to={link.to}\n                  onClick={handleClickOnLink(link.to)}\n                  key={link.id}\n                  label={formatMessage(link.intlLabel)}\n                  endAction={\n                    <>\n                      {link?.licenseOnly && (\n                        <Lightning fill=\"primary600\" width=\"1.5rem\" height=\"1.5rem\" />\n                      )}\n                      {link?.hasNotification && (\n                        <StyledBadge\n                          aria-label=\"Notification\"\n                          backgroundColor=\"primary600\"\n                          textColor=\"neutral0\"\n                        >\n                          1\n                        </StyledBadge>\n                      )}\n                    </>\n                  }\n                />\n              );\n            })}\n          </SubNav.Section>\n        ))}\n      </SubNav.Sections>\n    </SubNav.Main>\n  );\n};\n\nexport { SettingsNav };\nexport type { SettingsNavProps };\n", "import { useIntl } from 'react-intl';\nimport { Navigate, Outlet, useMatch } from 'react-router-dom';\n\nimport { Layouts } from '../../components/Layouts/Layout';\nimport { Page } from '../../components/PageHelpers';\nimport { useSettingsMenu } from '../../hooks/useSettingsMenu';\n\nimport { SettingsNav } from './components/SettingsNav';\n\nconst Layout = () => {\n  /**\n   * This ensures we're capturing the settingId from the URL\n   * but also lets any nesting after that pass.\n   */\n  const match = useMatch('/settings/:settingId/*');\n  const { formatMessage } = useIntl();\n  const { isLoading, menu } = useSettingsMenu();\n\n  // Since the useSettingsMenu hook can make API calls in order to check the links permissions\n  // We need to add a loading state to prevent redirecting the user while permissions are being checked\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (!match?.params.settingId) {\n    return <Navigate to=\"application-infos\" />;\n  }\n\n  return (\n    <Layouts.Root sideNav={<SettingsNav menu={menu} />}>\n      <Page.Title>\n        {formatMessage({\n          id: 'global.settings',\n          defaultMessage: 'Settings',\n        })}\n      </Page.Title>\n      <Outlet />\n    </Layouts.Root>\n  );\n};\n\nexport { Layout };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAMA,cAAc,CAACC,SACnBA,KAAKC,IAAI,CAACC,gBAAAA;AACR,QAAMC,iBAAiBD,YAAYE,MAAMH,IAAI,CAACI,UAAU;IACtD,GAAGA;IACHC,aAAa;IACf;AAEA,SAAO;IAAE,GAAGJ;IAAaE,OAAOD;EAAe;AACjD,CAAA;AA8BF,IAAMI,kBAAkB,MAAA;AAItB,QAAM,CAAC,EAAEC,WAAWR,KAAI,GAAIS,OAAQ,IAASC,eAG1C;IACDF,WAAW;IACXR,MAAM,CAAA;EACR,CAAA;AACA,QAAMW,yBAAyBC,QAC7B,mBACA,CAACC,UAAUA,MAAMC,uBAAuB;AAE1C,QAAMC,qBAAqBC,WAAW,mBAAmB,CAACH,UAAUA,MAAME,kBAAkB;AAC5F,QAAME,WAAWC,aAAa,mBAAmB,CAACL,UAAUA,MAAMI,QAAQ;AAC1E,QAAME,cAAcC,YAAYC,sBAAAA;AAMhC,QAAMC,UAAgBC,cAAQ,MAAMC,kBAAAA,GAAqB,CAAA,CAAE;AAE3D,QAAM,EAAEC,OAAOC,YAAYC,QAAQC,YAAW,IAAKC,cACjDP,SACA,aAAa,MAAM,OAAO,yBAAiC,GAAGQ,kBAAiB,GAC/E;IACEC,QAAQT,UAASU,SAAO;AACtB,aAAO;QACLP,OAAO;UAAIO,GAAAA,QAAQP;UAAUH,GAAAA,SAAQG;QAAM;QAC3CE,QAAQ;UAAIL,GAAAA,SAAQK;UAAWK,GAAAA,QAAQL;QAAO;MAChD;IACF;IACAM,cAAc;MACZR,OAAO,CAAA;MACPE,QAAQ,CAAA;IACV;EACF,CAAA;AAGF,QAAMO,iBAAuBC,kBAC3B,CAAC9B,SAAAA;;AACC,QAAI,CAACA,KAAK+B,IAAI;AACZ,YAAM,IAAIC,MAAM,mDAAA;IAClB;AAEA,WAAO;MACL,GAAGhC;MACHc,eAAaA,uBAAYF,aAAZE,mBAAuBd,KAAK+B,QAA5BjB,mBAAoEmB,SAAQ,CAAA;IAC3F;KAEF;IAACnB,YAAYF;EAAS,CAAA;AAGxBsB,EAAMC,gBAAU,MAAA;AACd,UAAMC,UAAU,YAAA;AAOd,YAAMC,uBAAuB,CAACC,cAC5BC,QAAQC,IACNF,UAASG,OAAsC,CAACC,KAAKC,SAASC,iBAAAA;AAC5D,cAAMC,uBAAuBF,QAAQ5C,MAAMH,IAAI,OAAOI,MAAM8C,eAAe;UACzEC,gBAAgB,MAAMzC,uBAAuBN,KAAKc,WAAW,GAAGkC,SAAS;UACzEJ;UACAE;UACF;AAEA,eAAO;UAAIJ,GAAAA;UAAQG,GAAAA;QAAqB;MAC1C,GAAG,CAAA,CAAE,CAAA;AAGT,YAAMI,kBAAkB,MAAMZ,qBAAqBC,QAAAA;AAEnDlC,cAAQ,CAAC8C,SAAAA;AACP,eAAO;UACL,GAAGA;UACH/C,WAAW;UACXR,MAAM2C,SAAS1C,IAAI,CAAC+C,SAASC,kBAAkB;YAC7C,GAAGD;YACH5C,OAAO4C,QAAQ5C,MAAMH,IAAI,CAACI,MAAM8C,cAAAA;AAC9B,oBAAMK,aAAaF,gBAAgBG,KACjC,CAACD,gBACCA,YAAWP,iBAAiBA,gBAAgBO,YAAWL,cAAcA,SAAAA;AAGzE,qBAAO;gBACL,GAAG9C;gBACHC,aAAaoD,QAAQF,yCAAYJ,aAAAA;cACnC;YACF,CAAA;YACF;QACF;MACF,CAAA;IACF;AAEA,UAAM,EAAEzB,QAAQ,GAAGgC,cAAAA,IAAkB1C;AACrC,UAAM0B,WAAW5C,YAAY;MAC3B;QACE,GAAG4B;QACHvB,WAAOwD,cAAAA,SAAO;UAAIjC,GAAAA,OAAOvB;UAAUwB,GAAAA,YAAY3B,IAAIiC,cAAAA;WAAkB,CAAC7B,SAASA,KAAK+B,EAAE,EAAEnC,IACtF,CAACI,UAAU;UACT,GAAGA;UACHwD,iBAAiBxD,KAAK+B,OAAO,2BAA2BrB;UAC1D;MAEJ;MACA;QACEqB,IAAI;QACJ0B,WAAW;UAAE1B,IAAI;UAAwB2B,gBAAgB;QAAuB;QAChF3D,OAAOsB,WAAWzB,IAAIiC,cAAAA;MACxB;MACG8B,GAAAA,OAAOC,OAAON,aAAAA;IAClB,CAAA;AAEDlB,YAAAA;KACC;IACDf;IACAE;IACAX;IACAF;IACAmB;IACAvB;EACD,CAAA;AAED,SAAO;IACLH;IACAR,MAAMA,KAAKC,IAAI,CAACiE,cAAc;MAC5B,GAAGA;MACH9D,OAAO8D,SAAS9D,MAAM+D,OAAO,CAAC9D,SAASA,KAAKC,WAAW;MACzD;EACF;AACF;;;;ACpLA,IAAM8D,cAAcC,GAAOC,KAAAA;;aAEd,CAAC,EAAEC,MAAK,MAAOA,MAAMC,OAAO,CAAA,CAAE;;;AAI3C,IAAMC,cAAc,CAAC,EAAEC,KAAI,MAAoB;AAC7C,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAU,IAAKC,YAAAA;AACvB,QAAM,EAAEC,SAAQ,IAAKC,YAAAA;AAErB,QAAMC,eAAeP,KAAKQ,OACxB,CAACC,YAAY,CAACA,QAAQC,MAAMC,MAAM,CAACC,SAASA,KAAKC,gBAAgB,KAAA,CAAA;AAGnE,QAAMC,WAAWP,aAAaQ,IAAI,CAACN,YAAAA;AACjC,WAAO;MACL,GAAGA;MACHO,OAAOP,QAAQQ;MACfP,OAAOD,QAAQC,MAAMK,IAAI,CAACH,SAAAA;AACxB,eAAO;UACL,GAAGA;UACHI,OAAOJ,KAAKK;UACZC,MAAMN,KAAKO;QACb;MACF,CAAA;IACF;EACF,CAAA;AAEA,QAAMC,QAAQnB,cAAc;IAC1BkB,IAAI;IACJE,gBAAgB;EAClB,CAAA;AAEA,QAAMC,oBAAoB,CAACC,gBAAwB,MAAA;AACjDpB,eAAW,gBAAgB;MAAEqB,MAAMnB;MAAUoB,IAAIF;IAAY,CAAA;EAC/D;AAEA,aACEG,yBAACC,OAAOC,MAAI;IAACC,cAAYT;;UACvBU,wBAACH,OAAOI,QAAM;QAACX;;UACfU,wBAACE,SAAAA;QAAQC,YAAW;QAAaC,cAAc;;UAC/CJ,wBAACH,OAAOQ,UAAQ;QACbrB,UAAAA,SAASC,IAAI,CAACN,gBACbqB,wBAACH,OAAOS,SAAO;UAAkBhB,OAAOnB,cAAcQ,QAAQQ,SAAS;UACpER,UAAAA,QAAQC,MAAMK,IAAI,CAACH,SAAAA;AAClB,uBACEkB,wBAACH,OAAOU,MAAI;cACVZ,IAAIb,KAAKa;cACTa,SAAShB,kBAAkBV,KAAKa,EAAE;cAElCL,OAAOnB,cAAcW,KAAKK,SAAS;cACnCsB,eACEb,yBAAAc,6BAAA;;mBACG5B,6BAAM6B,oBACLX,wBAACY,eAAAA;oBAAUC,MAAK;oBAAaC,OAAM;oBAASC,QAAO;;mBAEpDjC,6BAAMkC,wBACLhB,wBAACpC,aAAAA;oBACCmC,cAAW;oBACXkB,iBAAgB;oBAChBC,WAAU;oBACX,UAAA;;;;YAZFpC,GAAAA,KAAKO,EAAE;UAoBlB,CAAA;QA1BmBV,GAAAA,QAAQU,EAAE,CAAA;;;;AAgCzC;;;ACjFA,IAAM8B,SAAS,MAAA;AAKb,QAAMC,QAAQC,SAAS,wBAAA;AACvB,QAAM,EAAEC,cAAa,IAAKC,QAAAA;AAC1B,QAAM,EAAEC,WAAWC,KAAI,IAAKC,gBAAAA;AAI5B,MAAIF,WAAW;AACb,eAAOG,yBAACC,KAAKC,SAAO,CAAA,CAAA;EACtB;AAEA,MAAI,EAACT,+BAAOU,OAAOC,YAAW;AAC5B,eAAOJ,yBAACK,UAAAA;MAASC,IAAG;;EACtB;AAEA,aACEC,0BAACC,QAAQC,MAAI;IAACC,aAASV,yBAACW,aAAAA;MAAYb;;;UAClCE,yBAACC,KAAKW,OAAK;kBACRjB,cAAc;UACbkB,IAAI;UACJC,gBAAgB;QAClB,CAAA;;UAEFd,yBAACe,QAAAA,CAAAA,CAAAA;;;AAGP;", "names": ["formatLinks", "menu", "map", "menuSection", "formattedLinks", "links", "link", "isDisplayed", "useSettingsMenu", "isLoading", "setData", "useState", "checkUserHasPermission", "useAuth", "state", "checkUserHasPermissions", "shouldUpdateStrapi", "useAppInfo", "settings", "useStrapiApp", "permissions", "useSelector", "selectAdminPermissions", "ceLinks", "useMemo", "SETTINGS_LINKS_CE", "admin", "adminLinks", "global", "globalLinks", "useEnterprise", "SETTINGS_LINKS_EE", "combine", "eeLinks", "defaultValue", "addPermissions", "useCallback", "id", "Error", "main", "React", "useEffect", "getData", "buildMenuPermissions", "sections", "Promise", "all", "reduce", "acc", "section", "sectionIndex", "linksWithPermissions", "linkIndex", "hasPermission", "length", "menuPermissions", "prev", "permission", "find", "Boolean", "otherSections", "sortBy", "hasNotification", "intlLabel", "defaultMessage", "Object", "values", "menuItem", "filter", "StyledBadge", "styled", "Badge", "theme", "spaces", "SettingsNav", "menu", "formatMessage", "useIntl", "trackUsage", "useTracking", "pathname", "useLocation", "filteredMenu", "filter", "section", "links", "every", "link", "isDisplayed", "sections", "map", "title", "intlLabel", "name", "id", "label", "defaultMessage", "handleClickOnLink", "destination", "from", "to", "_jsxs", "SubNav", "Main", "aria-label", "_jsx", "Header", "Divider", "background", "marginBottom", "Sections", "Section", "Link", "onClick", "endAction", "_Fragment", "licenseOnly", "Lightning", "fill", "width", "height", "hasNotification", "backgroundColor", "textColor", "Layout", "match", "useMatch", "formatMessage", "useIntl", "isLoading", "menu", "useSettingsMenu", "_jsx", "Page", "Loading", "params", "settingId", "Navigate", "to", "_jsxs", "Layouts", "Root", "sideNav", "SettingsNav", "Title", "id", "defaultMessage", "Outlet"]}