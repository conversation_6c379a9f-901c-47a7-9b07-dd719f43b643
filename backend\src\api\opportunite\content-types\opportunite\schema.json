{"kind": "collectionType", "collectionName": "opportunites", "info": {"singularName": "opportunite", "pluralName": "opportunites", "displayName": "Opportunite"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"objet": {"type": "text", "required": true}, "pays_destination": {"type": "string", "required": true}, "date_debut": {"type": "date", "required": true}, "date_fin": {"type": "date", "required": true}, "secteur": {"type": "enumeration", "required": true, "enum": ["Agro-alimentaire", "Textile", "IME", "Service", "Artisanat", "Divers"]}, "importateur": {"type": "relation", "relation": "oneToOne", "target": "api::importateur.importateur"}, "exportateurs": {"type": "relation", "relation": "manyToMany", "target": "api::exportateur.exportateur", "inversedBy": "opportunites"}}}