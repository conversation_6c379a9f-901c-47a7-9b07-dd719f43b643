const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["MagicLinkEE-DZQ66Mn9.js","strapi-z7ApxZZq.js","strapi-D6821bIJ.css","SelectRoles-DgSmECKC.js","useAdminRoles-DhqKz3-J.js","CreateActionEE-CvhBAY-n.js","isNil-HHOJ63lS.js","ListPage-DmVMYnPx.js","useLicenseLimitNotification-wL5SpINj.js","SearchInput-Cmj1ynyp.js","users-DLc-PG84.js"])))=>i.map(i=>d[i]);
import{r as g,a as H,j as e,D as R,bJ as re,w as J,v as K,bi as E,_ as M,cp as ne,cc as x,cq as ie,cr as le,C as oe,aP as ce,e as v,$ as I,T as A,H as b,aU as $,a_ as de,aQ as q,aR as j,aT as ue,aL as W,P,z as me,V as ge,aA as pe,bk as he,aC as fe,cs as xe,B,ct as T,bE as o,I as Y,au as be,g as je,bm as Ee,cu as N,bt as Me,bu as Ae,cv as ye}from"./strapi-z7ApxZZq.js";import{S as _e}from"./SearchInput-Cmj1ynyp.js";import{g as Q}from"./users-DLc-PG84.js";import{M as Le,S as Se}from"./SelectRoles-DgSmECKC.js";import"./useAdminRoles-DhqKz3-J.js";const Ce=g.forwardRef((a,l)=>{const{formatMessage:p}=H();return e.jsx(R,{ref:l,startIcon:e.jsx(re,{}),size:"S",...a,children:p({id:"Settings.permissions.users.create",defaultMessage:"Invite new user"})})}),Ie=({onToggle:a})=>{const[l,p]=g.useState("create"),[w,k]=g.useState(""),{formatMessage:r}=H(),{toggleNotification:m}=J(),{_unstableFormatAPIError:F,_unstableFormatValidationErrors:y}=K(),d=E(Te,async()=>(await M(async()=>{const{ROLE_LAYOUT:t}=await import("./ModalForm-CamjQBT3.js");return{ROLE_LAYOUT:t}},[])).ROLE_LAYOUT,{combine(t,c){return[...t,...c]},defaultValue:[]}),U=E(G,async()=>(await M(async()=>{const{FORM_INITIAL_VALUES:t}=await import("./ModalForm-CamjQBT3.js");return{FORM_INITIAL_VALUES:t}},[])).FORM_INITIAL_VALUES,{combine(t,c){return{...t,...c}},defaultValue:G}),_=E(Le,async()=>(await M(async()=>{const{MagicLinkEE:t}=await import("./MagicLinkEE-DZQ66Mn9.js");return{MagicLinkEE:t}},__vite__mapDeps([0,1,2,3,4]))).MagicLinkEE),[h]=ne(),L=r({id:"Settings.permissions.users.create",defaultMessage:"Invite new user"}),D=async(t,{setErrors:c})=>{const n=await h({...t,roles:t.roles??[]});"data"in n?(n.data.registrationToken&&k(n.data.registrationToken),O()):(m({type:"danger",message:F(n.error)}),ue(n.error)&&n.error.name==="ValidationError"&&c(y(n.error)))},O=()=>{C?p(C):a()},{buttonSubmitLabel:S,isDisabled:f,next:C}=ve[l];return _?e.jsx(x.Root,{defaultOpen:!0,onOpenChange:a,children:e.jsxs(x.Content,{children:[e.jsx(x.Header,{children:e.jsx(ie,{label:L,children:e.jsx(le,{isCurrent:!0,children:L})})}),e.jsx(oe,{method:l==="create"?"POST":"PUT",initialValues:U??{},onSubmit:D,validationSchema:Pe,children:({isSubmitting:t})=>e.jsxs(e.Fragment,{children:[e.jsx(x.Body,{children:e.jsxs(v,{direction:"column",alignItems:"stretch",gap:6,children:[l!=="create"&&e.jsx(_,{registrationToken:w}),e.jsxs(I,{children:[e.jsx(A,{variant:"beta",tag:"h2",children:r({id:"app.components.Users.ModalCreateBody.block-title.details",defaultMessage:"User details"})}),e.jsx(I,{paddingTop:4,children:e.jsx(v,{direction:"column",alignItems:"stretch",gap:1,children:e.jsx(b.Root,{gap:5,children:Re.map(c=>c.map(({size:n,...i})=>e.jsx(b.Item,{col:n,direction:"column",alignItems:"stretch",children:e.jsx($,{...i,disabled:f,label:r(i.label),placeholder:r(i.placeholder)})},i.name)))})})})]}),e.jsxs(I,{children:[e.jsx(A,{variant:"beta",tag:"h2",children:r({id:"global.roles",defaultMessage:"User's role"})}),e.jsx(I,{paddingTop:4,children:e.jsxs(b.Root,{gap:5,children:[e.jsx(b.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(Se,{disabled:f})}),d.map(c=>c.map(({size:n,...i})=>e.jsx(b.Item,{col:n,direction:"column",alignItems:"stretch",children:e.jsx($,{...i,disabled:f,label:r(i.label),placeholder:i.placeholder?r(i.placeholder):void 0,hint:i.hint?r(i.hint):void 0})},i.name)))]})})]})]})}),e.jsxs(x.Footer,{children:[e.jsx(R,{variant:"tertiary",onClick:a,type:"button",children:r({id:"app.components.Button.cancel",defaultMessage:"Cancel"})}),l==="create"?e.jsx(R,{type:"submit",loading:t,children:r(S)}):e.jsx(R,{type:"button",loading:t,onClick:a,children:r(S)})]})]})})]})}):null},G={firstname:"",lastname:"",email:"",roles:[]},Te=[],Re=[[{label:{id:"Auth.form.firstname.label",defaultMessage:"First name"},name:"firstname",placeholder:{id:"Auth.form.firstname.placeholder",defaultMessage:"e.g. Kai"},type:"string",size:6,required:!0},{label:{id:"Auth.form.lastname.label",defaultMessage:"Last name"},name:"lastname",placeholder:{id:"Auth.form.lastname.placeholder",defaultMessage:"e.g. Doe"},type:"string",size:6}],[{label:{id:"Auth.form.email.label",defaultMessage:"Email"},name:"email",placeholder:{id:"Auth.form.email.placeholder",defaultMessage:"e.g. <EMAIL>"},type:"email",size:6,required:!0}]],Pe=ce().shape({firstname:q().trim().required({id:j.required.id,defaultMessage:"This field is required"}).nullable(),lastname:q(),email:q().email(j.email).required({id:j.required.id,defaultMessage:"This field is required"}).nullable(),roles:de().min(1,{id:j.required.id,defaultMessage:"This field is required"}).required({id:j.required.id,defaultMessage:"This field is required"})}),ve={create:{buttonSubmitLabel:{id:"app.containers.Users.ModalForm.footer.button-success",defaultMessage:"Invite user"},isDisabled:!1,next:"magic-link"},"magic-link":{buttonSubmitLabel:{id:"global.finish",defaultMessage:"Finish"},isDisabled:!0,next:null}},we=()=>{const{_unstableFormatAPIError:a}=K(),[l,p]=g.useState(!1),w=W(s=>s.admin_app.permissions),{allowedActions:{canCreate:k,canDelete:r,canRead:m}}=me(w.settings?.users),F=ge(),{toggleNotification:y}=J(),{formatMessage:d}=H(),{search:U}=pe(),[_,h]=g.useState(!1),[L,D]=g.useState([]),{data:O,isError:S,isLoading:f}=he(fe.parse(U,{ignoreQueryPrefix:!0})),{pagination:C,users:t=[]}=O??{},c=E(Ce,async()=>(await M(async()=>{const{CreateActionEE:s}=await import("./CreateActionEE-CvhBAY-n.js");return{CreateActionEE:s}},__vite__mapDeps([5,1,2,6]))).CreateActionEE),n=ke.map(s=>({...s,label:d(s.label)})),i=d({id:"global.users",defaultMessage:"Users"}),z=()=>{p(s=>!s)},[X]=xe(),Z=async s=>{try{const u=await X({ids:s});"error"in u&&y({type:"danger",message:a(u.error)})}catch{y({type:"danger",message:d({id:"global.error",defaultMessage:"An error occurred"})})}},ee=s=>()=>{m&&F(s.toString())},se=s=>async()=>{D([s]),h(!0)},ae=async()=>{await Z(L),h(!1)};return c?S?e.jsx(P.Error,{}):e.jsxs(P.Main,{"aria-busy":f,children:[e.jsx(P.Title,{children:d({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Users"})}),e.jsx(B.Header,{primaryAction:k&&e.jsx(c,{onClick:z}),title:i,subtitle:d({id:"Settings.permissions.users.listview.header.subtitle",defaultMessage:"All the users who have access to the Strapi admin panel"})}),e.jsx(B.Action,{startActions:e.jsxs(e.Fragment,{children:[e.jsx(_e,{label:d({id:"app.component.search.label",defaultMessage:"Search for {target}"},{target:i})}),e.jsxs(T.Root,{options:Fe,children:[e.jsx(T.Trigger,{}),e.jsx(T.Popover,{}),e.jsx(T.List,{})]})]})}),e.jsxs(B.Content,{children:[e.jsxs(o.Root,{rows:t,headers:n,children:[e.jsx(o.ActionBar,{}),e.jsxs(o.Content,{children:[e.jsxs(o.Head,{children:[r?e.jsx(o.HeaderCheckboxCell,{}):null,n.map(s=>e.jsx(o.HeaderCell,{...s},s.name))]}),e.jsx(o.Empty,{}),e.jsx(o.Loading,{}),e.jsx(o.Body,{children:t.map(s=>e.jsxs(o.Row,{onClick:ee(s.id),cursor:m?"pointer":"default",children:[r?e.jsx(o.CheckboxCell,{id:s.id}):null,n.map(({cellFormatter:u,name:V,...te})=>e.jsx(o.Cell,{children:typeof u=="function"?u(s,{name:V,...te}):e.jsx(A,{textColor:"neutral800",children:s[V]||"-"})},V)),m||r?e.jsx(o.Cell,{onClick:u=>u.stopPropagation(),children:e.jsxs(v,{justifyContent:"end",children:[m?e.jsx(Y,{tag:be,to:s.id.toString(),label:d({id:"app.component.table.edit",defaultMessage:"Edit {target}"},{target:Q(s)}),variant:"ghost",children:e.jsx(je,{})}):null,r?e.jsx(Y,{onClick:se(s.id),label:d({id:"global.delete-target",defaultMessage:"Delete {target}"},{target:Q(s)}),variant:"ghost",children:e.jsx(Ee,{})}):null]})}):null]},s.id))})]})]}),e.jsxs(N.Root,{...C,children:[e.jsx(N.PageSize,{}),e.jsx(N.Links,{})]})]}),l&&e.jsx(Ie,{onToggle:z}),e.jsx(Me.Root,{open:_,onOpenChange:h,children:e.jsx(Ae,{onConfirm:ae})})]}):null},ke=[{name:"firstname",label:{id:"Settings.permissions.users.firstname",defaultMessage:"Firstname"},sortable:!0},{name:"lastname",label:{id:"Settings.permissions.users.lastname",defaultMessage:"Lastname"},sortable:!0},{name:"email",label:{id:"Settings.permissions.users.email",defaultMessage:"Email"},sortable:!0},{name:"roles",label:{id:"Settings.permissions.users.roles",defaultMessage:"Roles"},sortable:!1,cellFormatter({roles:a}){return e.jsx(A,{textColor:"neutral800",children:a.map(l=>l.name).join(`,
`)})}},{name:"username",label:{id:"Settings.permissions.users.username",defaultMessage:"Username"},sortable:!0},{name:"isActive",label:{id:"Settings.permissions.users.user-status",defaultMessage:"User status"},sortable:!1,cellFormatter({isActive:a}){return e.jsx(v,{children:e.jsx(ye,{size:"S",variant:a?"success":"danger",children:e.jsx(A,{tag:"span",variant:"omega",fontWeight:"bold",children:a?"Active":"Inactive"})})})}}],Fe=[{name:"firstname",label:"Firstname",type:"string"},{name:"lastname",label:"Lastname",type:"string"},{name:"email",label:"Email",type:"email"},{name:"username",label:"Username",type:"string"},{name:"isActive",label:"Active user",type:"boolean"}],Ue=()=>{const a=E(we,async()=>(await M(async()=>{const{UserListPageEE:l}=await import("./ListPage-DmVMYnPx.js");return{UserListPageEE:l}},__vite__mapDeps([7,1,2,8,6,9,10,3,4]))).UserListPageEE);return a?e.jsx(a,{}):null},Ne=()=>{const a=W(l=>l.admin_app.permissions.settings?.users.read);return e.jsx(P.Protect,{permissions:a,children:e.jsx(Ue,{})})};export{Ue as ListPage,we as ListPageCE,Ne as ProtectedListPage};
