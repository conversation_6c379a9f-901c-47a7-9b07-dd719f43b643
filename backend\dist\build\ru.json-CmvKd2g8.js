var e="Группы",t="Типы Коллекции",n="Страница не найдена",i={"App.schemas.data-loaded":"Схемы были успешно загружены","EditRelations.title":"Связанные данные","HeaderLayout.button.label-add-entry":"Создать новую запись","ListViewTable.relation-loaded":"Отношения были загружены","ListViewTable.relation-loading":"Отношения загружаются","ListViewTable.relation-more":"Это отношение содержит больше сущностей, чем отображается","api.id":"API ID","apiError.This attribute must be unique":"{field} должно быть уникальным","bulk-publish.already-published":"Уже опубликовано","components.AddFilterCTA.add":"Фильтры","components.AddFilterCTA.hide":"Фильтры","components.DragHandle-label":"Перетащить","components.DraggableAttr.edit":"Нажмите чтобы редактировать","components.DraggableCard.delete.field":"Удалить {item}","components.DraggableCard.edit.field":"Редактировать {item}","components.DraggableCard.move.field":"Переместить {item}","components.DynamicZone.ComponentPicker-label":"Выберите один компонент","components.DynamicZone.add-component":"Добавить компонент в {componentName}","components.DynamicZone.delete-label":"Удалить {name}","components.DynamicZone.error-message":"Компонент содержит ошибку(-и)","components.DynamicZone.missing-components":"{number, plural, =0{# отсутствующих компонентов} one{# отсутствующий компонент} few{# отсутствующих компонента} many {# отсутствующих компонентов}}","components.DynamicZone.move-down-label":"Переместить компонент вниз","components.DynamicZone.move-up-label":"Переместить компонент вверх","components.DynamicZone.pick-compo":"Выберите компонент","components.DynamicZone.required":"Обязательный компонент","components.EmptyAttributesBlock.button":"Перейти в настройки","components.EmptyAttributesBlock.description":"Вы можете изменить текущие настройки","components.FieldItem.linkToComponentLayout":"Установить компоновку компонентов","components.FieldSelect.label":"Добавить поле","components.FilterOptions.button.apply":"Применить","components.Filters.usersSelect.label":"Поиск и выбор пользователя для фильтрации по","components.FiltersPickWrapper.PluginHeader.actions.apply":"Применить","components.FiltersPickWrapper.PluginHeader.actions.clearAll":"Очистить все","components.FiltersPickWrapper.PluginHeader.description":"Укажите условия для фильтрации записей","components.FiltersPickWrapper.PluginHeader.title.filter":"Фильтры","components.FiltersPickWrapper.hide":"Скрыть","components.LeftMenu.Search.label":"Поиск по типу содержимого","components.LeftMenu.collection-types":"Типы Коллекций","components.LeftMenu.single-types":"Одиночные Типы","components.LimitSelect.itemsPerPage":"Элементов на странице","components.ListViewTable.row-line":"строка {number}","components.NotAllowedInput.text":"Нет разрешений на просмотр этого поля","components.RelationInput.icon-button-aria-label":"Тяни","components.RepeatableComponent.error-message":"Компонент(-ы) содержит(-ат) ошибку(-и)","components.Search.placeholder":"Поиск записей...","components.Select.draft-info-title":"Состояние: Черновик","components.Select.publish-info-title":"Состояние: Опубликовано","components.SettingsViewWrapper.pluginHeader.description.edit-settings":"Настройте, как будет выглядеть вид редактирования.","components.SettingsViewWrapper.pluginHeader.description.list-settings":"Определите параметры представления списка.","components.SettingsViewWrapper.pluginHeader.title":"Настройка представления — {name}","components.TableDelete.delete":"Удалить все","components.TableDelete.deleteSelected":"Удалить выбранное","components.TableDelete.label":"выбрано записей: {number}","components.TableEmpty.withFilters":"Нет {contentType} с применёнными фильтрами...","components.TableEmpty.withSearch":"Нет {contentType} согласно поиску ({search})...","components.TableEmpty.withoutFilter":"Нет {contentType}...","components.empty-repeatable":"Ещё нет записей. Нажмите кнопку ниже, чтобы добавить.","components.notification.info.maximum-requirement":"Вы уже достигли максимального количества полей","components.notification.info.minimum-requirement":"Добавлено поле, соответствующее минимальным требованиям","components.repeatable.reorder.error":"Произошла ошибка при изменении порядка полей вашего компонента. Попробуйте ещё раз.","components.reset-entry":"Сбросить запись","components.uid.apply":"Применить","components.uid.available":"Доступный","components.uid.regenerate":"Восстановить","components.uid.suggested":"Предложенный","components.uid.unavailable":"Недоступный","containers.Edit.Link.Layout":"Настройка макета","containers.Edit.Link.Model":"Измените тип Коллекции","containers.Edit.addAnItem":"Добавить элемент...","containers.Edit.clickToJump":"Нажмите для перехода к записи","containers.Edit.delete":"Удалить","containers.Edit.delete-entry":"Удалить эту запись","containers.Edit.editing":"Редактирование...","containers.Edit.information":"Информация","containers.Edit.information.by":"Автор","containers.Edit.information.created":"Создано","containers.Edit.information.draftVersion":"черновая версия","containers.Edit.information.editing":"Редактирование","containers.Edit.information.lastUpdate":"Последнее обновление","containers.Edit.information.publishedVersion":"опубликованная версия","containers.Edit.pluginHeader.title.new":"Создать запись","containers.Edit.reset":"Сбросить","containers.Edit.returnList":"Вернуться к списку","containers.Edit.seeDetails":"Подробнее","containers.Edit.submit":"Сохранить","containers.EditSettingsView.modal-form.edit-field":"Отредактируйте это поле","containers.EditView.add.new-entry":"Добавить запись","containers.EditView.notification.errors":"Форма содержит некоторые ошибки","containers.Home.introduction":"Для того, чтобы отредактировать ваши записи используйте соответствующую ссылку в меню слева. У плагина отсутствует полноценная возможность редактировать настройки, и он всё ещё находится в стадии активной разработки.","containers.Home.pluginHeaderDescription":"Управляйте своими записями с помощью мощного и красивого интерфейса.","containers.Home.pluginHeaderTitle":"Редактор контента","containers.List.draft":"Черновик","containers.List.errorFetchRecords":"Ошибка","containers.List.published":"Опубликован","containers.list.displayedFields":"Отображаемые поля","containers.list.items":"{number, plural, =0{# элементов} one{# элемент} few{# элемента} many {# элементов}}","containers.list.selectedEntriesModal.publishedCount":"<b>{publishedCount}</b> {publishedCount, plural, =0{# записей} one{# запись} few{# записи} many {# записей}} опубликованы. <b>{withErrorsCount}</b> {withErrorsCount, plural, =0{# записей} one{# запись} few{# записи} many {# записей}} ожидают действий.","containers.list.selectedEntriesModal.selectedCount":"<b>{readyToPublishCount}</b> {readyToPublishCount, plural, =0{# записей} one{# запись} few{# записи} many {# записей}} готовы к публикации. <b>{withErrorsCount}</b> {withErrorsCount, plural, =0{# записей} one{# запись} few{# записи} many {# записей}} ожидают действий.","containers.list.selectedEntriesModal.title":"Опубликовать записи","containers.list.table-headers.publishedAt":"Состояние","containers.ListSettingsView.modal-form.edit-label":"Отредактировать {fieldName}","containers.SettingPage.add.field":"Добавить ещё одно поле","containers.SettingPage.add.relational-field":"Добавить ещё одно связанное поле","containers.SettingPage.attributes":"Поля атрибутов","containers.SettingPage.attributes.description":"Определить порядок атрибутов","containers.SettingPage.editSettings.description":"Перетащите поля, чтобы определить макет","containers.SettingPage.editSettings.entry.title":"Заголовок записи","containers.SettingPage.editSettings.entry.title.description":"Установите отображаемое поле вашей записи","containers.SettingPage.editSettings.relation-field.description":"Установите поле, которое будет отображаться как в режиме редактирования, так и в списке","containers.SettingPage.editSettings.title":"Редактирование — Настройки","containers.SettingPage.layout":"Макет","containers.SettingPage.listSettings.description":"Настройте параметры для этого типа Коллекции","containers.SettingPage.listSettings.title":"Просмотр списка — Настройки","containers.SettingPage.pluginHeaderDescription":"Настройте конкретные параметры для этого типа Коллекции","containers.SettingPage.relations":"Связанные поля","containers.SettingPage.settings":"Настройки","containers.SettingPage.view":"Вид","containers.SettingViewModel.pluginHeader.title":"Контент менеджер — {name}","containers.SettingsPage.Block.contentType.description":"Настроить отдельные параметры","containers.SettingsPage.Block.contentType.title":"Типы Коллекций","containers.SettingsPage.Block.generalSettings.description":"Настройте параметры по умолчанию для ваших типов Коллекций","containers.SettingsPage.Block.generalSettings.title":"Общее","containers.SettingsPage.pluginHeaderDescription":"Настройте параметры для всех ваших типов Коллекций и Групп","containers.SettingsView.list.subtitle":"Настройте макет и отображение ваших типов Коллекций и и Групп","containers.SettingsView.list.title":"Конфигурация отображения","dnd.cancel-item":"{item}, перемещён. Изменение порядка не произошло.","dnd.drop-item":"{item}, перемещён. Новое местоположение в списке: {position}.","dnd.grab-item":"{item}, перемещён. Текущее местоположение в списке: {position}. Нажимайте стрелки вверх и вниз, чтобы изменить положение, пробел, чтобы переместить, Escape, чтобы отменить.","dnd.instructions":"Нажмите пробел, чтобы захватить и изменить порядок","dnd.reorder":"{item}, перемещён. Новое местоположение в списке: {position}.","edit-settings-view.link-to-ctb.components":"Редактировать компонент","edit-settings-view.link-to-ctb.content-types":"Редактирование типа содержимого","emptyAttributes.button":"Перейдите в конструктор типов Коллекций","emptyAttributes.description":"Добавьте своё первое поле в тип Коллекции","emptyAttributes.title":"Пока нет полей","error.attribute.key.taken":"Это значение уже существует","error.attribute.sameKeyAndName":"Не может быть одинаковым","error.attribute.taken":"Поле с таким названием уже существует","error.contentTypeName.taken":"Это название уже существует","error.model.fetch":"Произошла ошибка во время настройки конфигурации модели.","error.record.create":"Произошла ошибка при создании записи.","error.record.delete":"Произошла ошибка при удалении записи.","error.record.fetch":"Произошла ошибка при извлечении записи.","error.record.update":"Произошла ошибка при обновлении записи.","error.records.count":"Произошла ошибка при подсчёте количества записей.","error.records.fetch":"Произошла ошибка при извлечении записей.","error.schema.generation":"Возникла ошибка во время генерации структуры.","error.validation.json":"Это не JSON","error.validation.max":"Слишком большое.","error.validation.maxLength":"Слишком длинное.","error.validation.min":"Слишком маленькое.","error.validation.minLength":"Слишком короткое.","error.validation.minSupMax":"Не может быть выше","error.validation.regex":"Значение не соответствует регулярному выражению.","error.validation.required":"Обязательное значение.","form.Input.bulkActions":"Включить массовые действия","form.Input.defaultSort":"Сортировка по умолчанию","form.Input.description":"Описание","form.Input.description.placeholder":"Имя, отображаемое в профиле","form.Input.editable":"Редактируемое поле","form.Input.filters":"Включить фильтры","form.Input.hint.character.unit":"{maxValue, plural, =0{# символов} one{# символ} few{# символа} many {# символов}}","form.Input.hint.minMaxDivider":" / ","form.Input.hint.text":"{min, select, undefined {} other {мин. {min}}}{divider}{max, select, undefined {} other {макс. {max}}}{unit}{br}{description}","form.Input.label":"Подпись","form.Input.label.inputDescription":"Это значение переопределяет название, отображаемое в заголовке таблицы","form.Input.pageEntries":"Записей на странице","form.Input.pageEntries.inputDescription":"Примечание: вы можете переопределить это значение на странице настроек типа Коллекции.","form.Input.placeholder":"Заполнитель","form.Input.placeholder.placeholder":"Моё значение","form.Input.search":"Включить поиск","form.Input.search.field":"Включить поиск по этому полю","form.Input.sort.field":"Включить сортировку по этому полю","form.Input.sort.order":"Сортировка по умолчанию","form.Input.wysiwyg":"Отображение в виде WYSIWYG","global.displayedFields":"Отображение полей",groups:e,"groups.numbered":"Группы ({number})","header.name":"Контент","link-to-ctb":"Редактировать модель","listView.validation.errors.message":"Пожалуйста, убедитесь перед публикацией, что все поля заполнены правильно (обязательное поле, минимальное/максимальное количество символов и т.д.).","listView.validation.errors.title":"Требуется действие",models:t,"models.numbered":"Типы Коллекции ({number})","notification.error.displayedFields":"Необходимо добавить хотя бы одно поле","notification.error.relationship.fetch":"Возникла ошибка при получении связей.","notification.info.SettingPage.disableSort":"У вас должен быть один атрибут с разрешенной сортировкой","notification.info.minimumFields":"Вам нужно иметь хотя бы одно отображаемое поле","notification.upload.error":"Произошла ошибка при загрузке ваших файлов",pageNotFound:n,"pages.ListView.header-subtitle":"{number, plural, =0 {Ничего не найдено} other {Найдено записей: #}}","pages.NoContentType.button":"Создайте свой первый тип контента","pages.NoContentType.text":"У вас ещё нет никакого контента, мы рекомендуем вам создать свой первый тип контента.","permissions.not-allowed.create":"У вас нет прав на создание документов","permissions.not-allowed.update":"У вас нет прав на просмотр этого документа","plugin.description.long":"Быстрый способ увидеть, отредактировать и удалить данные в вашей базе данных.","plugin.description.short":"Быстрый способ увидеть, отредактировать и удалить данные в вашей базе данных.","popUpWarning.bodyMessage.contentType.delete":"Вы уверены, что хотите удалить эту запись?","popUpWarning.bodyMessage.contentType.delete.all":"Вы уверены, что хотите удалить эти записи?","popUpWarning.bodyMessage.contentType.publish.all":"Are you sure you want to publish these entries?","popUpWarning.bodyMessage.contentType.unpublish.all":"Are you sure you want to unpublish these entries?","popUpWarning.warning.has-draft-relations.title":"Подтверждение","popUpWarning.warning.publish-question":"Вы уверены, что хотите опубликовать запись?","popUpWarning.warning.unpublish":"Если вы не опубликуете этот контент, он автоматически превратится в Черновик.","popUpWarning.warning.unpublish-question":"Вы уверены, что хотите её не публиковать?","popUpWarning.warning.updateAllSettings":"Это изменит все ваши настройки","popUpwarning.warning.bulk-has-draft-relations.message":"<b>{count} {count, plural, =0{# отношений} one{# отношение} few{# отношения} many {# отношений}} из {entities} {entities, =0{# записей} one{# записи} few{# записей} many {# записей}}</b> ещё не опубликованы и могут привести к неожиданному поведению.","popUpwarning.warning.has-draft-relations.button-confirm":"Да, публиковать","popUpwarning.warning.has-draft-relations.message":"<b>{count, plural, =0{# отношений записей} one{# отношение записи} few{# отношения записи} many {# отношений записей}}</b> ещё не опубликованы.<br></br>Это может привести к появлению неработающих ссылок и ошибок в вашем проекте.","popover.display-relations.label":"Показать отношения","relation.add":"Добавить отношение","relation.disconnect":"Удалить","relation.isLoading":"Отношения загружаются","relation.loadMore":"Загрузить ещё","relation.notAvailable":"Нет отношений","relation.publicationState.draft":"Черновик","relation.publicationState.published":"Опубликовано","reviewWorkflows.stage.label":"Просмотреть этап","select.currently.selected":"{count} выбрано","success.record.delete":"Удалено","success.record.publish":"Опубликовано","success.record.publishing":"Публикуем...","success.record.save":"Сохранено","success.record.unpublish":"Не опубликовано","utils.data-loaded":"{number, plural, =0{# записей} one{# запись} few{# записи} many {# записей}} успешно загружено","plugin.name":"Редактор контента"};export{i as default,e as groups,t as models,n as pageNotFound};
