import{g2 as Ve,g3 as Ge,g4 as V,g5 as ee,g6 as We,g7 as ze,cH as A,b_ as Ye,r as f,j as e,aV as Ze,k as U,$ as P,fL as Ke,a as E,eb as Q,e as y,T as b,bp as se,H as O,bq as oe,cg as Je,bV as ce,eo as T,bL as Xe,d8 as es,g8 as ss,g9 as ts,ga as rs,bG as le,d9 as ns,gb as is,eD as G,gc as as,bO as os,c4 as te,bZ as S,aP as cs,aQ as re,aR as ne,w as B,bW as I,v as de,P as M,b$ as w,V as ue,b as he,c1 as W,aE as ge,be as pe,bf as me,B as k,D as fe,E as be,M as v,aJ as xe,bh as je,aw as ls,c0 as ye,bg as ds,X as us,Y as ve,Z as q,au as Re,g as hs,I as gs,bm as ps,bH as ms,aM as fs,u as bs,z as xs,dg as js,b3 as ys,at as vs,av as Rs,W as Cs,bn as Ms,bo as N,as as Os,bt as Ss,bu as ws,R as Es,Q as F}from"./strapi-z7ApxZZq.js";import{P as x}from"./index-BpJMkQJ_.js";import{s as ie}from"./sortBy-DX26BOXv.js";import{_ as ks}from"./_arrayIncludesWith-BNzMLSv9.js";import{t as Ps,m as Ls}from"./tail-ClM4A4bE.js";import{S as Ds}from"./SearchInput-Cmj1ynyp.js";import"./_baseMap-BaaWdrf-.js";import"./_baseEach-ZnnftuGj.js";var As=function(s){Ve(t,s);function t(n,a){var r;return r=s.call(this)||this,r.client=n,r.queries=[],r.result=[],r.observers=[],r.observersMap={},a&&r.setQueries(a),r}var i=t.prototype;return i.onSubscribe=function(){var a=this;this.listeners.length===1&&this.observers.forEach(function(r){r.subscribe(function(o){a.onUpdate(r,o)})})},i.onUnsubscribe=function(){this.listeners.length||this.destroy()},i.destroy=function(){this.listeners=[],this.observers.forEach(function(a){a.destroy()})},i.setQueries=function(a,r){this.queries=a,this.updateObservers(r)},i.getCurrentResult=function(){return this.result},i.getOptimisticResult=function(a){return this.findMatchingObservers(a).map(function(r){return r.observer.getOptimisticResult(r.defaultedQueryOptions)})},i.findMatchingObservers=function(a){var r=this,o=this.observers,l=a.map(function(h){return r.client.defaultQueryObserverOptions(h)}),d=l.flatMap(function(h){var j=o.find(function(R){return R.options.queryHash===h.queryHash});return j!=null?[{defaultedQueryOptions:h,observer:j}]:[]}),p=d.map(function(h){return h.defaultedQueryOptions.queryHash}),g=l.filter(function(h){return!p.includes(h.queryHash)}),u=o.filter(function(h){return!d.some(function(j){return j.observer===h})}),c=g.map(function(h,j){if(h.keepPreviousData){var R=u[j];if(R!==void 0)return{defaultedQueryOptions:h,observer:R}}return{defaultedQueryOptions:h,observer:r.getObserver(h)}}),m=function(j,R){return l.indexOf(j.defaultedQueryOptions)-l.indexOf(R.defaultedQueryOptions)};return d.concat(c).sort(m)},i.getObserver=function(a){var r=this.client.defaultQueryObserverOptions(a),o=this.observersMap[r.queryHash];return o??new Ge(this.client,r)},i.updateObservers=function(a){var r=this;V.batch(function(){var o=r.observers,l=r.findMatchingObservers(r.queries);l.forEach(function(c){return c.observer.setOptions(c.defaultedQueryOptions,a)});var d=l.map(function(c){return c.observer}),p=Object.fromEntries(d.map(function(c){return[c.options.queryHash,c]})),g=d.map(function(c){return c.getCurrentResult()}),u=d.some(function(c,m){return c!==o[m]});o.length===d.length&&!u||(r.observers=d,r.observersMap=p,r.result=g,r.hasListeners()&&(ee(o,d).forEach(function(c){c.destroy()}),ee(d,o).forEach(function(c){c.subscribe(function(m){r.onUpdate(c,m)})}),r.notify()))})},i.onUpdate=function(a,r){var o=this.observers.indexOf(a);o!==-1&&(this.result=We(this.result,o,r),this.notify())},i.notify=function(){var a=this;V.batch(function(){a.listeners.forEach(function(r){r(a.result)})})},t}(ze);function Ts(s){var t=A.useRef(!1),i=A.useState(0),n=i[1],a=Ye(),r=f.useMemo(function(){return s.map(function(p){var g=a.defaultQueryObserverOptions(p);return g.optimisticResults=!0,g})},[s,a]),o=A.useState(function(){return new As(a,r)}),l=o[0],d=l.getOptimisticResult(r);return A.useEffect(function(){t.current=!0;var p=l.subscribe(V.batchCalls(function(){t.current&&n(function(g){return g+1})}));return function(){t.current=!1,p()}},[l]),A.useEffect(function(){l.setQueries(r,{listeners:!1})},[r,l]),d}const Ce=f.createContext({}),Me=({children:s,value:t})=>e.jsx(Ce.Provider,{value:t,children:s}),z=()=>f.useContext(Ce);Me.propTypes={children:x.node.isRequired,value:x.object.isRequired};function Is(s){switch(s){case"application":return"Application";case"plugin::content-manager":return"Content manager";case"plugin::content-type-builder":return"Content types builder";case"plugin::documentation":return"Documentation";case"plugin::email":return"Email";case"plugin::i18n":return"i18n";case"plugin::upload":return"Media Library";case"plugin::users-permissions":return"Users-permissions";default:return Ze(s.replace("api::","").replace("plugin::",""))}}const _s=(s,t)=>{const i=Object.keys(t).sort().map(n=>({name:n,isOpen:!1}));return{...s,collapses:i}},ae=Ke`
  background: ${s=>s.theme.colors.primary100};

  #cog {
    opacity: 1;
  }
`,$s=U(P)`
  display: flex;
  justify-content: space-between;
  align-items: center;

  #cog {
    opacity: 0;
    path {
      fill: ${s=>s.theme.colors.primary600};
    }
  }

  /* Show active style both on hover and when the action is selected */
  ${s=>s.isActive&&ae}
  &:hover {
    ${ae}
  }
`,qs=U.div`
  flex: 1;
  align-self: center;
  border-top: 1px solid ${({theme:s})=>s.colors.neutral150};
`,Oe=({subCategory:s})=>{const{formatMessage:t}=E(),{onChange:i,onChangeSelectAll:n,onSelectedAction:a,selectedAction:r,modifiedData:o}=z(),l=f.useMemo(()=>Q(o,s.name,{}),[o,s]),d=f.useMemo(()=>Object.values(l).every(c=>c.enabled===!0),[l]),p=f.useMemo(()=>Object.values(l).some(c=>c.enabled===!0)&&!d,[l,d]),g=f.useCallback(({target:{name:c}})=>{n({target:{name:c,value:!d}})},[d,n]),u=f.useCallback(c=>r===c,[r]);return e.jsxs(P,{children:[e.jsxs(y,{justifyContent:"space-between",alignItems:"center",children:[e.jsx(P,{paddingRight:4,children:e.jsx(b,{variant:"sigma",textColor:"neutral600",children:s.label})}),e.jsx(qs,{}),e.jsx(P,{paddingLeft:4,children:e.jsx(se,{name:s.name,checked:p?"indeterminate":d,onCheckedChange:c=>g({target:{name:s.name,value:c}}),children:t({id:"app.utils.select-all",defaultMessage:"Select all"})})})]}),e.jsx(y,{paddingTop:6,paddingBottom:6,children:e.jsx(O.Root,{gap:2,style:{flex:1},children:s.actions.map(c=>{const m=`${c.name}.enabled`;return e.jsx(O.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs($s,{isActive:u(c.name),padding:2,hasRadius:!0,children:[e.jsx(se,{checked:Q(o,m,!1),name:m,onCheckedChange:h=>i({target:{name:m,value:h}}),children:c.label}),e.jsxs("button",{type:"button",onClick:()=>a(c.name),style:{display:"inline-flex",alignItems:"center"},children:[e.jsx(oe,{tag:"span",children:t({id:"app.utils.show-bound-route",defaultMessage:"Show bound route for {route}"},{route:c.name})}),e.jsx(Je,{id:"cog",cursor:"pointer"})]})]})},c.name)})})})]})};Oe.propTypes={subCategory:x.object.isRequired};const Se=({name:s,permissions:t})=>{const i=f.useMemo(()=>ie(Object.values(t.controllers).reduce((n,a,r)=>{const o=`${s}.controllers.${Object.keys(t.controllers)[r]}`,l=ie(Object.keys(a).reduce((d,p)=>[...d,{...a[p],label:p,name:`${o}.${p}`}],[]),"label");return[...n,{actions:l,label:Object.keys(t.controllers)[r],name:o}]},[]),"label"),[s,t]);return e.jsx(P,{padding:6,children:i.map(n=>e.jsx(Oe,{subCategory:n},n.name))})};Se.propTypes={name:x.string.isRequired,permissions:x.object.isRequired};const Ns={collapses:[]},Qs=(s,t)=>ce(s,i=>{switch(t.type){case"TOGGLE_COLLAPSE":{i.collapses=s.collapses.map((n,a)=>a===t.index?{...n,isOpen:!n.isOpen}:{...n,isOpen:!1});break}default:return i}}),Us=()=>{const{modifiedData:s}=z(),{formatMessage:t}=E(),[{collapses:i}]=f.useReducer(Qs,Ns,n=>_s(n,s));return e.jsx(T.Root,{size:"M",children:e.jsx(y,{direction:"column",alignItems:"stretch",gap:1,children:i.map((n,a)=>e.jsxs(T.Item,{value:n.name,children:[e.jsx(T.Header,{variant:a%2===0?"secondary":void 0,children:e.jsx(T.Trigger,{caretPosition:"right",description:t({id:"users-permissions.Plugin.permissions.plugins.description",defaultMessage:"Define all allowed actions for the {name} plugin."},{name:n.name}),children:Is(n.name)})}),e.jsx(T.Content,{children:e.jsx(Se,{permissions:s[n.name],name:n.name})})]},n.name))})})};var Bs=ss,Hs=ts,Fs=ks,Vs=Xe,Gs=es,Ws=rs,zs=200;function Ys(s,t,i,n){var a=-1,r=Hs,o=!0,l=s.length,d=[],p=t.length;if(!l)return d;i&&(t=Vs(t,Gs(i))),n?(r=Fs,o=!1):t.length>=zs&&(r=Ws,o=!1,t=new Bs(t));e:for(;++a<l;){var g=s[a],u=i==null?g:i(g);if(g=n||g!==0?g:0,o&&u===u){for(var c=p;c--;)if(t[c]===u)continue e;d.push(g)}else r(t,u,n)||d.push(g)}return d}var Zs=Ys,Ks=Zs,Js=ns,Xs=is,et=Js(function(s,t){return Xs(s)?Ks(s,t):[]}),st=et;const tt=le(st),rt=s=>{switch(s){case"POST":return{text:"success600",border:"success200",background:"success100"};case"GET":return{text:"secondary600",border:"secondary200",background:"secondary100"};case"PUT":return{text:"warning600",border:"warning200",background:"warning100"};case"DELETE":return{text:"danger600",border:"danger200",background:"danger100"};default:return{text:"neutral600",border:"neutral200",background:"neutral100"}}},nt=U(P)`
  margin: -1px;
  border-radius: ${({theme:s})=>s.spaces[1]} 0 0 ${({theme:s})=>s.spaces[1]};
`;function Y({route:s}){const{formatMessage:t}=E(),{method:i,handler:n,path:a}=s,r=a?Ps(a.split("/")):[],[o="",l=""]=n?n.split("."):[],d=rt(s.method);return e.jsxs(y,{direction:"column",alignItems:"stretch",gap:2,children:[e.jsxs(b,{variant:"delta",tag:"h3",children:[t({id:"users-permissions.BoundRoute.title",defaultMessage:"Bound route to"})," ",e.jsx("span",{children:o}),e.jsxs(b,{variant:"delta",textColor:"primary600",children:[".",l]})]}),e.jsxs(y,{hasRadius:!0,background:"neutral0",borderColor:"neutral200",gap:0,children:[e.jsx(nt,{background:d.background,borderColor:d.border,padding:2,children:e.jsx(b,{fontWeight:"bold",textColor:d.text,children:i})}),e.jsx(P,{paddingLeft:2,paddingRight:2,children:Ls(r,p=>e.jsxs(b,{textColor:p.includes(":")?"neutral600":"neutral900",children:["/",p]},p))})]})]})}Y.defaultProps={route:{handler:"Nocontroller.error",method:"GET",path:"/there-is-no-path"}};Y.propTypes={route:x.shape({handler:x.string,method:x.string,path:x.string})};const it=()=>{const{formatMessage:s}=E(),{selectedAction:t,routes:i}=z(),n=tt(t.split("."),"controllers"),a=Q(i,n[0]),r=n.slice(1).join("."),o=G(a)?[]:a.filter(l=>l.handler.endsWith(r));return e.jsx(O.Item,{col:5,background:"neutral150",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,style:{minHeight:"100%"},direction:"column",alignItems:"stretch",children:t?e.jsx(y,{direction:"column",alignItems:"stretch",gap:2,children:o.map((l,d)=>e.jsx(Y,{route:l},d))}):e.jsxs(y,{direction:"column",alignItems:"stretch",gap:2,children:[e.jsx(b,{variant:"delta",tag:"h3",children:s({id:"users-permissions.Policies.header.title",defaultMessage:"Advanced settings"})}),e.jsx(b,{tag:"p",textColor:"neutral600",children:s({id:"users-permissions.Policies.header.hint",defaultMessage:"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route"})})]})})},at=(s,t,i)=>({...s,initialData:t,modifiedData:t,routes:i});var ot=os,ct=as;function lt(s,t,i){return s&&s.length?(t=i||t===void 0?1:ct(t),ot(s,0,t<0?0:t)):[]}var dt=lt;const ut=le(dt),ht={initialData:{},modifiedData:{},routes:{},selectedAction:"",policies:[]},gt=(s,t)=>ce(s,i=>{switch(t.type){case"ON_CHANGE":{const n=t.keys.length,a=t.keys[n-1]==="enabled";if(t.value&&a){const r=ut(t.keys,n-1).join(".");i.selectedAction=r}te(i,["modifiedData",...t.keys],t.value);break}case"ON_CHANGE_SELECT_ALL":{const n=["modifiedData",...t.keys],a=Q(s,n,{}),r=Object.keys(a).reduce((o,l)=>(o[l]={...a[l],enabled:t.value},o),{});te(i,n,r);break}case"ON_RESET":{i.modifiedData=s.initialData;break}case"ON_SUBMIT_SUCCEEDED":{i.initialData=s.modifiedData;break}case"SELECT_ACTION":{const{actionToSelect:n}=t;i.selectedAction=n===s.selectedAction?"":n;break}default:return i}}),we=f.forwardRef(({permissions:s,routes:t},i)=>{const{formatMessage:n}=E(),[a,r]=f.useReducer(gt,ht,g=>at(g,s,t));f.useImperativeHandle(i,()=>({getPermissions(){return{permissions:a.modifiedData}},resetForm(){r({type:"ON_RESET"})},setFormAfterSubmit(){r({type:"ON_SUBMIT_SUCCEEDED"})}}));const p={...a,onChange:({target:{name:g,value:u}})=>r({type:"ON_CHANGE",keys:g.split("."),value:u==="empty__string_value"?"":u}),onChangeSelectAll:({target:{name:g,value:u}})=>r({type:"ON_CHANGE_SELECT_ALL",keys:g.split("."),value:u}),onSelectedAction:g=>r({type:"SELECT_ACTION",actionToSelect:g})};return e.jsx(Me,{value:p,children:e.jsxs(O.Root,{gap:0,shadow:"filterShadow",hasRadius:!0,background:"neutral0",children:[e.jsx(O.Item,{col:7,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,direction:"column",alignItems:"stretch",children:e.jsxs(y,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsxs(y,{direction:"column",alignItems:"stretch",gap:2,children:[e.jsx(b,{variant:"delta",tag:"h2",children:n({id:S("Plugins.header.title"),defaultMessage:"Permissions"})}),e.jsx(b,{tag:"p",textColor:"neutral600",children:n({id:S("Plugins.header.description"),defaultMessage:"Only actions bound by a route are listed below."})})]}),e.jsx(Us,{})]})}),e.jsx(it,{})]})})});we.propTypes={permissions:x.object.isRequired,routes:x.object.isRequired};var Ee=f.memo(we);const ke=cs().shape({name:re().required(ne.required.id),description:re().required(ne.required.id)}),pt=s=>Object.keys(s).reduce((t,i)=>{const n=s[i].controllers,a=Object.keys(n).reduce((r,o)=>(G(n[o])||(r[o]=n[o]),r),{});return G(a)||(t[i]={controllers:a}),t},{}),Pe=()=>{const{toggleNotification:s}=B(),{get:t}=I(),{formatAPIError:i}=de(S),[{data:n,isLoading:a,error:r,refetch:o},{data:l,isLoading:d,error:p,refetch:g}]=Ts([{queryKey:["users-permissions","permissions"],async queryFn(){const{data:{permissions:m}}=await t("/users-permissions/permissions");return m}},{queryKey:["users-permissions","routes"],async queryFn(){const{data:{routes:m}}=await t("/users-permissions/routes");return m}}]),u=async()=>{await Promise.all([o(),g()])};f.useEffect(()=>{r&&s({type:"danger",message:i(r)})},[s,r,i]),f.useEffect(()=>{p&&s({type:"danger",message:i(p)})},[s,p,i]);const c=a||d;return{permissions:n?pt(n):{},routes:l??{},getData:u,isLoading:c}},mt=()=>{const{formatMessage:s}=E(),{toggleNotification:t}=B(),i=ue(),{isLoading:n,permissions:a,routes:r}=Pe(),{trackUsage:o}=he(),l=f.useRef(),{post:d}=I(),p=W(u=>d("/users-permissions/roles",u),{onError(){t({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occurred"})})},onSuccess(){o("didCreateRole"),t({type:"success",message:s({id:S("Settings.roles.created"),defaultMessage:"Role created"})}),i(-1)}}),g=async u=>{const c=l.current.getPermissions();await p.mutate({...u,...c,users:[]})};return e.jsxs(ge,{children:[e.jsx(M.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Roles"})}),e.jsx(pe,{enableReinitialize:!0,initialValues:{name:"",description:""},onSubmit:g,validationSchema:ke,children:({handleSubmit:u,values:c,handleChange:m,errors:h})=>e.jsxs(me,{noValidate:!0,onSubmit:u,children:[e.jsx(k.Header,{primaryAction:!n&&e.jsx(fe,{type:"submit",loading:p.isLoading,startIcon:e.jsx(be,{}),children:s({id:"global.save",defaultMessage:"Save"})}),title:s({id:"Settings.roles.create.title",defaultMessage:"Create a role"}),subtitle:s({id:"Settings.roles.create.description",defaultMessage:"Define the rights given to the role"})}),e.jsx(k.Content,{children:e.jsxs(y,{background:"neutral0",direction:"column",alignItems:"stretch",gap:7,hasRadius:!0,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,shadow:"filterShadow",children:[e.jsxs(y,{direction:"column",alignItems:"stretch",children:[e.jsx(b,{variant:"delta",tag:"h2",children:s({id:S("EditPage.form.roles"),defaultMessage:"Role details"})}),e.jsxs(O.Root,{gap:4,children:[e.jsx(O.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(v.Root,{name:"name",error:h?.name?s({id:h.name,defaultMessage:"Name is required"}):!1,required:!0,children:[e.jsx(v.Label,{children:s({id:"global.name",defaultMessage:"Name"})}),e.jsx(xe,{value:c.name||"",onChange:m}),e.jsx(v.Error,{})]})}),e.jsx(O.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(v.Root,{name:"description",error:h?.description?s({id:h.description,defaultMessage:"Description is required"}):!1,required:!0,children:[e.jsx(v.Label,{children:s({id:"global.description",defaultMessage:"Description"})}),e.jsx(je,{value:c.description||"",onChange:m}),e.jsx(v.Error,{})]})})]})]}),!n&&e.jsx(Ee,{ref:l,permissions:a,routes:r})]})})]})})]})},ft=()=>e.jsx(M.Protect,{permissions:w.createRole,children:e.jsx(mt,{})}),bt=()=>{const{formatMessage:s}=E(),{toggleNotification:t}=B(),{params:{id:i}}=ls("/settings/users-permissions/roles/:id"),{get:n}=I(),{isLoading:a,routes:r}=Pe(),{data:o,isLoading:l,refetch:d}=ye(["users-permissions","role",i],async()=>{const{data:{role:h}}=await n(`/users-permissions/roles/${i}`);return h}),p=f.useRef(),{put:g}=I(),{formatAPIError:u}=de(),c=W(h=>g(`/users-permissions/roles/${i}`,h),{onError(h){t({type:"danger",message:u(h)})},async onSuccess(){t({type:"success",message:s({id:S("Settings.roles.created"),defaultMessage:"Role edited"})}),await d()}}),m=async h=>{const j=p.current.getPermissions();await c.mutate({...h,...j,users:[]})};return l?e.jsx(M.Loading,{}):e.jsxs(ge,{children:[e.jsx(M.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Roles"})}),e.jsx(pe,{enableReinitialize:!0,initialValues:{name:o.name,description:o.description},onSubmit:m,validationSchema:ke,children:({handleSubmit:h,values:j,handleChange:R,errors:L})=>e.jsxs(me,{noValidate:!0,onSubmit:h,children:[e.jsx(k.Header,{primaryAction:a?null:e.jsx(fe,{disabled:o.code==="strapi-super-admin",type:"submit",loading:c.isLoading,startIcon:e.jsx(be,{}),children:s({id:"global.save",defaultMessage:"Save"})}),title:o.name,subtitle:o.description,navigationAction:e.jsx(ds,{fallback:".."})}),e.jsx(k.Content,{children:e.jsxs(y,{background:"neutral0",direction:"column",alignItems:"stretch",gap:7,hasRadius:!0,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,shadow:"filterShadow",children:[e.jsxs(y,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(b,{variant:"delta",tag:"h2",children:s({id:S("EditPage.form.roles"),defaultMessage:"Role details"})}),e.jsxs(O.Root,{gap:4,children:[e.jsx(O.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(v.Root,{name:"name",error:L?.name?s({id:L.name,defaultMessage:"Name is required"}):!1,required:!0,children:[e.jsx(v.Label,{children:s({id:"global.name",defaultMessage:"Name"})}),e.jsx(xe,{value:j.name||"",onChange:R}),e.jsx(v.Error,{})]})}),e.jsx(O.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(v.Root,{name:"description",error:L?.description?s({id:L.description,defaultMessage:"Description is required"}):!1,required:!0,children:[e.jsx(v.Label,{children:s({id:"global.description",defaultMessage:"Description"})}),e.jsx(je,{value:j.description||"",onChange:R}),e.jsx(v.Error,{})]})})]})]}),!a&&e.jsx(Ee,{ref:p,permissions:o.permissions,routes:r})]})})]})})]})},xt=()=>e.jsx(M.Protect,{permissions:w.updateRole,children:e.jsx(bt,{})}),jt=U(ms)`
  align-items: center;
  height: 3.2rem;
  width: 3.2rem;
  display: flex;
  justify-content: center;
  padding: ${({theme:s})=>`${s.spaces[2]}`};

  svg {
    height: 1.6rem;
    width: 1.6rem;

    path {
      fill: ${({theme:s})=>s.colors.neutral500};
    }
  }

  &:hover,
  &:focus {
    svg {
      path {
        fill: ${({theme:s})=>s.colors.neutral800};
      }
    }
  }
`,Z=({sortedRoles:s,canDelete:t,canUpdate:i,setRoleToDelete:n,onDelete:a})=>{const{formatMessage:r}=E(),o=ue(),[l,d]=a,p=u=>t&&!["public","authenticated"].includes(u.type),g=u=>{n(u),d(!l)};return e.jsx(us,{children:s?.map(u=>e.jsxs(ve,{cursor:"pointer",onClick:()=>o(u.id.toString()),children:[e.jsx(q,{width:"20%",children:e.jsx(b,{children:u.name})}),e.jsx(q,{width:"50%",children:e.jsx(b,{children:u.description})}),e.jsx(q,{width:"30%",children:e.jsx(b,{children:r({id:"Roles.RoleRow.user-count",defaultMessage:"{number, plural, =0 {# user} one {# user} other {# users}}"},{number:u.nb_users})})}),e.jsx(q,{children:e.jsxs(y,{justifyContent:"end",onClick:c=>c.stopPropagation(),children:[i?e.jsx(jt,{tag:Re,to:u.id.toString(),"aria-label":r({id:"app.component.table.edit",defaultMessage:"Edit {target}"},{target:`${u.name}`}),children:e.jsx(hs,{})}):null,p(u)&&e.jsx(gs,{onClick:()=>g(u.id.toString()),variant:"ghost",label:r({id:"global.delete-target",defaultMessage:"Delete {target}"},{target:`${u.name}`}),children:e.jsx(ps,{})})]})})]},u.name))})};Z.defaultProps={canDelete:!1,canUpdate:!1};Z.propTypes={onDelete:x.array.isRequired,setRoleToDelete:x.func.isRequired,sortedRoles:x.array.isRequired,canDelete:x.bool,canUpdate:x.bool};const yt=()=>{const{trackUsage:s}=he(),{formatMessage:t,locale:i}=E(),{toggleNotification:n}=B(),{notifyStatus:a}=fs(),[{query:r}]=bs(),o=r?._q||"",[l,d]=f.useState(!1),[p,g]=f.useState(),{del:u,get:c}=I(),{isLoading:m,allowedActions:{canRead:h,canDelete:j,canCreate:R,canUpdate:L}}=xs({create:w.createRole,read:w.readRoles,update:w.updateRole,delete:w.deleteRole}),{isLoading:Le,data:{roles:K},isFetching:De,refetch:Ae}=ye("get-roles",()=>$e(n,t,a),{initialData:{},enabled:h}),{contains:J}=js(i,{sensitivity:"base"}),X=ys(i,{sensitivity:"base"}),Te=Le||De||m,Ie=()=>{d(!l)},_e=async(C,D,H)=>{try{await u(`/users-permissions/roles/${C}`)}catch{H({type:"danger",message:D({id:"notification.error",defaultMessage:"An error occured"})})}},$e=async(C,D,H)=>{try{const{data:$}=await c("/users-permissions/roles");return H("The roles have loaded successfully"),$}catch($){throw C({type:"danger",message:D({id:"notification.error",defaultMessage:"An error occurred"})}),new Error($)}},qe={roles:{id:S("Roles.empty"),defaultMessage:"You don't have any roles yet."},search:{id:S("Roles.empty.search"),defaultMessage:"No roles match the search."}},Ne=t({id:"global.roles",defaultMessage:"Roles"}),Qe=W(C=>_e(C,t,n),{async onSuccess(){await Ae()}}),Ue=async()=>{await Qe.mutateAsync(p),d(!l)},_=(K||[]).filter(C=>J(C.name,o)||J(C.description,o)).sort((C,D)=>X.compare(C.name,D.name)||X.compare(C.description,D.description)),Be=o&&!_.length?"search":"roles",He=4,Fe=(K?.length||0)+1;return Te?e.jsx(M.Loading,{}):e.jsxs(k.Root,{children:[e.jsx(M.Title,{children:t({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:Ne})}),e.jsxs(M.Main,{children:[e.jsx(k.Header,{title:t({id:"global.roles",defaultMessage:"Roles"}),subtitle:t({id:"Settings.roles.list.description",defaultMessage:"List of roles"}),primaryAction:R?e.jsx(vs,{to:"new",tag:Re,onClick:()=>s("willCreateRole"),startIcon:e.jsx(Rs,{}),size:"S",children:t({id:S("List.button.roles"),defaultMessage:"Add new role"})}):null}),e.jsx(k.Action,{startActions:e.jsx(Ds,{label:t({id:"app.component.search.label",defaultMessage:"Search"})})}),e.jsxs(k.Content,{children:[!h&&e.jsx(M.NoPermissions,{}),h&&_&&_?.length?e.jsxs(Cs,{colCount:He,rowCount:Fe,children:[e.jsx(Ms,{children:e.jsxs(ve,{children:[e.jsx(N,{children:e.jsx(b,{variant:"sigma",textColor:"neutral600",children:t({id:"global.name",defaultMessage:"Name"})})}),e.jsx(N,{children:e.jsx(b,{variant:"sigma",textColor:"neutral600",children:t({id:"global.description",defaultMessage:"Description"})})}),e.jsx(N,{children:e.jsx(b,{variant:"sigma",textColor:"neutral600",children:t({id:"global.users",defaultMessage:"Users"})})}),e.jsx(N,{children:e.jsx(oe,{children:t({id:"global.actions",defaultMessage:"Actions"})})})]})}),e.jsx(Z,{sortedRoles:_,canDelete:j,canUpdate:L,permissions:w,setRoleToDelete:g,onDelete:[l,d]})]}):e.jsx(Os,{content:t(qe[Be])})]}),e.jsx(Ss.Root,{open:l,onOpenChange:Ie,children:e.jsx(ws,{onConfirm:Ue})})]})]})},vt=()=>e.jsx(M.Protect,{permissions:w.accessRoles,children:e.jsx(yt,{})}),Pt=()=>e.jsx(M.Protect,{permissions:w.accessRoles,children:e.jsxs(Es,{children:[e.jsx(F,{index:!0,element:e.jsx(vt,{})}),e.jsx(F,{path:"new",element:e.jsx(ft,{})}),e.jsx(F,{path:":id",element:e.jsx(xt,{})})]})});export{Pt as default};
